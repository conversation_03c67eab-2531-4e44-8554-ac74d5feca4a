import { Injectable } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ProbeHistoryResponse } from 'src/app/model/probe/ProbeHistoryResponse.model';
import { FeatureHistoryDetailComponent } from 'src/app/FeatureModule/Probe/feature-history-detail/feature-history-detail.component';

@Injectable({
  providedIn: 'root'
})
export class FeatureHistoryDetailService {

  constructor(private modalService: NgbModal) { }
  public openFeatureHistoryDetailModel(
    header: string,
    history: ProbeHistoryResponse,
    probeId: number,
    activeModelReference?: NgbActiveModal): Promise<boolean> {
    const modalRef = this.modalService.open(FeatureHistoryDetailComponent, { windowClass: "modal fade modal-dialog-lg-middle", size: 'lg' });
    modalRef.componentInstance.header = header;
    modalRef.componentInstance.history = history;
    modalRef.componentInstance.probeId = probeId;
    modalRef.componentInstance.activeModelReference = activeModelReference;
    return modalRef.result;

  }
}
