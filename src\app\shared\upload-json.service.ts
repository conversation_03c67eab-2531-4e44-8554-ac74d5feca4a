import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { UploadJsonDialogComponent } from '../video/upload-json-dialog/upload-json-dialog.component';
import { BasicModelConfig } from '../model/common/BasicModelConfig.model';

@Injectable({
  providedIn: 'root'
})
export class UploadJsonService {

  constructor(
    private modalService: NgbModal
  ) { }

  public confirm(
    basicModelConfig: BasicModelConfig,
    jsonFileObj: any,
    videoIds: number[] | null,
    btnBacktoSelection: string | null,
    dialogSize: 'sm' | 'lg' = 'sm'): Promise<any> {
    const modalRef = this.modalService.open(UploadJsonDialogComponent, { windowClass: 'uploadVideoModelwidth' });
    modalRef.componentInstance.basicModelConfig = basicModelConfig;
    modalRef.componentInstance.jsonFileObj = jsonFileObj;
    modalRef.componentInstance.videoIds = videoIds;
    modalRef.componentInstance.btnBacktoSelection = btnBacktoSelection;
    return modalRef.result;
  }
}
