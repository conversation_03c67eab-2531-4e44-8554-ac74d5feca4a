<!-- name : device component -->
<!-- description : use for listing of Devices -->
<!-- loading start -->
<div class="ringLoading" *ngIf="loading">
    <!-- loading gif start -->
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
    <!-- loading gif end -->
</div>
<!-- loading end -->

<body>
    <!--Device detail start-->
    <div class="row">

        <!--Filter start-->
        <div class="col-md-3 pr-0" *ngIf="(!isFilterHidden)">
            <app-device-filter [isFilterComponentInitWithApicall]="isFilterComponentInitWithApicall">
            </app-device-filter>
        </div>
        <!--Filter End-->
        <!--table Block Start-->
        <div [className]="(isFilterHidden)?'col-md-12 pr-0':'col-md-9 pr-0'" id="main">
            <div class="container-fluid">
                <!--view Devive Start-->
                <div class="row" class="headerAlignment">
                    <!--------------------------------------->
                    <!--Left Side-->
                    <!--------------------------------------->
                    <div class="childFlex">
                        <!-- show / hide filter - start -->
                        <div class="dropdown">
                            <button class="btn btn-sm btn-orange mr-3 ml-0 filter-symbol" (click)="toggleFilter()"
                                [id]="(isFilterHidden)?'Show Filter':'Hide Filter'">
                                <i class="fas fa-filter" aria-hidden="true"
                                    [id]="(isFilterHidden)?'Show Filter':'Hide Filter'"></i>
                                &nbsp;&nbsp;{{ hideShowFilterButtonText }}
                            </button>
                        </div>
                        <!-- show / hide filter - end -->
                        <!-- show device entry - start -->
                        <div>
                            <label class="mb-0">{{showEntry}}</label>
                            <!-- entry selection start -->
                            <select id="DeviceShowEntry" [(ngModel)]="drpselectsize"
                                class="form-control form-control-sm" (change)="changeDataSize($event)">
                                <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                                    <option [value]="dataSize">{{ dataSize }}</option>
                                </ng-template>
                            </select>
                            <!-- entry selection end -->
                        </div>
                        <!-- show device entry - end -->
                    </div>
                    <!--------------------------------------->
                    <!--Right Side-->
                    <!--------------------------------------->
                    <div class="childFlex">
                        <!-- device operation dropdown start -->
                        <div class="mr-3" *ngIf="deviceOperations.length > 1">
                            <select id="deviceOperation" class="form-control form-control-sm"
                                (change)="changeDeviceOperation($event)">
                                <ng-template ngFor let-deviceOperation [ngForOf]="deviceOperations">
                                    <option [value]="deviceOperation">{{ deviceOperation }}
                                    </option>
                                </ng-template>
                            </select>
                        </div>
                        <!--device operation dropdown end  -->
                        <div>
                            <!-- clear filter button -->
                            <button class="btn btn-sm btn-orange" id="refresh_DeviceList"
                                (click)="clickOnRefreshButton()"><em class="fa fa-refresh"></em></button>
                        </div>
                    </div>
                </div>
                <!--view device Start-->
                <div>Total {{totalDevice}} Devices
                    <p *ngIf="deviceIdList != null && deviceIdList.length > 0">
                        <strong>{{deviceIdList.length}}
                            Devices selected</strong>
                    </p>
                </div>
                <div class="device-table">
                    <!-------------------------------->
                    <!-- device list table - start -->
                    <!-------------------------------->
                    <table class="table table-sm table-bordered" aria-hidden="true">
                        <!-------------------------------->
                        <!-- table heading start -->
                        <!-------------------------------->
                        <thead>
                            <tr class="thead-light">
                                <th class="checkox-table width-unset" *ngIf="checkboxDisplayPermission">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" name="chkseleclall"
                                            id="selectDevice" (change)="selectAllDevice($event)">
                                        <label class="custom-control-label" for="selectDevice"></label>
                                    </div>
                                </th>
                                <th><span>{{serialNo}}</span></th>
                                <th><span>{{hwId}}</span></th>
                                <th><span>{{salesOrderNumber}}</span></th>
                                <th><span>{{costomerName}}</span></th>
                                <th><span>{{country}}</span></th>
                                <th><span>{{systemSwVerstion}}</span></th>
                                <th><span>{{lastCheckinDataAndType}}</span></th>
                                <th><span>{{deviceType}}</span></th>
                                <th class="min_column_width"><span>{{status}}</span></th>
                                <th class="min_column_width"><span>{{editable}}</span></th>
                                <th class="min_column_width"><span>{{locked}}</span></th>
                            </tr>
                        </thead>
                        <!-- table heading end -->
                        <!-- table body start -->
                        <tbody>
                            <tr *ngFor="let device of devices ; let i = index;">
                                <td *ngIf="checkboxDisplayPermission" class="width-unset">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" [id]="device.deviceId+i+i+i"
                                            name="device[]" [checked]="defaultSelectDevice(device.id)"
                                            (change)="onChangeDevice(device, $event)">
                                        <label class="custom-control-label" [for]="device.deviceId+i+i+i"></label>
                                    </div>
                                </td>
                                <td><span>{{device.deviceSerialNo}}</span></td>
                                <td *ngIf="deviceRederPermission" class="spanunderline" data-toggle="tooltip"
                                    data-placement="top" title="{{device.deviceId}}"
                                    (click)="deviceDetailModel(device.id)" [id]="'device' + device.deviceId">
                                    <span>{{device.deviceId}}</span>
                                </td>
                                <td *ngIf="!deviceRederPermission">
                                    <span>{{device.deviceId}}</span>
                                </td>
                                <td><span>{{device.salesOrderNumber}}</span>
                                </td>
                                <td><span>{{device.customerName}}</span></td>
                                <td>
                                    <span>{{ device.country }}</span>
                                </td>
                                <td><span>{{device.packageVersion}}</span></td>
                                <td><span>{{device.lastCheckInTime |
                                        date:dateFormat}}</span>
                                </td>
                                <td><span>{{device.deviceType | deviceTypeName}}</span>
                                </td>
                                <td class="min_column_width">{{device?.productStatus |
                                    enumMappingDisplayNamePipe:productStatusList}}
                                </td>
                                <td class="min_column_width text-center">
                                    <ng-container *ngIf="device.editable">
                                        <div title="Allowed">
                                            <span class="fa fa-pencil-alt"
                                                style="font-size: 18px; color: #f67409;"></span>
                                        </div>
                                    </ng-container>
                                    <ng-container *ngIf="!device.editable">
                                        <div title="Not Allowed">
                                            <img alt="edit Disable" src="assets/images/editd.png">
                                        </div>
                                    </ng-container>
                                </td>
                                <td class="min_column_width">
                                    <div class=" lockedDiv" *ngIf="device?.locked != null"
                                        [attr.title]="device.locked ? 'Lock' : 'Unlock'">
                                        <span [ngClass]="[ device.locked ? 'lock' : 'lock unlocked']"
                                            id="{{device.deviceId}}"></span>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                        <!-- table body end -->
                    </table>
                    <!-- device list table - end -->
                </div>

                <!--paging Start-->
                <div>
                    <div>Showing {{totalDeviceDisplay}} out of {{totalDevice}} Devices
                    </div>
                    <div class="float-right">
                        <ngb-pagination [collectionSize]="totalItems" [(page)]="page" id="device-pagination"
                            [pageSize]="itemsPerPage" [maxSize]="5" [rotate]="true" [boundaryLinks]="true"
                            (pageChange)="loadPage(page)">
                        </ngb-pagination>
                    </div>
                </div>
                <!--paging End-->
            </div>
            <!--flud tab 9 row-->
        </div>
        <!--table Block End-->
    </div>
</body>