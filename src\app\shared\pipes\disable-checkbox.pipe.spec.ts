import { DisableLicenseCheckBoxpipe } from './disable-license-check-box.pipe';
import { CommonsService } from '../util/commons.service'; // Import the CommonsService
import { TestBed } from '@angular/core/testing';
import { LocalStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';

describe('DisableLicenseCheckBoxpipe', () => {
  let commonsService: CommonsService; // Declare a variable for the service

  beforeEach(() => {
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    // Set up the TestBed to provide the service
    TestBed.configureTestingModule({
      providers: [CommonsService,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
      ],
    });

    // Get the instance of CommonsService
    commonsService = TestBed.inject(CommonsService);
  });

  it('should create an instance', () => {
    // Pass the commonsService when creating the pipe
    const pipe = new DisableLicenseCheckBoxpipe(commonsService);
    expect(pipe).toBeTruthy();
  });
});
