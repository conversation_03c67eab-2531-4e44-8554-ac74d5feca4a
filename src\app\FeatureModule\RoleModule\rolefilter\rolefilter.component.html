<!--####################################################-->
<!--Filter Start-->
<!--####################################################-->
<form id="roleFilterform" role="form" class="form" [formGroup]="filterRoleForm">

  <!-----------Display Name-------------->
  <div class="form-group">
    <label class="form-control-label" for="field_serialNumber"><strong>Role
        Name</strong></label>
    <input class="form-control" type="text" formControlName="roleName" />
    <div *ngIf="(filterRoleForm.get('roleName').touched || filterRoleForm.get('roleName').dirty) && 
                 filterRoleForm.get('roleName').invalid">
      <div *ngIf="filterRoleForm.get('roleName').errors['maxlength']" class="pb-2">
        <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
      </div>
      <div *ngIf="filterRoleForm.get('roleName').errors['pattern']" class="pb-2">
        <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
      </div>
    </div>
  </div>

  <!-----------PACS Type-------------->
  <div class="form-group">
    <label class="form-control-label" for="field_permission"><strong>Permission</strong></label>
    <ng-multiselect-dropdown name="pacsType" [placeholder]="''" formControlName="permission" [disabled]="disabled"
      [settings]="permissionSetting" [data]="permissionList">
    </ng-multiselect-dropdown>
  </div>

  <hr class="mt-1 mb-2">
  <!--####################################################-->
  <!---------Action Button Start------->
  <!--####################################################-->
  <div class="">
    <button class="btn btn-sm btn-orange mr-3" (click)="searchData()" id="roleFilterSearch"
      [disabled]="filterRoleForm.invalid">Search</button>
    <button class="btn btn-sm btn-orange" (click)="clearFilter(defaultListingPageReloadSubjectParameter)">Clear</button>
  </div>
  <!--####################################################-->
  <!---------Action Button End------->
  <!--####################################################-->
</form>