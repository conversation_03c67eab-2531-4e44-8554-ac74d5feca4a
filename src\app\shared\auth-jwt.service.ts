import { HttpClient, HttpResponse } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { isNullOrUndefined } from 'is-what';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { RdmAction, RdmAuthenticationToken, RdmEmailToken, RdmUserAssignedCountries, RdmUserId, RdmUserRoles } from '../app.constants';
import { LoginResponse } from '../model/Login/LoginResponse.model';
import { API_BASE_URL } from './config';
import { PermissionService } from './permission.service';
import { CommonsService } from './util/commons.service';



@Injectable({
  providedIn: 'root'
})
export class AuthJwtService {
  constructor(private http: HttpClient,
    private $localStorage: LocalStorageService,
    private $sessionStorage: SessionStorageService,
    @Inject(API_BASE_URL) public SERVER_API_URL: string,
    private permissionService: PermissionService,
    private commonsService: CommonsService,
    private router: Router) { }

  public getToken(): string {
    return this.$localStorage.retrieve(RdmAuthenticationToken) || this.$sessionStorage.retrieve(RdmAuthenticationToken);
  }

  /**
   * Call Sync API to Get User Roles ,Permission and Country
   * 
   * <AUTHOR>
   * @returns 
   */
  public syncUserInfo(): Observable<HttpResponse<LoginResponse>> {
    return this.http.get<LoginResponse>(this.SERVER_API_URL + 'api/sync', { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Login Page navigate
   * 
   * <AUTHOR>
   */
  public loginNavigate(): void {
    this.router.navigate(['']);
  }

  /**
   * Check Authenticate
   * 
   * <AUTHOR>
   * @returns 
   */
  public isAuthenticate(): boolean {
    return !isNullOrUndefined(this.$localStorage.retrieve(RdmUserId));
  }


  public setCountryMasters(countryMasters): void {
    this.$localStorage.store(RdmUserAssignedCountries, countryMasters);
  }

  setPermission(permissions) {
    this.permissionService.setPermission(permissions);
  }
  setUserId(userId: number) {
    this.$localStorage.store(RdmUserId, userId);
  }

  storeAuthenticationToken(jwt, rememberMe) {
    this.$localStorage.store(RdmAuthenticationToken, jwt);
    this.$sessionStorage.store(RdmAuthenticationToken, jwt);
  }

  logout(): Observable<any> {
    return new Observable(observer => {
      this.$localStorage.clear(RdmAuthenticationToken);
      this.$localStorage.clear(RdmUserId);
      this.$sessionStorage.clear(RdmEmailToken);
      this.$sessionStorage.clear(RdmAction);
      this.$localStorage.clear(RdmUserAssignedCountries);
      this.$localStorage.clear(RdmUserRoles);
      observer.complete();
    });
  }
  clear() {
    this.$localStorage.clear(RdmAuthenticationToken);
    this.$localStorage.clear(RdmUserId);
    this.$localStorage.clear(RdmEmailToken);
    this.$localStorage.clear(RdmAction);
    this.$localStorage.clear(RdmUserAssignedCountries);
    this.$localStorage.clear(RdmUserRoles);
  }

  getEmailToket(): boolean {
    return this.$localStorage.retrieve(RdmEmailToken) == null;
  }

  /**
   * Store Login User Roles
   * 
   * <AUTHOR>
   * @param authorities 
   */
  public setUserRoles(authorities: Array<string>) {
    this.$localStorage.store(RdmUserRoles, authorities);
  }

  /**
   * Get Login User roles
   * 
   * <AUTHOR>
   * @returns 
   */
  public getUserRoles(): Array<string> {
    let roles = this.$localStorage.retrieve(RdmUserRoles);
    return isNullOrUndefined(roles) ? [] : roles;
  }
}
