import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { LanguageResponse } from 'src/app/model/Languages/LanguageResponse.model';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { PermissionService } from '../../permission.service';
import { CountryAndLanguageService } from './country-and-language.service';

describe('CountryAndLanguageService', () => {
  let service: CountryAndLanguageService;
  let httpMock: HttpTestingController;
  let mockPermissionService: jasmine.SpyObj<PermissionService>;
  let mockExceptionService: jasmine.SpyObj<ExceptionHandlingService>;
  let mockConfigInjectService: jasmine.SpyObj<ConfigInjectService>;

  beforeEach(() => {
    mockPermissionService = jasmine.createSpyObj('PermissionService', ['getLanguagePermission']);
    mockExceptionService = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    mockConfigInjectService = jasmine.createSpyObj('ConfigInjectService', ['getServerApiUrl']);

    mockConfigInjectService.getServerApiUrl.and.returnValue('http://localhost/');

    TestBed.configureTestingModule({
      imports: [],
      providers: [
        CountryAndLanguageService,
        { provide: PermissionService, useValue: mockPermissionService },
        { provide: ExceptionHandlingService, useValue: mockExceptionService },
        { provide: ConfigInjectService, useValue: mockConfigInjectService },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting()
      ]
    });

    service = TestBed.inject(CountryAndLanguageService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should return language list when permission is granted and API succeeds', async () => {
    const mockResponse: LanguageResponse[] = [
      { shotName: 'EN', displayName: 'English' } as LanguageResponse,
      { shotName: 'FR', displayName: 'French' } as LanguageResponse
    ];

    mockPermissionService.getLanguagePermission.and.returnValue(true);

    const promise = service.getLanguageList();

    const req = httpMock.expectOne('http://localhost/api/language');
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);

    const result = await promise;
    expect(result).toEqual(mockResponse);
  });

  it('should return empty array if permission is denied', async () => {
    mockPermissionService.getLanguagePermission.and.returnValue(false);

    const result = await service.getLanguageList();
    expect(result).toEqual([]);
    httpMock.expectNone('http://localhost/api/language');
  });

  it('should return empty array on HTTP error and call error handler', async () => {
    mockPermissionService.getLanguagePermission.and.returnValue(true);

    const promise = service.getLanguageList();

    const req = httpMock.expectOne('http://localhost/api/language');
    req.flush({}, { status: 500, statusText: 'Server Error' });

    const result = await promise;
    expect(result).toEqual([]);
    expect(mockExceptionService.customErrorMessage).toHaveBeenCalled();
  });

  it('should return empty array if response body is null', async () => {
    mockPermissionService.getLanguagePermission.and.returnValue(true);

    const promise = service.getLanguageList();

    const req = httpMock.expectOne('http://localhost/api/language');
    req.flush(null);

    const result = await promise;
    expect(result).toEqual([]);
  });
});
