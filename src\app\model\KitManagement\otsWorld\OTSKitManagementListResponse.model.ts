export class OTSKitManagementListResponse {
    id: number;
    description: string;
    otsKitPartNumberCode: string;
    country: string;
    modifiedDate: number;
    probePartNumberCode1: string;
    probeConfigGroupPartNumberCode1: string;
    probePartNumberCode2: string;
    probeConfigGroupPartNumberCode2: string;


    constructor($id: number, $country: string, $otsKitPartNumberCode: string,//NOSONAR
        $probePartNumberCode1: string, $probeConfigGroupPartNumberCode1: string,
        $probePartNumberCode2: string, $probeConfigGroupPartNumberCode2: string,
        $description: string,
        $modifiedDate: number) {
        this.id = $id;
        this.country = $country;
        this.otsKitPartNumberCode = $otsKitPartNumberCode;
        this.probePartNumberCode1 = $probePartNumberCode1;
        this.probeConfigGroupPartNumberCode1 = $probeConfigGroupPartNumberCode1;
        this.probePartNumberCode2 = $probePartNumberCode2;
        this.probeConfigGroupPartNumberCode2 = $probeConfigGroupPartNumberCode2;
        this.description = $description;
        this.modifiedDate = $modifiedDate;
    }

}