import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ProbeConfigType } from '../../enum/ProbeConfigType.enum';
import { ValidityEnum } from '../../enum/ValidityEnum.enum';
import { CommonsService } from '../../util/commons.service';

@Pipe({
    name: 'probeConfigGroupCheckBoxPipe'
})
export class ProbeConfigGroupCheckBoxPipe implements PipeTransform {

    constructor(private commonsService: CommonsService) { }
    transform(config: Array<any>, configId: number, validity: ValidityEnum, probeTypeConfig: ProbeConfigType, reloadPipe: boolean): boolean {
        if (!isNullOrUndefined(config) && reloadPipe) {
            let validityEnum = this.commonsService.getEnumKey(ValidityEnum, validity)
            if (ProbeConfigType.FEATURE == probeTypeConfig) {
                return config.find(obj => obj.featureId === configId && obj.validity === validityEnum);
            } else if (ProbeConfigType.PRESET == probeTypeConfig) {
                return config.find(obj => obj.presetId === configId && obj.validity === validityEnum);
            }
        }
        return false;
    }
}