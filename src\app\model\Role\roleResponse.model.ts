import { RolePermissionResponse } from "./rolePermissionResponse.model";

export class RoleResponse {
	id: number;
	name: string;
	description: string;
	permissions: Array<RolePermissionResponse>;


	constructor(id: number, name: string, description: string, permissions: Array<RolePermissionResponse>) {
		this.id = id;
		this.name = name;
		this.description = description;
		this.permissions = permissions;
	}
}
