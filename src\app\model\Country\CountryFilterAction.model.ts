import { ListingPageReloadSubjectParameter } from "../common/listingPageReloadSubjectParameter.model";
import { CountryRequestBody } from "./CountryRequestBody.model";

export class CountryFilterAction {
    listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter;
    countryRequestBody: CountryRequestBody;

    constructor($listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, $countryRequestBody: CountryRequestBody) {
        this.listingPageReloadSubjectParameter = $listingPageReloadSubjectParameter;
        this.countryRequestBody = $countryRequestBody;
    }
} 