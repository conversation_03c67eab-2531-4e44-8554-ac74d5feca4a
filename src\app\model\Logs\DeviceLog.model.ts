
import { DeviceConnectionState } from "src/app/shared/enum/Device/DeviceConnectionState.model";
import { logType } from "../logtype.enum";

export class DeviceLog {
    deviceIdPk: number;
    deviceId: string;
    serialNumber: string;
    deviceStatus: DeviceConnectionState;
    logType: logType;
    createdDate: number;
    uploadedDate: number;
    fileSize: number;
    fileName: string
    examId: string;
}