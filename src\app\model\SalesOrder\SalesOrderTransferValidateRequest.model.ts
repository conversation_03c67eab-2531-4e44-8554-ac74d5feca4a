import { SalesOrderTransferProductRequest } from "../device/SalesOrderTransferProductRequest.model";
import { SalesOrderTransferProbeRequest } from "../probe/SalesOrderTransferProbeRequest.model";
import { SalesOrderTransferProductValidateRequest } from "./SalesOrderTransferProductValidateRequest.model";
import { SourceSelectedProbeAndDevice } from "./SourceSelectedProbeAndDevice.model";

export class SalesOrderTransferValidateRequest extends SourceSelectedProbeAndDevice {
    sourceSalesOrderId: number;
    destinationSalesOrderId: number;
    product: SalesOrderTransferProductValidateRequest;

    constructor(
        sourceSalesOrderId: number,
        destinationSalesOrderId: number,
        product: SalesOrderTransferProductValidateRequest,
        sourceDeviceSelected: SalesOrderTransferProductRequest,
        sourceProbeSelected: SalesOrderTransferProbeRequest
    ) {
        super(sourceDeviceSelected, sourceProbeSelected)
        this.sourceSalesOrderId = sourceSalesOrderId;
        this.destinationSalesOrderId = destinationSalesOrderId;
        this.product = product;
    }
}