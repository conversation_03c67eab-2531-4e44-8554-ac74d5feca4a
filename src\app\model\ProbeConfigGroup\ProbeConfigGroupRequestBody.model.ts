export class ProbeConfigGroupRequestBody {
  partNumberCode: string;
  probeTypes: Array<any>;
  features: Array<number>;
  presets: Array<number>

  constructor($partNumberCode: string, $probeTypes: Array<any>, $features: Array<number>, $presets: Array<number>) {
    this.partNumberCode = $partNumberCode;
    this.probeTypes = $probeTypes;
    this.features = $features;
    this.presets = $presets;
  }
}
