import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { LocalStorageService } from 'ngx-webstorage';
import { UploadPackageRequest } from '../../../../model/upload.package.request';
import { API_BASE_URL } from '../../../../shared/config';
import { SoftwareBuildApiCallService } from './software-build-api-call.service';

describe('InventoryService', () => {
  let service: SoftwareBuildApiCallService;
  let httpMock: HttpTestingController;
  let localStorageService: jasmine.SpyObj<LocalStorageService>;

  beforeEach(() => {
    localStorageService = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    TestBed.configureTestingModule({
      providers: [
        SoftwareBuildApiCallService,
        { provide: API_BASE_URL, useValue: 'http://example.com/' },
        { provide: LocalStorageService, useValue: localStorageService },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting()
      ]
    });

    service = TestBed.inject(SoftwareBuildApiCallService);
    httpMock = TestBed.inject(HttpTestingController);
    localStorageService = TestBed.inject(LocalStorageService) as jasmine.SpyObj<LocalStorageService>;
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should call inventoryList', () => {
    const mockData = [];
    service.inventoryList(null, {}).subscribe(res => {
      expect(res.body).toEqual(mockData);
    });

    const req = httpMock.expectOne('http://example.com/api/software-builds/search');
    expect(req.request.method).toBe('POST');
    req.flush(mockData);
  });

  it('should call deleteSoftwearBuild', () => {
    service.deleteSoftwearBuild(10).subscribe();

    const req = httpMock.expectOne('http://example.com/api/inventory/10');
    expect(req.request.method).toBe('DELETE');
    req.flush({});
  });

  it('should call getAttachmentUrl', () => {
    const mockResponse = null;
    service.getAttachmentUrl(5, {}).subscribe(res => {
      expect(res.body).toBeNull();
    });

    const req = httpMock.expectOne('http://example.com/api/inventory/getSignedURL/5');
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);
  });

  it('should call pushFileToStorage', () => {
    const data = {} as UploadPackageRequest;
    service.pushFileToStorage(data, {}).subscribe();

    const req = httpMock.expectOne('http://example.com/api/inventory/upload');
    expect(req.request.method).toBe('POST');
    req.flush({});
  });

  it('should call updateFirmwareUploadStatus', () => {
    const data = {} as UploadPackageRequest;
    service.updateFirmwareUploadStatus(data, {}).subscribe();

    const req = httpMock.expectOne('http://example.com/api/inventory/upload');
    expect(req.request.method).toBe('PUT');
    req.flush({});
  });

  it('should uploadFileToStorage with x-ms-blob-type', () => {
    const file = new File(['test content'], 'file.txt', { type: 'text/plain' });

    service.uploadFileToStorage(file, 'http://signed-url').subscribe();

    const req = httpMock.expectOne('http://signed-url');
    expect(req.request.method).toBe('PUT');
    expect(req.request.headers.get('x-ms-blob-type')).toBe('BlockBlob');
    req.flush({});
  });

  it('should commitFileToStorage with x-ms-blob-content-type', () => {
    const blob = new Blob(['commit content'], { type: 'text/plain' });

    service.commitFileToStorage(blob, 'http://signed-url', 'text/plain').subscribe();

    const req = httpMock.expectOne('http://signed-url');
    expect(req.request.method).toBe('PUT');
    expect(req.request.headers.get('x-ms-blob-content-type')).toBe('text/plain');
    req.flush({});
  });

  it('should call mapInventoryWithDeviceType', () => {
    service.mapInventoryWithDeviceType([1, 2], {}).subscribe();

    const req = httpMock.expectOne('http://example.com/api/inventory/1,2');
    expect(req.request.method).toBe('PUT');
    req.flush({});
  });

  it('should call markInventoriesActiveInactive', () => {
    service.markInventoriesActiveInactive([3], {}).subscribe();

    const req = httpMock.expectOne('http://example.com/api/inventory/3');
    expect(req.request.method).toBe('PUT');
    req.flush({});
  });

  it('should call updateInventory', () => {
    const data = { name: 'Firmware V2' };
    service.updateInventory(100, data).subscribe();

    const req = httpMock.expectOne('http://example.com/api/inventory/100');
    expect(req.request.method).toBe('PUT');
    expect(req.request.body).toEqual(data);
    req.flush({});
  });
});