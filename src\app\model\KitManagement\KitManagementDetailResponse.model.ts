import { KitManagementBaseResponse } from "./KitManagementBaseResponse.model";

export class KitManagementDetailResponse extends KitManagementBaseResponse {
    bridgePartNumber: string;
    probePartNumbers: Array<string>;

    constructor( //NOSONAR
        id: number,
        description: string,
        videoVersion: string,
        softWareVersion: string,
        modifiedDate: number,
        country: string,
        kitPartNumber: string,
        language: string,
        softWarePartNumber: string,
        dummyKitPartNumber: boolean,
        bridgePartNumber: string,
        probePartNumbers: Array<string>
    ) {
        super(
            id,
            description,
            videoVersion,
            softWareVersion,
            modifiedDate,
            country,
            kitPartNumber,
            language,
            softWarePartNumber,
            dummyKitPartNumber
        );

        this.bridgePartNumber = bridgePartNumber;
        this.probePartNumbers = probePartNumbers;
    }
}