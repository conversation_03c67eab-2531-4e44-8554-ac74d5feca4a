import { Component, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { AuthJwtService } from './shared/auth-jwt.service';
import { ConfigInjectService } from './shared/InjectService/config-inject.service';
import { UserApiCallService } from './shared/Service/user/user-api-call.service';
import { SubjectMessageService } from './shared/subject-message.service';
import { SSOLoginService } from './shared/Service/SSO/ssologin.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit {
  isNavbarCollapsed: boolean;
  loading = false;

  //subject message service
  subscription: Subscription;

  //version
  rdmVersion: string;

  //enviroment
  rdmEnvironment: string;

  ngOnInit() {
    this.subscribeSubjectMessageService();
  }

  constructor(
    private authservice: AuthJwtService,
    public subjectMessageService: SubjectMessageService,
    private configInjectService: ConfigInjectService,
    private userApiCallService: UserApiCallService,
    private ssoLoginService: SSOLoginService,
  ) {
    this.isNavbarCollapsed = true;
    this.rdmVersion = this.configInjectService.getRDMVersion();
    this.rdmEnvironment = this.configInjectService.getRDMEnvironment();
  }

  private subscribeSubjectMessageService(): void {
    this.subscription = this.subjectMessageService.message?.subscribe(
      (loading) => {
        this.loading = loading;
      });
  }
  public collapseNavbar(): void {
    this.isNavbarCollapsed = true;
  }

  isAuthenticated() {
    return this.authservice?.isAuthenticate();
  }



  public async logout(): Promise<void> {
    this.loading = true;
    if (await this.userApiCallService?.logOutMember()) {
      this.ssoLoginService.logOutInMicrosoft();
      this.collapseNavbar();
      this.authservice.clear();
      this.loading = false;
    } else {
      this.loading = false;
    }
  }

  public toggleNavbar(): void {
    this.isNavbarCollapsed = !this.isNavbarCollapsed;
  }

}
