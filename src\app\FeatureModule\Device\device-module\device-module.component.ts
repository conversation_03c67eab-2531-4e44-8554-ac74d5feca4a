import { Component, OnInit } from '@angular/core';
import { DeviceListResource } from 'src/app/app.constants';
import { DeviceOperationService } from '../DeviceService/Device-Operation/device-operation.service';

@Component({
  selector: 'app-device-module',
  templateUrl: './device-module.component.html',
  styleUrls: ['./device-module.component.css']
})
export class DeviceModuleComponent implements OnInit {

  isDeviceListingPageDisplay: boolean = true;
  isDeviceDetailPageDisplay: boolean = false;

  loading: boolean = false;

  deviceIdInput: number;
  deviceListResource = DeviceListResource;

  constructor(private deviceOperationService: DeviceOperationService) { }

  public getDeviceId(deviceId: number): void {
    this.deviceIdInput = deviceId;
  }

  public ngOnInit(): void {
    this.setDefaultState();
  }

  public ngOnDestroy(): void {
    this.setDefaultState();
  }

  private setDefaultState() {
    this.deviceOperationService.setDeviceSearchRequestBodyForListingApi(null);
    this.deviceOperationService.setIsFilterHiddenForListing(false);
    this.deviceOperationService.setListPageRefreshForbackToOtherPage(false);
  }

  public showDeviceDetail() {
    this.isDeviceDetailPageDisplay = true;
    this.isDeviceListingPageDisplay = false;
  }

  /**
  * Show device listing page from detail page
  * <AUTHOR>
  */
  public showDevice(): void {
    this.isDeviceListingPageDisplay = true;
    this.isDeviceDetailPageDisplay = false;
    this.deviceOperationService.setListPageRefreshForbackToOtherPage(true);
  }

}
