import { SalesOrderResponse } from "./SalesOrderResponse.model";
import { SalesOrderProduct } from "./SalesOrderProduct.model";
import { SalesOrderStatus } from "src/app/shared/enum/SalesOrder/SalesOrderStatus.enum";
import { SalesOrderTypeStatus } from "src/app/shared/enum/SalesOrder/SalesOrderTypeStatus.enum";

export class SalesOrderDetailResponse extends SalesOrderResponse {
    product: SalesOrderProduct;

    constructor( //NOSONAR
        salesOrderNumber: string,
        customerName: string,
        customerEmail: string,
        countryId: number,
        poNumber: string,
        id: number,
        country: string,
        soCreatedDate: number,
        status: SalesOrderStatus,
        orderType: SalesOrderTypeStatus,
        createdDate: number,
        modifiedDate: number,
        lastSyncDate: number,
        soStatus: string,
        product: SalesOrderProduct,
        deviceAutoLock: boolean,
        probeAutoLock: boolean,
        orderRecordType: string
    ) {
        super(
            salesOrderNumber,
            customerName,
            customerEmail,
            countryId,
            poNumber,
            id,
            country,
            soCreatedDate,
            status,
            orderType,
            createdDate,
            modifiedDate,
            lastSyncDate,
            soStatus,
            deviceAutoLock,
            probeAutoLock,
            orderRecordType
        );
        this.product = product;
    }
}
