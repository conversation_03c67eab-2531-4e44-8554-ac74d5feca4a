import { HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { TransferOrderSelectionDetailRequest } from 'src/app/model/SalesOrder/TransferOrderSelectionDetailRequest.model';
import { SalesOrderTransferValidateRequest } from 'src/app/model/SalesOrder/SalesOrderTransferValidateRequest.model';
import { TransferOrderSelectionResponse } from 'src/app/model/SalesOrder/TransferOrderSelectionResponse.model';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { CommonOperationsService } from '../../util/common-operations.service';
import { SalesOrderApiCallService } from '../SalesOrderService/sales-order-api-call.service';
import { isNullOrUndefined } from 'is-what';
import { FormControl, FormGroup, ValidatorFn } from '@angular/forms';
import { TransferOrderProductReviewResponse } from 'src/app/model/SalesOrder/TransferOrderProductReviewResponse.model';
import { ToastrService } from 'ngx-toastr';
import { EMPTY_TRANSFER_ORDER_PRODUCT, EMPTY_TRANSFER_ORDER_VALIDATE_PRODUCT } from 'src/app/app.constants';

@Injectable({
  providedIn: 'root'
})
export class TransferOrderService {

  constructor(private commonOperationsService: CommonOperationsService,
    private salesOrderApiCallService: SalesOrderApiCallService,
    private exceptionService: ExceptionHandlingService,
    private tosterService: ToastrService
  ) { }

  /**
  * Set loading status
  * @param status - Boolean value to enable or disable loading state
  */
  private setLoading(status: boolean): void {
    this.commonOperationsService.callCommonLoadingSubject(status);
  }

  /**
  * Get Transfer Order Selection Details
  * 
  * @param teansferOrderSelectionDetailRequest - Request body for transfer order selection
  * @returns TransferOrderSelectionResponse | null
  * <AUTHOR>
  */
  public async getTransferOrderSelectionDetail(
    transferOrderSelectionDetailRequest: TransferOrderSelectionDetailRequest
  ): Promise<TransferOrderSelectionResponse | null> {
    this.setLoading(true);

    try {
      const res: HttpResponse<TransferOrderSelectionResponse> = await firstValueFrom(
        this.salesOrderApiCallService.getTransferOrderSelectionDetails(transferOrderSelectionDetailRequest)
      );

      if (res.status === 200) {
        return res.body || null;
      }
      if (res.status === 204) {
        this.tosterService.info(EMPTY_TRANSFER_ORDER_PRODUCT);
        return null;
      }

    } catch (error: any) {
      this.exceptionService.customErrorMessage(error);
    } finally {
      this.setLoading(false);
    }

    return null; // Ensure a return statement outside the try-catch block
  }

  /**
  * Submit Transfer Order Details
  * 
  * @param requestBody - Request payload for submitting transfer order details
  * @returns TransferOrderSelectionResponse | null
  */
  public async submitTranferOrderDetails(
    requestBody: SalesOrderTransferValidateRequest
  ): Promise<TransferOrderProductReviewResponse | null> {
    this.setLoading(true);

    try {
      const res: HttpResponse<TransferOrderProductReviewResponse> = await firstValueFrom(
        this.salesOrderApiCallService.submitTransferOrder(requestBody)
      );

      if (res.status === 200) {
        return res.body || null;
      }
      if (res.status === 204) {
        this.tosterService.info(EMPTY_TRANSFER_ORDER_VALIDATE_PRODUCT);
        return null;
      }
    } catch (error: any) {
      this.exceptionService.customErrorMessage(error);
    } finally {
      this.setLoading(false);
    }

    return null; // Ensure a return statement outside the try-catch block
  }

  /**
  * Combine existing error list with a new error entry
  * 
  * @param errorList - Existing error object
  * @returns Updated error object
  */
  public getcombileErrorList(errorList: any): any {
    let combileErrorList = errorList;
    if (isNullOrUndefined(combileErrorList)) {
      // Create a new error object
      combileErrorList = { serialNumberexists: true };
    } else {
      // Add new error to the existing list
      combileErrorList['serialNumberexists'] = true;
    }
    return combileErrorList;
  }

  /**
  * Update form control validation for duplicate serial numbers
  * 
  * @param duplicateSerialNumberList - List of duplicate serial numbers
  * @param control - Form control to update validation
  */
  public updateFormControlvalidation(duplicateSerialNumberList: string[], control: FormControl) {
    let errorList = control.errors;
    if (duplicateSerialNumberList.includes(control.value)) {
      control.setErrors(this.getcombileErrorList(errorList));
    } else if (!isNullOrUndefined(errorList) && !isNullOrUndefined(errorList['serialNumberexists'])) {
      delete errorList["serialNumberexists"];
      let error = (Object.keys(errorList).length === 0) ? null : errorList;
      control.setErrors(error);
    }
  }

  /**
  * Identify duplicate serial numbers in the form group
  * 
  * @param productType - Product type key for accessing serial numbers
  * @param formGroup - Form group containing serial number controls
  * @returns Array of duplicate serial numbers
  */
  public duplicateSerialNumberValue(productType: string, formGroup: FormGroup): Array<string> {
    let controlTypeSerialNumberList = formGroup.get(productType).value;
    let duplicateValue: Array<string> = [];
    let serialNumberList: Array<string> = [];

    for (let bridgeSerialNumber of controlTypeSerialNumberList) {
      let serialNumber = bridgeSerialNumber?.serialNumber;
      if (!isNullOrUndefined(serialNumber) && serialNumber !== 'null') {
        if (serialNumberList.includes(serialNumber) && !duplicateValue.includes(serialNumber)) {
          duplicateValue.push(serialNumber);
        } else {
          serialNumberList.push(serialNumber);
        }
      }
    }
    return duplicateValue;
  }

  /**
   * Validates if the serial number is unique.
   */
  public distinctSerialNumberValidate(componentVariable: any, productType: string, controlIndex: any): ValidatorFn {
    return function validate(control: FormControl) {
      if (!isNullOrUndefined(control) && !isNullOrUndefined(control.value) && control.value !== 'null') {
        let allSerialNumberList = JSON.parse(JSON.stringify(componentVariable.formGroup.get(productType).value));
        if (!isNullOrUndefined(allSerialNumberList)) {
          allSerialNumberList.splice(controlIndex, 1);
        }
        let filterData = allSerialNumberList.filter(obj => {
          if (!isNullOrUndefined(obj.serialNumber)) {
            if (obj.serialNumber.toLowerCase() == control.value.toLowerCase()) {
              return obj;
            }
          }
        });
        if (filterData.length > 0) {
          return { serialNumberexists: true };
        }
      }
      return null;
    };
  }

}
