import { TransferredSalesOrder } from "./TransferredSalesOrder.model";
import { TransferSalesOrder } from "./TransferSalesOrder.model";

export class TransferOrderProductReviewResponse {
    sourceSalesOrder: TransferSalesOrder;
    destinationSalesOrder: TransferredSalesOrder;
    destinationSalesOrderIsManual: boolean;
    sourceSalesOrderIsManual: boolean;
    warning: boolean;
    warningMessages: Array<string>;
    error: boolean;
    errorMessages: Array<string>;

    constructor(
        sourceSalesOrder: TransferSalesOrder,
        destinationSalesOrder: TransferredSalesOrder,
        warning: boolean,
        warningMessages: Array<string>,
        error: boolean,
        errorMessages: Array<string>
    ) {
        this.sourceSalesOrder = sourceSalesOrder;
        this.destinationSalesOrder = destinationSalesOrder;
        this.warning = warning;
        this.warningMessages = warningMessages;
        this.error = error;
        this.errorMessages = errorMessages;
    }
}