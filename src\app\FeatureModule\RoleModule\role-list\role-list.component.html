<!-- loading start -->
<!-- loading gif start -->
<div class="ringLoading" *ngIf="loading">
  <div class="ringLoadingDiv">
    <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
  </div>
</div>
<!-- loading gif end -->
<!-- loading end -->


<body *ngIf="roleListDisplay">
  <!-- row start -->
  <div class="row">

    <!--############################################################-->
    <!--Filter start-->
    <!--############################################################-->
    <div class="col-md-3 pr-0" *ngIf="(!isFilterHidden)">
      <label class="col-md-12 h5-tag">Filter</label>
      <div class="card mt-3">
        <div class="card-body">
          <app-rolefilter [isFilterComponentInitWithApicall]="isFilterComponentInitWithApicall"
            [roleSearchRequestBody]="roleSearchRequestBody"
            [listPageRefreshForbackToDetailPage]="listPageRefreshForbackToDetailPage"></app-rolefilter>
        </div>
      </div>
    </div>
    <!--############################################################-->
    <!--Filter End-->
    <!--############################################################-->

    <!--table Block Start-->
    <div [className]="(isFilterHidden)?'col-md-12 pr-0':'col-md-9 pr-0'">
      <div class="container-fluid">
        <!--############################################################-->
        <!--############################################################-->
        <div class="row" class="headerAlignment">
          <!--############################################################-->
          <!--Left Side-->
          <!--############################################################-->
          <div class="childFlex">
            <!----------------------------------------------->
            <!------------Show/hide filter-------------------->
            <!----------------------------------------------->
            <div class="dropdown" id="hideShowFilter">
              <button class="btn btn-sm btn-orange mr-3 ml-0 filter-symbol" (click)="toggleFilter()"
                id="roleListHideShowButton">
                <i class="fas fa-filter" aria-hidden="true"></i>
                &nbsp;&nbsp;{{ hideShowFilterButtonText }}
              </button>
            </div>
            <!----------------------------------------------->
            <!------------Pagnatation drp-------------------->
            <!----------------------------------------------->
            <div>
              <label class="mb-0">Show entry</label>
              <select [(ngModel)]="drpselectsize" class="form-control form-control-sm" (change)="changeDataSize($event)"
                id="roleListShowEntry">
                <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                  <option [value]="dataSize">{{ dataSize }}</option>
                </ng-template>
              </select>
            </div>
          </div>
          <!--############################################################-->
          <!--Right Side-->
          <!--############################################################-->
          <div class="childFlex">
            <!------------------------------------------------->
            <!--------------Operations------------------->
            <!------------------------------------------------->
            <ng-template [ngIf]="operationsList.length > 1">
              <div class="mr-3">
                <select id="roleOperation" class="form-control form-control-sm"
                  (change)="changeOperation($any($event.target)?.value)">
                  <ng-template ngFor let-operation [ngForOf]="operationsList">
                    <option [value]="operation">{{ operation }}</option>
                  </ng-template>
                </select>
              </div>
            </ng-template>
            <div *ngIf="addRolePermission">
              <button class="btn btn-sm btn-orange mr-3" (click)="addRole()" id="AddRoleList">
                <em class="fa fa-plus"></em> &nbsp;&nbsp;New Role
              </button>
            </div>

            <!------------------------------------------------>
            <!----------------refresh------------------------->
            <!------------------------------------------------>
            <div>
              <button class="btn btn-sm btn-orange" id="refresh_roleList" (click)="clickOnRefreshButton()"><em
                  class="fa fa-refresh"></em></button>
            </div>
          </div>
        </div>
        <!--############################################################-->
        <!--############################################################-->
        <!-- selected probes start -->
        <div>Total {{totalRecord}} Role
          <p *ngIf="countItem>0">
            <strong>{{countItem}} Role(s) selected</strong>
          </p>
        </div>
        <!-- selected probes end -->

        <!-------------------------------------------->
        <!-------------------------------------------->
        <!-- probe table start -->
        <!-------------------------------------------->
        <!-------------------------------------------->
        <div class="commonTable">
          <table class="table table-sm table-bordered" style="overflow-x: scroll;" aria-hidden="true">
            <!--###########################################-->
            <!-- table header Start -->
            <!--###########################################-->
            <thead>
              <tr class="thead-light">
                <th class="checkox-table width-unset" *ngIf="operationsList.length > 1">
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" name="chkselectall" [id]="selectAllCheckboxId"
                      (change)="selectAllItem($any($event.target)?.checked)">
                    <label class="custom-control-label" [for]="selectAllCheckboxId"></label>
                  </div>
                </th>
                <th><span>Name</span></th>
                <th><span>Module(s)</span></th>
                <th><span>Description</span></th>
                <th><span>Permissions</span></th>
              </tr>
            </thead>
            <!--###########################################-->
            <!-- table body start -->
            <!--###########################################-->
            <tbody>
              <tr *ngFor="let roleObj of roleResponseList; let roleIndex = index">
                <td class="width-unset" *ngIf="operationsList.length > 1">
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" [id]="chkPreFix+roleObj.id+chkPreFix"
                      [name]="checkboxListName" (change)="selectCheckbox(roleObj,$any($event.target)?.checked)"
                      [checked]="selectedRoleIdList.includes(roleObj.id)">
                    <label class="custom-control-label" [for]="chkPreFix+roleObj.id+chkPreFix"></label>
                  </div>
                </td>
                <td (click)="showRoleDetail(roleObj?.id)" class="spanunderline" [id]="roleObj.id">
                  <span>{{roleObj?.name}}</span>
                </td>
                <td>
                  <span> {{ roleObj?.permissions | getPermissionModuleName | printListPipe }}</span>
                </td>
                <td class="maxWidth"><span>{{roleObj?.description}}</span></td>
                <td><span>{{roleObj?.permissions | GetRolePermissionNamePipe}}</span></td>
              </tr>
            </tbody>
            <!--###########################################-->
            <!-- table body end -->
            <!--###########################################-->
          </table>

        </div>
        <!-------------------------------------------->
        <!-------------------------------------------->
        <!-- probe table start -->
        <!-------------------------------------------->
        <!-------------------------------------------->

        <!----------------------------------------------------------->
        <!----------------------------------------------------------->
        <!--pagination Start-->
        <!----------------------------------------------------------->
        <!----------------------------------------------------------->
        <div>
          <div>Showing {{totalRecordDisplay}} out of {{totalRecord}} Role</div>
          <div class="float-right">
            <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="itemsPerPage" [maxSize]="5"
              [rotate]="true" id="roleList-pagination" [boundaryLinks]="true" (pageChange)="loadPage(page)">
            </ngb-pagination>
          </div>
        </div>
        <!----------------------------------------------------------->
        <!----------------------------------------------------------->
        <!--pagination end-->
        <!----------------------------------------------------------->
        <!----------------------------------------------------------->
      </div>
    </div>
    <!--table Block End-->
  </div>
  <!-- row end -->
</body>

<div *ngIf="roleDetailDisplay">
  <app-role-detail (showRoleList)="showRoleList()" [roleId]="roleId"></app-role-detail>
</div>