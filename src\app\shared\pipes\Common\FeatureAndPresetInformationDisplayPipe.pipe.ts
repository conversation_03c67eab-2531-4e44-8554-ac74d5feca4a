import { Pipe, PipeTransform } from '@angular/core';
import { SafeHtml } from '@angular/platform-browser';
import { isNullOrUndefined } from 'is-what';
import { FeatureResponse } from 'src/app/model/ProbeConfigGroup/FeatureResponse.model';
import { PresetResponse } from 'src/app/model/ProbeConfigGroup/PresetResponse.model';
import { AssociatedConfigLicence } from 'src/app/model/associated-config-licence.model';
import { ValidityEnum } from '../../enum/ValidityEnum.enum';
import { CommonsService } from '../../util/commons.service';

@Pipe({
    name: 'featureInformationDisplayPipe'
})
export class FeatureAndPresetInformationDisplayPipe implements PipeTransform {

    constructor(private commonService: CommonsService) { }

    transform(enableFeatureList: Array<FeatureResponse> | Array<PresetResponse> | Array<AssociatedConfigLicence>): SafeHtml {
        if (!isNullOrUndefined(enableFeatureList) && enableFeatureList.length !== 0) {

            // Create an array to store sanitized feature strings
            const featureList = enableFeatureList.map(feature => {
                if (!isNullOrUndefined(feature.validity)) {
                    // Use a templating method to create the HTML string
                    return `${this.commonService.escapeHtml(feature.displayName)} - ${ValidityEnum[feature.validity]}`;
                }
                return '';
            }).filter(feature => feature !== '');

            // Check if the featureList has valid features
            if (featureList.length > 0) {
                // Join the features into an unordered list
                return `<ul class="ulList"><li>${featureList.join('</li><li>')}</li></ul>`;
            }

        }
        return '';
    }
}
