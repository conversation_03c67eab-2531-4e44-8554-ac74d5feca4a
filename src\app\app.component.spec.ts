import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterModule } from '@angular/router';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { AppComponent } from './app.component';
import { ConfirmDialogService } from './FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { AuthJwtService } from './shared/auth-jwt.service';
import { ExceptionHandlingService } from './shared/ExceptionHandling.service';
import { UserApiCallService } from './shared/Service/user/user-api-call.service';
import { CommonsService } from './shared/util/commons.service';
import { commonsProviders } from './Tesing-Helper/test-utils';
import { TabComponent } from './FeatureModule/tab/tab.component';
import { NoDataMessageComponent } from './FeatureModule/NoData/no-data-message/no-data-message.component';


describe('AppComponent', () => {
  let component: AppComponent;
  let fixture: ComponentFixture<AppComponent>;


  beforeEach(async () => {
    const toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    await TestBed.configureTestingModule({
      declarations: [AppComponent, TabComponent, NoDataMessageComponent],
      imports: [RouterModule.forRoot([])],
      providers: [LocalStorageService, AuthJwtService, SessionStorageService,
        CommonsService,
        ConfirmDialogService,
        UserApiCallService,
        ExceptionHandlingService,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(AppComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
