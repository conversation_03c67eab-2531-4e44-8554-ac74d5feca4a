import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { ProbeTypeUpdateRequest } from 'src/app/model/probe/ProbeTypeUpdateRequest';
import { ProbeTypeResponse } from 'src/app/model/probe/probeTypeResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { ProbeTypeEnum } from 'src/app/shared/enum/ProbeType.enum';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';

@Component({
  selector: 'app-update-probe-type',
  templateUrl: './update-probe-type.component.html',
  styleUrls: ['./update-probe-type.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class UpdateProbeTypeComponent implements OnInit {

  @Input() title;
  @Input() btnCancelText;
  @Input() btnOkText;
  @Input() probeType;
  @Input() probeIdList;

  //Probe Type Response set
  probeTypeResponse: ProbeTypeResponse[] = [];

  probeForm = new FormGroup({
    probeType: new FormControl('', Validators.required),
  });

  constructor(
    private activeModal: NgbActiveModal,
    private probeApiService: ProbeApiService,
    private commonOperationsService: CommonOperationsService,
    private toste: ToastrService,
    private exceptionService: ExceptionHandlingService,
    private commonsService: CommonsService,
  ) { }

  /**
   * set init data
   * 
   * <AUTHOR>
   */
  public ngOnInit(): void {
    this.initCall();
  }

  /**
   * Get Probe Type
   * 
   * <AUTHOR>
   */
  public async initCall(): Promise<void> {
    this.probeTypeResponse = await this.probeApiService.getprobeTypeResponseList(true);
    let selectedProbeType = "-1";
    if (this.probeTypeResponse.length > 0) {
      let filterProbeType = this.probeTypeResponse.filter(obj => obj.displayName == this.probeType);
      if (filterProbeType.length == 1) {
        selectedProbeType = filterProbeType[0].probeTypeId.toString();
      }
    }
    this.probeForm.get("probeType").setValue(selectedProbeType);
    this.updateValidatation();
  }


  /**
   * Update Probe Type api call
   * 
   * <AUTHOR>
   */
  public accept(): void {
    let selectedType = this.probeForm.get('probeType').value;
    let filterProbeType = this.probeTypeResponse.filter(obj => obj.probeTypeId.toString() == selectedType);
    if (selectedType != "-1" && filterProbeType.length == 1) {
      this.setLoading(true);
      let probeTypeUpdateRequest = new ProbeTypeUpdateRequest(this.commonsService.getEnumKey(ProbeTypeEnum, filterProbeType[0].displayName));
      this.probeApiService.probeTypeUpdate(this.probeIdList, probeTypeUpdateRequest).subscribe({
        next: (res: HttpResponse<SuccessMessageResponse>) => {
          this.setLoading(false);
          this.toste.success(res.body.message);
          this.activeModal.close(true);
        },
        error: (error: HttpErrorResponse) => {
          this.setLoading(false);
          this.exceptionService.customErrorMessage(error);
        }
      });
    }
  }

  /**
   * Close Model
   * 
   * <AUTHOR>
   */
  public decline() {
    this.activeModal.close(false);
  }

  /**
   * Update Validatation
   * 
   * <AUTHOR>
   */
  public updateValidatation(): void {
    let control = this.probeForm.get('probeType');
    if (control.value == "-1") {
      control.setErrors({ required: true });
      control.markAsDirty();
    }
  }

  /**
   * Set loading 
   * 
   * <AUTHOR>
   * 
   * @param status 
   */
  private setLoading(status: boolean) {
    this.commonOperationsService.callCommonLoadingSubject(status);
  }

}
