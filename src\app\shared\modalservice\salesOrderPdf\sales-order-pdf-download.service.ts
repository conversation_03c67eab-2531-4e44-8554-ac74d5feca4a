import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { SalesOrderPdfDownloadComponent } from 'src/app/FeatureModule/SalesOrderPdf/sales-order-pdf-download/sales-order-pdf-download.component';

@Injectable({
  providedIn: 'root'
})
export class SalesOrderPdfDownloadService {

  constructor(private modalService: NgbModal) { }

  public openSalesOrderPdfDownloadModel(): Promise<boolean> {
    const modalRef = this.modalService.open(SalesOrderPdfDownloadComponent, { windowClass: "modal fade" });
    return modalRef.result;
  }

  
}
