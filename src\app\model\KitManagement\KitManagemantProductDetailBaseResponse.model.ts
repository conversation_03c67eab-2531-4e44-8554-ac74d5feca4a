import { PartNumberType } from "src/app/shared/enum/SalesOrder/PartNumberType.enum";
import { ProductConfigStatus } from "src/app/shared/enum/SalesOrder/ProductConfigStatus.enum";
import { EnableFeaturesResponse } from "../SalesOrder/EnableFeaturesResponse.model";

export class KitManagemantProductDetailBaseResponse {
    partNumber: string;
    productConfigStatus: ProductConfigStatus;
    serialNumber: string;
    partNumberType: PartNumberType;
    productEntityId: number;
    enableFeatures: Array<EnableFeaturesResponse>;
    probeType: string;
    sopnmId: number;
    productId: number;
}