import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AppComponent } from '../app/app.component';
import { SsoLoginComponent } from './sso-login/sso-login.component';
import { TabComponent } from './FeatureModule/tab/tab.component';

const routes: Routes = [
  { path: 'app', component: AppComponent },
  { path: '', component: SsoLoginComponent },
  { path: 'modules', component: TabComponent }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
