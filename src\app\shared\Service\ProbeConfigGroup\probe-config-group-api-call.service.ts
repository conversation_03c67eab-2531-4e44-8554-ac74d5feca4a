import { HttpClient, HttpResponse } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { catchError, Observable } from "rxjs";
import { SuccessMessageResponse } from "src/app/model/common/SuccessMessageResponse.model";
import { ProbeConfigGroupDeatilResponse } from "src/app/model/ProbeConfigGroup/ProbeConfigGroupDeatilResponse.model";
import { ProbeConfigGroupPageResponse } from "src/app/model/ProbeConfigGroup/probeConfigGroupPageResponse.model";
import { ProbeConfigGroupRequestBody } from "src/app/model/ProbeConfigGroup/ProbeConfigGroupRequestBody.model";
import { ConfigInjectService } from "../../InjectService/config-inject.service";
import { CommonsService } from "../../util/commons.service";
import { createRequestOption } from "../../util/request-util";
import { ProbeConfigGroupAddOrUpdateRequest } from "src/app/model/ProbeConfigGroup/ProbeConfigGroupAddOrUpdateRequest.model";

@Injectable({
  providedIn: "root",
})
export class ProbeConfigGroupApiCallService {
  public probeConfigGroupUrl = this.configInjectService.getServerApiUrl() + "api/probe-config-groups";

  constructor(
    private configInjectService: ConfigInjectService,
    private http: HttpClient,
    private commonsService: CommonsService
  ) { }

  /**
   * Get Probe Config Group list
   * @param requestBody
   * @param req
   * @returns
   */
  public getProbeConfigGroupList(requestBody: ProbeConfigGroupRequestBody, req: any): Observable<HttpResponse<ProbeConfigGroupPageResponse>> {
    const options = createRequestOption(req);
    return this.http.post<ProbeConfigGroupPageResponse>(this.probeConfigGroupUrl + "/search", requestBody, { params: options, observe: "response" }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Get Probe Config Group Details
   *
   * <AUTHOR>
   * @param probeConfigGroupId
   * @returns
   */
  public getProbeConfigGroupDetails(probeConfigGroupId: number): Observable<HttpResponse<ProbeConfigGroupDeatilResponse>> {
    return this.http.get<ProbeConfigGroupDeatilResponse>(this.probeConfigGroupUrl + "/" + probeConfigGroupId, { observe: "response" }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Delete probeConfigGroup
   * <AUTHOR>
   * @param probeConfigGroupId
   * @returns
   */
  public deleteProbeConfigGroup(probeConfigGroupId: number[]): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.delete<SuccessMessageResponse>(this.probeConfigGroupUrl + "/" + probeConfigGroupId, { observe: "response" }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Add Update probeConfigGroup
   * <AUTHOR>
   * @param probeConfigGroupAddOrUpdateRequest
   * @returns
   */
  public addOrUpdateProbeConfigGroup(probeConfigGroupAddOrUpdateRequest: ProbeConfigGroupAddOrUpdateRequest): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.post<SuccessMessageResponse>(this.probeConfigGroupUrl, probeConfigGroupAddOrUpdateRequest, { observe: "response" }).pipe(catchError(this.commonsService.handleError));
  }
}
