import { ConfigBaseMappingRequest } from "./ConfigBaseMappingRequest.model";

export class ConfigMappingRequest {
    features: Array<ConfigBaseMappingRequest>;
    presets: Array<ConfigBaseMappingRequest>;
    reminder: boolean;


    constructor($features: Array<ConfigBaseMappingRequest>, $presets: Array<ConfigBaseMappingRequest>, $reminder: boolean) {
        this.features = $features;
        this.presets = $presets;
        this.reminder = $reminder;
    }

}