@import '../../../../assets/css/custom_style.css';

.modal-footer {
  border: none;
}

.importUsers label {
  font-size: 12px;
  margin-bottom: 4px;
}

.importUsers input {
  min-width: 250px;
  border-right: 0 !important;
  width: 85%;
  padding-top: 2px;
  padding-left: 2px;
}

.importUsers div>span,
.importUsers div>a {
  font-size: 14px;
}

.importUsers .boldText {
  font-weight: 500;
}

.importUsers .listText {
  font-size: 14px;
  font-weight: 500;
}

.importLabel {
  font-size: 14px !important;
  cursor: pointer;
}

.importButton {
  margin-bottom: 14px;
}

.downloadTemplateLink {
  text-decoration: underline !important;
  cursor: pointer;
  color: #f79423 !important;
}

.validation {
  color: #c70e24;
  font-size: 12px;
  text-align: center;
}

.validationFailImg {
  color: #c70e24;
  font-size: 13px !important;
  margin-top: 4px;
}

.validationSuccessImg {
  color: #6bad47;
  font-size: 13px !important;
  margin-top: 4px;
}

.success {
  color: #6bad47;
  font-size: 11px;
  text-align: center;
}

.import-error-table {
  font-size: small;
}

.import-error-table tr th {
  position: sticky;
  top: 0;
  background-color: lightgray;
}

.import-error-table tr td {
  padding: 10px !important;
}

.import-error-table .breakWord {
  word-break: break-word;
}

.import-error-table .ULPadding {
  padding-left: 0.75rem;
}

.import-csv-mainDiv {
  height: auto;
  max-height: calc(100vh - 260px);
  width: 100%;
  overflow: auto;
  overflow-x: hidden;
}

.import-csv-mainDiv::-webkit-scrollbar {
  width: 6px;
  background-color: #fff !important;
  height: 6px;
}

.import-csv-mainDiv::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: #C1C1C1 !important;
}

.import-csv-mainDiv .text_nowrap {
  white-space: nowrap;
}