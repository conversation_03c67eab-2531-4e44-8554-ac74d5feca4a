import { Pipe, PipeTransform } from "@angular/core";
import { RolePermissionResponse } from "src/app/model/Role/rolePermissionResponse.model";
import { isNullOrUndefined } from 'is-what';


@Pipe({
    name: "getPermissionModuleName"
})
export class GetPermissionModuleName implements PipeTransform {

    transform(rolePermissionResponse: RolePermissionResponse[]): Array<string> {
        let moduleNameList = [];
        if (!isNullOrUndefined(rolePermissionResponse) && rolePermissionResponse.length > 0) {
            moduleNameList = Array.from(new Set(rolePermissionResponse.map((rolePermission) => rolePermission.module))).sort((x, y) => x.localeCompare(y));
        }
        return moduleNameList;
    }
}