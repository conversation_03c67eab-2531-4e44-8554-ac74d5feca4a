import { ListingPageReloadSubjectParameter } from "../common/listingPageReloadSubjectParameter.model";
import { RoleRequestBody } from "./roleRequestBody.model";

export class RoleFilterAction {
    listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter;
    roleRequestBody: RoleRequestBody;


    constructor($listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, $roleRequestBody: RoleRequestBody) {
        this.listingPageReloadSubjectParameter = $listingPageReloadSubjectParameter;
        this.roleRequestBody = $roleRequestBody;
    }

}