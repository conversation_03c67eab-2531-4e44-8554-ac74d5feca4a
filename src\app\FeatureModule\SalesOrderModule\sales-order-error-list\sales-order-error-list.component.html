<!-- loading start -->
<!-- loading gif start -->
<div class="ringLoading" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!-- loading gif end -->
<!-- loading end -->


<body>
    <div class="row">

        <!--############################################################-->
        <!--Filter start-->
        <!--############################################################-->
        <div class="col-md-3 pr-0" *ngIf="(!isFilterHidden)">
            <label class="col-md-12 h5-tag">Filter</label>
            <div class="card mt-3">
                <div class="card-body">
                    <app-sales-order-error-filter [isFilterComponentInitWithApicall]="isFilterComponentInitWithApicall"
                        [salesOrderFaieldSearchRequestBody]="salesOrderFaieldSearchRequestBody"></app-sales-order-error-filter>
                </div>
            </div>
        </div>
        <!--############################################################-->
        <!--Filter End-->
        <!--############################################################-->


        <div [className]="(isFilterHidden)?'col-md-12 pr-0':'col-md-9 pr-0'">
            <div class="container-fluid" id="salesOrderErrorList">
                <!--############################################################-->
                <!--############################################################-->
                <div class="row" class="headerAlignment">
                    <!--############################################################-->
                    <!--Left Side-->
                    <!--############################################################-->
                    <div class="childFlex">
                        <!----------------------------------------------->
                        <!------------Show/hide filter-------------------->
                        <!----------------------------------------------->
                        <div class="dropdown" id="hideShowFilter">
                            <button class="btn btn-sm btn-orange mr-3 ml-0 filter-symbol" (click)="toggleFilter()">
                                <i class="fas fa-filter" aria-hidden="true"></i>
                                &nbsp;&nbsp;{{ hideShowFilterButtonText }}
                            </button>
                        </div>
                        <!----------------------------------------------->
                        <!------------Pagnatation drp-------------------->
                        <!----------------------------------------------->
                        <div>
                            <label class="mb-0">Show entry</label>
                            <select id="salesOrderErrorListShowEntry" [(ngModel)]="drpselectsize"
                                class="form-control form-control-sm" (change)="changeDataSize($event)">
                                <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                                    <option [value]="dataSize">{{ dataSize }}</option>
                                </ng-template>
                            </select>
                        </div>
                        <app-sync-time-display [syncTimeResponse]="salesOrderSchedulerSyncTimeResponse"
                            [dateTimeDisplayFormat]="dateTimeDisplayFormat"></app-sync-time-display>
                        <!------------------------------------->
                    </div>
                    <!--############################################################-->
                    <!--Right Side-->
                    <!--############################################################-->
                    <div class="childFlex">
                        <div class="btn-group btn-group-sm mr-2" role="group" style="display: inline-block;">
                            <button type="button" (click)="hideShowSyncAndErrorListingEmit(true)"
                                class="btn btn-sm btn-cust-border">Synced</button>
                            <button type="button" class="btn btn-sm btn-orange btn-cust-border">Failed</button>
                        </div>

                        <!--------------------------------------------------->
                        <!--------------Fail Sales Order Operations---------->
                        <!--------------------------------------------------->
                        <div *ngIf="failedSalesOrderOperations.length > 1" class="mr-3">
                            <select id="failedSalesOrderOperation" class="form-control form-control-sm"
                                (change)="changeSalesOrderOperation($event)">
                                <ng-template ngFor let-failedSalesOrderOperation [ngForOf]="failedSalesOrderOperations">
                                    <option [value]="failedSalesOrderOperation">{{ failedSalesOrderOperation }}</option>
                                </ng-template>
                            </select>
                        </div>

                        <!------------------------------------------------>
                        <!----------------refresh------------------------->
                        <!------------------------------------------------>
                        <div>
                            <button class="btn btn-sm btn-orange" (click)="refreshFilter()"
                                id="refresh_salesOrderErrorList"><em class="fa fa-refresh"></em></button>
                        </div>
                    </div>
                </div>
                <!--############################################################-->
                <!--############################################################-->
                <!-- selected sales order start -->
                <div>Total {{totalRecord}} Sales Order(s)
                </div>
                <!-- selected sales order end -->

                <!-------------------------------------------->
                <!-------------------------------------------->
                <!-- sales order table start -->
                <!-------------------------------------------->
                <!-------------------------------------------->
                <div class="commonTable">
                    <table class="table table-sm table-bordered" style="overflow-x: scroll;" aria-hidden="true">
                        <!--###########################################-->
                        <!-- table header Start -->
                        <!--###########################################-->
                        <thead>
                            <tr class="thead-light">
                                <th><span>Order Number</span></th>
                                <th><span>Last Synced Date & Time</span></th>
                                <th><span>Error Message</span></th>
                            </tr>
                        </thead>
                        <!--###########################################-->
                        <!-- table body start -->
                        <!--###########################################-->
                        <tbody>
                            <ng-template ngFor let-salesOrderFailed [ngForOf]="salesOrderFailedResponse">
                                <tr>
                                    <td><span>{{salesOrderFailed?.salesOrderNumber}}</span></td>
                                    <td><span>{{salesOrderFailed?.lastSyncDate |date:dateTimeDisplayFormat}}</span></td>
                                    <td><span>{{salesOrderFailed?.message}}</span></td>
                                </tr>
                            </ng-template>
                        </tbody>
                        <!--###########################################-->
                        <!-- table body end -->
                        <!--###########################################-->
                    </table>

                </div>
                <!-------------------------------------------->
                <!-------------------------------------------->
                <!-- sales order table end -->
                <!-------------------------------------------->
                <!-------------------------------------------->

                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
                <!--pagination Start-->
                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
                <div>
                    <div>Showing {{totalRecordDisplay}} out of {{totalRecord}} Sales Order(s)</div>
                    <div class="float-right">
                        <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="itemsPerPage"
                            id="salesOrderErrotList-pagination" [maxSize]="5" [rotate]="true" [boundaryLinks]="true"
                            (pageChange)="loadPage(page)">
                        </ngb-pagination>
                    </div>
                </div>
                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
                <!--pagination end-->
                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
            </div>
        </div>
    </div>
</body>