import { HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { PresetDetailBaseResponse } from 'src/app/model/Presets/PresetDetailBaseResponse.model';
import { BASE_URL, commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { PresetApiService } from './preset-api.service';

describe('PresetApiService', () => {
  let service: PresetApiService;
  let httpMock: HttpTestingController;

  const mockExceptionService = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        PresetApiService,
        ConfigInjectService,
        { provide: ExceptionHandlingService, useValue: mockExceptionService },
        commonsProviders(null),
      ]
    });

    service = TestBed.inject(PresetApiService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify(); // Ensure no outstanding requests
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should return preset list on successful response', async () => {
    const mockResponse: PresetDetailBaseResponse[] = [
      { id: 1, displayName: 'Preset 1' } as PresetDetailBaseResponse,
      { id: 2, displayName: 'Preset 2' } as PresetDetailBaseResponse,
    ];

    const promise = service.getProbePresetsList();

    const req = httpMock.expectOne(`${BASE_URL}api/probes/presets`);
    expect(req.request.method).toBe('GET');

    req.flush(mockResponse, { status: 200, statusText: 'OK' });

    const result = await promise;
    expect(result).toEqual(mockResponse);
  });

  it('should handle HttpErrorResponse and return empty array', async () => {
    const promise = service.getProbePresetsList();

    const req = httpMock.expectOne(`${BASE_URL}api/probes/presets`);
    expect(req.request.method).toBe('GET');

    req.flush('Internal server error', {
      status: 500,
      statusText: 'Server Error',
    });

    const result = await promise;
    expect(mockExceptionService.customErrorMessage).toHaveBeenCalled();
    expect(result).toEqual([]);
  });
});
