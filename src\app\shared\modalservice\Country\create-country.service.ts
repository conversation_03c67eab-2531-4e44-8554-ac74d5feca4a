import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Cancel } from 'src/app/app.constants';
import { CreateCountryComponent } from 'src/app/FeatureModule/countryModule/create-country/create-country.component';
import { CreateCountryModelRequest } from 'src/app/model/Country/CreateUpdateCountry/CreateAndUpdateCountryModelRequest.model';

@Injectable({
  providedIn: 'root'
})
export class CreateCountryService {

  constructor(
    private modalService: NgbModal
  ) { }

  /**
 * Create/Update Model open
 * <AUTHOR>
 * @param createCountryModelRequest 
 * @returns 
 */
  public openAddCountryPopup(createCountryModelRequest: CreateCountryModelRequest): Promise<boolean> {
    const modalRef = this.modalService.open(CreateCountryComponent, { windowClass: "modal fade" });
    modalRef.componentInstance.createCountryModelRequest = createCountryModelRequest;
    return modalRef.result;
  }

  /**
   * Get Create Role Model Input
   * <AUTHOR>
   * @param resourceName 
   * @param isFilterHidden 
   * @returns 
   */
  public getCreateCountryParameter(resourceName: string, isFilterHidden: boolean): CreateCountryModelRequest {
    return new CreateCountryModelRequest("Create Country", "Create", Cancel, null, resourceName, isFilterHidden);
  }

}
