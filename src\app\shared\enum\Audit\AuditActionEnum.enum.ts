export enum AuditActionEnum {
    DEVICE_LOCK_UNLOCK = "Lock / Unlock Device",
    DEVICE_UPDATE_TYPE = "Update Device Type",
    DEVICE_COUNTRY_ASSOCIATION = "Country Association",
    DEVICE_REMOVE_COUNTRY = "Remove Country Association",
    DEVICE_CUSTOMER_SALESORDER_ASSOCIATION = "Customer / Sales Order Association",
    DEVICE_UPDATE_INFO = "Programmer - Device Update",
    DEVICE_ASSIGN_RELEASE_VERSION = "Release Version Association to Test Device",
    DEVICE_UPDATE_IMPORT_CSV = "Import CSV - Update Device",
    DEVICE_DISABLE = "Disable Device",
    DEVICE_RMA = "RMA Device",
    DEVICE_CONNECTED = "Device Connected",
    DEVICE_DISCONNECTED = "Device Disconnected",
    DEVICE_GENERATE_CERTIFICATE = "Certificate Generated",
    SAS_URI_GENERATED = "SAS URI Generated to Upload Us2 Logs",
    DEVICE_UPDATE_REQUEST = "Device Update Requested",
    DEVICE_REGISTER_ADD = "Device Registered",
    DEVICE_REGISTER_UPDATE = "Update Device Registration",

    PROBE_DELETE = "Delete Probe",
    PROBE_CUSTOMER_SALESORDER_ASSOCIATION = "Customer / Sales Order Association",
    PROBE_ADD = "Add Probe",
    PROBE_ASSIGN_FEATURE = "Assign Features",
    PROBE_UPDATE_TYPE = "Update Probe Type",
    PROBE_CREATE_IMPORT_CSV = "Import CSV - Add Probe",
    PROBE_UPDATE_IMPORT_CSV = "Import CSV - Update Probe",
    PROBE_DISABLE = "Disable Probe",
    PROBE_RMA = "RMA Probe",
    PROBE_UPDATE_INFO = "Add/Update Probe",

    USER_LOGIN = "Login User",
    USER_LOGOUT = "Logout User",
    USER_SET_PASSWORD = "Set Password",
    USER_RESET_PASSWORD = "Reset Password",
    USER_CHANGE_PASSWORD = "Change Password",
    USER_CREATE = "Create User",
    USER_UPDATE = "Update User",
    USER_DELETE = "Delete User",

    ROLE_CREATE = "Create Role",
    ROLE_UPDATE = "Update Role",
    ROLE_DELETE = "Delete Role",

    SOFTWARE_BUILD_SAVE = "Upload Software Build",
    SOFTWARE_BUILD_UPDATE_TYPE = "Associate Device Type",
    SOFTWARE_BUILD_ACTIVE_INACTIVE = "Active / Inactive Software Build",
    SOFTWARE_BUILD_UPDATE = "Update Software Build",
    SOFTWARE_BUILD_DELETE = "Delete Software Build",

    VIDEO_ADD_VIDEO = "Add Video",
    VIDEO_ADD_JSON = "Add JSON",
    VIDEO_UPDATE_JSON = "Update JSON",
    VIDEO_UPDATE_VIDEO = "Update Video",
    VIDEO_DELETE_VIDEO = "Delete Video",
    VIDEO_DELETE_JSON = "Delete JSON",

    BRIDGE_KIT_MANGEMENT_ADD_IMPORT_CSV = "Import CSV - Add Bridge Kit",
    BRIDGE_MANGEMENT_DELETE_IMPORT_CSV = "Import CSV - Delete Bridge Kit",

    OTS_KIT_MANGEMENT_ADD_IMPORT_CSV = "Import CSV - Add OTS Kit",
    OTS_KIT_MANGEMENT_DELETE_IMPORT_CSV = "Import CSV - Delete OTS Kit",

    SALES_ORDER_SCHEDULER_START = "Schedular Start",
    SALES_ORDER_SCHEDULER_COMPLETE = "Schedular Stop",
    SALES_ORDER_SYNC_SUCCESS = "Sync Success",
    SALES_ORDER_SYNC_ERROR = "Sync Error",
    SALES_ORDER_PROBE_DISABLE = "Disable Probe",
    SALES_ORDER_PROBE_RMA = "RMA Probe",
    SALES_ORDER_DEVICE_DISABLE = "Disable Device",
    SALES_ORDER_DEVICE_RMA = "RMA Device",
    SALES_ORDER_BRIDGE_RESET = "Reset Device",
    SALES_ORDER_BRIDGE_CONFIGURE = "Device Configuration - Completed",
    SALES_ORDER_BRIDGE_IN_PROGRESS = "Device Configuration - Started",
    SALES_ORDER_BRIDGE_FAILED = "Device Configuration - Error",
    SALES_ORDER_PROBE_CONFIGURE = "Configure Probe",
    SALES_ORDER_ADD_USER_DEFINED_ORDER = "Add User Defined Order",
    SALES_ORDER_UPDATE_USER_DEFINED_ORDER = "Update User Defined Order",

    COUNTRY_ADD = "Add Country",
    COUNTRY_DELETE = "Delete Country",

    PROBE_CONFIG_GROUP_DELETE = "Delete Probe Config Group",
    PROBE_CONFIG_GROUP_ADD = "Add Probe Config Group"
}