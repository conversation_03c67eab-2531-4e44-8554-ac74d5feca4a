import { AuditDataTypeEnum } from "src/app/shared/enum/Audit/AuditDataTypeEnum.enum";

export class AuditDetailResponse {
    title: string;
    oldValue: string;
    newValue: string;
    type: AuditDataTypeEnum;
    field: string;
    multiple: boolean;

    constructor(title: string, oldValue: string, newValue: string, type: AuditDataTypeEnum, field: string, multiple: boolean) {
        this.title = title;
        this.oldValue = oldValue;
        this.newValue = newValue;
        this.type = type;
        this.field = field;
        this.multiple = multiple;
    }
}