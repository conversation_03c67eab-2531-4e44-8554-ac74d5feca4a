<!--####################################################-->
<!--Filter Start-->
<!--Audit Filter--->
<!--####################################################-->
<form id="auditFilterform" role="form" class="form" [formGroup]="filterAuditForm">


    <!------------------------------------->
    <!-----------uniqueId-------------->
    <!------------------------------------->
    <div class="form-group">
        <label class="form-control-label" for="field_uniqueId"><strong>Unique Id
            </strong></label>
        <input class="form-control" type="text" formControlName="uniqueId" id="field_uniqueId" />
        <div *ngIf="(filterAuditForm.get('uniqueId').touched || filterAuditForm.get('uniqueId').dirty) &&  
                            filterAuditForm.get('uniqueId').invalid">
            <div *ngIf="filterAuditForm.get('uniqueId').errors['maxlength']">
                <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
            </div>
            <div *ngIf="filterAuditForm.get('uniqueId').errors['pattern']">
                <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
            </div>
        </div>
    </div>

    <!------------------------------------->
    <!-----------Module------------------->
    <!------------------------------------->
    <div class="form-group">
        <label class="form-control-label" for="field_Module"><strong>Module</strong></label>
        <ng-multiselect-dropdown name="Module" class="devicePageDeviceType" [placeholder]="''" formControlName="modules"
            [settings]="moduleSetting" [data]="moduleList" (onSelect)="changeModule($any($event)?.name,true)"
            (onDeSelect)="changeModule($any($event)?.name,false)">
        </ng-multiselect-dropdown>
    </div>


    <!------------------------------------->
    <!-----------Action------------------->
    <!------------------------------------->
    <div class="form-group">
        <label class="form-control-label" for="field_Module"><strong>Action</strong></label>
        <ng-multiselect-dropdown name="Action" [placeholder]="''" formControlName="actions" [settings]="actionSetting"
            [data]="actionList">
        </ng-multiselect-dropdown>
    </div>




    <!------------------------------------->
    <!-----------User-------------->
    <!------------------------------------->
    <div class="form-group">
        <label class="form-control-label" for="field_User"><strong>Modified By
            </strong></label>
        <input class="form-control" type="text" formControlName="user" id="field_User" />
        <div *ngIf="(filterAuditForm.get('user').touched || filterAuditForm.get('user').dirty) &&  
                            filterAuditForm.get('user').invalid">
            <div *ngIf="filterAuditForm.get('user').errors['maxlength']">
                <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
            </div>
            <div *ngIf="filterAuditForm.get('user').errors['pattern']">
                <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
            </div>
        </div>
    </div>

    <!--------------------------->
    <!-- Modified Start Date - start -->
    <!--------------------------->
    <div class="form-group">
        <label class="form-control-label" for="field_ModifiedStartDate"><strong>Modified Start Date &
                Time</strong></label>
        <mat-form-field>
            <input class="form-control" style="width:90%;display: inherit;" matInput [matDatepicker]="start"
                placeholder="Choose a Modified Start Date" formControlName="modifiedStartDate" [max]="maxdate">
            <mat-datepicker-toggle matSuffix [for]="start"></mat-datepicker-toggle>
            <mat-datepicker #start></mat-datepicker>

        </mat-form-field>
    </div>
    <!--------------------------->
    <!-- Modified Start Date - end -->
    <!--------------------------->

    <!--------------------------->
    <!-- Modified Start Date - start -->
    <!--------------------------->
    <div class="form-group">
        <label class="form-control-label" for="field_ModifiedEndDate"><strong>Modified End Date & Time</strong></label>
        <mat-form-field>
            <input class="form-control" style="width:90%;display: inherit;" matInput [matDatepicker]="end"
                placeholder="Choose a Modified End Date" formControlName="modifiedEndDate" [max]="maxdate">
            <mat-datepicker-toggle matSuffix [for]="end"></mat-datepicker-toggle>
            <mat-datepicker #end></mat-datepicker>
        </mat-form-field>
    </div>
    <!--------------------------->
    <!-- Modified Start Date - end -->
    <!--------------------------->
    <!--##################################################-->

    <!--Search From Historical Data Start-->
    <div class="form-group d-flex align-items-center justify-content-start" id="switchBtn">
        <div class="form-control-label archiveDataMargin">
            <strong>
                Search From Historical Data
            </strong>
        </div>
        <div>
            <label class="switch">
                <input type="checkbox" formControlName="archivedData" id="archivedData"
                    (change)="onArchivedDataChange($event)" [checked]="filterAuditForm.get('archivedData').value">
                <span class="slider round"></span>
            </label>
        </div>
    </div>
    <!--Search From Historical Data End-->

    <hr class="mt-1 mb-2">
    <!--####################################################-->
    <!---------Action Button Start------->
    <!--####################################################-->
    <div class="">
        <button class="btn btn-sm btn-orange mr-3" (click)="searchData()" id="auditFilterSearch"
            [disabled]="filterAuditForm.invalid">Search</button>
        <button class="btn btn-sm btn-orange"
            (click)="clearFilter(defaultListingPageReloadSubjectParameter)">Clear</button>
    </div>
    <!--####################################################-->
    <!---------Action Button End------->
    <!--####################################################-->
</form>
<!--####################################################-->
<!--Filter End-->
<!--Audit Filter--->
<!--####################################################-->