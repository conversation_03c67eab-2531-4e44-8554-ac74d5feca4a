import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { JobHistory } from '../model/job-history.model';
import { JobHistoryResponse } from '../model/job/jobHistoryResponse.model';
import { ExceptionHandlingService } from '../shared/ExceptionHandling.service';
import { JobService } from '../shared/Service/JobService/job.service';



@Component({
  selector: 'app-job-detail',
  templateUrl: './job-detail.component.html',
  styleUrls: ['./job-detail.component.css']
})
export class JobDetailComponent implements OnInit {

  @Input("jobScheduleStatusId") jobScheduleStatusId;
  @Output("showJob") showJob = new EventEmitter;

  // model data
  modelData: JobHistory[];
  jobHistoryResponse: JobHistoryResponse = null;

  loading: boolean = false;


  constructor(protected jobService: JobService,
    private exceptionService: ExceptionHandlingService) { }

  ngOnInit() {
    this.jobDetailInformation(this.jobScheduleStatusId);
  }

  jobDetailInformation(jobScheduleStatusId: number) {
    this.loading = true;
    this.jobService.getJobHistory(jobScheduleStatusId).subscribe({
      next: (res: HttpResponse<JobHistoryResponse>) => {
        if (res.body) {
          this.jobHistoryResponse = res.body;
          this.loading = false;
        }
      }, error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  back() {
    this.showJob.emit();
  }

  /**
  * Refresh Job Detail Page Data
  * 
  * <AUTHOR> 
  */
  public refreshJobDetailPage(): void {
    this.jobDetailInformation(this.jobScheduleStatusId);
  }
}
