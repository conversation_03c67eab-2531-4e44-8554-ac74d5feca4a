@import '../../../../../src/assets/css/custom_style.css';

.commonTable {
    overflow-x: auto;
    width:100%;
}

.commonTable th,
.commonTable td {
    min-width: 100px;
}
.commonTable th{
  white-space: nowrap;
}
.commonTable .width-unset {
    min-width: auto;
}

.versionDiv{
    font-size: 1.25rem;
    font-weight: 500;
}
.versionDiv .versionText{
    margin-top: 16px;
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.commonTable .descriptionText{
    max-width: 400px !important;
    word-break: break-word;
}

.commonTable .ulList{
    padding-left: 16px;
    margin: 0px;
}

.commonTable .multiprobe{
    max-width: unset;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.headerAlignment .btn-cust-border{
    border:1px solid #f79423;
}