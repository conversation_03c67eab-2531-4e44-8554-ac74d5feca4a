import { DatePipe } from '@angular/common';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ChangeDetectorRef } from '@angular/core';
import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { ProbDetailResource, ProbListResource, TransferOrderResource } from 'src/app/app.constants';
import { BasicModelConfig } from 'src/app/model/common/BasicModelConfig.model';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { ConfigBaseMappingRequest } from 'src/app/model/probe/ConfigBaseMappingRequest.model';
import { ConfigMappingRequest } from 'src/app/model/probe/ConfigMappingRequest.model';
import { ConfigureLicenceDetails } from 'src/app/model/probe/ConfigureLicenceDetails.model';
import { ConfigureLicenceResponse } from 'src/app/model/probe/ConfigureLicenceResponse.model';
import { ProbeDetailWithConfig } from 'src/app/model/probe/ProbeDetailWithConfig.model';
import { ProbeFeatureResponse } from 'src/app/model/probe/ProbeFeatureResponse.model';
import { ProbePresetResponse } from 'src/app/model/probe/ProbePresetResponse.model';
import { ProbeTypeResponse } from 'src/app/model/probe/probeTypeResponse.model';
import { EndDateOptions } from 'src/app/shared/enum/endDateOptions.enum';
import { ValidityEnum } from 'src/app/shared/enum/ValidityEnum.enum';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { AssignConfigCheckBoxPipe } from 'src/app/shared/pipes/Probe/assign-config-checkbox.pipe';
import { AssignConfigDisablePipe } from 'src/app/shared/pipes/Probe/assign-config-disable.pipe';
import { FeatureValidityOptionHideShowPipe } from 'src/app/shared/pipes/Probe/feature-validity-option-hide-show.pipe';
import { FeaturesBaseResponseDisplayPipe } from 'src/app/shared/pipes/Probe/features-base-response-display.pipe';
import { FeaturesCustomEndDateDisplayPipe } from 'src/app/shared/pipes/Probe/features-customEndDateDisplay.pipe';
import { FeaturesExpireDateDisplayPipe } from 'src/app/shared/pipes/Probe/features-expire-datedisplay.pipe';
import { FeaturesRadioButtonPipe } from 'src/app/shared/pipes/Probe/features-radio-button.pipe';
import { FeaturesSelectCustomDatePipe } from 'src/app/shared/pipes/Probe/features-selectCustomDate.pipe';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { ProbeService } from 'src/app/shared/Service/ProbeService/probe.service';
import { UpdateAssociationService } from 'src/app/shared/update-association.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { AssignFeaturesComponent } from './assign-features.component';

describe('AssignFeaturesComponent - Complete Coverage', () => {
  let component: AssignFeaturesComponent;
  let fixture: ComponentFixture<AssignFeaturesComponent>;
  let probeApiService: jasmine.SpyObj<ProbeApiService>;
  let probeService: jasmine.SpyObj<ProbeService>;
  let permissionService: jasmine.SpyObj<PermissionService>;
  let activeModal: jasmine.SpyObj<NgbActiveModal>;
  let toastrService: jasmine.SpyObj<ToastrService>;
  let exceptionService: jasmine.SpyObj<ExceptionHandlingService>;
  let updateAssociationService: jasmine.SpyObj<UpdateAssociationService>;
  let cdr: jasmine.SpyObj<ChangeDetectorRef>;

  const mockProbeTypes: ProbeTypeResponse[] = [{
    probeTypeId: 1,
    name: 'TestProbe',
    prefix: 'TP',
    displayName: 'TestProbe',
    features: [{
      id: 1,
      featureId: 1,
      displayName: 'Feature1',
      name: 'Feature1',
      validity: ValidityEnum.ONE_YEAR,
      partNumbers: []
    } as ProbeFeatureResponse],
    presets: [{
      id: 1,
      presetId: 1,
      displayName: 'Preset1',
      name: 'Preset1',
      validity: ValidityEnum.PERPETUAL,
      partNumbers: []
    } as ProbePresetResponse]
  }];

  const mockFeatures: ProbeFeatureResponse[] = [
    { id: 1, featureId: 1, displayName: 'Feature1', name: 'Feature1', validity: ValidityEnum.ONE_YEAR, partNumbers: [] } as ProbeFeatureResponse,
    { id: 2, featureId: 2, displayName: 'Feature2', name: 'Feature2', validity: ValidityEnum.PERPETUAL, partNumbers: [] } as ProbeFeatureResponse
  ];

  const mockPresets: ProbePresetResponse[] = [
    {
      id: 1,
      presetId: 1,
      displayName: 'Preset1',
      name: 'Preset1',
      validity: ValidityEnum.ONE_YEAR,
      partNumbers: []
    } as ProbePresetResponse,
    {
      id: 2,
      presetId: 2,
      displayName: 'Preset2',
      name: 'Preset2',
      validity: ValidityEnum.PERPETUAL,
      partNumbers: []
    } as ProbePresetResponse
  ];

  const mockConfigRequests: ConfigBaseMappingRequest[] = [
    { id: 1, enable: false, startDate: null, endDate: null, endDateUi: null } as ConfigBaseMappingRequest,
    { id: 2, enable: true, startDate: Date.now(), endDate: Date.now() + 31536000000, endDateUi: EndDateOptions.ONE_YEAR } as ConfigBaseMappingRequest
  ];

  beforeEach(async () => {
    const probeApiSpy = jasmine.createSpyObj('ProbeApiService', [
      'getprobeTypeResponseList',
      'getFeaturesList',
      'getPresetsList',
      'getAsyncProbeDetailInfo',
      'updateProbeFeatures',
      'dowloadSasUriofFeatureLicenseAsync'
    ]);
    const probeServiceSpy = jasmine.createSpyObj('ProbeService', [
      'getConfigListForAssign',
      'getFeaturesListForUpdate',
      'getPresetsListForUpdate',
      'getEndDateOptions',
      'setReminderForUserActionForAssignConfig'
    ]);
    const permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getProbPermission']);
    const activeModalSpy = jasmine.createSpyObj('NgbActiveModal', ['close']);
    const toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const exceptionServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    const updateAssociationServiceSpy = jasmine.createSpyObj('UpdateAssociationService', ['openUpdateAssociationModel']);
    const cdrSpy = jasmine.createSpyObj('ChangeDetectorRef', ['detectChanges']);

    await TestBed.configureTestingModule({
      declarations: [AssignFeaturesComponent, AssignConfigCheckBoxPipe, AssignConfigDisablePipe, FeaturesSelectCustomDatePipe, FeaturesBaseResponseDisplayPipe, FeatureValidityOptionHideShowPipe, FeaturesRadioButtonPipe, FeaturesCustomEndDateDisplayPipe, FeaturesExpireDateDisplayPipe],
      imports: [MatFormFieldModule, MatInputModule, MatSelectModule, FormsModule, ReactiveFormsModule, MatNativeDateModule, MatDatepickerModule, BrowserAnimationsModule],
      providers: [
        { provide: ProbeApiService, useValue: probeApiSpy },
        { provide: ProbeService, useValue: probeServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: NgbActiveModal, useValue: activeModalSpy },
        CommonsService,
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: ExceptionHandlingService, useValue: exceptionServiceSpy },
        { provide: UpdateAssociationService, useValue: updateAssociationServiceSpy },
        { provide: ChangeDetectorRef, useValue: cdrSpy },
        LocalStorageService,
        SessionStorageService,
        DatePipe
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AssignFeaturesComponent);
    component = fixture.componentInstance;

    // Get service instances
    probeApiService = TestBed.inject(ProbeApiService) as jasmine.SpyObj<ProbeApiService>;
    probeService = TestBed.inject(ProbeService) as jasmine.SpyObj<ProbeService>;
    permissionService = TestBed.inject(PermissionService) as jasmine.SpyObj<PermissionService>;
    activeModal = TestBed.inject(NgbActiveModal) as jasmine.SpyObj<NgbActiveModal>;
    toastrService = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
    exceptionService = TestBed.inject(ExceptionHandlingService) as jasmine.SpyObj<ExceptionHandlingService>;
    updateAssociationService = TestBed.inject(UpdateAssociationService) as jasmine.SpyObj<UpdateAssociationService>;
    cdr = TestBed.inject(ChangeDetectorRef) as jasmine.SpyObj<ChangeDetectorRef>;

    // Setup component inputs
    component.basicModelConfig = new BasicModelConfig('Title', 'Message', 'OK', 'Cancel');
    component.configureLicenceDetails = new ConfigureLicenceDetails(1, 'TestProbe', { features: [], presets: [], reminder: false });

    // Setup default mock returns
    permissionService.getProbPermission.and.returnValue(true);
    probeApiService.getprobeTypeResponseList.and.returnValue(Promise.resolve(mockProbeTypes));
    probeApiService.getFeaturesList.and.returnValue(Promise.resolve(mockFeatures));
    probeApiService.getPresetsList.and.returnValue(Promise.resolve(mockPresets));
    probeService.getConfigListForAssign.and.returnValue(mockConfigRequests);
    probeService.setReminderForUserActionForAssignConfig.and.returnValue(false);
  });

  describe('Component Initialization', () => {
    it('should create component', () => {
      expect(component).toBeTruthy();
    });

    it('should call getInitData on ngOnInit', () => {
      spyOn(component as any, 'getInitData');
      component.ngOnInit();
      expect(component['getInitData']).toHaveBeenCalled();
    });

    it('should initialize with default values', () => {
      expect(component.currentFeatures).toEqual([]);
      expect(component.currentPresets).toEqual([]);
      expect(component.featuresRequestList).toEqual([]);
      expect(component.presetsRequestList).toEqual([]);
      expect(component.isDownload).toBeFalse();
      expect(component.isDownloadLicenceDisplay).toBeTrue();
      expect(component.reloadPipe).toBeTrue();
      expect(component.allApiCall).toBeFalse();
      expect(component.loading).toBeFalse();
    });
  });

  describe('getInitData Method', () => {
    beforeEach(() => {
      probeService.getFeaturesListForUpdate.and.returnValue(mockConfigRequests);
      probeService.getPresetsListForUpdate.and.returnValue(mockConfigRequests);
    });

    it('should handle ProbListResource correctly', fakeAsync(() => {
      component.resource = ProbListResource;

      component['getInitData']();
      tick();

      expect(component.setReminderOptionDisplayPermission).toBeTrue();
      expect(component.loading).toBeFalse();
      expect(component.allApiCall).toBeTrue();
      expect(probeService.getConfigListForAssign).toHaveBeenCalledTimes(2);
    }));

    it('should handle ProbDetailResource correctly', fakeAsync(() => {
      component.resource = ProbDetailResource;
      const mockProbeDetail: ProbeDetailWithConfig = {
        id: 1,
        type: 'TestProbe',
        features: mockConfigRequests,
        presets: mockConfigRequests,
        reminder: true
      } as ProbeDetailWithConfig;

      probeApiService.getAsyncProbeDetailInfo.and.returnValue(Promise.resolve(mockProbeDetail));

      component['getInitData']();
      tick();

      expect(probeApiService.getAsyncProbeDetailInfo).toHaveBeenCalledWith(1);
      expect(probeService.getFeaturesListForUpdate).toHaveBeenCalled();
      expect(probeService.getPresetsListForUpdate).toHaveBeenCalled();
      expect(component.probeObject.reminder).toBeTrue();
    }));

    it('should handle TransferOrderResource correctly', fakeAsync(() => {
      component.resource = TransferOrderResource;
      component.configureLicenceDetails.probeObject = {
        features: [{ id: 1, endDate: null } as ConfigBaseMappingRequest],
        presets: [{ id: 2, endDate: 123456789 } as ConfigBaseMappingRequest],
        reminder: true
      };

      probeService.getEndDateOptions.and.returnValue(EndDateOptions.UNLIMITED);

      component['getInitData']();
      tick();

      expect(component.isDownloadLicenceDisplay).toBeFalse();
      expect(component.probeObject.reminder).toBeTrue();
      expect(probeService.getEndDateOptions).toHaveBeenCalled();
    }));

    it('should close modal when API calls fail', fakeAsync(() => {
      component.resource = ProbListResource;
      probeApiService.getprobeTypeResponseList.and.returnValue(Promise.resolve([]));

      component['getInitData']();
      tick();

      expect(activeModal.close).toHaveBeenCalledWith(false);
    }));

    it('should close modal when features list is empty', fakeAsync(() => {
      component.resource = ProbListResource;
      probeApiService.getFeaturesList.and.returnValue(Promise.resolve([]));

      component['getInitData']();
      tick();

      expect(activeModal.close).toHaveBeenCalledWith(false);
    }));

    it('should close modal when presets list is empty', fakeAsync(() => {
      component.resource = ProbListResource;
      probeApiService.getPresetsList.and.returnValue(Promise.resolve([]));

      component['getInitData']();
      tick();

      expect(activeModal.close).toHaveBeenCalledWith(false);
    }));
  });

  describe('setDefaultCurrentFeatures Method', () => {
    it('should set features when probe type matches', () => {
      component['setDefaultCurrentFeatures'](mockProbeTypes, 'TestProbe', mockFeatures);

      expect(component.currentFeatures).toEqual(mockProbeTypes[0].features);
    });

    it('should set all features when probe type does not match', () => {
      component['setDefaultCurrentFeatures'](mockProbeTypes, 'NonExistentProbe', mockFeatures);

      expect(component.currentFeatures).toEqual(mockFeatures);
    });

    it('should set empty array when featuresList is null', () => {
      component['setDefaultCurrentFeatures'](mockProbeTypes, 'NonExistentProbe', null);

      expect(component.currentFeatures).toEqual([]);
    });
  });

  describe('setDefaultCurrentPresets Method', () => {
    it('should set presets when probe type matches', () => {
      component['setDefaultCurrentPresets'](mockProbeTypes, 'TestProbe', mockPresets);

      expect(component.currentPresets).toEqual(mockProbeTypes[0].presets);
    });

    it('should set all presets when probe type does not match', () => {
      component['setDefaultCurrentPresets'](mockProbeTypes, 'NonExistentProbe', mockPresets);

      expect(component.currentPresets).toEqual(mockPresets);
    });

    it('should set empty array when presetsList is null', () => {
      component['setDefaultCurrentPresets'](mockProbeTypes, 'NonExistentProbe', null);

      expect(component.currentPresets).toEqual([]);
    });
  });

  describe('UI Control Methods', () => {
    it('should update download flag', () => {
      component.onChangeDownloadLicense(true);
      expect(component.isDownload).toBeTrue();

      component.onChangeDownloadLicense(false);
      expect(component.isDownload).toBeFalse();
    });

    it('should close modal with false response', () => {
      component.close();
      expect(activeModal.close).toHaveBeenCalledWith(new ConfigureLicenceResponse(false, null));
    });

    it('should close modal with true response', () => {
      component.accept();
      expect(activeModal.close).toHaveBeenCalledWith(new ConfigureLicenceResponse(true, null));
    });

    it('should reload UI with pipe correctly', () => {
      component.reloadUiWithPipe();

      expect(cdr.detectChanges).toHaveBeenCalledTimes(0);
      expect(component.reloadPipe).toBeTrue();
    });

    it('should set reminder flag', () => {
      component.probeObject = new ConfigMappingRequest([], [], false);

      component.setIsReminder(true);
      expect(component.probeObject.reminder).toBeTrue();

      component.setIsReminder(false);
      expect(component.probeObject.reminder).toBeFalse();
    });
  });

  describe('Reminder Logic', () => {
    beforeEach(() => {
      component.probeObject = new ConfigMappingRequest(
        [{ id: 1, enable: true }, { id: 2, enable: false }] as ConfigBaseMappingRequest[],
        [{ id: 3, enable: false }, { id: 4, enable: true }] as ConfigBaseMappingRequest[],
        false
      );
    });

    it('should disable reminder when no features or presets are enabled', () => {
      component.probeObject.features = [{ id: 1, enable: false }] as ConfigBaseMappingRequest[];
      component.probeObject.presets = [{ id: 1, enable: false }] as ConfigBaseMappingRequest[];

      component.setReminderDisabledForAssignConfig();

      expect(component.isReminderDisabled).toBeTrue();
    });

    it('should enable reminder when at least one feature is enabled', () => {
      component.setReminderDisabledForAssignConfig();

      expect(component.isReminderDisabled).toBeFalse();
    });

    it('should update reminder for user action', () => {
      spyOn(component, 'setReminderDisabledForAssignConfig');
      spyOn(component, 'setIsReminder');
      spyOn(component, 'reloadUiWithPipe');

      component.updateReminderForUserAction();

      expect(component.setReminderDisabledForAssignConfig).toHaveBeenCalled();
      expect(component.setIsReminder).toHaveBeenCalled();
      expect(component.reloadUiWithPipe).toHaveBeenCalled();
    });
  });

  describe('Feature Management', () => {
    beforeEach(() => {
      component.probeObject = new ConfigMappingRequest(
        [{ id: 1, enable: false, startDate: null, endDate: null, endDateUi: null }] as ConfigBaseMappingRequest[],
        [],
        false
      );
      spyOn(component, 'updateReminderForUserAction');
    });

    it('should enable feature correctly', () => {
      const mockFeature: ProbeFeatureResponse = {
        id: 1,
        featureId: 1,
        displayName: 'Feature1',
        name: 'Feature1',
        partNumbers: []
      };
      const startDate = Date.now();

      component.onChangeFeaturesForUpdate(1, true, startDate, mockFeature);

      expect(component.probeObject.features[0].enable).toBeTrue();
      expect(component.probeObject.features[0].startDate).toBe(startDate);
      expect(component.updateReminderForUserAction).toHaveBeenCalled();
    });

    it('should disable feature correctly', () => {
      component.probeObject.features[0].enable = true;
      const mockFeature: ProbeFeatureResponse = {
        id: 1,
        featureId: 1,
        displayName: 'Feature1',
        name: 'Feature1',
        partNumbers: []
      };

      component.onChangeFeaturesForUpdate(1, false, Date.now(), mockFeature);

      expect(component.probeObject.features[0].enable).toBeFalse();
      expect(component.probeObject.features[0].startDate).toBeNull();
      expect(component.probeObject.features[0].endDate).toBeNull();
      expect(component.probeObject.features[0].endDateUi).toBeNull();
    });

    it('should handle non-existent feature ID', () => {
      const mockFeature = {} as ProbeFeatureResponse;

      component.onChangeFeaturesForUpdate(999, true, Date.now(), mockFeature);

      expect(component.updateReminderForUserAction).toHaveBeenCalled();
    });

    it('should update feature end date', () => {
      const startDate = Date.now();
      const customEndDate = new Date();

      component.onChangeFeaturesEndDateForUpdate(1, startDate, EndDateOptions.CUSTOMDATE, customEndDate);

      expect(component.probeObject.features[0].enable).toBeTrue();
      expect(component.probeObject.features[0].startDate).toBe(startDate);
      expect(component.probeObject.features[0].endDateUi).toBe(EndDateOptions.CUSTOMDATE);
    });

    it('should get correct feature index', () => {
      const index = component.getFetauresIndex(1);
      expect(index).toBe(0);

      const notFoundIndex = component.getFetauresIndex(999);
      expect(notFoundIndex).toBe(-1);
    });
  });

  describe('Preset Management', () => {
    beforeEach(() => {
      component.probeObject = new ConfigMappingRequest(
        [],
        [{ id: 1, enable: false, startDate: null, endDate: null, endDateUi: null }] as ConfigBaseMappingRequest[],
        false
      );
      spyOn(component, 'updateReminderForUserAction');
    });

    it('should enable preset correctly', () => {
      const mockPreset = {} as ProbePresetResponse;
      const startDate = Date.now();

      component.onChangePresetsForUpdate(1, true, startDate, mockPreset);

      expect(component.probeObject.presets[0].enable).toBeTrue();
      expect(component.probeObject.presets[0].startDate).toBe(startDate);
      expect(component.updateReminderForUserAction).toHaveBeenCalled();
    });

    it('should disable preset correctly', () => {
      component.probeObject.presets[0].enable = true;
      const mockPreset = {} as ProbePresetResponse;

      component.onChangePresetsForUpdate(1, false, Date.now(), mockPreset);

      expect(component.probeObject.presets[0].enable).toBeFalse();
      expect(component.probeObject.presets[0].startDate).toBeNull();
      expect(component.probeObject.presets[0].endDate).toBeNull();
      expect(component.probeObject.presets[0].endDateUi).toBeNull();
    });

    it('should handle non-existent preset ID', () => {
      const mockPreset = {} as ProbePresetResponse;

      component.onChangePresetsForUpdate(999, true, Date.now(), mockPreset);

      expect(component.updateReminderForUserAction).toHaveBeenCalled();
    });

    it('should update preset end date', () => {
      const startDate = Date.now();
      const customEndDate = new Date();

      component.onChangePresetsEndDateForUpdate(1, startDate, EndDateOptions.UNLIMITED, customEndDate);

      expect(component.probeObject.presets[0].enable).toBeTrue();
      expect(component.probeObject.presets[0].startDate).toBe(startDate);
      expect(component.probeObject.presets[0].endDateUi).toBe(EndDateOptions.UNLIMITED);
    });

    it('should get correct preset index', () => {
      const index = component.getPresetsIndex(1);
      expect(index).toBe(0);

      const notFoundIndex = component.getPresetsIndex(999);
      expect(notFoundIndex).toBe(-1);
    });
  });

  describe('Submit and Assignment Logic', () => {
    beforeEach(() => {
      component.probeObject = new ConfigMappingRequest([], [], false);
      component.configureLicenceDetails = new ConfigureLicenceDetails(1, 'TestProbe', null);
    });

    it('should submit assignment for non-TransferOrder resource with confirmation', fakeAsync(() => {
      component.resource = ProbDetailResource;
      updateAssociationService.openUpdateAssociationModel.and.returnValue(Promise.resolve(true));
      spyOn(component, 'assignFeatureUpdate');

      component.submitAssignFeature();
      tick();

      expect(updateAssociationService.openUpdateAssociationModel).toHaveBeenCalled();
      expect(component.assignFeatureUpdate).toHaveBeenCalled();
    }));

    it('should not submit when confirmation is cancelled', fakeAsync(() => {
      component.resource = ProbDetailResource;
      updateAssociationService.openUpdateAssociationModel.and.returnValue(Promise.resolve(false));
      spyOn(component, 'assignFeatureUpdate');

      component.submitAssignFeature();
      tick();

      expect(component.assignFeatureUpdate).not.toHaveBeenCalled();
    }));

    it('should submit assignment directly for TransferOrder resource', () => {
      component.resource = TransferOrderResource;
      spyOn(component, 'assignFeatureUpdate');

      component.submitAssignFeature();

      expect(component.assignFeatureUpdate).toHaveBeenCalled();
    });

    it('should handle TransferOrder assignment correctly', () => {
      component.resource = TransferOrderResource;

      component.assignFeatureUpdate();

      expect(activeModal.close).toHaveBeenCalledWith(new ConfigureLicenceResponse(true, component.probeObject));
    });

    it('should handle regular assignment with success', () => {
      component.resource = ProbDetailResource;
      const mockResponse = new HttpResponse<SuccessMessageResponse>({ body: { message: 'Success' } });
      probeApiService.updateProbeFeatures.and.returnValue(of(mockResponse));
      spyOn(component, 'accept');

      component.assignFeatureUpdate();

      expect(probeApiService.updateProbeFeatures).toHaveBeenCalledWith(1, component.probeObject);
      expect(toastrService.success).toHaveBeenCalledWith('Success');
      expect(component.accept).toHaveBeenCalled();
      expect(component.loading).toBeFalse();
    });

    it('should handle regular assignment with download', fakeAsync(() => {
      component.resource = ProbDetailResource;
      component.isDownload = true;
      const mockResponse = new HttpResponse<SuccessMessageResponse>({ body: { message: 'Success' } });
      probeApiService.updateProbeFeatures.and.returnValue(of(mockResponse));
      spyOn(component, 'downloadProbe').and.returnValue(Promise.resolve());

      component.assignFeatureUpdate();
      tick();

      expect(component.downloadProbe).toHaveBeenCalledWith([1]);
    }));

    it('should handle assignment error', () => {
      component.resource = ProbDetailResource;
      const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
      probeApiService.updateProbeFeatures.and.returnValue(throwError(() => mockError));
      spyOn(component, 'close');

      component.assignFeatureUpdate();

      expect(exceptionService.customErrorMessage).toHaveBeenCalledWith(mockError);
      expect(component.close).toHaveBeenCalled();
      expect(component.loading).toBeFalse();
    });
  });

  describe('Download Functionality', () => {
    it('should download probe successfully', fakeAsync(() => {
      probeApiService.dowloadSasUriofFeatureLicenseAsync.and.returnValue(Promise.resolve());
      spyOn(component, 'accept');

      component.downloadProbe([1, 2, 3]);
      tick();

      expect(probeApiService.dowloadSasUriofFeatureLicenseAsync).toHaveBeenCalledWith([1, 2, 3], null);
      expect(component.accept).toHaveBeenCalled();
    }));
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle empty probe types response', fakeAsync(() => {
      component.resource = ProbListResource;
      probeApiService.getprobeTypeResponseList.and.returnValue(Promise.resolve([]));

      component['getInitData']();
      tick();

      expect(activeModal.close).toHaveBeenCalledWith(false);
    }));

    it('should handle case-insensitive probe type matching', () => {
      const probeTypes = [{
        probeTypeId: 1,
        name: 'TestProbe',
        prefix: 'TP',
        displayName: 'TestProbe',
        features: mockFeatures,
        presets: mockPresets
      }];

      component['setDefaultCurrentFeatures'](probeTypes, 'testprobe', mockFeatures);
      expect(component.currentFeatures).toEqual(mockFeatures);
    });

    it('should handle multiple matching probe types', () => {
      const duplicateProbeTypes = [
        ...mockProbeTypes,
        {
          probeTypeId: 2,
          name: 'TestProbe',
          prefix: 'TP2',
          displayName: 'TestProbe',
          features: [{ id: 3, name: 'Feature3' }] as ProbeFeatureResponse[],
          presets: [{ id: 3, name: 'Preset3' }] as ProbePresetResponse[]
        }
      ];

      component['setDefaultCurrentFeatures'](duplicateProbeTypes, 'TestProbe', mockFeatures);
      // Should use the first matching probe type
      expect(component.currentFeatures.length).toEqual(2);
    });

    it('should handle null endDate in TransferOrder mapping', fakeAsync(() => {
      component.resource = TransferOrderResource;
      component.configureLicenceDetails.probeObject = {
        features: [
          { id: 1, endDate: null } as ConfigBaseMappingRequest,
          { id: 2, endDate: 123456789 } as ConfigBaseMappingRequest
        ],
        presets: [{ id: 3, endDate: null } as ConfigBaseMappingRequest],
        reminder: false
      };

      probeService.getEndDateOptions.and.returnValue(EndDateOptions.UNLIMITED);

      component['getInitData']();
      tick();

      expect(component.featuresRequestList[0].endDateUi).toBeNull();
      expect(component.featuresRequestList[1].endDateUi).toBe(EndDateOptions.UNLIMITED);
    }));

    it('should handle permission service returning false', fakeAsync(() => {
      component.resource = ProbListResource;
      permissionService.getProbPermission.and.returnValue(false);

      component['getInitData']();
      tick();

      expect(component.setReminderOptionDisplayPermission).toBeFalse();
    }));
  });

  describe('Component Properties and Constants', () => {
    it('should have correct default date values', () => {
      expect(component.defaultEndDateOptions).toBe(EndDateOptions.UNLIMITED);
      expect(component.oneYearEndDateOptions).toBe(EndDateOptions.ONE_YEAR);
      expect(component.unlimitedEndDateOptions).toBe(EndDateOptions.UNLIMITED);
      expect(component.customEndDateOptions).toBe(EndDateOptions.CUSTOMDATE);
    });

    it('should have correct validity enum values', () => {
      expect(component.validityPerpetual).toBe(ValidityEnum.PERPETUAL);
      expect(component.validityOneYear).toBe(ValidityEnum.ONE_YEAR);
    });

    it('should have correct probe config types', () => {
      expect(component.featureProbeConfigType).toBeDefined();
      expect(component.presetProbeConfigType).toBeDefined();
    });

    it('should initialize default start date', () => {
      const now = Date.now();
      expect(component.defaultStartDate).toBeLessThanOrEqual(now);
      expect(component.defaultStartDate).toBeGreaterThan(now - 1000); // Within last second
    });
  });

  describe('Complex Scenarios', () => {
    it('should handle complete feature assignment workflow', fakeAsync(() => {
      component.resource = ProbDetailResource;

      // Setup initial data
      const probeDetail: ProbeDetailWithConfig = {
        id: 1,
        type: 'TestProbe',
        features: [{ id: 1, enable: false }] as ConfigBaseMappingRequest[],
        presets: [{ id: 1, enable: false }] as ConfigBaseMappingRequest[],
        reminder: false
      } as ProbeDetailWithConfig;

      probeApiService.getAsyncProbeDetailInfo.and.returnValue(Promise.resolve(probeDetail));
      probeService.getFeaturesListForUpdate.and.returnValue([
        { id: 1, enable: false, startDate: null, endDate: null, endDateUi: null }
      ] as ConfigBaseMappingRequest[]);
      probeService.getPresetsListForUpdate.and.returnValue([
        { id: 1, enable: false, startDate: null, endDate: null, endDateUi: null }
      ] as ConfigBaseMappingRequest[]);

      // Initialize component
      component['getInitData']();
      tick();

      // Enable a feature
      const mockFeature = {} as ProbeFeatureResponse;
      component.onChangeFeaturesForUpdate(1, true, Date.now(), mockFeature);

      // Enable a preset
      const mockPreset = {} as ProbePresetResponse;
      component.onChangePresetsForUpdate(1, true, Date.now(), mockPreset);

      // Update end dates
      component.onChangeFeaturesEndDateForUpdate(1, Date.now(), EndDateOptions.UNLIMITED, null);
      component.onChangePresetsEndDateForUpdate(1, Date.now(), EndDateOptions.CUSTOMDATE, new Date());

      // Submit assignment
      updateAssociationService.openUpdateAssociationModel.and.returnValue(Promise.resolve(true));
      const mockResponse = new HttpResponse<SuccessMessageResponse>({ body: { message: 'Assignment successful' } });
      probeApiService.updateProbeFeatures.and.returnValue(of(mockResponse));
      spyOn(component, 'accept');

      component.submitAssignFeature();
      tick();

      expect(component.probeObject.features[0].enable).toBeTrue();
      expect(component.probeObject.presets[0].enable).toBeTrue();
      expect(toastrService.success).toHaveBeenCalledWith('Assignment successful');
      expect(component.accept).toHaveBeenCalled();
    }));

    it('should handle assignment with download workflow', fakeAsync(() => {
      component.resource = ProbDetailResource;
      component.isDownload = true;

      // Setup probe object
      component.probeObject = new ConfigMappingRequest([], [], false);
      component.configureLicenceDetails = new ConfigureLicenceDetails(1, 'TestProbe', null);

      // Mock successful API calls
      const mockResponse = new HttpResponse<SuccessMessageResponse>({ body: { message: 'Success with download' } });
      probeApiService.updateProbeFeatures.and.returnValue(of(mockResponse));
      probeApiService.dowloadSasUriofFeatureLicenseAsync.and.returnValue(Promise.resolve());
      spyOn(component, 'accept');

      component.assignFeatureUpdate();
      tick();

      expect(probeApiService.updateProbeFeatures).toHaveBeenCalled();
      expect(probeApiService.dowloadSasUriofFeatureLicenseAsync).toHaveBeenCalledWith([1], null);
      expect(component.accept).toHaveBeenCalled();
    }));

    it('should handle error during confirmation dialog', fakeAsync(() => {
      component.resource = ProbDetailResource;
      updateAssociationService.openUpdateAssociationModel.and.returnValue(Promise.reject('Dialog error'));
      spyOn(component, 'assignFeatureUpdate');

      component.submitAssignFeature();
      tick();

      // Should not proceed with assignment on dialog error
      expect(component.assignFeatureUpdate).not.toHaveBeenCalled();
    }));
  });

  describe('Memory and Performance', () => {
    it('should properly clone objects to avoid reference issues', fakeAsync(() => {
      component.resource = ProbListResource;

      component['getInitData']();
      tick();

      // Verify that current features/presets are cloned, not referenced
      if (component.currentFeatures.length > 0) {
        expect(component.currentFeatures).not.toBe(mockProbeTypes[0].features);
      }
      if (component.currentPresets.length > 0) {
        expect(component.currentPresets).not.toBe(mockProbeTypes[0].presets);
      }
    }));
  });

  describe('Input Validation and Boundary Conditions', () => {
    it('should handle zero and negative feature IDs', () => {
      component.probeObject = new ConfigMappingRequest(
        [{ id: 0, enable: false }] as ConfigBaseMappingRequest[],
        [],
        false
      );

      const index = component.getFetauresIndex(0);
      expect(index).toBe(0);

      const negativeIndex = component.getFetauresIndex(-1);
      expect(negativeIndex).toBe(-1);
    });
  });
});

// Additional test helper functions if needed
function createMockConfigRequest(id: number, enable: boolean = false): ConfigBaseMappingRequest {
  return {
    id,
    enable,
    startDate: enable ? Date.now() : null,
    endDate: enable ? Date.now() + 31536000000 : null,
    endDateUi: enable ? EndDateOptions.ONE_YEAR : null
  } as ConfigBaseMappingRequest;
}
