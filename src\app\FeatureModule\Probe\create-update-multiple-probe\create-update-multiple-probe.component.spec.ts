import { DatePipe } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { CreateUpdateMultipleProbeComponent } from './create-update-multiple-probe.component';
import { FeaturesTextDisplayPipe } from 'src/app/shared/pipes/Probe/features-textdisplay.pipe';

describe('CreateUpdateMultipleProbeComponent', () => {
  let component: CreateUpdateMultipleProbeComponent;
  let fixture: ComponentFixture<CreateUpdateMultipleProbeComponent>;

  beforeEach(async () => {
    const toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    await TestBed.configureTestingModule({
      declarations: [CreateUpdateMultipleProbeComponent, FeaturesTextDisplayPipe],
      imports: [NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule],
      providers: [
        CommonsService,
        SessionStorageService,
        DatePipe,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(CreateUpdateMultipleProbeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
