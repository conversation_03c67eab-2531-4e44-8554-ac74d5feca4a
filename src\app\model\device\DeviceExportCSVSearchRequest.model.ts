import { DeviceSearchRequest } from "./deviceSearchRequest.model";

export class DeviceExportCSVSearchRequest {
    deviceIds: Array<number>;
    timezoneOffset: number;
    filters: DeviceSearchRequest;
    constructor($deviceIds: Array<number>, $timezoneOffset: number, $filters: DeviceSearchRequest) {
        this.deviceIds = $deviceIds;
        this.timezoneOffset = $timezoneOffset;
        this.filters = $filters;
    }
}