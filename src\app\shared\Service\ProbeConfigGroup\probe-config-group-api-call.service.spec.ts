import { HttpResponse } from '@angular/common/http';
import { HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { BASE_URL, commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { ProbeConfigGroupAddOrUpdateRequest } from 'src/app/model/ProbeConfigGroup/ProbeConfigGroupAddOrUpdateRequest.model';
import { ProbeConfigGroupDeatilResponse } from 'src/app/model/ProbeConfigGroup/ProbeConfigGroupDeatilResponse.model';
import { ProbeConfigGroupRequestBody } from 'src/app/model/ProbeConfigGroup/ProbeConfigGroupRequestBody.model';
import { ProbeConfigGroupPageResponse } from 'src/app/model/ProbeConfigGroup/probeConfigGroupPageResponse.model';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { CommonsService } from '../../util/commons.service';
import { ProbeConfigGroupApiCallService } from './probe-config-group-api-call.service';

describe('ProbeConfigGroupApiCallService', () => {
  let service: ProbeConfigGroupApiCallService;
  let httpMock: HttpTestingController;
  const mockApiUrl = BASE_URL;

  beforeEach(() => {

    TestBed.configureTestingModule({
      imports: [],
      providers: [
        ProbeConfigGroupApiCallService,
        CommonsService,
        ConfigInjectService,
        commonsProviders(null)
      ]
    });

    service = TestBed.inject(ProbeConfigGroupApiCallService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should have correct probe config group URL', () => {
    expect(service.probeConfigGroupUrl).toBe(mockApiUrl + 'api/probe-config-groups');
  });

  describe('getProbeConfigGroupList', () => {
    it('should send a POST request to search endpoint with correct parameters', () => {
      // Arrange
      const mockRequestBody: ProbeConfigGroupRequestBody = new ProbeConfigGroupRequestBody(null, null, null, null)
      const mockQueryParams = { page: 0, size: 10 };
      const mockResponse: ProbeConfigGroupPageResponse = new ProbeConfigGroupPageResponse(null, null, null, null, null, null, null, null, null, null, null)

      // Act
      let actualResponse: HttpResponse<ProbeConfigGroupPageResponse> | undefined;
      service.getProbeConfigGroupList(mockRequestBody, mockQueryParams)
        .subscribe(response => {
          actualResponse = response;
        });

      // Assert
      const req = httpMock.expectOne(request => {
        return request.url === `${service.probeConfigGroupUrl}/search` &&
          request.method === 'POST';
      });
      expect(req.request.body).toEqual(mockRequestBody);
      expect(req.request.params.get('page')).toBe('0');
      expect(req.request.params.get('size')).toBe('10');

      req.flush(mockResponse, {
        status: 200,
        statusText: 'OK',
        headers: { 'Content-Type': 'application/json' }
      });

      expect(actualResponse?.body).toEqual(mockResponse);
      expect(actualResponse?.status).toBe(200);
    });
  });

  describe('getProbeConfigGroupDetails', () => {
    it('should send a GET request to the correct endpoint with the given id', () => {
      // Arrange
      const mockId = 1;
      const mockResponse: ProbeConfigGroupDeatilResponse = new ProbeConfigGroupDeatilResponse(null, null, null, null, null, null, null, null)

      // Act
      let actualResponse: HttpResponse<ProbeConfigGroupDeatilResponse> | undefined;
      service.getProbeConfigGroupDetails(mockId)
        .subscribe(response => {
          actualResponse = response;
        });

      // Assert
      const req = httpMock.expectOne(`${service.probeConfigGroupUrl}/${mockId}`);
      expect(req.request.method).toBe('GET');

      req.flush(mockResponse, {
        status: 200,
        statusText: 'OK',
        headers: { 'Content-Type': 'application/json' }
      });

      expect(actualResponse?.body).toEqual(mockResponse);
      expect(actualResponse?.status).toBe(200);
    });
  });

  describe('deleteProbeConfigGroup', () => {
    it('should send a DELETE request to the correct endpoint with the given ids', () => {
      // Arrange
      const mockIds = [1, 2, 3];
      const mockResponse: SuccessMessageResponse = new SuccessMessageResponse('Successfully deleted')

      // Act
      let actualResponse: HttpResponse<SuccessMessageResponse> | undefined;
      service.deleteProbeConfigGroup(mockIds)
        .subscribe(response => {
          actualResponse = response;
        });

      // Assert
      const req = httpMock.expectOne(`${service.probeConfigGroupUrl}/${mockIds}`);
      expect(req.request.method).toBe('DELETE');

      req.flush(mockResponse, {
        status: 200,
        statusText: 'OK',
        headers: { 'Content-Type': 'application/json' }
      });

      expect(actualResponse?.body).toEqual(mockResponse);
      expect(actualResponse?.status).toBe(200);
    });
  });

  describe('addOrUpdateProbeConfigGroup', () => {
    it('should send a POST request to the correct endpoint with the given request body', () => {
      // Arrange
      const mockRequest: ProbeConfigGroupAddOrUpdateRequest = new ProbeConfigGroupAddOrUpdateRequest(null, null, null, null, null)
      const mockResponse: SuccessMessageResponse = new SuccessMessageResponse('Successfully saved')

      // Act
      let actualResponse: HttpResponse<SuccessMessageResponse> | undefined;
      service.addOrUpdateProbeConfigGroup(mockRequest)
        .subscribe(response => {
          actualResponse = response;
        });

      // Assert
      const req = httpMock.expectOne(service.probeConfigGroupUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(mockRequest);

      req.flush(mockResponse, {
        status: 200,
        statusText: 'OK',
        headers: { 'Content-Type': 'application/json' }
      });

      expect(actualResponse?.body).toEqual(mockResponse);
      expect(actualResponse?.status).toBe(200);
    });
  });

  describe('error handling', () => {
    it('should handle errors through CommonsService.handleError', () => {
      // Arrange
      const mockId = 1;
      const errorResponse = {
        status: 404,
        statusText: 'Not Found'
      };
      const spy = spyOn(TestBed.inject(CommonsService), 'handleError').and.callThrough();

      // Act
      service.getProbeConfigGroupDetails(mockId).subscribe({
        next: () => fail('Expected an error, not success'),
        error: error => {
          // Assert that error handling works
          expect(error).toBeTruthy();
          expect(spy).toHaveBeenCalled();
        }
      });

      // Assert
      const req = httpMock.expectOne(`${service.probeConfigGroupUrl}/${mockId}`);
      req.flush('Not Found', errorResponse);
    });
  });
});