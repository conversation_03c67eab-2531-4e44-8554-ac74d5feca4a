import { OTSKitManagementProbeResponse } from "./OTSKitManagementProbeResponse.model";

export class OTSKitManagementDetailResponse {
    id: number;
    country: string;
    revVersion: string;
    otsKitPartNumberCode: string;
    description: string;
    modifiedDate: number;
    probes: Array<OTSKitManagementProbeResponse>


    constructor($id: number, $country: string, $otsKitPartNumberCode: string, $description: string, $modifiedDate: number, $probes: Array<OTSKitManagementProbeResponse>) {
        this.id = $id;
        this.country = $country;
        this.otsKitPartNumberCode = $otsKitPartNumberCode;
        this.description = $description;
        this.modifiedDate = $modifiedDate;
        this.probes = $probes;

    }

}