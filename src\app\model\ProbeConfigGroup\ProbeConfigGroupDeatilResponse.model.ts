import { FeatureResponse } from "./FeatureResponse.model";
import { PresetResponse } from "./PresetResponse.model";
import { ProbeConfigGroupListResponse } from "./ProbeConfigGroupListResponse.model";

export class ProbeConfigGroupDeatilResponse extends ProbeConfigGroupListResponse {
    createdDate: number;

    constructor(//NOSONAR
        id: number,
        partNumberCode: string,
        probeType: string,
        features: Array<FeatureResponse>,
        presets: Array<PresetResponse>,
        description: string,
        modifiedDate: number,
        createdDate: number
    ) {
        super(
            id,
            partNumberCode,
            probeType,
            features,
            presets,
            description,
            modifiedDate
        );
        this.createdDate = createdDate;
    }
}