{"name": "echonous-angular", "version": "4.2.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production --aot --output-hashing=all", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": false, "publishConfig": {"registry": "https://rdmnexus.kosmosup.com/repository/rdm-ui/"}, "dependencies": {"@angular/animations": "^18.2.13", "@angular/common": "^18.2.13", "@angular/compiler": "^18.2.13", "@angular/core": "^18.2.13", "@angular/forms": "^18.2.13", "@angular/localize": "^18.2.13", "@angular/material": "^18.2.14", "@angular/platform-browser": "^18.2.13", "@angular/platform-browser-dynamic": "^18.2.13", "@angular/router": "^18.2.13", "@fortawesome/angular-fontawesome": "^0.15.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@ng-bootstrap/ng-bootstrap": "^17.0.1", "bootstrap": "^4.3.1", "bootstrap-toggle": "^2.2.2", "core-js": "^3.39.0", "font-awesome-icons": "^1.6.0", "hammerjs": "^2.0.8", "is-what": "^4.1.16", "jquery": "^3.7.1", "jszip": "^3.10.1", "ng-multiselect-dropdown": "^1.0.0", "ngx-cookie": "^6.0.1", "ngx-toastr": "^19.0.0", "ngx-webstorage": "^18.0.0", "rxjs": "^7.8.1", "tslib": "^2.8.1", "zone.js": "^0.14.4"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.12", "@angular/cli": "^18.2.12", "@angular/compiler-cli": "^18.2.13", "@types/jasmine": "^5.1.5", "jasmine-core": "^5.5.0", "karma": "^6.4.4", "karma-chrome-launcher": "^3.2.0", "karma-coverage": "^2.2.1", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "typescript": "~5.5.4"}}