import { ListingPageReloadSubjectParameter } from "../common/listingPageReloadSubjectParameter.model";
import { ProbeListFilterRequestBody } from "./ProbeListFilterRequestBody.model";

/**
* Probe Filter Action model for communication between filter and listing components
*/
export class ProbeFilterAction {
    listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter;
    probeListFilterRequestBody: ProbeListFilterRequestBody;

    constructor(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, probeListFilterRequestBody: ProbeListFilterRequestBody) {
        this.listingPageReloadSubjectParameter = listingPageReloadSubjectParameter;
        this.probeListFilterRequestBody = probeListFilterRequestBody;
    }
}