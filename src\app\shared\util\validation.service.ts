import { Injectable } from '@angular/core';
import { AbstractControl, FormControl, ValidationErrors, ValidatorFn } from '@angular/forms';
import { isNullOrUndefined } from 'is-what';
import { Subscription } from 'rxjs';
import { Find_Letter_Pattern, REPLACE_COMMA_STRING, SERACH_REG_EXP_FOR_COMMA_PATTERN } from 'src/app/app.constants';
import { ProductStatusEnum } from '../enum/Common/ProductStatus.enum';
import { ToastrService } from 'ngx-toastr';

@Injectable({
  providedIn: 'root'
})
export class ValidationService {

  constructor(private toste: ToastrService) { }

  /**
   * matches same strung in another control or not
   * @param otherControlName
   */
  public matchOtherValidator(otherControlName: string): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } => {
      const otherControl: AbstractControl = control.root.get(otherControlName);

      if (otherControl) {
        const subscription: Subscription = otherControl
          .valueChanges
          .subscribe(() => {
            control.updateValueAndValidity();
            subscription.unsubscribe();
          });
      }

      return (otherControl && control.value !== otherControl.value) ? { match: true } : null;
    };
  }

  /**
   * Remove Spaces
   * 
   * @param control 
   * @returns 
   */
  public removeSpaces(control: FormControl): FormControl {
    if (control && control.value && (typeof control.value == "string" && !control.value.replace(/\s/g, '').length)) {
      control.setValue('');
    }
    return null;
  }

  /**
   * Serial number Form Control validate
   * 
   * <AUTHOR>
   *  
   * @param control 
   * @returns 
   */
  public serialNumberForminputValidate(control: FormControl): boolean {
    return !isNullOrUndefined(control) &&
      !isNullOrUndefined(control.value) &&
      (typeof control.value == "string" && control.value.replace(/\s/g, '').length > 0)
  }

  /**
   * Serial Number Start with find and validate and throw error with message
   * 
   * @param startWithListFilter 
   * @param finalValue 
   * @param startWithList 
   * @returns 
   */
  public getSerialNumberStartWith(startWithListFilter: string[], finalValue: string, startWithList: string[]): any[] {
    let startWith = null;
    let message = null;
    //Find Start With 
    if (startWithListFilter.length == 1) {
      startWith = startWithListFilter[0];
    } else if (startWithListFilter.length > 1) {
      startWith = startWithListFilter[startWithListFilter.length - 1];
    }
    //Validate serial number
    let isValid = startWith == null ? false : finalValue.startsWith(startWith);
    if (!isValid) {
      let startWithListMessage = startWithList.toString().replace(SERACH_REG_EXP_FOR_COMMA_PATTERN, REPLACE_COMMA_STRING);
      let postFixMessage = isNullOrUndefined(startWith) ? startWithListMessage : startWith;
      message = "Serial No should be start with " + postFixMessage;
    }
    return [isValid, message];
  }

  /**
   * serial Number Validation Start with T1,T3,L1 and L1A 
   * 
   * <AUTHOR>
   * 
   * @param componentVariable 
   * @returns 
   */
  public serialNumberStartWithValidate(componentVariable: any): ValidatorFn {
    return function validate(control: FormControl) {
      if (componentVariable.validationService.serialNumberForminputValidate(control)) {
        let controlValue: any = control.value;
        let index = controlValue.indexOf(controlValue.match(Find_Letter_Pattern));
        let finalValue = controlValue.substr(index);
        let startWithList = isNullOrUndefined(componentVariable.probeTypePrefixList) ? [] : componentVariable.probeTypePrefixList;
        let isValid = false;
        let message = null;
        if (!isNullOrUndefined(finalValue)) {
          let startWithListFilter = startWithList.filter(probeTypeObj => finalValue.toUpperCase().includes(probeTypeObj));
          //1st value is valid/Invalid
          //2nd value is message
          let validateSerialNumber = componentVariable.validationService.getSerialNumberStartWith(startWithListFilter, finalValue, startWithList);
          isValid = validateSerialNumber[0];
          message = validateSerialNumber[1];
        }
        if (!isValid) {
          return { serialNumberStartWith: true, message: message };
        }
      }
      return null;
    };
  }

  /**
   * serial Number Validation Start with T1,T3,L1 and L1A 
   * 
   * <AUTHOR>
   * 
   * @param startWith 
   * @returns 
   */
  public serialNumberStartWithValidateInSalesOrder(startWith: string): ValidatorFn {
    return function validate(control: FormControl) {
      if (!isNullOrUndefined(control) && !isNullOrUndefined(control.value) && (typeof control.value == "string" && control.value.replace(/\s/g, '').length > 0)) {
        let controlValue: any = control.value;
        if (!isNullOrUndefined(controlValue)) {
          let index = controlValue.indexOf(controlValue.match(Find_Letter_Pattern));
          let finalValue = controlValue.substr(index);
          let isValid = false;
          if (!isNullOrUndefined(finalValue) && !isNullOrUndefined(startWith)) {
            isValid = finalValue.startsWith(startWith.toUpperCase());
          }
          if (!isValid) {
            let message = "Serial No should be start with " + startWith.toUpperCase();
            return { serialNumberStartWith: true, message: message };
          }
        }
      }
      return null;
    };

  }

  /**
   * Input Box contain space then throw error
   * 
   * <AUTHOR>
   * @param control 
   * @returns 
   */
  public cannotContainSpace(control: AbstractControl): ValidationErrors | null {
    if (!isNullOrUndefined(control.value) && (control.value as string).indexOf(' ') >= 0) {
      return { cannotContainSpace: true }
    }

    return null;
  }

  /**
   * Validate RMA Action
   * 
   * <AUTHOR>
   * @param currentStatus 
   * @returns 
   */
  public validateProductStatusForRMAAction(currentStatus: string): boolean {
    let rmaActionAllowed: Array<string> = [ProductStatusEnum.ENABLED, ProductStatusEnum.RMA];
    return rmaActionAllowed.includes(currentStatus);
  }
}