import { ValidityEnum } from "src/app/shared/enum/ValidityEnum.enum";
import { BaseResponse } from "../common/BaseResponse.model";

export class PresetResponse extends BaseResponse {
    partNumberCode: string;
    validity: ValidityEnum;

    constructor(id: number, name: string, displayName: string, partNumberCode: string, validity: ValidityEnum) {
        super(id, name, displayName);
        this.partNumberCode = partNumberCode;
        this.validity = validity;
    }
}