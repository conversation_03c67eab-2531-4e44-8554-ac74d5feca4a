import { Injectable } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService } from 'ngx-webstorage';
import { DEVICE_EDIT_DISABLE_NO_ACTION_ALLOWED_MESSAGE, DeviceDetailResource, DeviceListResource, MODULE_USER_WITH_NO_COUNTRY_MESSAGE, MODULE_VALIDATE_WITH_USER_COUNTRY_INVALID_DEVICE, MODULE_VALIDATE_WITH_USER_COUNTRY_INVALID_PROBE, MODULE_VALIDATE_WITH_USER_COUNTRY_INVALID_SOFTWARE_BUILD, PROBE_EDIT_DISABLE_NO_ACTION_ALLOWED_MESSAGE, ProbDetailResource, ProbListResource, RdmUserAssignedCountries, SoftwareBuildListResource } from 'src/app/app.constants';
import { ValidationResponse } from 'src/app/model/common/ValidationResponse.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';

@Injectable({
  providedIn: 'root'
})
export class ModuleValidationServiceService {

  constructor(
    private $localStorage: LocalStorageService,
    private toste: ToastrService) { }

  /**
   * User Associated Countries
   * 
   * <AUTHOR>
   * @returns 
   */
  private filterOutUserAssociatedCountries(): Array<string> {
    let rdmUserAssociatedCountries: Array<CountryListResponse> = this.$localStorage.retrieve(RdmUserAssignedCountries);
    if (isNullOrUndefined(rdmUserAssociatedCountries) || rdmUserAssociatedCountries.length == 0) {
      return [];
    } else {
      return rdmUserAssociatedCountries?.map(country => country.country);
    }
  }

  /**
   * Resource Validate With User Country
   * 
   * <AUTHOR>
   * @param rdmUserAssociatedCountries 
   * @param moduleCountryList 
   * @param moduleName
   * @returns 
   */
  private resourceValidateWithUserCountryForMultileRecord(rdmUserAssociatedCountries: Array<string>, moduleCountryList: Array<string>, moduleName: string): ValidationResponse {
    if (rdmUserAssociatedCountries.length == 0) {
      return new ValidationResponse(false, MODULE_USER_WITH_NO_COUNTRY_MESSAGE);
    }
    if (isNullOrUndefined(moduleCountryList) || moduleCountryList.includes(null)) {
      return new ValidationResponse(true, null);
    }
    let filterCountry = moduleCountryList.filter(moduleCountry => !rdmUserAssociatedCountries.includes(moduleCountry));
    return new ValidationResponse((filterCountry.length == 0), this.getModuleCountryValidateWithUserContryInvalidErrorMessage(moduleName));
  }

  /**
   * Resource Validate With User Country
   * 
   * <AUTHOR>
   * @param rdmUserAssociatedCountries 
   * @param moduleCountry 
   * @param moduleName
   * @returns 
   */
  private resourceValidateWithUserCountryForSingleRecord(rdmUserAssociatedCountries: Array<string>, moduleCountry: string, moduleName: string): ValidationResponse {
    if (rdmUserAssociatedCountries.length == 0) {
      return new ValidationResponse(false, MODULE_USER_WITH_NO_COUNTRY_MESSAGE);
    }
    if (isNullOrUndefined(moduleCountry)) {
      return new ValidationResponse(true, null);
    }
    let deviceAssociatedFilteredCountry = rdmUserAssociatedCountries.filter(u_Countries => u_Countries == moduleCountry);
    return new ValidationResponse((deviceAssociatedFilteredCountry.length > 0), this.getModuleCountryValidateWithUserContryInvalidErrorMessage(moduleName));
  }

  /**
   * Validate With User Country
   * 
   * <AUTHOR>
   * @param moduleCountry 
   * @param moduleName 
   * @param isAlertDisplay
   * @returns 
   */
  public validateWithUserCountryForSingleRecord(moduleCountry: string, moduleName: string, isAlertDisplay: boolean): boolean {
    let rdmUserAssociatedCountries = this.filterOutUserAssociatedCountries();
    let validationResponse: ValidationResponse = this.resourceValidateWithUserCountryForSingleRecord(rdmUserAssociatedCountries, moduleCountry, moduleName);
    if (!validationResponse.isValid && isAlertDisplay) {
      this.toste.info(validationResponse.errorMessage);
    }
    return validationResponse.isValid;
  }

  /**
   * Validate With User Country
   * 
   * <AUTHOR>
   * @param moduleCountry 
   * @param moduleName 
   * @param isAlertDisplay
   * @returns 
   */
  public validateWithUserCountryForMultileRecord(moduleCountry: Array<string>, moduleName: string, isAlertDisplay: boolean): boolean {
    let rdmUserAssociatedCountries = this.filterOutUserAssociatedCountries();
    let validationResponse: ValidationResponse = this.resourceValidateWithUserCountryForMultileRecord(rdmUserAssociatedCountries, moduleCountry, moduleName);
    if (!validationResponse.isValid && isAlertDisplay) {
      this.toste.info(validationResponse.errorMessage, '', {
        timeOut: 10000
      });
    }
    return validationResponse.isValid;
  }

  /**
   * Edit Status Validation
   * 
   * <AUTHOR>
   * @param moduleEditState 
   * @param moduleName 
   * @returns 
   */
  public validateWithEditStateForSingleRecord(moduleEditState: boolean, moduleName: string): boolean {
    if (!moduleEditState) {
      this.toste.info(this.getEditValidationMessage(moduleName));
      return false;
    }
    return true;
  }

  /**
  * Edit Status Validation
  * 
  * @param moduleEditState 
  * @param moduleName 
  * @returns 
  */
  public validateWithEditableWithMultipalRecoard(moduleEditState: Array<boolean>, moduleName: string): boolean {
    if (moduleEditState.includes(false)) {
      this.toste.info(this.getEditValidationMessage(moduleName), '', {
        timeOut: 10000
      });
      return false;
    }
    return true;
  }

  /**
 * Get Edit Validation message
 * 
 * <AUTHOR>
 * @param moduleName 
 * @returns 
 */
  private getEditValidationMessage(moduleName: string): string {
    if (moduleName == ProbListResource || moduleName == ProbDetailResource) {
      return PROBE_EDIT_DISABLE_NO_ACTION_ALLOWED_MESSAGE;
    } else if (moduleName == DeviceListResource || moduleName == DeviceDetailResource) {
      return DEVICE_EDIT_DISABLE_NO_ACTION_ALLOWED_MESSAGE;
    }
    return null;
  }

  /**
   * Get Module Country Validate With User Country Invalid Error Message
   * 
   * <AUTHOR>
   * @param moduleName 
   * @returns 
   */
  private getModuleCountryValidateWithUserContryInvalidErrorMessage(moduleName: string): string {
    if (moduleName == ProbListResource || moduleName == ProbDetailResource) {
      return MODULE_VALIDATE_WITH_USER_COUNTRY_INVALID_PROBE;
    } else if (moduleName == DeviceListResource || moduleName == DeviceDetailResource) {
      return MODULE_VALIDATE_WITH_USER_COUNTRY_INVALID_DEVICE;
    } else if (moduleName == SoftwareBuildListResource) {
      return MODULE_VALIDATE_WITH_USER_COUNTRY_INVALID_SOFTWARE_BUILD;
    }
    return null;
  }
}
