import { HttpErrorResponse, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import { UserListPageResponse } from '../../../model/User/UserListPageResponse.model';
import { UserResponse } from '../../../model/User/UserResponse.model';
import { SuccessMessageResponse } from '../../../model/common/SuccessMessageResponse.model';
import { Member } from '../../../model/memberAdd.model';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { UserApiCallService } from './user-api-call.service';
import { CommonsService } from '../../util/commons.service';
import { UserSearchRequest } from '../../../model/User/userSearchRequest';

describe('UserApiCallService', () => {
    let service: UserApiCallService;
    let httpMock: HttpTestingController;
    let configInjectService: jasmine.SpyObj<ConfigInjectService>;
    let commonsService: jasmine.SpyObj<CommonsService>;
    let exceptionService: jasmine.SpyObj<ExceptionHandlingService>;

    const mockApiUrl = 'http://localhost:8080/api/user';

    beforeEach(() => {
        const configInjectServiceSpy = jasmine.createSpyObj('ConfigInjectService', ['getServerApiUrl']);
        configInjectServiceSpy.getServerApiUrl.and.returnValue('http://localhost:8080/');

        TestBed.configureTestingModule({
            imports: [],
            providers: [
                UserApiCallService,
                { provide: ConfigInjectService, useValue: configInjectServiceSpy },
                { provide: CommonsService, useValue: jasmine.createSpyObj('CommonsService', ['handleError']) },
                { provide: ExceptionHandlingService, useValue: jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']) },
                provideHttpClient(withInterceptorsFromDi()),
                provideHttpClientTesting()
            ]
        });

        // now inject AFTER spy has been configured
        commonsService = TestBed.inject(CommonsService) as jasmine.SpyObj<CommonsService>;
        exceptionService = TestBed.inject(ExceptionHandlingService) as jasmine.SpyObj<ExceptionHandlingService>;
        service = TestBed.inject(UserApiCallService);
        httpMock = TestBed.inject(HttpTestingController);

        commonsService.handleError.and.returnValue(throwError(() => new Error('Error')));
    });

    afterEach(() => {
        httpMock.verify();
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should call getMemberList', () => {
        const mockResponse = {} as UserListPageResponse;
        const userSearchRequest = {} as UserSearchRequest;

        service.getMemberList(userSearchRequest, { page: 0 }).subscribe();

        const req = httpMock.expectOne(`${mockApiUrl}/search?page=0`);
        expect(req.request.method).toBe('POST');
        req.flush(mockResponse);
    });

    it('should call saveMember', () => {
        const mockMember: Member = null;
        const mockResponse: UserResponse = null;

        service.saveMember(mockMember).subscribe();

        const req = httpMock.expectOne(mockApiUrl);
        expect(req.request.method).toBe('POST');
        req.flush(mockResponse);
    });

    it('should call updateMember', () => {
        const mockMember: Member = null;
        const mockResponse: UserResponse = null;

        service.updateMember(mockMember, 1).subscribe();

        const req = httpMock.expectOne(`${mockApiUrl}/1`);
        expect(req.request.method).toBe('PUT');
        req.flush(mockResponse);
    });

    it('should call getSingleMember', () => {
        const mockResponse: UserResponse = null;

        service.getSingleMember(2).subscribe();

        const req = httpMock.expectOne(`${mockApiUrl}/2`);
        expect(req.request.method).toBe('GET');
        req.flush(mockResponse);
    });

    it('should call deleteMultipleMember', () => {
        const mockResponse: SuccessMessageResponse = null;
        const ids = [1, 2, 3];

        service.deleteMultipleMember(ids).subscribe();

        const req = httpMock.expectOne(`${mockApiUrl}/${ids}`);
        expect(req.request.method).toBe('DELETE');
        req.flush(mockResponse);
    });

    it('should return true for successful logOutMember', async () => {
        const mockResponse: SuccessMessageResponse = null;

        service['http'].put = jasmine.createSpy().and.returnValue(of({ body: mockResponse }));

        const result = await service.logOutMember();

        expect(result).toBeTrue();
    });

    it('should return false and call customErrorMessage on logOutMember error', async () => {
        const errorResponse = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });

        service['http'].put = jasmine.createSpy().and.returnValue(throwError(() => errorResponse));

        const result = await service.logOutMember();

        expect(result).toBeFalse();
        expect(exceptionService.customErrorMessage).toHaveBeenCalledWith(errorResponse);
    });
});
