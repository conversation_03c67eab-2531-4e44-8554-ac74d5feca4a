export class KitManagemantSearchRequestBody {
    kitPartNumber: string;
    bridgePartNumber: string;
    countryIds: number[];
    languageIds: number[];
    videoVersion: string;
    softWareVersion: string;

    constructor($kitPartNumber: string, $bridgePartNumber: string, $countryIds: number[], $languageIds: number[],
        $videoVersion: string, $softWareVersion: string) {
        this.kitPartNumber = $kitPartNumber;
        this.bridgePartNumber = $bridgePartNumber;
        this.countryIds = $countryIds;
        this.languageIds = $languageIds;
        this.videoVersion = $videoVersion;
        this.softWareVersion = $softWareVersion;
    }
}