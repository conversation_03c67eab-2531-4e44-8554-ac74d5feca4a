import { HttpClient, HttpResponse } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { createRequestOption } from '../../util/request-util';
import { DeviceLogPageResponse } from '../../../model/Logs/DeviceLogPageResponse.model';
import { DeviceLogsSearchRequest } from '../../../model/Logs/DeviceLogsSearchRequest.model';
import { AuthJwtService } from '../../auth-jwt.service';
import { API_BASE_URL } from '../../config';

@Injectable({
  providedIn: 'root'
})
export class DeviceLogApiCallService {
  public resourceUrl = this.SERVER_API_URL + 'api/log';

  constructor(protected http: HttpClient, @Inject(API_BASE_URL) public SERVER_API_URL: string, private authServerProvider: AuthJwtService) { }

  getLogDetail(logsSearchRequest: DeviceLogsSearchRequest, req?: any): Observable<HttpResponse<DeviceLogPageResponse>> {
    const options = createRequestOption(req);
    return this.http.post<DeviceLogPageResponse>(this.resourceUrl, logsSearchRequest, { params: options, observe: 'response' });
  }

  getLogFileUrl(fileName) {
    return this.http.post(this.resourceUrl + "/download/url", fileName, { observe: 'response' })
  }

  getLogTypes() {
    return this.http.get(this.resourceUrl + "/logtypes", { observe: 'response' });
  }

  downloadMyFile(attachmentUrl: string, filename: string) {

    const link = document.createElement('a');
    link.setAttribute('target', '_blank');
    link.setAttribute('download', filename);
    link.setAttribute('name', filename);
    link.setAttribute('href', this.SERVER_API_URL + 'api/' + attachmentUrl);
    document.body.appendChild(link);
    link.click();
    link.remove();
  }

  getToken() {
    return '&Authorization=' + "Bearer " + this.authServerProvider.getToken();
  }
}
