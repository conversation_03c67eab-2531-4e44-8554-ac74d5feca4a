import { Component } from '@angular/core';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { PermissionService } from 'src/app/shared/permission.service';

@Component({
  selector: 'app-kit-management-module',
  templateUrl: './kit-management-module.component.html',
  styleUrl: './kit-management-module.component.css'
})
export class KitManagementModuleComponent {

  //permissions
  otsKitDisplayPermissions: boolean = false;
  bridgeKitDisplayPermissions: boolean = false;

  bridgeWorldListDisplay: boolean = false;
  otsWorldListDisplay: boolean = false;

  constructor(
    private authservice: AuthJwtService,
    private permissionService: PermissionService) {
  }

  public ngOnInit(): void {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.bridgeKitDisplayPermissions = this.permissionService.getKitManagementPermission(PermissionAction.GET_BRIDGE_KIT_MANAGEMENT_ACTION);
      this.otsKitDisplayPermissions = this.permissionService.getKitManagementPermission(PermissionAction.GET_OTS_KIT_MANAGEMENT_ACTION);
      this.setDefalutPageDisplay();
    }
  }

  public setDefalutPageDisplay(): void {
    if (this.bridgeKitDisplayPermissions) {
      this.showBridgeWorldListDisplay();
    } else {
      this.showOtsWorldListDisplay();
    }
  }

  public showBridgeWorldListDisplay(): void {
    this.hideShowListingPage(true, false);
  }
  public showOtsWorldListDisplay(): void {
    this.hideShowListingPage(false, true);
  }
  private hideShowListingPage(bridgeWorldListDisplay: boolean, otsWorldListDisplay: boolean): void {
    this.bridgeWorldListDisplay = bridgeWorldListDisplay;
    this.otsWorldListDisplay = otsWorldListDisplay;
  }

}
