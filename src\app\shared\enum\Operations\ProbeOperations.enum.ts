export enum ProbeOperationsEnum {
    Probe_Operations = "Probe Operations",
    EDIT_ENABLE_PROBE = "Mark Probe as Editable",
    EDIT_DISABLE_PROBE = "Mark Probe as Read-Only",
    UNLOCK_PROBES = "Unlock Probe(s)",
    LOCK_PROBES = "Lock Probe(s)",
    ASSIGN_FEATURES_TO_PROBE = "Configure License",
    CUSTOMER_ASSOCIATION = "Customer / Sales Order Association",
    DOWNLOAD_PROBES = "Download License",
    DELETE_PROBES = "Delete Probe(s)",
    DISABLED_PROBE = "Update Probe as Disable",
    RMA_PROBE = "Update Probe as RMA",
    UPDATE_PROBE_TYPE = "Update Probe Type(s)",
    EXPORT_HISTORICAL_CONNECTIONS = "Export Historical Connections",
    Export_CSV = "Export CSV",
    TRANSFER_PROBE = "Transfer Product",
}