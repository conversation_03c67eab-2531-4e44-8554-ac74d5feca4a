<!------------------------------------------->
<!------------------------------------------->
<!-- loading start -->
<!-- loading gif start -->
<div class="ringLoading" *ngIf="loading">
  <div class="ringLoadingDiv">
    <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
  </div>
</div>
<!-- loading gif end -->
<!-- loading end -->
<!------------------------------------------->
<!------------------------------------------->

<body class="bg-white">
  <div class="container-fluid " id="addProbeFeature">
    <div class="row">
      <div class="col-md-12">
        <!---------######################################------------->
        <!------------------------------------------------------------>
        <!----------1 Add Probe Title--------------------------------->
        <!----------2 Back button ------------------------------------>
        <!------------------------------------------------------------>
        <!---------######################################------------->
        <div class="row">
          <label class="col-md-6 h5-tag">Add Probe</label>
          <div class="ml-auto col-md-6 text-right mb-3">
            <div class="row">
              <div class="ml-auto col-md-2 text-right">
                <button class="btn btn-sm btn-outline-secondary" (click)="back()"><i class="fa fa-reply"
                    aria-hidden="true"></i>&nbsp;&nbsp;Back</button>
              </div>
            </div>
          </div>
        </div>
        <!---------#####################################------------->
        <!------------------------------------------->
        <!------------------------------------------->
        <!------------------------------------------->
        <!---------#####################################------------->
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-body">
                <div class="card shadow">
                  <div class="card-body">
                    <!---------######################################------------->
                    <!------------------------------------------->
                    <!------------------------------------------->
                    <!--Sales Order Number--->
                    <!--Customer Name--->
                    <!------------------------------------------->
                    <!------------------------------------------->
                    <!---------######################################------------->
                    <form [formGroup]="probeDetailForm">
                      <div class="row">
                        <div class="col-lg-4 col-md-6 col-sm-12">
                          <div class="form-group">
                            <div>
                              <label><strong class="">Sales Order Number</strong></label>
                            </div>
                            <div>
                              <ng-multiselect-dropdown class="devicePageDeviceType sales-order-control"
                                id="salesOrderNumberField" name="salesOrderNumber" [placeholder]="''"
                                (click)="onItemClickValidation('salesOrderNumber')"
                                (onSelect)="onDropdownSelect($event)" (onDeSelect)="onDropdownDeSelect()"
                                formControlName="salesOrderNumber" [disabled]="isSalesOrderDisabled"
                                [settings]="salesOrderDropdownSettings" [data]="salesOrderNumberList">
                              </ng-multiselect-dropdown>

                              <button class="btn btn-sm btn-orange radius-50 ml-2" (click)="openManualSalesOrderField()"
                                id="addSalesOrderBtn">
                                <em class="fa fa-plus"></em>
                              </button>

                              <!------------------------------------------->
                              <!-------------------------------------------->
                              <!---------Manual Sales Order TextBox--------->
                              <!-------------------------------------------->
                              <!------------------------------------------->
                              <div id="manualSalesOrder">
                                <div class="manual-sales-order-control" id="salesOrderField">
                                  <div class="form-group">
                                    <div>
                                      <input tabindex="-1" class="form-control sales-order-control mt-2" type="text"
                                        formControlName="manualSalesOrderNumber" />
                                      <button class="btn btn-sm btn-orange radius-50 close-field ml-2 mt-2"
                                        id="closeManualSalesOrderBtn" (click)="closeManualSalesOrderField()">
                                        <em class="fa fa-close"></em>
                                      </button>
                                    </div>
                                  </div>
                                  <div class="mt-2"
                                    *ngIf="(probeDetailForm.get('manualSalesOrderNumber').touched || probeDetailForm.get('manualSalesOrderNumber').dirty) && probeDetailForm.get('manualSalesOrderNumber').invalid">
                                    <span
                                      *ngIf="(probeDetailForm.get('manualSalesOrderNumber').errors && probeDetailForm.get('manualSalesOrderNumber').errors['required'])"
                                      class="alert-color">{{enterSalesOrderNumber}}</span>
                                    <div *ngIf="probeDetailForm.get('manualSalesOrderNumber').errors['maxlength']"
                                      class="alert-color">
                                      {{small_maxCharactersAllowedMessage}}</div>
                                    <div *ngIf="probeDetailForm.get('manualSalesOrderNumber').errors['pattern']"
                                      class="alert-color">
                                      {{specialCharacterErrorMessage}}</div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div class="mt-1"
                              *ngIf="(probeDetailForm.get('salesOrderNumber').touched || probeDetailForm.get('salesOrderNumber').dirty) && probeDetailForm.get('salesOrderNumber').invalid">
                              <span
                                *ngIf="(probeDetailForm.get('salesOrderNumber').errors && probeDetailForm.get('salesOrderNumber').errors['required'])"
                                class="alert-color">{{enterSalesOrderNumber}}</span>
                            </div>
                          </div>
                        </div>

                        <div class="col-lg-4 col-md-6 col-sm-12">
                          <div class="form-group">
                            <label><strong class="">Customer name</strong></label>
                            <input type="text" class="form-control" name="customerName" formControlName="customerName"
                              [readOnly]="isDisabled">
                            <div
                              *ngIf="(probeDetailForm.get('customerName').touched || probeDetailForm.get('customerName').dirty) && probeDetailForm.get('customerName').invalid">
                              <span
                                *ngIf="(probeDetailForm.get('customerName').errors && probeDetailForm.get('customerName').errors['required'])"
                                class="alert-color">{{enterCustomerName}}</span>
                              <div *ngIf="probeDetailForm.get('customerName').errors['maxlength']" class="alert-color">
                                {{maxCharactersAllowedMessage}}</div>
                              <div *ngIf="probeDetailForm.get('customerName').errors['pattern']" class="alert-color">
                                {{specialCharacterErrorMessage}}</div>
                            </div>
                          </div>
                        </div>

                        <!-- countries form field start -->
                        <div class="col-lg-4 col-md-6 col-sm-12 ">
                          <label class="form-control-label" for="field_countries"
                            id="label_countries"><strong>Country</strong></label>
                          <ng-multiselect-dropdown id="field_countries" name="countries" [placeholder]="''"
                            formControlName="countries" [settings]="countrySetting" [data]="countriesList"
                            [disabled]="isDisabled">
                          </ng-multiselect-dropdown>
                          <!-- countries form field end -->
                          <div
                            *ngIf="(probeDetailForm.get('countries').touched || probeDetailForm.get('countries').dirty) && probeDetailForm.get('countries').invalid ">
                            <div *ngIf="probeDetailForm.get('countries').errors['required']">
                              <span class="alert-color mb-3">{{countryValidMessage}}</span>
                            </div>
                          </div>
                        </div>

                      </div>
                      <div class="row">
                        <div class="col-lg-4 col-md-6 col-sm-12 ">
                          <div class="form-group">
                            <label><strong class="">PO#</strong></label>
                            <input type="text" class="form-control ponumber" name="poNumber" formControlName="poNumber"
                              [readOnly]="isDisabled">
                            <div
                              *ngIf="(probeDetailForm.get('poNumber').touched || probeDetailForm.get('poNumber').dirty) && probeDetailForm.get('poNumber').invalid">
                              <div *ngIf="probeDetailForm.get('poNumber').errors['maxlength']" class="alert-color">
                                {{maxCharactersAllowedMessage}}</div>
                              <div *ngIf="probeDetailForm.get('poNumber').errors['pattern']" class="alert-color">
                                {{specialCharacterErrorMessage}}</div>
                            </div>
                            <span class="salesforceMessage text-info">
                              * Country and PO Number cannot be updated for Salesforce Orders.
                            </span>
                          </div>
                        </div>


                        <div class="col-lg-4 col-md-6 col-sm-12 ">
                          <!-- Customer E-mail form field start -->
                          <div class="form-group m-0">
                            <label><strong class="">Customer E-mail</strong></label>
                            <input type="text" class="form-control ponumber" name="customerEmail"
                              formControlName="customerEmail" [readOnly]="isDisabled">
                          </div>
                          <!-- Customer E-mail form field End -->
                          <div *ngIf="(probeDetailForm.get('customerEmail').touched || probeDetailForm.get('customerEmail').dirty) &&
                          probeDetailForm.get('customerEmail').invalid ">
                            <!-- email pattern validation error-->
                            <div
                              *ngIf="probeDetailForm.get('customerEmail').errors['pattern'] && !(probeDetailForm.controls['customerEmail'].hasError('maxlength'))">
                              <span class="alert-color font-12"> {{enterValidEmail}}</span>
                            </div>
                            <!-- customer email maxlength validation error-->
                            <div *ngIf="probeDetailForm.controls['customerEmail'].hasError('maxlength')">
                              <span class="alert-color font-12">{{maxCharactersAllowedMessage}}</span>
                            </div>
                          </div>
                        </div>

                        <!-- order Record Type form field start -->
                        <div class="col-lg-4 col-md-6 col-sm-12 ">
                          <label class="form-control-label" for="field_orderRecordType"
                            id="label_orderRecordType"><strong>Order Record Type</strong></label>
                          <ng-multiselect-dropdown id="field_orderRecordType" name="orderRecordType" [placeholder]="''"
                            formControlName="orderRecordType" [settings]="countrySetting" [data]="orderRecordTypeList"
                            [disabled]="isDisabled">
                          </ng-multiselect-dropdown>
                          <!-- order Record Type form field end -->
                          <div
                            *ngIf="(probeDetailForm.get('orderRecordType').touched || probeDetailForm.get('orderRecordType').dirty) && probeDetailForm.get('orderRecordType').invalid ">
                            <div *ngIf="probeDetailForm.get('orderRecordType').errors['required']">
                              <span class="alert-color mb-3">{{orderRecordTypeMessage}}</span>
                            </div>
                          </div>
                        </div>
                      </div>


                      <div class="row">
                        <div class="col-lg-4 col-md-6 col-sm-12 d-flex align-items-center justify-content-start">
                          <!-- Form group for Device Auto Lock checkbox -->
                          <div class="form-group d-flex align-items-center">
                            <!-- Label for Device Auto Lock checkbox -->
                            <label><strong class="text-nowrap">Device Auto Lock</strong></label>
                            <!-- Checkbox to toggle Device Auto Lock, bound to the deviceAutoLock form control -->
                            <input type="checkbox" class="form-control deviceAutoLock mr-2" name="deviceAutoLock"
                              formControlName="deviceAutoLock" [checked]="probeDetailForm.get('deviceAutoLock').value"
                              [attr.disabled]="isDisabled ? true : null">
                          </div>
                          <!-- Form group for Probe Auto Lock checkbox -->
                          <div class="form-group d-flex align-items-center">
                            <!-- Label for Probe Auto Lock checkbox -->
                            <label><strong class="text-nowrap">Probe Auto Lock</strong></label>
                            <!-- Checkbox to toggle Probe Auto Lock, bound to the probeAutoLock form control -->
                            <input type="checkbox" class="form-control deviceAutoLock" name="probeAutoLock"
                              formControlName="probeAutoLock" [checked]="probeDetailForm.get('probeAutoLock').value"
                              [attr.disabled]="isDisabled ? true : null">
                          </div>
                        </div>
                      </div>

                      <span class="text-primary" *ngIf="isDisabled">*Order details cannot be updated as it is
                        associated
                        with a
                        Salesforce-generated order.
                      </span>
                    </form>
                    <!---------######################################------------->
                    <!------------------------------------------->
                    <!------------------------------------------->
                    <!---Add/Edit Probe---->
                    <!------------------------------------------->
                    <!------------------------------------------->
                    <!---------######################################------------->
                    <div class="row mt-3 mb-2">
                      <div class="col-md-12" style="display: flex;">
                        <div>
                          <ng-template [ngIf]="!editProbeMode">
                            <label class="h5-tag">Add Probe</label>
                          </ng-template>
                          <ng-template [ngIf]="editProbeMode">
                            <label class="h5-tag">Edit Probe</label>
                          </ng-template>
                        </div>
                        <ng-template [ngIf]="!editProbeMode">
                          <div>
                            <button class="btn btn-sm btn-orange radius-50 ml-2" id="addSalesOrderFormBtn"
                              (click)="openAddNewProbForm()" [disabled]="addProdeMode">
                              <em class="fa fa-plus"></em>
                            </button>
                          </div>
                        </ng-template>
                      </div>
                    </div>
                    <ng-template [ngIf]="addProdeMode || editProbeMode">
                      <form [formGroup]="probeFeaturesForm">
                        <div class="row">
                          <div class="col-md-12">
                            <div class="card">
                              <div class="card-body">
                                <div class="card shadow">
                                  <div class="card-body">
                                    <div class="row">
                                      <div class="col-md-6">
                                        <div class="form-group">
                                          <div>
                                            <label><strong class="">Probe Serial Number</strong></label>
                                          </div>
                                          <div>
                                            <input type="text" class="form-control" name="probeSerialNumber"
                                              id="createProbeSerialNumber"
                                              (input)="updateSerialNumber($any($event.target)?.value)"
                                              formControlName="serialNumber" autocomplete="off">
                                            <div
                                              *ngIf="(probeFeaturesForm.get('serialNumber').touched || probeFeaturesForm.get('serialNumber').dirty) && probeFeaturesForm.get('serialNumber').invalid">
                                              <span
                                                *ngIf="(probeFeaturesForm.get('serialNumber').errors && probeFeaturesForm.get('serialNumber').errors['required'])"
                                                class="alert-color">{{enterQRCode}}</span>
                                              <!-------------------------->
                                              <!--Max Length-->
                                              <!-------------------------->
                                              <span *ngIf="probeFeaturesForm.get('serialNumber').errors && 
                                                probeFeaturesForm.get('serialNumber').errors['maxlength']"
                                                class="alert-color">
                                                {{small_maxCharactersAllowedMessage}}</span>
                                              <!-------------------------->
                                              <!--cannotContainSpace-->
                                              <!-------------------------->
                                              <span *ngIf="probeFeaturesForm.get('serialNumber').errors && 
                                                probeFeaturesForm.get('serialNumber').errors['cannotContainSpace']"
                                                class="alert-color">
                                                {{enterValidSerialNumber}}</span>
                                              <!-------------------------->
                                              <!--pattern-->
                                              <!-------------------------->
                                              <span
                                                *ngIf="(probeFeaturesForm.get('serialNumber').errors && probeFeaturesForm.get('serialNumber').errors['pattern'])"
                                                class="alert-color">{{enterValidSerialNumber}}</span>
                                              <span *ngIf="(probeFeaturesForm.get('serialNumber').errors && 
                                                probeFeaturesForm.get('serialNumber').errors['serialNumberexists'])"
                                                class="alert-color">{{serialNumberexists}}</span>
                                              <span
                                                *ngIf="(probeFeaturesForm.get('serialNumber').errors && 
                                                   !probeFeaturesForm.get('serialNumber').errors['pattern'] &&
                                                   !probeFeaturesForm.get('serialNumber').errors['serialNumberexists'] &&
                                                   probeFeaturesForm.get('serialNumber').errors['serialNumberStartWith'])"
                                                class="alert-color">{{probeFeaturesForm.get('serialNumber').errors['message']}}</span>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                      <div class="col-md-6">
                                        <div class="form-group">
                                          <label><strong class="">Probe Type</strong></label>
                                          <select class="form-control form-control-sm form_dropdown" name="probeType"
                                            id="probeTypeForNewProbe"
                                            (change)="updateCurrentFeatures($any($event.target)?.value,true,true)"
                                            formControlName="probeType">
                                            <ng-template ngFor let-probeTypeObject [ngForOf]="probeTypeResponse">
                                              <option [value]="probeTypeObject.probeTypeId">
                                                {{probeTypeObject.displayName}}
                                              </option>
                                            </ng-template>
                                          </select>
                                          <div
                                            *ngIf="(probeFeaturesForm.get('probeType').touched || probeFeaturesForm.get('probeType').dirty) && probeFeaturesForm.get('probeType').invalid">
                                            <span
                                              *ngIf="(probeFeaturesForm.get('probeType').errors && probeFeaturesForm.get('probeType').errors['required'])"
                                              class="alert-color">{{probeTypeMessage}}</span>
                                          </div>
                                        </div>
                                      </div>
                                    </div>

                                    <!---------######################################------------->
                                    <!----------------------------------------->
                                    <!---------Preset Start------------->
                                    <!----------------------------------------->
                                    <!--##########----2 Options Display--#######------>
                                    <!----------1 12 Month---------------------->
                                    <!----------2 Perpetual--------------------->
                                    <!--##########----End Date Display--#######------>
                                    <!----if probe Type with no feature Then message display--->
                                    <!----------------------------------------->
                                    <!----------------------------------------->
                                    <!---------######################################------------->
                                    <div class="row my-2 mx-1" id="ConfigureFeatures" *ngIf="currentPresets != null">
                                      <div class="col-3 col_border middleText">
                                        <strong class="">Configure Presets :</strong>
                                      </div>
                                      <div class="col-9 p-0">
                                        <div class="row m-0">
                                          <ng-template [ngIf]="currentPresets.length > 0">
                                            <ng-template ngFor let-presetsObject [ngForOf]="currentPresets">
                                              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col_border p-1">
                                                <div class="custom-control custom-checkbox">
                                                  <input type="checkbox" class="custom-control-input"
                                                    [id]="presetsObject.displayName+'_'+presetsObject.presetId.toString()"
                                                    name="feature[]"
                                                    [checked]="probeObject.presets | featuresCheckBoxPipe:presetsObject.presetId:reloadPipe"
                                                    (change)="onChangePresets(presetsObject.presetId,$any($event.target)?.checked,defaultStartDate,presetsObject)"
                                                    [disabled]="this.currentPresets | assignConfigDisablePipe:presetsObject.presetId:presetProbeConfigType">
                                                  <label class="custom-control-label checboxLineHeight"
                                                    [for]="presetsObject.displayName+'_'+presetsObject.presetId.toString()">{{presetsObject
                                                    | featuresBaseResponseDisplayPipe}}</label>
                                                </div>
                                                <div style="display: flex;justify-content: space-around;">
                                                  <ng-template
                                                    [ngIf]="presetsObject | featureValidityOptionHideShow : validityPerpetual">
                                                    <div class="custom-control custom-radio">
                                                      <input type="radio" class="custom-control-input"
                                                        [id]="'Perpetual_'+presetsObject.displayName+'_'+presetsObject.presetId.toString()"
                                                        [name]="presetsObject.displayName+'_'+presetsObject.presetId.toString()"
                                                        [checked]="probeObject.presets | featuresRadioButtonPipe:presetsObject.presetId:unlimitedEndDateOptions:reloadPipe"
                                                        (change)="onChangePresetsEndDateForUpdate(presetsObject.presetId,defaultStartDate,unlimitedEndDateOptions,null)"
                                                        [disabled]="this.currentPresets | assignConfigDisablePipe:presetsObject.presetId:presetProbeConfigType">
                                                      <label class="custom-control-label radioButtonLineHeight"
                                                        [for]="'Perpetual_'+presetsObject.displayName+'_'+presetsObject.presetId.toString()">{{presetsObject
                                                        |featuresValidityPartNumberDisplayPipe:validityPerpetual}}</label>
                                                    </div>
                                                  </ng-template>
                                                  <!-- ------------------------------------- -->
                                                  <ng-template
                                                    [ngIf]="presetsObject |featureValidityOptionHideShow : validityOneYear">
                                                    <div class="custom-control custom-radio">
                                                      <input type="radio" class="custom-control-input"
                                                        [id]="'month_'+presetsObject.displayName+'_'+presetsObject.presetId.toString()"
                                                        [name]="presetsObject.displayName+'_'+presetsObject.presetId.toString()"
                                                        [checked]="probeObject.presets | featuresRadioButtonPipe:presetsObject.presetId:oneYearEndDateOptions:reloadPipe"
                                                        (change)="onChangePresetsEndDateForUpdate(presetsObject.presetId,defaultStartDate,oneYearEndDateOptions,null)"
                                                        [disabled]="this.currentPresets | assignConfigDisablePipe:presetsObject.presetId:presetProbeConfigType">
                                                      <label class="custom-control-label radioButtonLineHeight"
                                                        [for]="'month_'+presetsObject.displayName+'_'+presetsObject.presetId.toString()">{{presetsObject
                                                        |featuresValidityPartNumberDisplayPipe:validityOneYear}}</label>
                                                    </div>
                                                  </ng-template>
                                                  <!-- ------------------------------------- -->
                                                  <div class="custom-control custom-radio">
                                                    <input type="radio" class="custom-control-input"
                                                      [id]="'customDate_'+presetsObject.displayName+'_'+presetsObject.presetId.toString()"
                                                      [name]="presetsObject.displayName+'_'+presetsObject.presetId.toString()"
                                                      [checked]="probeObject.presets | featuresRadioButtonPipe:presetsObject.presetId:customEndDateOptions:reloadPipe"
                                                      (change)="onChangePresetsEndDateForUpdate(presetsObject.presetId,defaultStartDate,customEndDateOptions,null)"
                                                      [disabled]="this.currentPresets | assignConfigDisablePipe:presetsObject.presetId:presetProbeConfigType">
                                                    <label class="custom-control-label radioButtonLineHeight"
                                                      [for]="'customDate_'+presetsObject.displayName+'_'+presetsObject.presetId.toString()">{{customEndDateOptions}}</label>
                                                  </div>
                                                </div>
                                                <!-------------------------------------->
                                                <!-------------------------------------->
                                                <!------------Custom date start--------->
                                                <!-------------------------------------->
                                                <div style="display: flex;" class="mt-2" id="datepickerId"
                                                  *ngIf="probeObject.presets | featuresCustomEndDateDisplayPipe:presetsObject.presetId:reloadPipe">
                                                  <label class="customDate-lbl pl-3" for="field_logRange">
                                                    Date
                                                  </label>
                                                  <mat-form-field>
                                                    <input class="form-control datePikerTextBox" matInput
                                                      [matDatepicker]="customDate" placeholder="Choose a Start date"
                                                      readonly
                                                      [value]="probeObject.presets | featuresSelectCustomDatePipe:presetsObject.presetId:reloadPipe"
                                                      (dateChange)="onChangePresetsEndDateForUpdate(presetsObject.presetId,defaultStartDate,customEndDateOptions,$any($event.target)?.value)">
                                                    <mat-datepicker-toggle matSuffix
                                                      [for]="customDate"></mat-datepicker-toggle>
                                                    <mat-datepicker #customDate></mat-datepicker>
                                                  </mat-form-field>
                                                </div>
                                                <!-------------------------------------->
                                                <!--------------------------------------->
                                                <!------------Custom date end------------>
                                                <!--------------------------------------->
                                                <div class="pl-3 pt-2"><span
                                                    style="color: #919199;">{{probeObject.presets
                                                    |
                                                    featuresExpireDateDisplayPipe:presetsObject.presetId:reloadPipe}}</span>
                                                </div>
                                              </div>
                                            </ng-template>
                                          </ng-template>
                                          <ng-template [ngIf]="currentPresets.length == 0">
                                            <div class="col-12 col_border p-3"><span>No preset available to
                                                configure</span></div>
                                          </ng-template>
                                        </div>
                                      </div>
                                    </div>
                                    <!---------######################################------------->
                                    <!----------------------------------------->
                                    <!---------Features End--------------->
                                    <!----------------------------------------->
                                    <!---------######################################------------->
                                    <!---------######################################------------->


                                    <!---------######################################------------->
                                    <!----------------------------------------->
                                    <!---------Features Start------------->
                                    <!----------------------------------------->
                                    <!--##########----2 Options Display--#######------>
                                    <!----------1 12 Month---------------------->
                                    <!----------2 Perpetual--------------------->
                                    <!--##########----End Date Display--#######------>
                                    <!----if probe Type with no feature Then message display--->
                                    <!----------------------------------------->
                                    <!----------------------------------------->
                                    <!---------######################################------------->
                                    <div class="row my-3 mx-1" id="ConfigureFeatures" *ngIf="currentFeatures != null">
                                      <div class="col-3 col_border middleText">
                                        <strong class="">Configure Features :</strong>
                                      </div>
                                      <div class="col-9 p-0">
                                        <div class="row m-0">
                                          <ng-template [ngIf]="currentFeatures.length > 0">
                                            <ng-template ngFor let-featuresObject [ngForOf]="currentFeatures">
                                              <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col_border p-1">
                                                <div class="custom-control custom-checkbox">
                                                  <input type="checkbox" class="custom-control-input"
                                                    [id]="featuresObject.displayName+'_'+featuresObject.featureId.toString()"
                                                    name="feature[]"
                                                    [checked]="probeObject.features | featuresCheckBoxPipe:featuresObject.featureId:reloadPipe"
                                                    (change)="onChangeFeatures(featuresObject.featureId,$any($event.target)?.checked,defaultStartDate,featuresObject)"
                                                    [disabled]="this.currentFeatures | assignConfigDisablePipe:featuresObject.featureId:featureProbeConfigType">
                                                  <label class="custom-control-label checboxLineHeight"
                                                    [for]="featuresObject.displayName+'_'+featuresObject.featureId.toString()">{{featuresObject
                                                    | featuresBaseResponseDisplayPipe}}</label>
                                                </div>
                                                <div style="display: flex;justify-content: space-around;">
                                                  <ng-template
                                                    [ngIf]="featuresObject | featureValidityOptionHideShow : validityPerpetual">
                                                    <div class="custom-control custom-radio">
                                                      <input type="radio" class="custom-control-input"
                                                        [id]="'Perpetual_'+featuresObject.displayName+'_'+featuresObject.featureId.toString()"
                                                        [name]="featuresObject.displayName+'_'+featuresObject.featureId.toString()"
                                                        [checked]="probeObject.features | featuresRadioButtonPipe:featuresObject.featureId:unlimitedEndDateOptions:reloadPipe"
                                                        (change)="onChangeFeaturesEndDate(featuresObject.featureId,defaultStartDate,unlimitedEndDateOptions,null)">
                                                      <label class="custom-control-label radioButtonLineHeight"
                                                        [for]="'Perpetual_'+featuresObject.displayName+'_'+featuresObject.featureId.toString()">{{featuresObject
                                                        |featuresValidityPartNumberDisplayPipe:validityPerpetual}}</label>
                                                    </div>
                                                  </ng-template>
                                                  <!-- ------------------------------------- -->
                                                  <ng-template
                                                    [ngIf]="featuresObject |featureValidityOptionHideShow : validityOneYear">
                                                    <div class="custom-control custom-radio">
                                                      <input type="radio" class="custom-control-input"
                                                        [id]="'month_'+featuresObject.displayName+'_'+featuresObject.featureId.toString()"
                                                        [name]="featuresObject.displayName+'_'+featuresObject.featureId.toString()"
                                                        [checked]="probeObject.features | featuresRadioButtonPipe:featuresObject.featureId:oneYearEndDateOptions:reloadPipe"
                                                        (change)="onChangeFeaturesEndDate(featuresObject.featureId,defaultStartDate,oneYearEndDateOptions,null)"
                                                        [disabled]="this.currentFeatures | assignConfigDisablePipe:featuresObject.featureId:featureProbeConfigType">
                                                      <label class="custom-control-label radioButtonLineHeight"
                                                        [for]="'month_'+featuresObject.displayName+'_'+featuresObject.featureId.toString()">{{featuresObject
                                                        |featuresValidityPartNumberDisplayPipe:validityOneYear}}</label>
                                                    </div>
                                                  </ng-template>
                                                  <!-- ------------------------------------- -->
                                                  <div class="custom-control custom-radio">
                                                    <input type="radio" class="custom-control-input"
                                                      [id]="'customDate_'+featuresObject.displayName+'_'+featuresObject.featureId.toString()"
                                                      [name]="featuresObject.displayName+'_'+featuresObject.featureId.toString()"
                                                      [checked]="probeObject.features | featuresRadioButtonPipe:featuresObject.featureId:customEndDateOptions:reloadPipe"
                                                      (change)="onChangeFeaturesEndDate(featuresObject.featureId,defaultStartDate,customEndDateOptions,null)"
                                                      [disabled]="this.currentFeatures | assignConfigDisablePipe:featuresObject.featureId:featureProbeConfigType">
                                                    <label class="custom-control-label radioButtonLineHeight"
                                                      [for]="'customDate_'+featuresObject.displayName+'_'+featuresObject.featureId.toString()">{{customEndDateOptions}}</label>
                                                  </div>
                                                </div>
                                                <!-------------------------------------->
                                                <!-------------------------------------->
                                                <!------------Custom date start--------->
                                                <!-------------------------------------->
                                                <div style="display: flex;" class="mt-2" id="datepickerId"
                                                  *ngIf="probeObject.features | featuresCustomEndDateDisplayPipe:featuresObject.featureId:reloadPipe">
                                                  <label class="customDate-lbl pl-3" for="field_logRange">
                                                    Date
                                                  </label>
                                                  <mat-form-field>
                                                    <input class="form-control datePikerTextBox" matInput
                                                      [matDatepicker]="customDate" placeholder="Choose a Start date"
                                                      readonly
                                                      [value]="probeObject.features | featuresSelectCustomDatePipe:featuresObject.featureId:reloadPipe"
                                                      (dateChange)="onChangeFeaturesEndDate(featuresObject.featureId,defaultStartDate,customEndDateOptions,$any($event.target)?.value)">
                                                    <mat-datepicker-toggle matSuffix
                                                      [for]="customDate"></mat-datepicker-toggle>
                                                    <mat-datepicker #customDate></mat-datepicker>
                                                  </mat-form-field>
                                                </div>
                                                <!-------------------------------------->
                                                <!--------------------------------------->
                                                <!------------Custom date end------------>
                                                <!--------------------------------------->
                                                <div class="pl-3 pt-2"><span
                                                    style="color: #919199;">{{probeObject.features
                                                    |
                                                    featuresExpireDateDisplayPipe:featuresObject.featureId:reloadPipe}}</span>
                                                </div>
                                              </div>
                                            </ng-template>
                                          </ng-template>
                                          <ng-template [ngIf]="currentFeatures.length == 0">
                                            <div class="col-12 col_border p-3"><span>No feature available to
                                                configure</span></div>
                                          </ng-template>
                                        </div>
                                      </div>
                                    </div>
                                    <!---------######################################------------->
                                    <!----------------------------------------->
                                    <!---------Features End--------------->
                                    <!----------------------------------------->
                                    <!---------######################################------------->
                                    <!---------######################################------------->

                                    <!---------######################################------------->
                                    <!----------------------------------------->
                                    <!---------1 Reminder selection------------>
                                    <!-----------Reminder options display based on permission-->
                                    <!---------2 Close Button------------------>
                                    <!---------3 Add/Edit Button--------------->
                                    <!----------------------------------------->
                                    <!----------------------------------------->
                                    <!---------######################################------------->
                                    <div class="row mt-3">
                                      <div class="col-md-8 col-sm-8">
                                        <ng-template [ngIf]="setReminderOptionDisplayPermission">
                                          <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="temp"
                                              (change)="setIsReminder($any($event.target)?.checked)"
                                              [checked]="probeObject.reminder" [disabled]="isReminderDisabled">
                                            <label class="custom-control-label checboxLineHeight" for="temp">Set
                                              Reminder
                                              Option</label>
                                          </div>
                                        </ng-template>
                                      </div>
                                      <div class="col-md-4 col-sm-4">
                                        <div class="multiprobe-div">
                                          <!---------------------------------->
                                          <!----------1 Add button------------>
                                          <!----------2 Close button---------->
                                          <!---------------------------------->
                                          <ng-template [ngIf]="addProdeMode">
                                            <button class="form-control btn btn-sm btn-outline-secondary mr-2"
                                              style="text-align: center;" (click)="closeAddEditMode()"><span
                                                class="Pointer">Close</span></button>

                                            <button class="form-control btn btn-sm btn-orange" id="addSingleProbeBtn"
                                              style="text-align: center;" (click)="addNewProb()"
                                              [disabled]="probeFeaturesForm.invalid"><span
                                                class="Pointer">Add</span></button>
                                          </ng-template>
                                          <!---------------------------------->
                                          <!----------1 edit button----------->
                                          <!----------2 Close button---------->
                                          <!---------------------------------->
                                          <ng-template [ngIf]="editProbeMode">
                                            <button class="form-control btn btn-sm btn-outline-secondary mr-2"
                                              style="text-align: center;" (click)="closeEditMode()"><span
                                                class="Pointer">Close</span></button>

                                            <button class="form-control btn btn-sm btn-orange"
                                              style="text-align: center;" (click)="editProbAndSave()"
                                              id="updateEditedProbeBtn" [disabled]="probeFeaturesForm.invalid"><span
                                                class="Pointer">Update</span></button>
                                          </ng-template>
                                          <!---------------------------------->
                                          <!---------------------------------->
                                          <!---------------------------------->
                                        </div>
                                      </div>
                                    </div>
                                    <!---------######################################------------->
                                    <!----------------------------------------->
                                    <!----------------------------------------->
                                    <!----------------------------------------->
                                    <!---------######################################------------->
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </form>
                    </ng-template>
                    <!---------######################################------------->
                    <!-------------------------------------------------->
                    <!----------------Probe Table Start----------------->
                    <!-------------------------------------------------->
                    <!---------######################################------------->
                    <!--1 Probe Serial Number--->
                    <!--2 Probe Type--->
                    <!--3 Configure Features--->
                    <!--4 Set Reminder Option--->
                    <!--5 Action--->
                    <!---------######################################------------->
                    <!---------######################################------------->
                    <!--2 Action Button display--->
                    <!--1 Update Button---->
                    <!--2 delete Button---->
                    <!---------######################################------------->
                    <!---------######################################------------->
                    <ng-template [ngIf]="probeFeatureRequest.probes != null && probeFeatureRequest.probes.length > 0">
                      <div class="row">
                        <div class="col-md-12">
                          <div class="probe-table">
                            <table class="table table-sm table-bordered" style="overflow-x: scroll;" aria-hidden="true">
                              <tr class="thead-light">
                                <th>Probe Serial Number</th>
                                <th>Probe Type</th>
                                <th>Presets</th>
                                <th>Features</th>
                                <th style="width: 130px;">Reminder</th>
                                <th class="col_button" colspan="2" style="text-align: center;">Action</th>
                              </tr>
                              <ng-template ngFor let-probes [ngForOf]="probeFeatureRequest.probes"
                                let-probeIndex="index">
                                <tr>
                                  <td>{{probes.serialNumber}}
                                    <div *ngIf="probes.errorMessage!=null">
                                      <Span class="text-danger">{{probes.errorMessage}}</Span>
                                    </div>
                                  </td>
                                  <td>{{probeTypeEnum[probes.probeType]}}</td>
                                  <td>{{probes.presets| featuresTextDisplayPipe}}</td>
                                  <td>{{probes.features| featuresTextDisplayPipe}}</td>
                                  <td style="width: 130px;">{{probes.reminder | commonBooleanValueDisplayPipe}}</td>
                                  <td class="col_button">
                                    <button class="form-control btn btn-sm Pointer" style="text-align: center;"
                                      id="editSingleProbeBtn" (click)="editModeOpen(probeIndex)"
                                      [disabled]="(editProbeMode || addProdeMode)"><span class="editIcon"><em
                                          class="fa fa-pencil-alt"></em></span></button>
                                  </td>
                                  <td class="col_button">
                                    <button class="form-control btn btn-sm" (click)="deleteProbe(probeIndex)"
                                      id="deleteSingleProbeBtn" style="text-align: center;"
                                      [disabled]="(editProbeMode || addProdeMode)">
                                      <em class="fas fa-trash-alt deleteIcon"></em>
                                    </button>
                                  </td>
                                </tr>
                              </ng-template>
                            </table>
                          </div>
                        </div>
                      </div>
                      <!---------######################################------------->
                      <!------------------------------------------->
                      <!----------Button Start---------------------->
                      <!--------------------------------------------->
                      <!---------1 Download License------------------>
                      <!---------2 Save------------------------------>
                      <!--------------------------------------------->
                      <!---------######################################------------->
                      <div class="row">
                        <div class="col-md-12">
                          <div class="card shadow">
                            <div class="card-body">
                              <div class="row">
                                <div class="col-md-10 col-sm-8">
                                  <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="isDownload"
                                      name="isDownload"
                                      (change)="onChangeDownloadLicense($any($event.target)?.checked)">
                                    <label class="custom-control-label pt-1" for="isDownload">Download
                                      license</label>
                                  </div>

                                </div>
                                <div class="col-md-2 col-sm-4">
                                  <div class="multiprobe-div">
                                    <button class="form-control btn btn-sm btn-orange" (click)="multiprobeSave()"
                                      [disabled]="probeDetailForm.invalid || errorInAddProbeRequest()"><span
                                        class="Pointer">Save</span></button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!---------######################################------------->
                      <!------------------------------------------->
                      <!----------Button end----------------------->
                      <!------------------------------------------->
                      <!---------######################################------------->
                    </ng-template>
                    <!---------######################################------------->
                    <!-------------------------------------------------->
                    <!----------------Probe Table End----------------->
                    <!-------------------------------------------------->
                    <!---------######################################------------->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!---------######################################------------->
        <!------------------------------->
        <!------------------------------->
        <!------------------------------->
        <!---------######################################------------->
      </div>
    </div>
  </div>
</body>