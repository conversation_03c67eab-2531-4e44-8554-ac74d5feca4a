export class UploadPackageRequest {
    title: string;
    attachmentFileName: string;
    releaseNoteFileName: string;
    attachmentSize: number;
    countryIds: Array<number>;
    jsonId: number;
    partNumber: string;

    constructor(title: string, attachmentFileName: string, releaseNoteFileName: string, attachmentSize: number, $countryIds: Array<number>, jsonId: number, partNumber: string) {
        this.title = title;
        this.attachmentFileName = attachmentFileName;
        this.releaseNoteFileName = releaseNoteFileName;
        this.attachmentSize = attachmentSize;
        this.countryIds = $countryIds;
        this.jsonId = jsonId;
        this.partNumber = partNumber;
    }
}