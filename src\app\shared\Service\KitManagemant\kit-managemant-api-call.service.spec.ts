import { TestBed } from '@angular/core/testing';
import { LocalStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { CommonsService } from '../../util/commons.service';
import { KitManagemantApiCallService } from './kit-managemant-api-call.service';

describe('KitManagemantApiCallService', () => {
  let service: KitManagemantApiCallService;

  beforeEach(() => {
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        CommonsService,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        commonsProviders(null)
      ]
    });
    service = TestBed.inject(KitManagemantApiCallService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
