import { ListingPageReloadSubjectParameter } from "../common/listingPageReloadSubjectParameter.model";
import { ProbeConfigGroupRequestBody } from "./ProbeConfigGroupRequestBody.model";

export class ProbeConfigGroupFilterAction {
  listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter;
  probeConfigGroupRequestBody: ProbeConfigGroupRequestBody;

  constructor($listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, $probeConfigGroupRequestBody: ProbeConfigGroupRequestBody) {
    this.listingPageReloadSubjectParameter = $listingPageReloadSubjectParameter;
    this.probeConfigGroupRequestBody = $probeConfigGroupRequestBody;
  }
}
