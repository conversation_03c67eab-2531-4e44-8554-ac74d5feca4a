import { HttpErrorResponse, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { ComponentFixture } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ToastrService } from 'ngx-toastr';
import { Observable, throwError } from 'rxjs';
import { INTERNAL_SERVER_ERROR } from '../app.constants';
import { CountryListResponse } from '../model/Country/CountryListResponse.model';
import { PresetDetailBaseResponse } from '../model/Presets/PresetDetailBaseResponse.model';
import { FeaturePartNumberResponse } from '../model/probe/FeaturePartNumberResponse.model';
import { PresetPartNumberResponse } from '../model/probe/PresetPartNumberResponse.model';
import { ProbeFeatureResponse } from '../model/probe/ProbeFeatureResponse.model';
import { ProbePresetResponse } from '../model/probe/ProbePresetResponse.model';
import { ProbeTypeResponse } from '../model/probe/probeTypeResponse.model';
import { RolePermissionResponse } from '../model/Role/rolePermissionResponse.model';
import { API_BASE_URL, API_SSO_BASE_URL, RDM_ENVIRONMENT, RDM_VERSION, SSO_REGISTRATION_ID } from '../shared/config';
import { collapseFilterTextEnum } from '../shared/enum/collapseFilterButtonText.enum';
import { ValidityEnum } from '../shared/enum/ValidityEnum.enum';

/**
* Tests the authentication flow by verifying redirection for unauthenticated users.
*
* <AUTHOR>
* @param authServiceSpy - Spy object for the authentication service.
* @param component - The Angular component under test.
* @param fixture - The fixture associated with the component.
*
* @throws Will fail the test if the login navigation method is not called when the user is unauthenticated.
*/
export function testAuthentication(authServiceSpy: jasmine.SpyObj<any>, component: any, fixture: ComponentFixture<any>) {
    authServiceSpy.isAuthenticate?.and.returnValue(false);
    component.ngOnInit();
    fixture.detectChanges();
    expect(authServiceSpy.loginNavigate).toHaveBeenCalled();
}

/**
* Simulates interaction with a dropdown element and verifies the component's state updates correctly.
*
* <AUTHOR>
* @param fixture - The fixture associated with the component.
* @param component - The Angular component under test.
* @param selectElementId - The ID of the dropdown element to interact with.
*
* @example
* testDropdownInteraction(fixture, component, '#itemsPerPageDropdown');
*
* @throws Will fail the test if the selected value does not match the expected value.
*/
export function testDropdownInteraction(fixture: ComponentFixture<any>, component: any, selectElementId: string) {
    const selectElement = fixture.nativeElement.querySelector(selectElementId);
    if (selectElement) {
        selectElement.value = selectElement?.options[3].value; // Select "100"
        selectElement.dispatchEvent(new Event('change'));
        expect(component.itemsPerPage).toBe(selectElement?.options[3]?.value);
    }
}/**
* Tests the pagination functionality by simulating a page change event and verifying the state.
*
* <AUTHOR>
* @param fixture - The fixture associated with the component.
* @param component - The Angular component under test.
* @param paginationElementId - The ID of the pagination element.
* @param selectedPage - The page number to simulate selection.
*
* @example
* testPagination(fixture, component, '#paginationComponent', 2);
*
* @throws Will fail the test if the component's page state does not update to the selected page.
*/
export function testPagination(fixture: ComponentFixture<any>, component: any, paginationElementId: string, selectedPage: number) {
    const paginationElement = fixture.debugElement.query(By.css(paginationElementId));
    paginationElement.triggerEventHandler('pageChange', selectedPage || 1);
    fixture.detectChanges();
    expect(component.page).toBe(selectedPage || 1);
}

/**
* Tests the toggle functionality of a filter component, verifying the hidden state and button text.
*
* <AUTHOR>
* @param component - The Angular component under test.
*
* @example
* testToggleFilter(component);
*
* @throws Will fail the test if the filter's state or button text does not toggle as expected.
*/
export function testToggleFilter(component: any) {
    // Initial state setup (Make sure initial states are correctly set)
    component['isFilterHidden'] = true;
    component['hideShowFilterButtonText'] = collapseFilterTextEnum.SHOW_FILTER;

    // Act: Call the toggle method
    component.toggleFilter();  // Assuming toggleFilter() is a method

    // Assert: Verify the visibility and button text after the first toggle
    expect(component['isFilterHidden']).toBe(false);
    expect(component['hideShowFilterButtonText']).toBe(collapseFilterTextEnum.HIDE_FILTER);

    // Act: Toggle back to the initial state
    component.toggleFilter();

    // Assert: Verify the visibility and button text after toggling back
    expect(component['isFilterHidden']).toBe(true);
    expect(component['hideShowFilterButtonText']).toBe(collapseFilterTextEnum.SHOW_FILTER);
}

/**
* Tests a dropdown's operation list by simulating user selection and verifying the expected value.
*
* <AUTHOR>
* @param fixture - The fixture associated with the component.
* @param paginationElementId - The ID of the dropdown element.
* @param selectedPage - The index of the option to select.
*
* @example
* operationsListTestcase(fixture, '#operationsDropdown', 1);
*
* @throws Will fail the test if the selected option value does not match the expected value.
*/
export function operationsListTestcase(fixture: ComponentFixture<any>, paginationElementId: string, selectedPage: number) {
    // **Act: Select a new value in the dropdown**
    const selectElement = fixture.nativeElement.querySelector(paginationElementId);
    selectElement.value = selectElement?.options[selectedPage].value; // Select the second option
    selectElement.dispatchEvent(new Event('change')); // Dispatch the change event to simulate user interaction

    fixture.detectChanges();
    // **Assert: Verify that the correct option value is selected**
    // Check that the selected option has the expected value ("Import CSV")
    expect("Import CSV").toBe(selectElement?.options[selectedPage]?.value);
}

/**
* Tests the behavior of the "Select All" checkbox by simulating user interaction.
* It ensures that the `selectAllItem` method is called with the correct value when the checkbox is clicked.
*
* @param fixture - The fixture associated with the component under test.
* @param component - The Angular component being tested.
* @param selectAllId - The ID of the "Select All" checkbox element.
*
* @example
* selectAllTestCase(fixture, component, '#selectAllCheckbox');
*
* @throws Will fail the test if the `selectAllItem` method is not called with the expected values when toggling the checkbox.
*/
export function selectAllTestCase(fixture: ComponentFixture<any>, component: any, selectAllId: string) {

    spyOn(component, 'selectAllItem')?.and.callThrough(); // Track calls to the `selectAllItem` method
    // **Act: Simulate user interaction**
    // Simulate clicking the "Select All" checkbox
    const selectAllCheckbox = fixture.nativeElement.querySelector(selectAllId);
    selectAllCheckbox?.click(); // User selects all items
    fixture.detectChanges();
    // **Assert: Verify the behavior for 'Select All'**
    // Ensure the `selectAllItem` method is called with `true` when the checkbox is clicked
    expect(component.selectAllItem).toHaveBeenCalledWith(true);

    // Simulate unchecking the "Select All" checkbox
    selectAllCheckbox?.click(); // User deselects all items
    fixture.detectChanges();
}

export function selectOneFromListTestCase(fixture: ComponentFixture<any>, component: any, selectedId: string) {
    // Save a reference to the original method
    const originalSelectCheckbox = component.selectCheckbox;

    // Spy on the `selectCheckbox` method
    const spy = spyOn(component, 'selectCheckbox').and.callThrough();

    try {
        // Arrange: Select the checkbox element by ID
        const checkboxDebugEl = fixture.debugElement.query(By.css(selectedId));
        const checkboxNativeEl = checkboxDebugEl.nativeElement as HTMLInputElement;

        // Simulate the click
        checkboxNativeEl.click();

        // Assert: Verify the initial checkbox selection
        expect(spy).toHaveBeenCalled(); // Validate method call
    } finally {
        // Restore the original method after the test
        component.selectCheckbox = originalSelectCheckbox;
    }
}

/**
* Simulates the selection of an option from a dropdown/select element in Angular tests.
* 
* <AUTHOR>
* @param fixture - The Angular component fixture used for testing
* @param optionIndex - The index of the option to select (zero-based)
* @param operationId - The CSS selector for the dropdown/select element
* 
* @example
* // Selects the 2nd option (index 1) from a dropdown with ID '#operationSelect'
* await selectOperationOption(fixture, 1, '#operationSelect');
* 
* @returns A promise that resolves when the fixture is stable after the selection
*/
export async function selectOperationOption(fixture: ComponentFixture<any>, optionIndex: number, operationId: string) {
    const operation = fixture.nativeElement.querySelector(operationId);
    if (operation) {
        operation.value = operation.options[optionIndex]?.value;
        operation.dispatchEvent(new Event('change'));
    }
    fixture.detectChanges();
    await fixture.whenStable();
}

/**
* Simulates user confirmation/cancellation of dialogs in Angular test environment.
* 
* <AUTHOR> 158648
* 
* @param selector - CSS selector for the dialog button to click (e.g., '#confirmButton', '#cancelButton')
* 
* Testing Workflow:
* 1. Locates dialog button using provided selector
* 2. Simulates native click event on the button
* 3. Triggers Angular change detection
* 4. Waits for async operations to complete
* 
* Used to test dialog interactions like confirmation popups
*/
export async function conformDialog(fixture: any, selector: string) {
    const okBtnActive = document.querySelector<HTMLElement>(selector);
    if (okBtnActive) {
        // Simulate the button click for the updated state
        okBtnActive.click();

        fixture.detectChanges();
        await fixture.whenStable();
    }
}

/**
* Simulates clicking a checkbox or button in Angular tests and waits for the fixture to stabilize.
* 
* <AUTHOR>
* @param fixture - The Angular component fixture used for testing
* @param checkboxId - The CSS selector for the checkbox element
* 
* @example
* // Clicks a checkbox with ID '#deviceId222'
* await clickOnButtonOrCheckBox(fixture, '#deviceId222');
* 
* @returns A promise that resolves when the fixture is stable after the click
*/
export async function clickOnButtonOrCheckBox(fixture: ComponentFixture<any>, checkboxId: string) {
    const selectCheckbox = fixture.debugElement.query(By.css(checkboxId)).nativeElement;
    selectCheckbox.click();
    fixture.detectChanges();
    await fixture.whenStable();
}
// Create simulated server error response
export const mockError = new HttpErrorResponse({
    status: 500,
    statusText: 'Server Error'
});

/** 
 * Common function to test error handling in API calls
 * @param methodName - The API method to be mocked (e.g., 'getVideo', 'updateVideo')
 * @param action - The function in the component that should trigger the API call
 */
export async function testErrorHandling(methodName: any, action: () => void, exceptionHandlingService: any, toastrServiceMock: any, fixture: ComponentFixture<any>) {
    // Explicitly cast to jasmine.Spy
    (methodName as jasmine.Spy)?.and.returnValue(throwError(() => mockError));

    spyOn(exceptionHandlingService, 'customErrorMessage').and.callThrough();

    // Act: Execute the function that triggers the API call
    action();
    fixture.detectChanges();
    await fixture.whenStable();

    // Assert: Check if error handling was performed correctly
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
}

export const BASE_URL = 'https://localhost:8080/'
export const apiProviders = [
    { provide: API_BASE_URL, useValue: BASE_URL },
    { provide: API_SSO_BASE_URL, useValue: 'http://example.com/sso' },
    { provide: SSO_REGISTRATION_ID, useValue: 'your_registration_id' },
    { provide: RDM_ENVIRONMENT, useValue: 'RDM_ENVIRONMENT_VALUE' },
];

export const versionProviders = [
    { provide: RDM_VERSION, useValue: '1.0.0' },
];
export function commonsProviders(toastrServiceObject: jasmine.SpyObj<ToastrService>) {
    return [
        { provide: ToastrService, useValue: toastrServiceObject },
        ...apiProviders,
        ...versionProviders,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting()
    ];
}

export function simulateApiError(): Observable<never> {
    const error = new Error('API Error');
    return throwError(() => error);
}

export const countryListResponse: Array<CountryListResponse> = [
    {
        id: 73,
        country: "Algeria",
        isDisabled: false,
        languages: null
    },
    {
        id: 45,
        country: "Argentina",
        isDisabled: false,
        languages: null
    },
    {
        id: 7,
        country: "Australia",
        isDisabled: false,
        languages: null
    },
    {
        id: 18,
        country: "Austria",
        isDisabled: false,
        languages: null
    },
    {
        id: 57,
        country: "Belgium",
        isDisabled: false,
        languages: null
    }
];

export const getLanguageListResponse = [{
    "id": 1,
    "name": "English",
    "displayName": "English",
    "shotName": "en"
}, {
    "id": 2,
    "name": "Danish",
    "displayName": "Danish",
    "shotName": "da"
}, {
    "id": 3,
    "name": "Spanish",
    "displayName": "Spanish",
    "shotName": "es"
}, {
    "id": 4,
    "name": "German",
    "displayName": "German",
    "shotName": "de"
}, {
    "id": 5,
    "name": "Chinese",
    "displayName": "Chinese",
    "shotName": "zh"
}, {
    "id": 6,
    "name": "Dutch",
    "displayName": "Dutch",
    "shotName": "nl"
}, {
    "id": 7,
    "name": "Italian",
    "displayName": "Italian",
    "shotName": "it"
}];

export const rolePermissions: Array<RolePermissionResponse> = [
    new RolePermissionResponse(48, "Audit Reader", "Read the Audit Logs (Listing and View Activity Pop-Up)", "AUDIT"),
    new RolePermissionResponse(66, "Add Country", "Permission used to add new country", "COUNTRY"),
    new RolePermissionResponse(68, "Country Admin", "Manage the Country and explicitly user will get all country permission", "COUNTRY"),
    new RolePermissionResponse(32, "Country Reader", "Read the Country Information", "COUNTRY"),
    new RolePermissionResponse(6, "Associate Sales Order / Customer info To Device", "Associate the Device with Sales Order / Customer info. Note - This permission gives an privileges to the user to override the Sales Order / Customer info to the device hence only recommended to Super Admin user And explicitly user will get the Device Reader Permission", "DEVICES"),
    new RolePermissionResponse(60, "Device - Update Read & Edit Access", "Permission to read and edit the details of device functionality", "DEVICES"),
    new RolePermissionResponse(1, "Device Admin", "Manage all Device actions and explicitly user will get the Device Reader Permission", "DEVICES"),
    new RolePermissionResponse(4, "Device Lock/Unlock", "Update Lock/Unlock state of the Device and explicitly user will get the Device Reader Permission", "DEVICES"),
    new RolePermissionResponse(2, "Device Reader", "Read Device Information (Listing and Detail Page)", "DEVICES"),
    new RolePermissionResponse(3, "Device Type Update", "Update the Device Type and explicitly user will get the Device Reader Permission", "DEVICES"),
    new RolePermissionResponse(51, "Disable Device", "Update Devices as Disabled and explicitly user will get the Device Reader Permission", "DEVICES"),
    new RolePermissionResponse(52, "RMA Device", "Update Devices as RMA and explicitly user will get the Device Reader Permission", "DEVICES"),
    new RolePermissionResponse(31, "Job Reader", "Read Job Information (Listing and Detail Page)", "JOBS"),
    new RolePermissionResponse(41, "Bridge Kit Management Admin", "Manage the Bridge Kits via CSV import and explicitly user will get the Bridge Kit Management Reader Permission", "KIT MANAGEMENT"),
    new RolePermissionResponse(42, "Bridge Kit Management Reader", "Read the Bridge Kits. (Listing and Detail Page)", "KIT MANAGEMENT"),
    new RolePermissionResponse(64, "OTS Kit Management Admin", "Manage the OTS Kits via CSV import and explicitly user will get the OTS Kit Management Reader Permission", "KIT MANAGEMENT"),
    new RolePermissionResponse(65, "OTS Kit Management Reader", "Read the OTS Kits. (Listing and Detail Page)", "KIT MANAGEMENT"),
    new RolePermissionResponse(30, "Device Log Reader", "Read / Download the Logs uploaded from Device", "LOGS"),
    new RolePermissionResponse(62, "Add Probe Config Group", "Add new Probe Config Group and explicitly user will get the Probe Config Group Reader Permission", "PROBE CONFIG GROUP"),
    new RolePermissionResponse(63, "Probe Config Group Admin", "Manage all Probe Config Group actions and explicitly user will get the Probe Config Group Permission", "PROBE CONFIG GROUP"),
    new RolePermissionResponse(59, "Probe Config Group Reader", "Read Probe Config Group (Listing and Detail Page)", "PROBE CONFIG GROUP"),
    new RolePermissionResponse(9, "Add Probe", "Create New Probe. Note - This permission gives an privileges to the user to create probe outside the sales order module hence only recommended to Super Admin user And explicitly user will get the Probe Reader Permission", "PROBES"),
    new RolePermissionResponse(50, "Associate Sales Order / Customer Info To Probe", "Associate the Probe with Sales Order / Customer info. Note - This permission gives an privileges to the user to override the Sales Order / Customer info to the Probe hence only recommended to Super Admin user And explicitly user will get the Probe Reader Permission", "PROBES"),
    new RolePermissionResponse(10, "Configure License To Probe", "Associate Configure License with Probe. Note - This permission gives an privileges to the user to update the license information for the probe And explicitly user will get the Probe Reader Permission", "PROBES"),
    new RolePermissionResponse(53, "Disable Probe", "Update Probes as Disabled and explicitly user will get the Probe Reader Permission", "PROBES"),
    new RolePermissionResponse(61, "Probe - Update Read & Edit Access", "Permission to read and edit the details of probe functionality", "PROBES"),
    new RolePermissionResponse(7, "Probe Admin", "Manage all Probe actions and explicitly user will get the Probe Reader Permission", "PROBES"),
    new RolePermissionResponse(58, "Probe Lock/Unlock", "Update Lock/Unlock state of the Probe and explicitly user will get the Probe Reader Permission", "PROBES"),
    new RolePermissionResponse(8, "Probe Reader", "Read Probe Information (Listing and Detail Page)", "PROBES"),
    new RolePermissionResponse(40, "Probe Set Reminder", "Set Reminders for the Probes to notify on device about the expiry of feature licenses while configuring an license and explicitly user will get the Probe Reader Permission", "PROBES"),
    new RolePermissionResponse(54, "RMA Probe", "Update Probes as RMA and explicitly user will get the Probe Reader Permission", "PROBES"),
    new RolePermissionResponse(49, "Update Probe Type(s)", "Update the Probe Type. Note - This permission gives an privileges to the user to update the existing probe type for the probes hence only recommended to Super Admin user And explicitly user will get the Probe Reader Permission", "PROBES"),
    new RolePermissionResponse(20, "Add Role", "Create new Role and explicitly user will get Role Reader Permission", "ROLE"),
    new RolePermissionResponse(18, "Role Admin", "Manage all Role actions and explicitly user will get Role Reader Permission.", "ROLE"),
    new RolePermissionResponse(19, "Role Reader", "Read the Roles (Listing and Detail Page)", "ROLE"),
    new RolePermissionResponse(21, "Update Role", "Update the Role and explicitly user will get Role Reader Permission", "ROLE"),
    new RolePermissionResponse(44, "Sales Order - Admin", "Manage all Sales Order actions and explicitly user will get Sales Order Reader Permission.", "SALES ORDER"),
    new RolePermissionResponse(55, "Sales Order - Configure Bridge / Probe with SO", "Configure Bridge / Probe with Sales Order functionality in Programmer and explicitly user will get Sales Order Reader Permission.", "SALES ORDER"),
    new RolePermissionResponse(57, "Sales Order - Configure Bridge / Probe without SO (Skip)", "Configure Bridge / Probe with Skip Sales Order functionality in Programmer.", "SALES ORDER"),
    new RolePermissionResponse(56, "Sales Order - Configure Probe", "Manage the probe configurations for a sales order and explicitly user will get Sales Order Reader Permission.", "SALES ORDER"),
    new RolePermissionResponse(45, "Sales Order - Reader", "Read the Sales Orders (Listing and Detail Page) and explicitly user will get Device Reader Permission, Probe Reader Permission and Kit Management Reader Permission", "SALES ORDER"),
    new RolePermissionResponse(11, "Software Build Admin", "Manage all Software Build actions and explicitly user will get the Software Build Reader Permission.", "SOFTWARE BUILDS"),
    new RolePermissionResponse(12, "Software Build Reader", "Read the Software Builds.", "SOFTWARE BUILDS"),
    new RolePermissionResponse(13, "Update Software Build", "Update the details of the Software Build explicitly and explicitly user will get the Software Build Reader Permission", "SOFTWARE BUILDS"),
    new RolePermissionResponse(24, "Add User", "Create new User and explicitly user will get the User Reader Permission", "USERS"),
    new RolePermissionResponse(25, "Update User", "Update the User and explicitly user will get the User Reader Permission", "USERS"),
    new RolePermissionResponse(22, "User Admin", "Manage all User actions and explicitly user will get the User Reader Permission", "USERS"),
    new RolePermissionResponse(23, "User Reader", "Read the Users (Listing and Detail Page)", "USERS"),
    new RolePermissionResponse(28, "Add Video", "Upload new Video / JSON and explicitly user will get Video Reader Permission", "VIDEOS"),
    new RolePermissionResponse(29, "Update Video", "Update the Video / JSON and explicitly user will get Video Reader Permission", "VIDEOS"),
    new RolePermissionResponse(26, "Video Admin", "Manage the Videos and explicitly user will get the Video Reader Permission", "VIDEOS"),
    new RolePermissionResponse(27, "Video Reader", "Read the Videos.", "VIDEOS")
];

export const probeTypeResponse: Array<ProbeTypeResponse> = [
    new ProbeTypeResponse(
        4,
        "Torso1, USB",
        "Torso1, USB",
        "T1A",
        [
            new ProbeFeatureResponse(6, "Trio", "Trio", 6, [
                new FeaturePartNumberResponse("P007651-001", "PERPETUAL" as ValidityEnum, false, true, null),
                new FeaturePartNumberResponse("P008420-001", "ONE_YEAR" as ValidityEnum, false, true, 10),
            ]),
            new ProbeFeatureResponse(1, "PW Doppler", "PW Doppler", 1, [
                new FeaturePartNumberResponse("P008420-001", "PERPETUAL" as ValidityEnum, false, true, 91),
                new FeaturePartNumberResponse("P008420-001", "ONE_YEAR" as ValidityEnum, false, true, 10),
            ]),
            new ProbeFeatureResponse(5, "AI Fast", "AI Fast", 1, [
                new FeaturePartNumberResponse("P007651-001", "PERPETUAL" as ValidityEnum, false, true, null),
            ]),
        ],
        [
            new ProbePresetResponse(1, "Heart", "Heart", 1, [
                new PresetPartNumberResponse("P007786-001", "PERPETUAL" as ValidityEnum, false, true, 3),
                new PresetPartNumberResponse("P007786-002", "ONE_YEAR" as ValidityEnum, false, true, 10),
            ]),
        ]
    ),
    new ProbeTypeResponse(
        3,
        "Lexsa",
        "Lexsa",
        "L1A",
        [
            new ProbeFeatureResponse(1, "PW Doppler", "PW Doppler", 1, [
                new FeaturePartNumberResponse("P007935-001", ValidityEnum.PERPETUAL, false, true, 9),
                new FeaturePartNumberResponse("P007935-002", ValidityEnum.ONE_YEAR, false, true, 16),
            ]),
        ],
        [
            new ProbePresetResponse(6, "Msk", "Msk", 6, [
                new PresetPartNumberResponse(null, ValidityEnum.PERPETUAL, true, false, 98),
            ]),
            new ProbePresetResponse(7, "Nerve", "Nerve", 7, [
                new PresetPartNumberResponse(null, ValidityEnum.PERPETUAL, true, false, 99),
            ]),
        ]
    ),
    new ProbeTypeResponse(
        1,
        "Torso1",
        "Torso1",
        "T1B",
        [
            new ProbeFeatureResponse(5, "AI Fast", "AI Fast", 1, [
                new FeaturePartNumberResponse("P007651-001", ValidityEnum.PERPETUAL, false, true, null),
            ]),
        ],
        [
            new ProbePresetResponse(1, "Heart", "Heart", 1, [
                new PresetPartNumberResponse(null, ValidityEnum.PERPETUAL, true, false, 102),
            ]),
            new ProbePresetResponse(2, "Lungs Torso", "Lungs Torso", 2, [
                new PresetPartNumberResponse(null, ValidityEnum.PERPETUAL, true, false, 103),
            ]),
            new ProbePresetResponse(3, "Abdomen", "Abdomen", 3, [
                new PresetPartNumberResponse(null, ValidityEnum.PERPETUAL, true, false, 104),
            ]),
        ]
    ),
    new ProbeTypeResponse(
        2,
        "Torso3",
        "Torso3",
        "T3B",
        [
            new ProbeFeatureResponse(5, "AI Fast", "AI Fast", 2, [
                new FeaturePartNumberResponse("P007651-002", ValidityEnum.PERPETUAL, false, true, null),
            ]),
        ],
        [
            new ProbePresetResponse(1, "Heart", "Heart", 1, [
                new PresetPartNumberResponse(null, ValidityEnum.PERPETUAL, true, false, 107),
            ]),
            new ProbePresetResponse(2, "Lungs Torso", "Lungs Torso", 2, [
                new PresetPartNumberResponse(null, ValidityEnum.PERPETUAL, true, false, 108),
            ]),
            new ProbePresetResponse(3, "Abdomen", "Abdomen", 3, [
                new PresetPartNumberResponse(null, ValidityEnum.PERPETUAL, true, false, 109),
            ]),
        ]
    )
];

export const presetListResponse: Array<PresetDetailBaseResponse> = [
    new PresetDetailBaseResponse(1, 'Heart'),
    new PresetDetailBaseResponse(7, 'Nerve'),
    new PresetDetailBaseResponse(10, 'Lungs Lexsa'),
    new PresetDetailBaseResponse(5, 'Ob'),
    new PresetDetailBaseResponse(6, 'Msk'),
    new PresetDetailBaseResponse(2, 'Lungs Torso'),
    new PresetDetailBaseResponse(4, 'Bladder'),
    new PresetDetailBaseResponse(8, 'Vascular'),
    new PresetDetailBaseResponse(9, 'Gyn'),
    new PresetDetailBaseResponse(3, 'Abdomen'),
];

export const featuresListResponse: Array<ProbeFeatureResponse> = [
    new ProbeFeatureResponse(2, 'CW Doppler', 'CW Doppler', 2, []),
    new ProbeFeatureResponse(3, 'Auto EF', 'Auto EF', 3, []),
    new ProbeFeatureResponse(9, 'Trio 2.0 (Educational)', 'Trio 2.0 (Educational)', 9, []),
    new ProbeFeatureResponse(8, 'Auto Doppler', 'Auto Doppler', 8, []),
    new ProbeFeatureResponse(4, 'TDI', 'TDI', 4, []),
    new ProbeFeatureResponse(7, 'Auto Preset', 'Auto Preset', 7, []),
    new ProbeFeatureResponse(6, 'Trio 2.0', 'Trio 2.0', 6, []),
    new ProbeFeatureResponse(1, 'PW Doppler', 'PW Doppler', 1, []),
    new ProbeFeatureResponse(5, 'AI Fast', 'AI Fast', 5, []),
];

export const presetResponse: Array<ProbePresetResponse> = [
    new ProbePresetResponse(1, 'Heart', 'Heart', 1, []),
    new ProbePresetResponse(7, 'Nerve', 'Nerve', 7, []),
    new ProbePresetResponse(10, 'Lungs Lexsa', 'Lungs Lexsa', 10, []),
    new ProbePresetResponse(5, 'Ob', 'Ob', 5, []),
    new ProbePresetResponse(6, 'Msk', 'Msk', 6, []),
    new ProbePresetResponse(2, 'Lungs Torso', 'Lungs Torso', 2, []),
    new ProbePresetResponse(4, 'Bladder', 'Bladder', 4, []),
    new ProbePresetResponse(8, 'Vascular', 'Vascular', 8, []),
    new ProbePresetResponse(9, 'Gyn', 'Gyn', 9, []),
    new ProbePresetResponse(3, 'Abdomen', 'Abdomen', 3, []),
];


