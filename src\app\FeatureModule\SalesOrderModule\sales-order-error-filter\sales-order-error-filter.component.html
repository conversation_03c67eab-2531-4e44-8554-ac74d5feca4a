<!--####################################################-->
<!--Filter Start-->
<!--Sales Order Filter--->
<!--####################################################-->
<form id="salesOrderErrorFilterform" role="form" class="form" [formGroup]="filterSalesOrderFailedForm">

    <!------------------------------------->
    <!-----------Order Number-------------->
    <!------------------------------------->
    <div class="form-group">
        <label class="form-control-label" for="field_order_number"><strong>Order
                Number</strong></label>
        <input class="form-control" type="text" formControlName="salesOrderNumber" />
        <div *ngIf="(filterSalesOrderFailedForm.get('salesOrderNumber').touched || filterSalesOrderFailedForm.get('salesOrderNumber').dirty) &&  
                filterSalesOrderFailedForm.get('salesOrderNumber').invalid">
            <div *ngIf="filterSalesOrderFailedForm.get('salesOrderNumber').errors['maxlength']">
                <span class="alert-color font-12">{{small_textBoxMaxLengthMessage}}</span>
            </div>
            <div *ngIf="filterSalesOrderFailedForm.get('salesOrderNumber').errors['pattern']">
                <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
            </div>
        </div>
    </div>

    <hr class="mt-1 mb-2">
    <!--####################################################-->
    <!---------Action Button Start------->
    <!--####################################################-->
    <div class="">
        <button class="btn btn-sm btn-orange mr-3" (click)="searchData()" id="salesOrderFailSearchBtn"
            [disabled]="filterSalesOrderFailedForm.invalid">Search</button>
        <button class="btn btn-sm btn-orange"
            (click)="clearFilter(defaultListingPageReloadSubjectParameter)">Clear</button>
    </div>
    <!--####################################################-->
    <!---------Action Button End------->
    <!--####################################################-->
</form>
<!--####################################################-->
<!--Filter End-->
<!--Sales Order Filter--->
<!--####################################################-->