<!-- loading start -->
<!-- loading gif start -->
<div class="ringLoading" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!-- loading gif end -->
<!-- loading end -->

<body>
    <!--row start-->
    <div class="row">
        <!--Filter start-->
        <div class="col-md-3 pr-0" id="countryFilterBtn" *ngIf="(!isFilterHidden)">
            <label class="col-md-12 h5-tag">Filter</label>
            <div class="card mt-3">
                <div class="card-body">
                    <app-country-filter [isFilterComponentInitWithApicall]="isFilterComponentInitWithApicall"
                        [countryRequestBody]="countryRequestBody"></app-country-filter>
                </div>
            </div>
        </div>
        <!--Filter End-->


        <!--Table block start-->
        <div [className]="(isFilterHidden)?'col-md-12 pr-0':'col-md-9 pr-0'">
            <div class="container-fluid">
                <!--############################################################-->
                <!--############################################################-->
                <div class="row" class="headerAlignment">
                    <!--############################################################-->
                    <!--Left side-->
                    <!--############################################################-->
                    <div class="childFlex">
                        <!----------------------------------------------->
                        <!--Show/Hide filter-->
                        <!----------------------------------------------->
                        <div class="dropdown" id="hideShowFilter">
                            <button class="btn btn-sm btn-orange mr-3 ml-0 filter-symbol" (click)="toggleFilter()"
                                id="countryListHideShowButton">
                                <i class="fas fa-filter" aria-hidden="true"></i>
                                &nbsp;&nbsp;{{hideShowFilterButtonText}}
                            </button>
                        </div>
                        <!----------------------------------------------->
                        <!--Pagination-->
                        <!----------------------------------------------->
                        <div>
                            <label class="mb-0">Show entry</label>
                            <select [(ngModel)]="drpselectsize" class="form-control form-control-sm"
                                (change)="changeDataSize($event)" id="countryListShowEntry">
                                <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                                    <option [value]="dataSize">{{dataSize}}</option>
                                </ng-template>
                            </select>
                        </div>
                    </div>
                    <!--Right side-->
                    <!--Refresh-->
                    <div class="childFlex">

                        <!------------------------------------------------->
                        <!--------------Operations------------------->
                        <!------------------------------------------------->
                        <ng-template [ngIf]="deleteCountryPermission">
                            <!-- delete button start -->
                            <button class="btn btn-sm btn-outline-secondary mr-3" (click)="deleteCountry()"
                                id="deleteCountry"><i class="fa fa-trash"
                                    aria-hidden="true"></i>&nbsp;&nbsp;Delete</button>
                            <!-- delete button end -->
                        </ng-template>

                        <div *ngIf="addCountryPermission">
                            <button class="btn btn-sm btn-orange mr-3" id="AddCountry" (click)="addCountry()">
                                <em class="fa fa-plus"></em> &nbsp;&nbsp;New Country
                            </button>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-orange" id="refresh_countryList" (click)="refreshFilter()"><em
                                    class="fa fa-refresh">
                                </em></button>
                        </div>
                    </div>
                    <!--Refresh End-->
                </div>

                <!-- selected Country List start -->
                <div>Total {{totalRecord}} Countries
                    <p *ngIf="selectedCountriesIdList != null && selectedCountriesIdList.length>0">
                        <strong>{{selectedCountriesIdList.length}} Country selected</strong>
                    </p>
                </div>
                <!-- selected Country List end -->

                <!--Table starts-->
                <div class="commonTable">
                    <table class="table table-sm table-bordered" style="overflow-x: scroll;" aria-hidden="true">
                        <!--Header start-->
                        <thead>
                            <tr class="thead-light">
                                <th class="checkox-table width-unset" *ngIf="showCheckBox && deleteCountryPermission">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" name="chkselectall"
                                            [id]="selectAllCheckboxId"
                                            (change)="selectAllItem($any($event.target)?.checked)">
                                        <label class="custom-control-label" [for]="selectAllCheckboxId"></label>
                                    </div>
                                </th>
                                <th><span>Countries</span></th>
                                <th><span>Languages</span></th>
                            </tr>
                        </thead>
                        <!--Header End-->

                        <!--Body Start-->
                        <tbody>
                            <tr *ngFor="let countryObj of countryResponseList;let countryIndex=index">
                                <td class="width-unset" *ngIf="showCheckBox && deleteCountryPermission">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input"
                                            [id]="chkPreFix+countryObj.id+chkPreFix" [name]="checkboxListName"
                                            (change)="selectCheckbox(countryObj,$any($event.target)?.checked)"
                                            [checked]="selectedCountriesIdList.includes(countryObj.id)">
                                        <label class="custom-control-label"
                                            [for]="chkPreFix+countryObj.id+chkPreFix"></label>
                                    </div>
                                </td>
                                <td><span>{{countryObj?.country}}</span></td>
                                <td><span>{{countryObj?.languages | printListPipe}}</span></td>
                            </tr>
                        </tbody>
                        <!--Body End-->
                    </table>
                    <!--Table End-->
                </div>
                <!--Display Pagination and total items-->
                <div>
                    <div>Showing {{totalRecordDisplay}} out of {{totalRecord}} Countries</div>
                    <div class="float-right">
                        <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="itemsPerPage"
                            [maxSize]="5" [rotate]="true" [boundaryLinks]="true" (pageChange)="loadPage(page)">
                        </ngb-pagination>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>