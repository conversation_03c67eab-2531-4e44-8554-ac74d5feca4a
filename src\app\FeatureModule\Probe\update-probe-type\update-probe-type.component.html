<!-------------------------------------------------->
<!-------------------------------------------------->
<!------------UPDATE PROBE TYPE--------------------->
<!-------------------------------------------------->
<!-------------------------------------------------->
<div>
    <!----------------------Header Start------------------->
    <div class="modal-header">
        <label class="modal-title">{{ title }}</label>
    </div>
    <!----------------------Body Start------------------->
    <div class="modal-body">
        <div>
            <!-------------------Form Start------------------>
            <!--1 Probe Type-->
            <!----------------------------------------------->
            <form name='fileinfo' [formGroup]="probeForm">
                <div class="row mb-1">
                    <div class="col-4 pr-0">
                        <label><strong class="textlabel">Probe Type</strong></label>
                    </div>
                    <div class="col-8 pl-0">
                        <div class="form-group">
                            <select class="form-control form-control-sm form_dropdown" name="probeType"
                                id="updateProbeType" (change)="updateValidatation()" formControlName="probeType">
                                <ng-template ngFor let-probeTypeObject [ngForOf]="probeTypeResponse">
                                    <option [value]="probeTypeObject.probeTypeId">
                                        {{probeTypeObject.displayName}}
                                    </option>
                                </ng-template>
                            </select>
                        </div>
                    </div>
                </div>
            </form>
            <!-------------------Form End------------------>
        </div>
    </div>
    <!----------------------Body end----------------------->

    <!----------------------Footer Start------------------->
    <div class="modal-footer">
        <button type="button" class="btn btn-sm btn-outline-secondary" id="declineProbeUpdateBtn" (click)="decline()">{{
            btnCancelText }}</button>
        <button type="button" class="btn btn-sm btn-orange" id="uploadBtn" (click)="accept()"
            [disabled]="probeForm.invalid">{{
            btnOkText }}</button>
    </div>
    <!----------------------Footer End------------------->
</div>