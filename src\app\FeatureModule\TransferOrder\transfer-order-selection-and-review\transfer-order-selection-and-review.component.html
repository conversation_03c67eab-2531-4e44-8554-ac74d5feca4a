<div *ngIf="tranferOrderSelectionDisaplay">
    <!------------------------------------------->
    <!-- loading start -------------------------->
    <!------------------------------------------->
    <div class="ringLoading" *ngIf="loading">
        <!-- loading gif start -->
        <div class="ringLoadingDiv">
            <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
        </div>
        <!-- loading gif end -->
    </div>
    <!------------------------------------------->
    <!-- loading end ---------------------------->
    <!------------------------------------------->

    <body class="bg-white">
        <!------------------------------------------->
        <!-- container fluid start -->
        <!------------------------------------------->
        <div class="container-fluid" id="salesOrderSelectionPage" *ngIf="tranferOrderData !== null">
            <!-- row start -->
            <div class="row">
                <div class="col-md-12">
                    <div class="row" class="headerAlignment">
                        <div>
                            <label class="childFlex h5-tag">Transfer Order - Select Products {{deviceSerialNumber}}
                            </label>
                        </div>

                        <div class="childFlex">

                            <!-------------------------------------------->
                            <!-- back button div start -->
                            <button class="btn btn-sm btn-outline-secondary role-back-btn  mr-3"
                                id="salesOrderDetailBack" (click)="back()"><i class="fa fa-reply"
                                    aria-hidden="true"></i>&nbsp;&nbsp;Back</button>
                            <!-- back button div end -->
                            <!-------------------------------------------->

                            <!-------------------------------------------->
                            <!-- Refresh button Start ------>
                            <button class="btn btn-sm btn-orange" id="salesOrderRefresh" (click)="initData()"><em
                                    class="fa fa-refresh"></em></button>
                            <!-------------------------------------------->
                            <!-- Refresh button End ------>
                        </div>
                    </div>

                    <div>
                        <label class="text-primary">
                            {{ transferProductDetail?.serialNumber |
                            transferOrderMessageDisplay:tranferOrderData.destinationSalesOrderIsManual }}
                        </label>
                    </div>

                    <!---##############################################################-->
                    <!---##############################################################-->
                    <!-----------------Source SalesOrder Product------------------------->
                    <!---##############################################################-->
                    <!---##############################################################-->

                    <div class="row">
                        <div class="col-md-12">
                            <!-- main card start -->
                            <div class="card">
                                <div class="card-body">
                                    <div class="card shadow">
                                        <div class="card-body">
                                            <!-- ------------------------------------------------------- -->
                                            <!-- ------Source Sales Order Probe Table start ------------ -->
                                            <!-- ------------------------------------------------------- -->
                                            <div class="container-fluid ml-4 mr-4">
                                                <label class="mb-1 h5-tag"><span>Transfer Order -
                                                        {{tranferOrderData?.sourceSalesOrder?.salesOrderNumber}} -
                                                        {{tranferOrderData?.sourceSalesOrder?.countryDisplayName}}</span></label>
                                                <hr class="hrMargin">
                                                <ng-template
                                                    [ngIf]="tranferOrderData?.sourceSalesOrder?.product?.probes !== null">
                                                    <label class="mb-1 h5-tag"><span>Probe(s)</span></label>
                                                    <hr class="hrMargin">
                                                    <!-------------------------------------->
                                                    <!---------------Total Probe(s)-------->
                                                    <div class="bottomMargin-5">Total
                                                        {{tranferOrderData?.sourceSalesOrder?.product?.probes?.length}}
                                                        Transfer Order Probe(s)</div>
                                                    <div class="salesSelectionTable">
                                                        <table class="table table-sm table-bordered mr-1"
                                                            id="relatedProduct" aria-hidden="true">
                                                            <!-- table header start -->
                                                            <thead>
                                                                <tr class="thead-light">
                                                                    <th class="checkox-table width-unset"
                                                                        *ngIf="(tranferOrderData.destinationSalesOrderIsManual && !selectAllProbeCheckboxDisplay)">
                                                                        <div class="custom-control custom-checkbox">
                                                                            <input type="checkbox"
                                                                                class="custom-control-input"
                                                                                name="chkselectall"
                                                                                [id]="probeSelectAllCheckboxId"
                                                                                (change)="selectAllProbeItem($any($event.target)?.checked)">
                                                                            <label class="custom-control-label"
                                                                                [for]="probeSelectAllCheckboxId"></label>
                                                                        </div>
                                                                    </th>
                                                                    <th class="nowrap">Sr. No.</th>
                                                                    <th class="nowrap">Probe Type</th>
                                                                    <th class="nowrap">Part Number</th>
                                                                    <th class="nowrap">Bridge Kit Part Number</th>
                                                                    <th class="nowrap">OTS Kit Part Number</th>
                                                                    <th class="nowrap">Preset(s)</th>
                                                                    <th class="nowrap">Feature(s)</th>
                                                                    <th class="nowrap">Config Groups(s)</th>
                                                                    <th class="nowrap max_width">Serial Number</th>
                                                                    <th class="nowrap">Transferred Order Number</th>
                                                                    <th class="nowrap">Status</th>
                                                                </tr>
                                                            </thead>
                                                            <!-- table header end -->
                                                            <!-- table body start -->
                                                            <tbody>
                                                                <tr
                                                                    *ngFor="let tranferUserProbe of tranferOrderData?.sourceSalesOrder?.product?.probes; let probeIndex = index">
                                                                    <td class="width-unset"
                                                                        *ngIf="tranferOrderData.destinationSalesOrderIsManual && !selectAllProbeCheckboxDisplay">
                                                                        <div class="custom-control custom-checkbox"
                                                                            *ngIf="tranferUserProbe.entityStatus| hideAndShowTransferOrderCheckBox">
                                                                            <input type="checkbox"
                                                                                class="custom-control-input"
                                                                                [id]="probeChkPreFix+tranferUserProbe.sopmId+probeChkPreFix"
                                                                                [name]="probeCheckboxListName"
                                                                                (change)="selectProbeCheckbox(tranferUserProbe,$any($event.target)?.checked)"
                                                                                [checked]="selectedSalesOrderProbeIdList.includes(tranferUserProbe.sopmId)">
                                                                            <label class="custom-control-label"
                                                                                [for]="probeChkPreFix+tranferUserProbe.sopmId+probeChkPreFix"></label>
                                                                        </div>
                                                                    </td>
                                                                    <td>{{probeIndex + 1}}</td>
                                                                    <td class="nowrap">{{tranferUserProbe.probeType}}
                                                                    </td>
                                                                    <td class="nowrap">
                                                                        {{tranferUserProbe.productCode}}</td>
                                                                    <td class="nowrap">
                                                                        {{tranferUserProbe.bridgeKitPartNumberCode}}
                                                                    </td>
                                                                    <td class="nowrap">
                                                                        {{tranferUserProbe.otsKitPartNumberCode}}
                                                                    </td>
                                                                    <td class="nowrap"
                                                                        [innerHTML]="tranferUserProbe.associatedPresets | featureInformationDisplayPipe">
                                                                    </td>
                                                                    <td class="nowrap"
                                                                        [innerHTML]="tranferUserProbe.associatedFeatures | featureInformationDisplayPipe">
                                                                    </td>
                                                                    <td class="nowrap">
                                                                        {{tranferUserProbe.probeConfigGroupPartNumberCodes}}
                                                                    </td>
                                                                    <td class="nowrap max_width">
                                                                        {{tranferUserProbe.entitySerialNumber}}
                                                                    </td>
                                                                    <td class="nowrap">
                                                                        {{tranferUserProbe.destinationSalesOrderNumber}}
                                                                    </td>
                                                                    <td class="nowrap">{{tranferUserProbe?.entityStatus
                                                                        |salesOrderStatusDisplay}}</td>
                                                                </tr>
                                                            </tbody>
                                                            <!-- table body end -->
                                                        </table>
                                                    </div>
                                                </ng-template>
                                            </div>
                                            <!-- ------------------------------------------------------- -->
                                            <!-- ------Source Sales Order Probe Table end-- ------------ -->
                                            <!-- ------------------------------------------------------- -->

                                            <!-- ------------------------------------------------------- -->
                                            <!-- ---------Source SalesOrder Bridge table start --------- -->
                                            <!-- ------------------------------------------------------- -->
                                            <div class="container-fuild ml-4 mr-4 px-3 mt-3"
                                                *ngIf="tranferOrderData?.sourceSalesOrder?.product?.bridges !== null">
                                                <label class="mb-1 h5-tag"><span>Bridge(s)</span>
                                                </label>
                                                <hr class="hrMargin">
                                                <!-------------------------------------->
                                                <!-------------------------------------->
                                                <!-------------------------------------->
                                                <!---------------Total Bridge(s)-------->
                                                <div class="bottomMargin-5">Total
                                                    {{tranferOrderData?.sourceSalesOrder?.product?.bridges?.length}}
                                                    Transfer Order Bridge(s)</div>
                                                <table class="table table-sm table-bordered" aria-hidden="true"
                                                    id="bridgeTable">
                                                    <!-- table header start -->
                                                    <thead>
                                                        <tr class="thead-light">
                                                            <th class="checkox-table width-unset"
                                                                *ngIf="tranferOrderData.destinationSalesOrderIsManual && !selectAllDeviceCheckboxDisplay">
                                                                <div class="custom-control custom-checkbox">
                                                                    <input type="checkbox" class="custom-control-input"
                                                                        name="chkselectall"
                                                                        [id]="deviceSelectAllCheckboxId"
                                                                        (change)="selectAllDeviceItem($any($event.target)?.checked)">
                                                                    <label class="custom-control-label"
                                                                        [for]="deviceSelectAllCheckboxId"></label>
                                                                </div>
                                                            </th>
                                                            <th class="nowrap">Sr. No.</th>
                                                            <th class="nowrap">Part Number</th>
                                                            <th class="nowrap">Bridge Kit Part Number</th>
                                                            <th class="nowrap max_width">Serial Number</th>
                                                            <th class="nowrap">Transferred Order Number</th>
                                                            <th class="nowrap">Status</th>
                                                        </tr>
                                                    </thead>
                                                    <!-- table header end -->
                                                    <!-- table body start -->
                                                    <tbody>
                                                        <tr
                                                            *ngFor="let tranferUserBridge of tranferOrderData?.sourceSalesOrder?.product?.bridges; let bridgeIndex = index">
                                                            <td class="width-unset"
                                                                *ngIf="tranferOrderData.destinationSalesOrderIsManual  && !selectAllDeviceCheckboxDisplay">
                                                                <div class="custom-control custom-checkbox"
                                                                    *ngIf="tranferUserBridge.entityStatus| hideAndShowTransferOrderCheckBox">
                                                                    <input type="checkbox" class="custom-control-input"
                                                                        [id]="deviceChkPreFix+tranferUserBridge.sopmId+deviceChkPreFix"
                                                                        [name]="deviceCheckboxListName"
                                                                        (change)="selectDeviceCheckbox(tranferUserBridge,$any($event.target)?.checked)"
                                                                        [checked]="selectedSalesOrderDeviceIdList.includes(tranferUserBridge.sopmId)">
                                                                    <label class="custom-control-label"
                                                                        [for]="deviceChkPreFix+tranferUserBridge.sopmId+deviceChkPreFix"></label>
                                                                </div>
                                                            </td>
                                                            <td class="nowrap">{{bridgeIndex + 1}}</td>
                                                            <td class="nowrap">
                                                                {{tranferUserBridge.productCode}}
                                                            </td>
                                                            <td class="nowrap">
                                                                {{tranferUserBridge.bridgeKitPartNumberCode}}</td>
                                                            <td class="nowrap max_width">
                                                                {{tranferUserBridge.entitySerialNumber}}
                                                            </td>
                                                            <td class="nowrap">
                                                                {{tranferUserBridge.destinationSalesOrderNumber}}
                                                            </td>
                                                            <td class="nowrap">
                                                                {{tranferUserBridge?.entityStatus |
                                                                salesOrderStatusDisplay}}</td>
                                                        </tr>
                                                    </tbody>
                                                    <!-- table body start -->
                                                </table>
                                            </div>
                                            <!-- ------------------------------------------------------- -->
                                            <!-- ---------sourceSalesOrder Bridge table End-- ---------- -->
                                            <!-- ------------------------------------------------------- -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!---##############################################################-->
                    <!---##############################################################-->
                    <!---------------Destination SalesOrder Product--------------------->
                    <!---##############################################################-->
                    <!---##############################################################-->

                    <div class="row">
                        <div class="col-md-12">
                            <!-- main card start -->
                            <div class="card">
                                <div class="card-body">
                                    <div class="card shadow">
                                        <div class="card-body">
                                            <!-- ------------------------------------------------------- -->
                                            <!-- -------destinationSalesOrder Probe table start -------- -->
                                            <!-- ------------------------------------------------------- -->
                                            <div class="container-fluid ml-4 mr-4">
                                                <label class="mb-1 h5-tag"><span>Transferred Order -
                                                        {{tranferOrderData?.destinationSalesOrder?.salesOrderNumber}} -
                                                        {{tranferOrderData?.destinationSalesOrder?.countryDisplayName}}</span></label>
                                                <hr class="hrMargin">
                                                <ng-template
                                                    [ngIf]="tranferOrderData?.destinationSalesOrder?.product?.probes !== null">
                                                    <label class="mb-1 h5-tag"><span>Probe(s)</span></label>
                                                    <hr class="hrMargin">
                                                    <!-------------------------------------->
                                                    <!---------------Total Probe(s)-------->
                                                    <div class="bottomMargin-5 table-responsive">Total
                                                        {{tranferOrderData?.destinationSalesOrder?.product?.probes?.length}}
                                                        Transferred Order Probe(s)</div>
                                                    <ng-container>
                                                        <form [formGroup]="formGroup" class="salesSelectionTable">
                                                            <ng-container formArrayName="probeSerialNumberList">
                                                                <table
                                                                    class="table table-sm table-bordered mr-1 overflow-scroll"
                                                                    id="relatedProduct" aria-hidden="true">
                                                                    <!-- table header start -->
                                                                    <thead>
                                                                        <tr class="thead-light">
                                                                            <th class="nowrap">Sr. No.</th>
                                                                            <th class="nowrap">Probe Type</th>
                                                                            <th class="nowrap">Part Number</th>
                                                                            <th class="nowrap">Bridge Kit Part Number
                                                                            </th>
                                                                            <th class="nowrap">OTS Kit Part Number</th>
                                                                            <th class="nowrap">Preset(s)</th>
                                                                            <th class="nowrap">Feature(s)</th>
                                                                            <th class="nowrap">Config Groups(s)</th>
                                                                            <th class="nowrap min_width">Serial Number
                                                                            </th>
                                                                            <th class="nowrap">Status</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <!-- table header end -->
                                                                    <!-- table body start -->
                                                                    <tbody>
                                                                        <tr
                                                                            *ngFor="let endUserProbe of tranferOrderData?.destinationSalesOrder?.product?.probes; let probeIndex = index">
                                                                            <td>{{probeIndex + 1}}</td>
                                                                            <td class="nowrap">
                                                                                {{endUserProbe.probeType}}
                                                                            </td>
                                                                            <td class="nowrap">
                                                                                {{endUserProbe.productCode}}</td>
                                                                            <td class="nowrap">
                                                                                {{endUserProbe.bridgeKitPartNumberCode}}
                                                                            </td>
                                                                            <td class="nowrap">
                                                                                {{endUserProbe.otsKitPartNumberCode}}
                                                                            </td>
                                                                            <td class="nowrap"
                                                                                [innerHTML]="endUserProbe.associatedPresets | featureInformationDisplayPipe">
                                                                            </td>
                                                                            <td class="nowrap"
                                                                                [innerHTML]="endUserProbe.associatedFeatures | featureInformationDisplayPipe">
                                                                            </td>
                                                                            <td class="nowrap">
                                                                                {{endUserProbe.probeConfigGroupPartNumberCodes}}
                                                                            </td>
                                                                            <td class="nowrap max_width drp_padding">
                                                                                <div [formGroupName]="probeIndex">
                                                                                    <ng-template
                                                                                        [ngIf]="endUserProbe.entitySerialNumber == null">
                                                                                        <select
                                                                                            class="form-control selectWidth"
                                                                                            formControlName="serialNumber"
                                                                                            (change)="probeSerialNumberChange(probeIndex,PROBE_SERIAL_NUMBER_LIST_CONTROL,tranferOrderData?.destinationSalesOrder?.product?.probes,$any($event.target)?.value,endUserProbe.sopmId)">
                                                                                            <option value="null">Select
                                                                                                Serial Number</option>
                                                                                            <option
                                                                                                *ngFor="let item of tranferOrderData.sourceSalesOrderProbeSerialNumbers[endUserProbe.probeType] || []"
                                                                                                [value]="item?.sopmId">
                                                                                                {{ item?.serialNumber }}
                                                                                            </option>
                                                                                        </select>

                                                                                        <div
                                                                                            *ngIf="(formGroup.get(PROBE_SERIAL_NUMBER_LIST_CONTROL)['controls'][probeIndex].get('serialNumber').touched
                                                                            || formGroup.get(PROBE_SERIAL_NUMBER_LIST_CONTROL)['controls'][probeIndex].get('serialNumber').dirty) &&
                                                                            formGroup.get(PROBE_SERIAL_NUMBER_LIST_CONTROL)['controls'][probeIndex].get('serialNumber').invalid">

                                                                                            <span class="alert-color"
                                                                                                id="SalesOrderDetailserialNumberExists"
                                                                                                *ngIf="(formGroup.get(PROBE_SERIAL_NUMBER_LIST_CONTROL)['controls'][probeIndex].get('serialNumber').errors['serialNumberexists'])">
                                                                                                Probe serial number
                                                                                                already
                                                                                                exists
                                                                                            </span>
                                                                                        </div>

                                                                                    </ng-template>
                                                                                    <ng-template
                                                                                        [ngIf]="endUserProbe.entitySerialNumber != null">
                                                                                        <span>{{endUserProbe.entitySerialNumber
                                                                                            }}</span>
                                                                                    </ng-template>
                                                                                </div>
                                                                            </td>
                                                                            <td class="nowrap">
                                                                                {{endUserProbe?.entityStatus
                                                                                |salesOrderStatusDisplay}}</td>

                                                                        </tr>
                                                                    </tbody>
                                                                    <!-- table body end -->
                                                                </table>
                                                            </ng-container>
                                                        </form>
                                                    </ng-container>
                                                </ng-template>
                                            </div>
                                            <!-- ------------------------------------------------------- -->
                                            <!-- -------destinationSalesOrder Probe table End ---------- -->
                                            <!-- ------------------------------------------------------- -->

                                            <!-- ------------------------------------------------------- -->
                                            <!-- ------destinationSalesOrder Bridge table start -------- -->
                                            <!-- ------------------------------------------------------- -->
                                            <div class="container-fuild ml-4 mr-4 px-3 mt-3"
                                                *ngIf="tranferOrderData?.destinationSalesOrder?.product?.bridges !== null">
                                                <label class="mb-1 h5-tag"><span>Bridge(s)</span>
                                                </label>
                                                <hr class="hrMargin">
                                                <!-------------------------------------->
                                                <!-------------------------------------->
                                                <!-------------------------------------->
                                                <!---------------Total Bridge(s)-------->
                                                <div class="bottomMargin-5">Total
                                                    {{tranferOrderData?.destinationSalesOrder?.product?.bridges?.length}}
                                                    Transferred Order Bridge(s)</div>
                                                <!-------------------------------------->
                                                <form [formGroup]="formGroup" class="salesSelectionTable">
                                                    <ng-container formArrayName="bridgeSerialNumberList">
                                                        <table class="table table-sm table-bordered" aria-hidden="true"
                                                            id="bridgeTable">
                                                            <!-- table header start -->
                                                            <thead>
                                                                <tr class="thead-light">
                                                                    <th class="nowrap">Sr. No.</th>
                                                                    <th class="nowrap">Part Number</th>
                                                                    <th class="nowrap">Bridge Kit Part Number
                                                                    </th>
                                                                    <th class="nowrap max_width">Serial Number</th>
                                                                    <th class="nowrap">Status</th>

                                                                </tr>
                                                            </thead>
                                                            <!-- table header end -->
                                                            <!-- table body start -->
                                                            <tbody>
                                                                <tr
                                                                    *ngFor="let endUserBridge of tranferOrderData?.destinationSalesOrder?.product?.bridges; let bridgeIndex = index">
                                                                    <td class="nowrap">{{bridgeIndex + 1}}</td>
                                                                    <td class="nowrap">
                                                                        {{endUserBridge.productCode}}</td>
                                                                    <td class="nowrap">
                                                                        {{endUserBridge.bridgeKitPartNumberCode}}
                                                                    </td>
                                                                    <td class="nowrap max_width drp_padding">
                                                                        <div [formGroupName]="bridgeIndex">
                                                                            <ng-template
                                                                                [ngIf]="endUserBridge.entitySerialNumber == null">
                                                                                <select class="form-control selectWidth"
                                                                                    formControlName="serialNumber"
                                                                                    id="transferredOrderBridgeDrp"
                                                                                    (change)="bridgeSerialNumberChange(bridgeIndex,BRIDGE_SERIAL_NUMBER_LIST_CONTROL,tranferOrderData?.destinationSalesOrder?.product?.bridges,$any($event.target)?.value,endUserBridge.sopmId)">
                                                                                    <option value="null">Select Serial
                                                                                        Number</option>
                                                                                    <option
                                                                                        *ngFor="let bridgeSerialnumber of tranferOrderData?.sourceSalesOrderBridgeSerialNumbers"
                                                                                        [value]="bridgeSerialnumber.sopmId">
                                                                                        {{
                                                                                        bridgeSerialnumber?.serialNumber
                                                                                        }}
                                                                                    </option>
                                                                                </select>
                                                                                <div
                                                                                    *ngIf="(formGroup.get(BRIDGE_SERIAL_NUMBER_LIST_CONTROL)['controls'][bridgeIndex].get('serialNumber').touched
                                                                || formGroup.get(BRIDGE_SERIAL_NUMBER_LIST_CONTROL)['controls'][bridgeIndex].get('serialNumber').dirty) &&
                                                                formGroup.get(BRIDGE_SERIAL_NUMBER_LIST_CONTROL)['controls'][bridgeIndex].get('serialNumber').invalid">

                                                                                    <span class="alert-color"
                                                                                        id="SalesOrderDetailserialNumberExists"
                                                                                        *ngIf="(formGroup.get(BRIDGE_SERIAL_NUMBER_LIST_CONTROL)['controls'][bridgeIndex].get('serialNumber').errors['serialNumberexists'])">
                                                                                        Bridge serial number already
                                                                                        exists
                                                                                    </span>
                                                                                </div>

                                                                            </ng-template>
                                                                            <ng-template
                                                                                [ngIf]="endUserBridge?.entitySerialNumber != null">
                                                                                <span>{{endUserBridge.entitySerialNumber
                                                                                    }}</span>
                                                                            </ng-template>
                                                                        </div>
                                                                    </td>
                                                                    <td class="nowrap">
                                                                        {{endUserBridge?.entityStatus
                                                                        |salesOrderStatusDisplay}}</td>
                                                                </tr>
                                                            </tbody>
                                                            <!-- table body start -->
                                                        </table>
                                                    </ng-container>
                                                </form>
                                            </div>
                                            <!-- ------------------------------------------------------- -->
                                            <!-- -------destinationSalesOrder Bridge table end --------- -->
                                            <!-- ------------------------------------------------------- -->
                                        </div>
                                    </div>
                                </div>

                                <!----------  Transfer Order Button Section Start ------ -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="card">
                                            <div class="card-body">
                                                <div class="card shadow">
                                                    <div class="card-body p-2">
                                                        <div class="row mt-3">
                                                            <div class="col-md-8 col-sm-8"></div>
                                                            <div
                                                                class="col-md-4 col-sm-4 d-flex align-items-end justify-content-end">
                                                                <!-------------------------------------------->
                                                                <!-- Review and Confirm button Div start ------>
                                                                <div class="multiprobe-div mr-3">
                                                                    <button class="btn btn-sm btn-orange w-100 mb-3"
                                                                        id="salesOrderDetailSaveAllSarialNumber"
                                                                        (click)="reviewProduct()"
                                                                        [disabled]="(formGroup?.invalid || (transferOrderRequest?.product?.bridges?.length === 0 && transferOrderRequest?.product?.probes?.length === 0)) || (selectedSalesOrderDeviceIdList === null || selectedSalesOrderProbeIdList === null)">
                                                                        Review and Confirm
                                                                    </button>
                                                                </div>
                                                                <!-------------------------------------------->
                                                                <!-- Review and Confirm button Div End -------->
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!----------  Transfer Order Button Section End ------ -->

                            </div>
                            <!-- main card end -->
                        </div>
                    </div>
                </div>
            </div>
            <!------------------------------------------->
            <!-- row end -->
            <!------------------------------------------->
        </div>
        <!------------------------------------------->
        <!-- container fluid end -->
        <!------------------------------------------->
    </body>
</div>