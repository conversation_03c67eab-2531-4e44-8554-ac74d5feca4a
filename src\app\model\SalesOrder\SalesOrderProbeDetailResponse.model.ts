import { ProductConfigStatus } from "src/app/shared/enum/SalesOrder/ProductConfigStatus.enum";
import { AssociatedConfigLicence } from "../associated-config-licence.model";
import { LicensesRequest } from "../probe/multiProbe/LicensesRequest.model";
import { ProbeRequest } from "../probe/multiProbe/ProbeRequest.model";
import { SalesOrderProductBaseDetailResponse } from "./SalesOrderProductBaseDetailResponse.model";

export class SalesOrderProbeDetailResponse extends SalesOrderProductBaseDetailResponse {
    reminder: boolean;
    enableFeatures: Array<LicensesRequest>;
    enablePresets: Array<LicensesRequest>;
    // UI-side properties (not in constructor)
    isEditMode: boolean;
    probe: ProbeRequest;
    apiErrorMessage: string;

    constructor( //NOSONAR
        sopmId: number,
        probeType: string,
        bridgeKitPartNumberCode: string,
        otsKitPartNumberCode: string,
        productCode: string,
        entitySerialNumber: string,
        entityPk: number,
        entityStatus: ProductConfigStatus,
        probeConfigGroupPartNumberCodes: Array<string>,
        associatedFeatures: Array<AssociatedConfigLicence>,
        associatedPresets: Array<AssociatedConfigLicence>,
        enableFeatures: Array<LicensesRequest>,
        enablePresets: Array<LicensesRequest>,
        transferredSalesOrderNumber: string,
        reminder: boolean
    ) {
        // Call to parent class constructor
        super(sopmId, entitySerialNumber, entityStatus, entityPk, probeType, bridgeKitPartNumberCode, otsKitPartNumberCode, productCode, transferredSalesOrderNumber, probeConfigGroupPartNumberCodes, associatedFeatures, associatedPresets);

        // Initialize properties from constructor arguments
        this.reminder = reminder;
        this.enableFeatures = enableFeatures;
        this.enablePresets = enablePresets;
    }
}
