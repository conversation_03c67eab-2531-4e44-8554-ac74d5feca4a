import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpResponse } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of } from 'rxjs';
import { transferOrderResponse, transferredOrderredResponse, transferSalesOrderProbeSerialNumbers } from 'src/app/Tesing-Helper/SalesOrder/salesOrder';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { TransferOrderSelectionResponse } from 'src/app/model/SalesOrder/TransferOrderSelectionResponse.model';
import { TransferOrderSerialNumber } from 'src/app/model/SalesOrder/TransferOrderSerialNumber.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { FeatureAndPresetInformationDisplayPipe } from 'src/app/shared/pipes/Common/FeatureAndPresetInformationDisplayPipe.pipe';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { SalesOrderStatusDisplay } from 'src/app/shared/pipes/SalesOrder/SalesOrderStatusDisplay.pipe';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { TransferOrderSelectionAndReviewComponent } from './transfer-order-selection-and-review.component';
import { TransferOrderProductReviewResponse } from 'src/app/model/SalesOrder/TransferOrderProductReviewResponse.model';
import { TransferProductDetails } from 'src/app/model/device/TransferProductDetails.model';
import { DeviceDetailResource, ProbDetailResource } from 'src/app/app.constants';
import { TransferOrderMessageDisplayPipe } from 'src/app/shared/pipes/Transfer Order/TransferOrderMessageDisplay.pipe';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';

describe('TransferOrderSelectionAndReviewComponent', () => {
  let component: TransferOrderSelectionAndReviewComponent;
  let fixture: ComponentFixture<TransferOrderSelectionAndReviewComponent>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let salesOrderApiCallServicespy: jasmine.SpyObj<SalesOrderApiCallService>;


  const transferOrderReviewResponse: TransferOrderProductReviewResponse = new TransferOrderProductReviewResponse(transferOrderResponse, null, false, [], false, [])

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    salesOrderApiCallServicespy = jasmine.createSpyObj('SalesOrderApiCallService', ['getTransferOrderSelectionDetails', 'submitTransferOrder']);


    await TestBed.configureTestingModule({
      declarations: [TransferOrderSelectionAndReviewComponent, TransferOrderMessageDisplayPipe, FeatureAndPresetInformationDisplayPipe, SalesOrderStatusDisplay],
      imports: [NgbModule,
        ReactiveFormsModule,
        FormsModule],
      providers: [CommonOperationsService,
        RoleApiCallService,
        ExceptionHandlingService,
        AuthJwtService,
        LocalStorageService,
        SessionStorageService,
        ConfirmDialogService,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        HidePermissionNamePipe,
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServicespy },
        commonsProviders(toastrServiceMock)]
    })
      .compileComponents();

    fixture = TestBed.createComponent(TransferOrderSelectionAndReviewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should create init component and validate otherSerialNumberValidate method with Error', async () => {
    // Mock API response
    salesOrderApiCallServicespy.getTransferOrderSelectionDetails?.and.returnValue(
      of(new HttpResponse<TransferOrderSelectionResponse>({
        body: new TransferOrderSelectionResponse(
          transferOrderResponse,
          transferredOrderredResponse,
          [
            new TransferOrderSerialNumber(64497, "maitri-test-function-audit48"),
            new TransferOrderSerialNumber(64498, "maitri-test-function-audit49")
          ],
          transferSalesOrderProbeSerialNumbers,
          false,
          false
        ),
        status: 200,
        statusText: 'OK',
      }))
    );

    // Call ngOnInit() which fetches API data
    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Manually patch formGroup with serial numbers
    component.formGroup.patchValue({
      [component.BRIDGE_SERIAL_NUMBER_LIST_CONTROL]: [
        { serialNumber: "64497" },
        { serialNumber: "64497" },
        { serialNumber: "64497" }
      ]
    });

    // Manually patch formGroup with serial numbers
    component.formGroup.patchValue({
      [component.PROBE_SERIAL_NUMBER_LIST_CONTROL]: [
        { serialNumber: "L1A-987" },
        { serialNumber: "null" },
      ]
    });

    fixture.detectChanges();
    await fixture.whenStable();

    // Call the method and check expected results
    component.bridgeSerialNumberChange(
      0,
      component.BRIDGE_SERIAL_NUMBER_LIST_CONTROL,
      component.tranferOrderData?.destinationSalesOrder?.product?.bridges,
      "64503",
      64497
    );

    component.bridgeSerialNumberChange(
      0,
      component.PROBE_SERIAL_NUMBER_LIST_CONTROL,
      component.tranferOrderData?.destinationSalesOrder?.product?.probes,
      "64503",
      64497
    );

    fixture.detectChanges();
    await fixture.whenStable();

  });

  it('should create init component and validate otherSerialNumberValidate method', async () => {
    // Mock API response
    salesOrderApiCallServicespy.getTransferOrderSelectionDetails?.and.returnValue(
      of(new HttpResponse<TransferOrderSelectionResponse>({
        body: new TransferOrderSelectionResponse(
          transferOrderResponse,
          transferredOrderredResponse,
          [
            new TransferOrderSerialNumber(64497, "maitri-test-function-audit48"),
            new TransferOrderSerialNumber(64498, "maitri-test-function-audit49")
          ],
          transferSalesOrderProbeSerialNumbers,
          false,
          false
        ),
        status: 200,
        statusText: 'OK',
      }))
    );

    salesOrderApiCallServicespy.submitTransferOrder?.and.returnValue(
      of(new HttpResponse<TransferOrderProductReviewResponse>({
        body: transferOrderReviewResponse,
        status: 200,
        statusText: 'OK',
      }))
    )

    // Call ngOnInit() which fetches API data
    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Manually patch formGroup with serial numbers
    component.formGroup.patchValue({
      [component.BRIDGE_SERIAL_NUMBER_LIST_CONTROL]: [
        { serialNumber: "64497" },
        { serialNumber: "64498" },
        { serialNumber: "null" }
      ]
    });

    fixture.detectChanges();
    await fixture.whenStable();

    // Call the method and check expected results
    component.probeSerialNumberChange(
      0,
      component.PROBE_SERIAL_NUMBER_LIST_CONTROL,
      component.tranferOrderData?.destinationSalesOrder?.product?.probes,
      "64503",
      64497
    );

    //Deselect
    component.probeSerialNumberChange(
      0,
      component.PROBE_SERIAL_NUMBER_LIST_CONTROL,
      component.tranferOrderData?.destinationSalesOrder?.product?.probes,
      "null",
      64497
    );

    fixture.detectChanges();
    await fixture.whenStable();

    // Call the method and check expected results
    component.probeSerialNumberChange(
      0,
      component.PROBE_SERIAL_NUMBER_LIST_CONTROL,
      component.tranferOrderData?.destinationSalesOrder?.product?.probes,
      "64503",
      64497
    );

    //Deselect
    component.probeSerialNumberChange(
      0,
      component.PROBE_SERIAL_NUMBER_LIST_CONTROL,
      component.tranferOrderData?.destinationSalesOrder?.product?.probes,
      null,
      64497
    );

    fixture.detectChanges();
    await fixture.whenStable();

    //Select
    component.probeSerialNumberChange(
      0,
      component.PROBE_SERIAL_NUMBER_LIST_CONTROL,
      component.tranferOrderData?.destinationSalesOrder?.product?.probes,
      "64503",
      64497
    );

    fixture.detectChanges();
    await fixture.whenStable();
    const salesOrderDetailSaveAllSarialNumber = fixture.debugElement.query(By.css('#salesOrderDetailSaveAllSarialNumber')).nativeElement as HTMLInputElement
    salesOrderDetailSaveAllSarialNumber.click();

    expect(salesOrderDetailSaveAllSarialNumber).toBeTruthy();
  });

  it('should create init component and validate otherSerialNumberValidate method', async () => {
    // Mock API response
    salesOrderApiCallServicespy.getTransferOrderSelectionDetails?.and.returnValue(
      of(new HttpResponse<TransferOrderSelectionResponse>({
        body: new TransferOrderSelectionResponse(
          transferOrderResponse,
          transferredOrderredResponse,
          [
            new TransferOrderSerialNumber(64497, "maitri-test-function-audit48"),
            new TransferOrderSerialNumber(64498, "maitri-test-function-audit49")
          ],
          transferSalesOrderProbeSerialNumbers,
          false,
          false
        ),
        status: 200,
        statusText: 'OK',
      }))
    );

    // Call ngOnInit() which fetches API data
    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    // Manually patch formGroup with serial numbers
    component.formGroup.patchValue({
      [component.BRIDGE_SERIAL_NUMBER_LIST_CONTROL]: [
        { serialNumber: "64497" },
        { serialNumber: "64498" },
        { serialNumber: "null" }
      ]
    });

    fixture.detectChanges();
    await fixture.whenStable();

    // Call the method and check expected results
    component.bridgeSerialNumberChange(
      0,
      component.BRIDGE_SERIAL_NUMBER_LIST_CONTROL,
      component.tranferOrderData?.destinationSalesOrder?.product?.bridges,
      "64503",
      64497
    );

    //Deselect
    component.bridgeSerialNumberChange(
      0,
      component.BRIDGE_SERIAL_NUMBER_LIST_CONTROL,
      component.tranferOrderData?.destinationSalesOrder?.product?.bridges,
      "null",
      64497
    );

    fixture.detectChanges();
    await fixture.whenStable();

    //Select
    component.bridgeSerialNumberChange(
      0,
      component.BRIDGE_SERIAL_NUMBER_LIST_CONTROL,
      component.tranferOrderData?.destinationSalesOrder?.product?.bridges,
      "64503",
      64497
    );

    fixture.detectChanges();
    await fixture.whenStable();
    const salesOrderDetailSaveAllSarialNumber = fixture.debugElement.query(By.css('#salesOrderDetailSaveAllSarialNumber')).nativeElement as HTMLInputElement
    salesOrderDetailSaveAllSarialNumber.click();

    expect(component.reviewOrderResponse).toBeNull();
  });

  it("Source is Manual And Destination is Manual", async () => {
    spyOn(component, "reviewProduct").and.callThrough();
    salesOrderApiCallServicespy.getTransferOrderSelectionDetails?.and.returnValue(
      of(new HttpResponse<TransferOrderSelectionResponse>({
        body: new TransferOrderSelectionResponse(
          transferOrderResponse,
          transferredOrderredResponse,
          [
            new TransferOrderSerialNumber(64497, "maitri-test-function-audit48"),
            new TransferOrderSerialNumber(64498, "maitri-test-function-audit49")
          ],
          transferSalesOrderProbeSerialNumbers,
          true,
          true
        ),
        status: 200,
        statusText: 'OK',
      }))
    );

    component.destinationSalesOrderId = 123;
    component.transferProductDetail = new TransferProductDetails(12, 14, "someType", null, DeviceDetailResource);

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.reviewProduct).toHaveBeenCalled();
  });

  it("Source is Manual And Destination is Manual", async () => {
    salesOrderApiCallServicespy.getTransferOrderSelectionDetails?.and.returnValue(
      of(new HttpResponse<TransferOrderSelectionResponse>({
        body: new TransferOrderSelectionResponse(
          transferOrderResponse,
          transferredOrderredResponse,
          [
            new TransferOrderSerialNumber(64497, "maitri-test-function-audit48"),
            new TransferOrderSerialNumber(64498, "maitri-test-function-audit49")
          ],
          transferSalesOrderProbeSerialNumbers,
          true,
          false
        ),
        status: 200,
        statusText: 'OK',
      }))
    );

    component.destinationSalesOrderId = 123;
    component.transferProductDetail = new TransferProductDetails(12, 14, "someType", null, ProbDetailResource);

    component.ngOnInit();
    fixture.detectChanges();
    await fixture.whenStable();

    component.selectAllProbeItem(true);

    expect(component.transferOrderRequest.product.probes.length).toBeGreaterThan(0);

    fixture.detectChanges();
    await fixture.whenStable();

    component.selectAllProbeItem(false);

    expect(component.transferOrderRequest.product.probes.length).toBe(0);

    fixture.detectChanges();
    await fixture.whenStable();

    component.selectProbeCheckbox(component.tranferOrderData.sourceSalesOrder.product.probes[0], true);

    expect(component.transferOrderRequest.product.probes.length).toBeGreaterThan(0);

    fixture.detectChanges();
    await fixture.whenStable();

    component.selectProbeCheckbox(component.tranferOrderData.sourceSalesOrder.product.probes[0], false);

    expect(component.transferOrderRequest.product.probes.length).toBe(0);

    fixture.detectChanges();
    await fixture.whenStable();


    component.selectAllDeviceItem(true);

    expect(component.transferOrderRequest.product.bridges.length).toBeGreaterThan(0);

    fixture.detectChanges();
    await fixture.whenStable();

    component.selectAllDeviceItem(false);

    expect(component.transferOrderRequest.product.bridges.length).toBe(0);

    fixture.detectChanges();
    await fixture.whenStable();

    component.selectDeviceCheckbox(component.tranferOrderData.sourceSalesOrder.product.bridges[0], true);

    expect(component.transferOrderRequest.product.bridges.length).toBeGreaterThan(0);

    fixture.detectChanges();
    await fixture.whenStable();

    component.selectDeviceCheckbox(component.tranferOrderData.sourceSalesOrder.product.probes[0], false);

    expect(component.transferOrderRequest.product.bridges.length).toBe(0);



  });

});
