import { ValidityEnum } from "src/app/shared/enum/ValidityEnum.enum";
import { PartNumberBaseResponse } from "./PartNumberBaseResponse.model";

export class PresetPartNumberResponse extends PartNumberBaseResponse {
    probeTypeMasterPresetMasterMappingId: number;

    constructor(
        partNumber: string,
        validity: ValidityEnum,
        isDefault: boolean,
        allowedToEdit: boolean,
        probeTypeMasterPresetMasterMappingId: number
    ) {
        super(partNumber, validity, isDefault, allowedToEdit);
        this.probeTypeMasterPresetMasterMappingId = probeTypeMasterPresetMasterMappingId;
    }
}