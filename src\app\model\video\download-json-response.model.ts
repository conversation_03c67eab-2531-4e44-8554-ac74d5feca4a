export class SubTitleInformation {
    public lang: string;
    public title: string;
    constructor($lang: string, $title: string) {
        this.lang = $lang;
        this.title = $title;
    }
}
export class VideoInformation {
    id: string;
    duration: string;
    size: number;
    subTitles: SubTitleInformation[];
    constructor($id: string, $duration: string, $size: number, $subTitles: SubTitleInformation[]) {
        this.id = $id;
        this.duration = $duration;
        this.size = $size;
        this.subTitles = $subTitles;
    }
}
export class DownloadJsonResponse {
    public version: string;
    public videos: VideoInformation[];
    constructor($version: string, $videos: VideoInformation[]) {
        this.version = $version;
        this.videos = $videos;
    }
}
