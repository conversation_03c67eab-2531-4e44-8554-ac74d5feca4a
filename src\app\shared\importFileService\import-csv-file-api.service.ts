import { HttpClient, HttpResponse } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { DeviceListResource, ListKitManagementResource, OTSKitManagementListResource, ProbDetailResource, ProbListResource } from 'src/app/app.constants';
import { ImportFileForUpdateTemplateDataResponse } from 'src/app/model/importCSVFile/ImportFileForUpdateTemplateDataResponse.model';
import { API_BASE_URL } from '../config';

@Injectable({
  providedIn: 'root'
})
export class ImportCsvFileApiService {
  public deviceMastersUrl = this.SERVER_API_URL + 'api/deviceMasters/';
  public probeUrl = this.SERVER_API_URL + 'api/probe/';
  public kitUrl = this.SERVER_API_URL + 'api/kitManagement/bridge-world/';
  public otsKitUrl = this.SERVER_API_URL + 'api/kit-management/ots-world/';

  constructor(protected http: HttpClient,
    @Inject(API_BASE_URL) public SERVER_API_URL: string,) { }

  /**
   * <AUTHOR>
   * @param resourse 
   * @returns This Function Return the base Url
   */
  public getBaseUrlForResource(resourse: string): string {
    if (resourse == DeviceListResource) {
      return this.deviceMastersUrl;
    } else if (resourse == ProbListResource || resourse == ProbDetailResource) {
      return this.probeUrl;
    } else if (resourse == ListKitManagementResource) {
      return this.kitUrl;
    } else if (resourse == OTSKitManagementListResource) {
      return this.otsKitUrl;
    }
    return this.SERVER_API_URL;
  }

  /**
   * <AUTHOR>
   * @param resourse 
   * @returns Return CSV File Data 
   */
  public downloadCsvTemplate(resourse: string): Observable<HttpResponse<any>> {
    return this.http.get<any>(this.getBaseUrlForResource(resourse) + 'csv/import/download', { observe: 'response', responseType: 'blob' as 'json' });
  }

  /**
   * <AUTHOR>
   * @param formData 
   * @param resourse 
   * @returns Return ImportFileForUpdateTemplateDataResponse (Totel success/fail Records count And Error object list)
   */
  public importFileForUpdateTemplateData(formData: FormData, resourse: string): Observable<HttpResponse<ImportFileForUpdateTemplateDataResponse>> {
    return this.http.post<ImportFileForUpdateTemplateDataResponse>(this.getBaseUrlForResource(resourse) + "csv/import", formData, { observe: 'response' });
  }
}
