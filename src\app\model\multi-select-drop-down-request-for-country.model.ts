
export class MultiSelectDropDownRequestForCountryModel {
    id: number;
    name: string;
    country: string;
    isDisabled: boolean;

    /**
     * Creates an instance of MultiSelectDropDownRequestForCountryModel model.
     * 
     * <AUTHOR>
     * @param $id 
     * @param $name 
     * @param $country 
     * @param $isDisabled 
     */
    constructor($id: number, $name: string, $country: string, $isDisabled: boolean) {
        this.id = $id;
        this.name = $name;
        this.country = $country;
        this.isDisabled = $isDisabled;
    }

}