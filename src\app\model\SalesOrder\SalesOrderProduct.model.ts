import { SalesOrderProbeDetailResponse } from "./SalesOrderProbeDetailResponse.model";
import { SalesOrderBridgeDetailResponse } from "./SalesOrderBridgeDetailResponse.model";
import { SalesOrderLicenceDetailResponse } from "./SalesOrderLicenceDetailResponse.model";
import { SalesOrderHierarchyResponse } from "./SalesOrderHierarchyResponse.model";

export class SalesOrderProduct {
    probes: Array<SalesOrderProbeDetailResponse>;
    bridges: Array<SalesOrderBridgeDetailResponse>;
    licenses: Array<SalesOrderLicenceDetailResponse>;
    parentSalesOrders: Array<SalesOrderHierarchyResponse>;
    childSalesOrders: Array<SalesOrderHierarchyResponse>;

    constructor(
        probes: Array<SalesOrderProbeDetailResponse>,
        bridges: Array<SalesOrderBridgeDetailResponse>
    ) {
        this.probes = probes;
        this.bridges = bridges;
    }
}
