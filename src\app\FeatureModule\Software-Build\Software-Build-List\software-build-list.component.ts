import { DatePipe } from '@angular/common';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { isNullOrUndefined, isUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { forkJoin, Subscription } from 'rxjs';
import { CLIENT_DEVICE, DEMO_DEVICE, ITEMS_PER_PAGE, NotAssociated, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH, SoftwareBuildListResource, SPECIAL_CHARACTER_ERROR_MESSAGE, SPECIAL_CHARACTER_PATTERN } from '../../../app.constants';
import { CountryListResponse } from '../../../model/Country/CountryListResponse.model';
import { MultiSelectDropdownSettings } from '../../../model/MultiSelectDropdownSettings.model';
import { BasicModelConfig } from '../../../model/common/BasicModelConfig.model';
import { SuccessMessageResponse } from '../../../model/common/SuccessMessageResponse.model';
import { SoftwareBuildListResponse } from '../../../model/SoftwaarBuilds/SoftwareBuildListResponse.model';
import { SoftwareBuildSearchRequestBody } from '../../../model/SoftwaarBuilds/SoftwareBuildSearchRequestBody';
import { Jsonlist } from '../../../model/video/jsonlist.model';
import { ExceptionHandlingService } from '../../../shared/ExceptionHandling.service';
import { CountryCacheService } from '../../../shared/Service/CacheService/countrycache.service';
import { AuthJwtService } from '../../../shared/auth-jwt.service';
import { EditSoftwareBuildDialogService } from '../software-build-services/edit-software-build-dialog/edit-software-build-dialog.service';
import { PermissionAction } from '../../../shared/enum/Permission/permissionAction.enum';
import { collapseFilterTextEnum } from '../../../shared/enum/collapseFilterButtonText.enum';
import { deviceTypesEnum } from '../../../shared/enum/deviceTypesEnum.enum';
import { SoftwareBuildStatusEnum } from '../../../shared/enum/SoftwareBuildStatusEnum';
import { SoftWareBuildConformationService } from '../software-build-services/conformation-software-build-dialog/software-build-conformation.service';
import { PermissionService } from '../../../shared/permission.service';
import { UploadScanService } from '../../../shared/upload-scan.service';
import { CommonCheckboxService } from '../../../shared/util/common-checkbox.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { DownloadService } from '../../../shared/util/download.service';
import { ModuleValidationServiceService } from '../../../shared/util/module-validation-service.service';
import { MultiSelectDropDownSettingService } from '../../../shared/util/multi-select-drop-down-setting.service';
import { VideoService } from '../../../shared/videoservice/video.service';
import { DeleteSoftwareBildDialogService } from '../software-build-services/delete-software-build-dialog/delete-software-build-dialog.service';
import { SoftwareBuildApiCallService } from '../software-build-services/software-api-call/software-build-api-call.service';

@Component({
  selector: 'app-software-build-list',
  templateUrl: './software-build-list.component.html',
  styleUrls: ['./software-build-list.component.css']
})
export class SoftwareBuildListComponent implements OnInit {
  loading = false;
  createdDt: any;
  itemsPerPage: number;
  page: number = 0;
  previousPage: number;
  inventory: Array<SoftwareBuildListResponse> = [];

  drpselectsize: number = ITEMS_PER_PAGE;

  //Text box max limit set
  textBoxMaxLength: number = SMALL_TEXTBOX_MAX_LENGTH;
  textBoxMaxCharactersAllowedMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;
  totalItems: number;
  closeResult: string;
  modalTable: SoftwareBuildListResponse[];
  totalItemInventoryDisplay: number = 0;
  totalItemInventory: number = 0;
  filterForm = this.fb.group({
    itemNumber: new FormControl('', [Validators.maxLength(this.textBoxMaxLength), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    deviceType: new FormControl([]),
    country: new FormControl([]),
    jsonVersions: new FormControl([]),
    inventoryStatus: null,
    partNumber: new FormControl('', [Validators.maxLength(this.textBoxMaxLength), Validators.pattern(SPECIAL_CHARACTER_PATTERN)])
  });
  deviceTypes: string[];
  inventoryStatus: string[];
  countryList: CountryListResponse[] = [];
  jsonVersionList: Array<Jsonlist> = [];
  dropdownSettingsInventoryStatus: MultiSelectDropdownSettings;
  dropdownSettingsDeviceType: MultiSelectDropdownSettings;
  dropdownSettingsJsonVersions: MultiSelectDropdownSettings;
  dropdownSettingsCountry: MultiSelectDropdownSettings;

  inventoryIdList = [];
  localInventoryList: SoftwareBuildListResponse[];
  seletedinventoryIdWithCountry: Map<number, Array<string>> = new Map();

  isFilterHidden: boolean = false;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;
  userAssociatedCountrys: CountryListResponse[] = [];

  // show entry selection
  dataSizes: string[] = [];

  //unique CheckBox Name
  chkPreFix = "itemInventory";
  selectAllCheckboxId = "selectAllitemInventory";
  checkboxListName = "itemInventory[]";

  //Permission
  deleteSoftwareBuildPermission: boolean = false;
  uploadSoftwareBuildPermission: boolean = false;
  updateSoftwareBuildPermission: boolean = false;

  //Default options
  softwareBuildOperations: string = "Software Build Operations";

  //subscription
  subscriptionForLoading: Subscription;

  constructor(
    private softwareBuildApiCallService: SoftwareBuildApiCallService,
    private datePipe: DatePipe,
    private fb: FormBuilder,
    private uploadScanService: UploadScanService,
    private editSoftwareBuildDialogService: EditSoftwareBuildDialogService,
    private permissionService: PermissionService,
    private toste: ToastrService,
    private exceptionService: ExceptionHandlingService,
    private authservice: AuthJwtService,
    private deleteSoftwareBildDialogService: DeleteSoftwareBildDialogService,
    private countryCacheService: CountryCacheService,
    private videoService: VideoService,
    private commonsService: CommonsService,
    private softWareBuildConformationService: SoftWareBuildConformationService,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private downloadService: DownloadService,
    private moduleValidationServiceService: ModuleValidationServiceService,
    private commonCheckboxService: CommonCheckboxService
  ) {
    this.itemsPerPage = ITEMS_PER_PAGE;
  }

  ngOnInit(): void {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.getInitCall();
      this.dataSizes = this.commonsService.accessDataSizes();
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.drpselectsize = ITEMS_PER_PAGE;
      this.page = 0;
      this.previousPage = 1;
      this.inventoryIdList = []
      this.setItemInventoryPermission();
      this.loadAll();
    }
    this.subjectInit()
  }

  /**
  * Destroy subscription
  * 
  * <AUTHOR>
  */
  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForLoading)) { this.subscriptionForLoading.unsubscribe() }
  }

  private subjectInit(): void {
    /**
     * Loading Hide/Display
     * <AUTHOR>
     */
    this.subscriptionForLoading = this.downloadService.getisLoadingSubject()?.subscribe((res: boolean) => {
      this.loading = res;
    });
  }

  public async getInitCall(): Promise<void> {
    this.dropdownSettingsCountry = this.multiSelectDropDownSettingService.getCountryDrpSetting(false, false);
    this.dropdownSettingsDeviceType = this.multiSelectDropDownSettingService.getDeviceTypeDropdownSetting();
    this.dropdownSettingsInventoryStatus = this.multiSelectDropDownSettingService.getSoftwareStatusDropdownSetting();
    this.dropdownSettingsJsonVersions = this.multiSelectDropDownSettingService.getjsonVersionDropdownSetting(false);
    this.loading = true;

    this.initApiCall();

    this.deviceTypes = [];
    this.deviceTypes.push(CLIENT_DEVICE);
    this.deviceTypes.push(DEMO_DEVICE);
    this.deviceTypes.push(deviceTypesEnum.ABOVE_BOTH);
    this.deviceTypes.push(NotAssociated)
    this.inventoryStatus = [];
    this.inventoryStatus.push(SoftwareBuildStatusEnum.ACTIVE);
    this.inventoryStatus.push(SoftwareBuildStatusEnum.INACTIVE);
  }

  /**
   * set Item Inventory
   */
  private setItemInventoryPermission(): void {
    this.deleteSoftwareBuildPermission = this.permissionService.getSoftwearBuildPermission(PermissionAction.DELETE_SOFTWARE_BUILD_ACTION);
    this.uploadSoftwareBuildPermission = this.permissionService.getSoftwearBuildPermission(PermissionAction.UPLOAD_SOFTWARE_BUILD_ACTION);
    this.updateSoftwareBuildPermission = this.permissionService.getSoftwearBuildPermission(PermissionAction.UPDATE_SOFTWARE_BUILD_ACTION);
  }

  /**
  * Initialize API calls for fetching json versions and country list.
  *
  * <AUTHOR>
  */
  public initApiCall(): void {
    forkJoin({
      jsonVersionList: this.videoService.getListofJsonVersions(),
      countryList: this.countryCacheService.getCountryListFromCache()
    }).subscribe({
      next: ({ jsonVersionList, countryList }) => {
        this.jsonVersionList = jsonVersionList.body;
        this.countryList = countryList;
        this.userAssociatedCountrys = this.countryCacheService.filterOutUserAssociatedCountries(this.countryList);
      },
      error: (error) => {
        this.exceptionService.customErrorMessage(error);
      }
    })
  }

  /**
  * Refresh button click
  * 
  * <AUTHOR>
  */
  public clickOnRefreshButton(): void {
    this.page = 0;
    this.initApiCall();
    this.loadAll();
  }

  changeDataSize(datasize): void {
    this.loading = true;
    this.itemsPerPage = datasize.target.value;
    this.reloadItem();
  }

  public reloadItem(): void {
    this.inventoryIdList = [];
    this.page = 0;
    this.loadAll();
  }

  /**
   * Load list of Software Builds with filter values
   */
  private loadAll(): void {
    this.loading = true;
    if (this.filterForm.invalid) {
      this.filterForm.reset();
    }
    this.filterForm.get('itemNumber').setValue(this.commonsService.checkNullFieldValue(this.filterForm.get('itemNumber').value));
    this.filterForm.get('partNumber').setValue(this.commonsService.checkNullFieldValue(this.filterForm.get('partNumber').value));
    let itemNumber = this.filterForm.get('itemNumber').value;
    let countryValue = this.filterForm.get("country").value;
    let jsonVersionValue = this.filterForm.get("jsonVersions").value;
    let countryIds: number[] = isNullOrUndefined(countryValue) ? null : this.commonsService.getIdsFromArray(countryValue);
    let jsonVersionIds: number[] = isNullOrUndefined(jsonVersionValue) ? null : this.commonsService.getIdsFromArray(jsonVersionValue);
    let isActive = this.commonsService.getValuesFromArray(this.filterForm, 'inventoryStatus');
    let deviceType = this.commonsService.getDeviceTypeFilterValueArray(this.filterForm, 'deviceType');
    let partNumber = this.filterForm.get('partNumber').value;
    let requestObject = new SoftwareBuildSearchRequestBody(itemNumber, countryIds, deviceType, isActive, jsonVersionIds, partNumber);
    this.softwareBuildApiCallService.inventoryList(
      requestObject,
      {
        page: this.page - 1,
        size: this.itemsPerPage
      }
    )?.subscribe(
      {
        next: (res: HttpResponse<any>) => {
          if (res.status != 200 || res.body == null) {
            this.inventory = null;
            this.totalItemInventory = 0;
            this.totalItemInventoryDisplay = 0;
            this.loading = false;
          } else {
            this.paginateDevices(res.body);
            this.setLocalDeviceId(res.body.content);
          }
        },
        error: (error: HttpErrorResponse) => {
          this.loading = false;
          this.exceptionService.customErrorMessage(error);
        }
      }
    );

  }

  loadPage(page: number): void {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.commonCheckboxService.clearSelectAllCheckbox(this.selectAllCheckboxId);
      this.loadAll();
    }
  }

  protected paginateDevices(data: any): void {
    this.totalItems = parseInt(data.totalElements, 10);
    this.inventory = data.content;
    this.page = data.number + 1;
    this.totalItemInventory = data.totalElements;
    this.totalItemInventoryDisplay = data.numberOfElements;

    for (let index = 0; index < data.content.length; index++) {
      this.createdDt = this.datePipe.transform(new Date(data.content[index].createdDate), 'MMM d, y, h:mm:ss a');
      this.inventory[index].createdDate = this.createdDt;
    }
    this.loading = false;
  }

  setLocalDeviceId(inventoryList: Array<SoftwareBuildListResponse>): void {
    this.localInventoryList = [];
    for (let inventory of inventoryList) {
      this.localInventoryList.push(inventory);
    }
    this.defaultSelectAll();
  }

  /**
   * Filter Value of Software build
   */
  public searchInventoryFilter(): void {
    if (this.commonsService.checkNullFieldValue(this.filterForm.get('itemNumber').value) == null &&
      this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('deviceType').value) &&
      this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('country').value) &&
      this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('jsonVersions').value) &&
      this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('inventoryStatus').value) &&
      this.commonsService.checkNullFieldValue(this.filterForm.get('partNumber').value) == null) {
      this.toste.info("Please Select Filter To Search")
    }
    else {
      this.reloadItem();
    }
  }


  /**
   * Clear all Filter
   */
  private clearAllFilter(): void {
    this.filterForm.get('itemNumber').setValue(null);
    this.filterForm.get('country').setValue(null);
    this.filterForm.get('jsonVersions').setValue(null);
    this.filterForm.get('deviceType').setValue(null);
    this.filterForm.get('inventoryStatus').setValue(null);
    this.filterForm.get('partNumber').setValue(null);
    this.page = 0;
    this.inventoryIdList = [];
  }

  /**
   * Clear filter form value
   */
  public clearFilter(): void {
    this.clearAllFilter();
    this.loadAll();
  }

  public changeInventoryOperation(event): void {
    let selectedOperation = event.target.value;
    switch (selectedOperation) {
      case "Map to Client Devices": {
        this.mapWithDeviceType([deviceTypesEnum.CLIENT_DEVICE]);
        break;
      }
      case "Map to Demo Devices": {
        this.mapWithDeviceType([deviceTypesEnum.DEMO_DEVICE]);
        break;
      }
      case "Map to Both type of Devices": {
        this.mapWithDeviceType([deviceTypesEnum.CLIENT_DEVICE, deviceTypesEnum.DEMO_DEVICE]);
        break;
      }
      case "Mark as active": {
        this.changeInventoryStatus(true);
        break;
      }
      case "Mark as Inactive": {
        this.changeInventoryStatus(false);
        break;
      }
      default:
        break;
    }
    let selection = document.getElementById('inventoryOperation') as HTMLSelectElement;
    if (selection) {
      selection.value = this.softwareBuildOperations;
    }
  }

  /**
   * get Item Associated Country List 
   * 
   * <AUTHOR>
   * @returns 
   */
  private getItemAssociatedCountryList(): string[] {
    let itemAssociatedCountryList: Array<string> = [];
    for (let key of this.seletedinventoryIdWithCountry.keys()) {
      let countrys = this.seletedinventoryIdWithCountry.get(key);
      if (!isNullOrUndefined(countrys) && countrys.length > 0) {
        itemAssociatedCountryList = countrys
      }
    }

    return itemAssociatedCountryList;
  }

  /**
   * Make inventory status as active / in-active
   * @param isActive 
   */
  private changeInventoryStatus(isActive: boolean): void {
    if (this.inventoryIdList.length != 0) {
      if (this.moduleValidationServiceService.validateWithUserCountryForMultileRecord(this.getItemAssociatedCountryList(), SoftwareBuildListResource, true)) {
        this.softWareBuildConformationService.openInventoryOperationModel(
          "Confirm",
          "Cancel",
          "Confirmation",
          "Are you sure you want to mark Software Build(s) as " + (isActive ? "Active?" : "Inactive? Doing same will remove the release association with test device if any.")
        ).then((value: boolean) => {
          if (value) {
            this.markInventoriesActiveInactive(isActive);
          }
        });
      }
    } else {
      this.toste.info("Please Select Software Build(s)")
    }
  }

  private markInventoriesActiveInactive(isActive: boolean): void {
    this.loading = true;
    this.softwareBuildApiCallService.markInventoriesActiveInactive(this.inventoryIdList, { active: isActive })?.subscribe({
      next: (res) => {
        this.loading = false;
        this.inventoryIdList = []
        this.loadAll();
        this.toste.success(res.body['message']);
      }, error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  /**
   * Map Inventory with Device Types
   * @param deviceType 
   */
  private mapWithDeviceType(deviceType: deviceTypesEnum[]): void {
    if (this.inventoryIdList.length != 0) {
      if (this.moduleValidationServiceService.validateWithUserCountryForMultileRecord(this.getItemAssociatedCountryList(), SoftwareBuildListResource, true)) {
        this.softWareBuildConformationService.openInventoryOperationModel(
          "Confirm",
          "Cancel",
          "Confirmation",
          this.commonsService.getDeviceTypeMapMessage(deviceType)
        ).then((value: boolean) => {
          if (value) {
            this.mapInventoryWithDeviceType(deviceType);
          }
        });
      }
    } else {
      this.toste.info("Please Select Software Build(s)")
    }
  }

  private mapInventoryWithDeviceType(deviceType: deviceTypesEnum[]): void {
    this.loading = true;
    this.softwareBuildApiCallService.mapInventoryWithDeviceType(this.inventoryIdList, { deviceType: deviceType })?.subscribe({
      next: (res) => {
        this.responseOfMapInventoryWithDeviceType(res.body['message']);
      }, error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  private responseOfMapInventoryWithDeviceType(message: string): void {
    this.loading = false;
    this.inventoryIdList = [];
    this.loadAll();
    this.toste.success(message);
  }

  onChangeInventory(id: number, countryMaster: Array<string>, event) {
    if (event.target.checked) {
      this.inventoryIdList.push(id);
      this.seletedinventoryIdWithCountry.set(id, countryMaster);
    }
    else {
      let index = this.inventoryIdList.findIndex(obj => obj == id);
      this.inventoryIdList.splice(index, 1);
      this.seletedinventoryIdWithCountry.delete(id);
    }
    this.defaultSelectAll();
  }

  /**
  * select All checkbox select or deSelect
  * <AUTHOR>
  */
  private defaultSelectAll(): void {
    this.commonCheckboxService.defaultSelectAll(this.localInventoryList.map(i => i.id), this.inventoryIdList, this.selectAllCheckboxId);
  }



  public selectAllItem(isChecked: boolean): void {
    this.inventoryIdList = this.commonCheckboxService.selectAllItem(isChecked, this.localInventoryList.map(i => i.id), this.inventoryIdList, this.checkboxListName);

    if (isChecked) {
      for (let inventory of this.localInventoryList) {
        let inventoryindex = this.inventoryIdList.findIndex(id => id == inventory);
        if (inventoryindex < 0) {

          this.seletedinventoryIdWithCountry.set(inventory.id, inventory.countries);
        }
      }
    }
    else {
      for (let inventoryObj of this.localInventoryList) {
        this.seletedinventoryIdWithCountry.delete(inventoryObj.id);
      }
    }
  }

  /**
   * Get Attachment URL
   * <AUTHOR>
   * @param id 
   * @param status 
   */
  public getAttachmentUrl(id: number): void {
    this.getAttachmentUrlApiCall(id, "attachment");
  }

  /**
   * Get Release Notes
   * 
   * <AUTHOR>
   * @param id 
   * @param status 
   */
  public getReleaseNoteUrl(id: number): void {
    this.getAttachmentUrlApiCall(id, "releaseNote");
  }

  public getAttachmentUrlApiCall(id: number, attachmentType: string): void {
    this.loading = true;
    this.softwareBuildApiCallService.getAttachmentUrl(id, { attachmentType: attachmentType })?.subscribe({
      next: (res) => {
        this.downloadMyFileAsync(res.body.urlToDownload);
      },
      error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      },
    });
  }

  /**
   * Download File
   * 
   * <AUTHOR>
   * @param url 
   */
  public async downloadMyFileAsync(url: string): Promise<void> {
    await this.downloadService.downloadMyFile(url);
    this.loading = false;
  }

  /**
   * @param inventoryItemId
   */
  deleteItem(inventoryItemId: number, itemNumber: string): void {
    this.deleteSoftwareBildDialogService.confirm('Delete', 'Are you sure you want to delete', 'Delete', 'Cancel', itemNumber, 'Doing same will remove the release association with test device if any.')
      .then((confirmed) => {
        if (confirmed) {
          this.delete(inventoryItemId);
        }
      });
  }

  private delete(inventoryItemId: number): void {
    this.loading = true;
    this.softwareBuildApiCallService.deleteSoftwearBuild(inventoryItemId)?.subscribe({
      next: (res: HttpResponse<SuccessMessageResponse>) => {
        if (res.status != 200) {
          this.loading = false;
          this.toste.error("Error while deleting Software Build");
        } else {
          this.toste.success(res.body.message);
          this.reloadItem();
        }
      }, error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error)
      }
    });
  }

  /**
   * Upload Firmware manually
   */
  public confirmDownloadDataset(): void {
    this.uploadScanService.confirm('Upload Firmware', 'Please confirm to upload.', this.userAssociatedCountrys, this.jsonVersionList)
      .then((confirmed: boolean) => {
        if (confirmed) {
          this.loading = true;
          this.reloadItem();
        } else {
          this.loading = false;
        }
      });
  }


  /**
  * Toggle Filter
  * 
  */
  public toggleFilter(): void {
    this.isFilterHidden = !this.isFilterHidden;
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }

  /**
   * Edit Inventory
   * 
   * <AUTHOR>
   * @param inventory 
   */
  public editInventoryDetails(inventory: SoftwareBuildListResponse): void {
    let basicModelConfig: BasicModelConfig = new BasicModelConfig('Edit Software Build', null, 'Update', 'Cancel');
    this.editSoftwareBuildDialogService.openEditInventoryModel(basicModelConfig, inventory, this.jsonVersionList, this.userAssociatedCountrys)
      .then((confirmed: boolean) => {
        if (confirmed) {
          this.reloadItem();
        }
      });
  }

  public downloadJSONFile(jsonMaster: Jsonlist): void {
    this.loading = true;
    this.videoService.downloadJSONFile(jsonMaster?.id)?.subscribe({
      next: (downloadJsonResponse) => {
        let jsonData = downloadJsonResponse.body["data"];
        const blob = new Blob([JSON.stringify(jsonData)], { type: 'application/json' });
        let downloadLink = document.createElement('a');
        downloadLink.href = window.URL.createObjectURL(new Blob([blob], { type: blob.type }));
        downloadLink.setAttribute('download', `${jsonMaster.version}.json`);
        document.body?.appendChild(downloadLink);
        downloadLink.click();
        this.loading = false;
      }, error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });
  }
}
