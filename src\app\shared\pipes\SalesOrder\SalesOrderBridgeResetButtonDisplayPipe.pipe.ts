import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ProductConfigStatus } from '../../enum/SalesOrder/ProductConfigStatus.enum';

@Pipe({
    name: 'salesOrderBridgeResetButtonDisplayPipe'
})
export class SalesOrderBridgeResetButtonDisplayPipe implements PipeTransform {

    //Reset Buttom Show
    resetButtonShowStatusList: Array<string> = [ProductConfigStatus.IN_PROGRESS];

    public transform(productStatus: ProductConfigStatus, resetButtonPermission: boolean): boolean {
        if (resetButtonPermission && !isNullOrUndefined(productStatus)) {
            return this.resetButtonShowStatusList.includes(ProductConfigStatus[productStatus]);
        }
        return false;
    }

}
