import { HttpResponse } from '@angular/common/http';
import { HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { CreateJsonRequest } from 'src/app/model/video/create-json-request.model';
import { DownloadVideoResponse } from 'src/app/model/video/download-video-response.model';
import { Jsonlist } from 'src/app/model/video/jsonlist.model';
import { UploadVideoRequest } from 'src/app/model/video/upload-video-request.model';
import { UploadVideoResponse } from 'src/app/model/video/upload-video-response.model';
import { VideoSearchRequest } from 'src/app/model/video/video-search-request.model';
import { VideoListPagableResponse } from 'src/app/model/video/videoListPagableResponse.model';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { API_BASE_URL } from '../config';
import { CommonsService } from '../util/commons.service';
import { VideoService } from './video.service';
import { DownloadJsonResponse } from 'src/app/model/video/download-json-response.model';

describe('VideoService', () => {
  let service: VideoService;
  let httpMock: HttpTestingController;
  const mockApiUrl = 'http://example.com/api/';

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        VideoService,
        CommonsService,
        { provide: API_BASE_URL, useValue: mockApiUrl },
        commonsProviders(null)
      ]
    });
    service = TestBed.inject(VideoService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should fetch video list', () => {
    const mockResponse: VideoListPagableResponse = new VideoListPagableResponse(null, null, null, null, null, null, null, null, null, null, null);
    service.getVideoList(new VideoSearchRequest(null, null)).subscribe(response => {
      expect(response.body.content).toBeUndefined();
    });
    const req = httpMock.expectOne(`${service.getVideoServerUrl}`);
    expect(req.request.method).toBe('POST');
    req.flush(new HttpResponse({ body: mockResponse }));
  });

  it('should upload a video', () => {
    const mockResponse: UploadVideoResponse = new UploadVideoResponse(null, null, null, null, null, null);
    service.uploadVideo(new UploadVideoRequest(null, null, null, null, null, null, null, null, null, null)).subscribe(response => {
      expect(response.body.subTitles).toBeUndefined();
    });
    const req = httpMock.expectOne(`${service.videoServerUrl}`);
    expect(req.request.method).toBe('POST');
    req.flush(new HttpResponse({ body: mockResponse }));
  });

  it('should get a video', () => {
    service.getVideo(1).subscribe(response => {
      expect(response).toBeTruthy();
    });
    const req = httpMock.expectOne(`${service.getVideoServerUrl}/1`);
    expect(req.request.method).toBe('GET');
    req.flush(new HttpResponse({ body: {} }));
  });

  it('should delete a video', () => {
    const mockResponse: SuccessMessageResponse = { message: 'Deleted' };
    service.deleteVideo(1).subscribe(response => {
      expect(response.status).toEqual(200);
    });
    const req = httpMock.expectOne(`${service.videoServerUrl}/1`);
    expect(req.request.method).toBe('DELETE');
    req.flush(new HttpResponse({ body: mockResponse, status: 200 }));
  });

  it('should delete a JSON file', () => {
    const mockResponse: SuccessMessageResponse = { message: 'Deleted' };
    service.deleteJson(1).subscribe(response => {
      expect(response.status).toEqual(200);
    });
    const req = httpMock.expectOne(`${service.videoServerUrl}/json/1`);
    expect(req.request.method).toBe('DELETE');
    req.flush(new HttpResponse({ body: mockResponse, status: 200 }));
  });

  it('should update video', () => {
    const mockRequest: UploadVideoRequest = new UploadVideoRequest(null, null, null, null, null, null, null, null, null);
    const mockResponse: SuccessMessageResponse = new SuccessMessageResponse('Updated');
    service.updateVideo(mockRequest, 1).subscribe(response => {
      expect(response.status).toEqual(200);
    });
    const req = httpMock.expectOne(`${service.videoServerUrl}/1`);
    expect(req.request.method).toBe('PUT');
    req.flush(new HttpResponse({ body: mockResponse, status: 200 }));
  });

  it('should create a JSON file', () => {
    const mockResponse: SuccessMessageResponse = { message: 'Created' };
    service.createJson(new CreateJsonRequest(null, null, null, null)).subscribe(response => {
      expect(response.status).toEqual(200);
    });
    const req = httpMock.expectOne(`${service.videoServerUrl}/json`);
    expect(req.request.method).toBe('POST');
    req.flush(new HttpResponse({ body: mockResponse, status: 200 }));
  });

  it('should get list of JSON versions', () => {
    const mockResponse: Jsonlist[] = [{ id: 1, version: 'v1.0' }];
    service.getListofJsonVersions().subscribe(response => {
      expect(response.status).toEqual(200);
    });
    const req = httpMock.expectOne(`${service.getVideoServerUrl}/software-builds/json`);
    expect(req.request.method).toBe('GET');
    req.flush(new HttpResponse({ body: mockResponse, status: 200 }));
  });

  it('should update JSON', () => {
    const jsonId = 456;
    const updateRequest: CreateJsonRequest = new CreateJsonRequest(null, null, null, null);
    const mockResponse: SuccessMessageResponse = new SuccessMessageResponse('Updated');

    service.updateJson(updateRequest, jsonId).subscribe(response => {
      expect(response.status).toEqual(200);
    });

    const req = httpMock.expectOne(`${service.videoServerUrl}/json/${jsonId}`);
    expect(req.request.method).toBe('PUT');

    req.flush(new HttpResponse({ body: mockResponse, status: 200 }));
  });

  it('should download video zip file', () => {
    const videoId = 789;
    const mockResponse: DownloadVideoResponse = new DownloadVideoResponse(null, null, null);

    service.downloadVideoZipFile(videoId).subscribe(response => {
      expect(response.status).toEqual(200);
    });

    const req = httpMock.expectOne(`${service.getVideoServerUrl}/${videoId}/download`);
    expect(req.request.method).toBe('GET');
    req.flush(new HttpResponse({ body: mockResponse, status: 200 }));
  });

  it('should download JSON file', () => {
    const jsonId = 1011;
    const mockResponse: DownloadJsonResponse = new DownloadJsonResponse(null, null);

    service.downloadJSONFile(jsonId).subscribe(response => {
      expect(response.status).toEqual(200);
    });

    const req = httpMock.expectOne(`${service.getVideoServerUrl}/json/download/${jsonId}`);
    expect(req.request.method).toBe('GET');
    req.flush(new HttpResponse({ body: mockResponse, status: 200 }));
  });

});
