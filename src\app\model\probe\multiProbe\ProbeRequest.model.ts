import { ProbeTypeEnum } from "src/app/shared/enum/ProbeType.enum";
import { ConfigBaseMappingRequest } from "../ConfigBaseMappingRequest.model";
import { ConfigMappingRequest } from "../ConfigMappingRequest.model";

export class ProbeRequest extends ConfigMappingRequest {
    serialNumber: string;
    probeType: ProbeTypeEnum;
    sopmId: number;

    //UI Side
    errorMessage: string;



    constructor($features: Array<ConfigBaseMappingRequest>, $presets: Array<ConfigBaseMappingRequest>, $reminder: boolean, $serialNumber: string, $probeType: ProbeTypeEnum, $sopmId: number,//NOSONOR
    ) {
        super($features, $presets, $reminder)
        this.serialNumber = $serialNumber;
        this.probeType = $probeType;
        this.sopmId = $sopmId;

    }

}