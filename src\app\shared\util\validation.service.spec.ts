import { TestBed } from '@angular/core/testing';

import { ValidationService } from './validation.service';
import { ToastrService } from 'ngx-toastr';

describe('ValidationService', () => {
  let service: ValidationService;

  beforeEach(() => {
    const toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);

    TestBed.configureTestingModule({
      providers: [{ provide: ToastrService, useValue: toastrServiceMock },
      ]
    });
    service = TestBed.inject(ValidationService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
