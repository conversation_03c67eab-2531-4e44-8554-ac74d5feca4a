import { HttpErrorResponse } from '@angular/common/http';
import { ChangeDetectorRef, Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { UpdateAssociationService } from 'src/app//shared/update-association.service';
import { CancelBtn, FeatureAssociationConfirmationConfirmBtn, FeatureAssociationConfirmationHeader, FeatureAssociationConfirmationMessageForProbeDetail, ProbDetailResource, ProbListResource, TransferOrderResource } from 'src/app/app.constants';
import { BasicModelConfig } from 'src/app/model/common/BasicModelConfig.model';
import { ConfigBaseMappingRequest } from 'src/app/model/probe/ConfigBaseMappingRequest.model';
import { ConfigMappingRequest } from 'src/app/model/probe/ConfigMappingRequest.model';
import { ConfigureLicenceDetails } from 'src/app/model/probe/ConfigureLicenceDetails.model';
import { ConfigureLicenceResponse } from 'src/app/model/probe/ConfigureLicenceResponse.model';
import { ProbeDetailWithConfig } from 'src/app/model/probe/ProbeDetailWithConfig.model';
import { ProbeFeatureResponse } from 'src/app/model/probe/ProbeFeatureResponse.model';
import { ProbePresetResponse } from 'src/app/model/probe/ProbePresetResponse.model';
import { ProbeTypeResponse } from 'src/app/model/probe/probeTypeResponse.model';
import { EndDateOptions } from 'src/app/shared/enum/endDateOptions.enum';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { ProbeConfigType } from 'src/app/shared/enum/ProbeConfigType.enum';
import { ValidityEnum } from 'src/app/shared/enum/ValidityEnum.enum';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { ProbeService } from 'src/app/shared/Service/ProbeService/probe.service';
import { CommonsService } from 'src/app/shared/util/commons.service';

@Component({
  selector: 'app-assign-features',
  templateUrl: './assign-features.component.html',
  styleUrls: ['./assign-features.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class AssignFeaturesComponent implements OnInit {

  @Input('basicModelConfig') basicModelConfig: BasicModelConfig;
  @Input('configureLicenceDetails') configureLicenceDetails: ConfigureLicenceDetails;
  //Only Listing to pass probe type for reduce api call
  @Input('resource') resource: string;

  //Based on probeType feature set
  currentFeatures: Array<ProbeFeatureResponse> = [];
  currentPresets: Array<ProbePresetResponse> = [];
  featuresRequestList: Array<ConfigBaseMappingRequest> = [];
  presetsRequestList: Array<ConfigBaseMappingRequest> = [];
  isDownload: boolean = false;

  //single Probe Request 
  probeObject: ConfigMappingRequest = null;
  isDownloadLicenceDisplay: boolean = true;

  //Default Date
  defaultStartDate = new Date().getTime();
  defaultEndDateOptions = EndDateOptions.UNLIMITED;
  oneYearEndDateOptions = EndDateOptions.ONE_YEAR;
  unlimitedEndDateOptions = EndDateOptions.UNLIMITED;
  customEndDateOptions = EndDateOptions.CUSTOMDATE;
  validityPerpetual = ValidityEnum.PERPETUAL;
  validityOneYear = ValidityEnum.ONE_YEAR;
  featureProbeConfigType = ProbeConfigType.FEATURE;
  presetProbeConfigType = ProbeConfigType.PRESET;
  reloadPipe: boolean = true;
  allApiCall: boolean = false;
  loading: boolean = false;

  //permission
  setReminderOptionDisplayPermission: boolean = false;

  //setRemindarDisabled
  isReminderDisabled: boolean = true;

  constructor(private activeModal: NgbActiveModal,
    private probeApiService: ProbeApiService,
    private probeService: ProbeService,
    private cdr: ChangeDetectorRef,
    private commonsService: CommonsService,
    private toste: ToastrService,
    private exceptionService: ExceptionHandlingService,
    private updateAssociationService: UpdateAssociationService,
    private permissionService: PermissionService) { }

  ngOnInit() {
    this.getInitData();
  }

  /**
   * Get Features List
   * Get ProbeType List
   * 
   * <AUTHOR>
   */
  private async getInitData() {
    this.loading = true;
    this.setReminderOptionDisplayPermission = this.permissionService.getProbPermission(PermissionAction.ADD_PROBE_SET_REMINDER_OPTIONS_DISPLAY);
    let probeTypesResponse: Array<ProbeTypeResponse> = await this.probeApiService.getprobeTypeResponseList(false);
    let featuresList = await this.probeApiService.getFeaturesList();
    let presetsList = await this.probeApiService.getPresetsList();
    if (!(probeTypesResponse.length > 0 && featuresList.length > 0 && presetsList.length > 0)) {
      this.activeModal.close(false);
    }
    let isReminder: boolean = false;
    //Listing page to set all featuresRequestList with empty object
    if (this.resource == ProbListResource) {
      this.setDefaultCurrentFeatures(probeTypesResponse, this.configureLicenceDetails.type, featuresList);
      this.setDefaultCurrentPresets(probeTypesResponse, this.configureLicenceDetails.type, presetsList);
      this.featuresRequestList = this.probeService.getConfigListForAssign(featuresList, this.currentFeatures, this.featureProbeConfigType);
      this.presetsRequestList = this.probeService.getConfigListForAssign(presetsList, this.currentPresets, this.presetProbeConfigType);
    } else if (this.resource == ProbDetailResource) {
      //Detail page to set featuresRequestList with old data
      let probeDetailWithFeature: ProbeDetailWithConfig = await this.probeApiService.getAsyncProbeDetailInfo(this.configureLicenceDetails.id);
      this.setDefaultCurrentFeatures(probeTypesResponse, probeDetailWithFeature.type, featuresList);
      this.setDefaultCurrentPresets(probeTypesResponse, probeDetailWithFeature.type, presetsList);
      this.featuresRequestList = this.probeService.getFeaturesListForUpdate(featuresList, probeDetailWithFeature.features, this.currentFeatures);
      this.presetsRequestList = this.probeService.getPresetsListForUpdate(presetsList, probeDetailWithFeature.presets, this.currentPresets);
      isReminder = probeDetailWithFeature.reminder;
    }
    else if (this.resource === TransferOrderResource) {
      this.isDownloadLicenceDisplay = false;
      this.setDefaultCurrentFeatures(probeTypesResponse, this.configureLicenceDetails.type, featuresList);
      this.setDefaultCurrentPresets(probeTypesResponse, this.configureLicenceDetails.type, presetsList);
      this.featuresRequestList = this.configureLicenceDetails.probeObject.features.map(feature => {
        feature.endDateUi = isNullOrUndefined(feature.endDate) ? null : this.probeService.getEndDateOptions(feature);
        return feature;
      });

      this.presetsRequestList = this.configureLicenceDetails.probeObject.presets.map(preset => {
        preset.endDateUi = isNullOrUndefined(preset.endDate) ? null : this.probeService.getEndDateOptions(preset);
        return preset;
      });
      isReminder = this.configureLicenceDetails.probeObject.reminder;
    }
    this.probeObject = new ConfigMappingRequest(JSON.parse(JSON.stringify(this.featuresRequestList)), JSON.parse(JSON.stringify(this.presetsRequestList)), isReminder);

    this.setReminderDisabledForAssignConfig();
    this.loading = false;
    this.allApiCall = true;
  }

  /**
  * set default features
  * 
  * <AUTHOR>
  */
  private setDefaultCurrentFeatures(probeTypesResponse: Array<ProbeTypeResponse>, currentProbeType: string, featuresList: Array<ProbeFeatureResponse>): void {
    let features = probeTypesResponse.filter(obj => obj.displayName.toLowerCase() == currentProbeType.toLowerCase());
    if (features.length == 1) {
      this.currentFeatures = JSON.parse(JSON.stringify(features[0].features));
    } else {
      //If probe type Not exist in probeTypesResponse then Assign all the features
      this.currentFeatures = isNullOrUndefined(featuresList) ? [] : featuresList;
    }
  }

  /**
* set default presets
* 
* <AUTHOR>
*/
  private setDefaultCurrentPresets(probeTypesResponse: Array<ProbeTypeResponse>, currentProbeType: string, presetsList: Array<ProbePresetResponse>): void {
    let presets = probeTypesResponse.filter(obj => obj.displayName.toLowerCase() == currentProbeType.toLowerCase());
    if (presets.length == 1) {
      this.currentPresets = JSON.parse(JSON.stringify(presets[0].presets));
    } else {
      //If probe type Not exist in probeTypesResponse then Assign all the features
      this.currentPresets = isNullOrUndefined(presetsList) ? [] : presetsList;
    }
  }

  /**
   * set DownloadLicense Flag
   * 
   * @param isDownload 
   */
  public onChangeDownloadLicense(isDownload: boolean): void {
    this.isDownload = isDownload;
  }

  /**
   * close update feature dialog
   * 
   * <AUTHOR>
   */
  public close(): void {
    this.activeModal.close(new ConfigureLicenceResponse(false, null));
  }

  /**
   * close update feature dialog
   * 
   * <AUTHOR>
   */
  public accept(): void {
    this.activeModal.close(new ConfigureLicenceResponse(true, null));
  }


  /**
   * Reload the pipe for checkbox and radio button checked/unchecked
   * <AUTHOR>
   */
  public reloadUiWithPipe(): void {
    this.reloadPipe = false;
    this.cdr.detectChanges();
    this.reloadPipe = true;
    this.cdr.detectChanges();
  }


  /**
   * set Reminder in probe object
   * 
   * <AUTHOR>
   * @param isReminder 
   */
  public setIsReminder(isReminder: boolean): void {
    this.probeObject.reminder = isReminder;
  }

  /**
   * set Remindar 
   * 
   * <AUTHOR>
   */
  public setReminderDisabledForAssignConfig(): void {
    this.isReminderDisabled = this.probeObject.features.filter(obj => obj.enable).length == 0 && this.probeObject.presets.filter(obj => obj.enable).length == 0;
  }

  /**
   * features checkbox enebale/disable feature
   * startDate = new date
   * endDate = default set based on validity
   * 
   * <AUTHOR> 
   * @param featureId 
   * @param isFeatureEnable 
   * @param startDate 
   * @param featuresBaseResponse 
   */
  public onChangeFeaturesForUpdate(featureId: number, isFeatureEnable: boolean, startDate: number, featuresBaseResponse: ProbeFeatureResponse): void {
    let fetauresIndex = this.getFetauresIndex(featureId);
    if (fetauresIndex != -1) {
      this.probeObject.features[fetauresIndex].enable = isFeatureEnable;
      if (isFeatureEnable) {
        let endDate: EndDateOptions = this.commonsService.getFeatureDefalutValidty(featuresBaseResponse);
        this.probeObject.features[fetauresIndex].startDate = startDate;
        this.probeObject.features[fetauresIndex].endDate = this.commonsService.getEndDateConvertion(endDate, null);
        this.probeObject.features[fetauresIndex].endDateUi = endDate;
      } else {
        this.probeObject.features[fetauresIndex].startDate = null;
        this.probeObject.features[fetauresIndex].endDate = null;
        this.probeObject.features[fetauresIndex].endDateUi = null;
      }
    }
    this.updateReminderForUserAction();
  }


  /**
  * preset checkbox enebale/disable preset
  * startDate = new date
  * endDate = default set based on validity
  * 
  * <AUTHOR> 
  * @param presetId 
  * @param isPresetEnable 
  * @param startDate 
  * @param presetsBaseResponse 
  */
  public onChangePresetsForUpdate(presetId: number, isPresetEnable: boolean, startDate: number, presetsBaseResponse: ProbePresetResponse): void {
    let presetsIndex = this.getPresetsIndex(presetId);
    if (presetsIndex != -1) {
      this.probeObject.presets[presetsIndex].enable = isPresetEnable;
      if (isPresetEnable) {
        let endDate: EndDateOptions = this.commonsService.getFeatureDefalutValidty(presetsBaseResponse);
        this.probeObject.presets[presetsIndex].startDate = startDate;
        this.probeObject.presets[presetsIndex].endDate = this.commonsService.getEndDateConvertion(endDate, null);
        this.probeObject.presets[presetsIndex].endDateUi = endDate;
      } else {
        this.probeObject.presets[presetsIndex].startDate = null;
        this.probeObject.presets[presetsIndex].endDate = null;
        this.probeObject.presets[presetsIndex].endDateUi = null;
      }
    }
    this.updateReminderForUserAction();
  }


  /**
  * Update the Remainder options 
  * Note :If user select 12 month any Features then setReminder select
  * <AUTHOR>
  */
  public updateReminderForUserAction(): void {
    this.setReminderDisabledForAssignConfig();
    this.setIsReminder(this.probeService.setReminderForUserActionForAssignConfig(this.probeObject));
    this.reloadUiWithPipe();
  }

  /**
   * Get Index from probeObject.features based on feature Id
   * 
   * <AUTHOR>
   * @param featureId 
   * @returns 
   */
  public getFetauresIndex(featureId: number): number {
    return this.probeObject.features.findIndex(obj => obj.id == featureId);
  }

  /**
 * Get Index from probeObject.presets based on preset Id
 * 
 * <AUTHOR>
 * @param presetId 
 * @returns 
 */
  public getPresetsIndex(presetId: number): number {
    return this.probeObject.presets.findIndex(obj => obj.id == presetId);
  }



  /**
   * Features radion button options 12 month and Unlimited based on selection end date set
   * 
   * <AUTHOR>
   * @param featureId 
   * @param startDate 
   * @param endDate 
   */
  public onChangeFeaturesEndDateForUpdate(featureId: number, startDate: number, endDateOptions: EndDateOptions, customEndDate: Date): void {
    let index = this.getFetauresIndex(featureId);
    if (index != -1) {
      this.probeObject.features[index].enable = true;
      this.probeObject.features[index].startDate = startDate;
      this.probeObject.features[index].endDateUi = endDateOptions;
      this.probeObject.features[index].endDate = this.commonsService.getEndDateConvertion(endDateOptions, customEndDate);
    }
    this.updateReminderForUserAction();
  }

  /**
   * presets radion button options 12 month and Unlimited based on selection end date set
   * 
   * <AUTHOR>
   * @param presetId 
   * @param startDate 
   * @param endDate 
   */
  public onChangePresetsEndDateForUpdate(PresetId: number, startDate: number, endDateOptions: EndDateOptions, customEndDate: Date): void {
    let index = this.getPresetsIndex(PresetId);
    if (index != -1) {
      this.probeObject.presets[index].enable = true;
      this.probeObject.presets[index].startDate = startDate;
      this.probeObject.presets[index].endDateUi = endDateOptions;
      this.probeObject.presets[index].endDate = this.commonsService.getEndDateConvertion(endDateOptions, customEndDate);
    }
    this.updateReminderForUserAction();
  }

  /**
   * Submit Feature and open Confirmation Model
   * 
   * <AUTHOR>
   */
  public submitAssignFeature(): void {
    if (this.resource !== TransferOrderResource) {
      this.updateAssociationService.openUpdateAssociationModel(
        FeatureAssociationConfirmationHeader,
        FeatureAssociationConfirmationMessageForProbeDetail,
        FeatureAssociationConfirmationConfirmBtn,
        CancelBtn
      ).then(response => {
        if (response) {
          this.assignFeatureUpdate();
        }
      }).finally(() => {
      })
    } else {
      this.assignFeatureUpdate();
    }
  }

  /**
   * Assign feature APi call
   * 
   * <AUTHOR>
   */
  public assignFeatureUpdate(): void {
    this.loading = true;
    if (this.resource === TransferOrderResource) {
      this.activeModal.close(new ConfigureLicenceResponse(true, this.probeObject));
    } else {
      this.probeApiService.updateProbeFeatures(this.configureLicenceDetails.id, this.probeObject).subscribe({
        next: (response) => {
          this.toste.success(response.body.message);
          if (this.isDownload) {
            this.downloadProbe([this.configureLicenceDetails.id]);
          } else {
            this.accept();
            this.loading = false;
          }
        }, error: (error: HttpErrorResponse) => {
          this.loading = false;
          this.exceptionService.customErrorMessage(error);
          this.close();
        }
      });
    }
  }

  /**
   * Download Probe Feature License
   * 
   * <AUTHOR>
   * @param probeIdList 
   */
  public async downloadProbe(probeIdList: Array<number>): Promise<void> {
    await this.probeApiService.dowloadSasUriofFeatureLicenseAsync(probeIdList, null);
    this.accept();
  }
}
