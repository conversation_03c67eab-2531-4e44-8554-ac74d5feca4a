<div>
    <!-- model header start -->
    <div class="modal-header" style="display: flex;">
        <div>
            <label class="modal-title">{{ auditResponse?.action }}</label>
        </div>
        <div>
            <button type="button" class="btn btn-sm close-btn-icon" (click)="decline()"><i class="fa fa-times"
                    aria-hidden="true"></i></button>
        </div>
    </div>
    <!-- model header end -->
    <!---------------------------------->
    <!---------------------------------->
    <!-----------Sub Heading start-------->
    <!---------------------------------->
    <!---------------------------------->
    <div class="modal-sub-header mb-3">
        <div class="textEllips leftChildDiv">
            <span class="subTitle">
                <span *ngIf="moduleUniqueKeyDisplayName!=null"
                    class="subTitleBold">{{moduleUniqueKeyDisplayName}}&nbsp;-&nbsp;</span>
                <span class="subTitle">{{auditResponse?.moduleUniqueKey}}</span>
            </span>
        </div>
        <div class="textEllips rightChildDiv">
            <table aria-hidden="true" class="headertable">
                <tr>
                    <td><strong>Modified By </strong></td>
                    <td>: <span>{{auditResponse?.modifiedBy}}</span></td>
                </tr>
                <tr>
                    <td><strong>Modified Date & Time </strong></td>
                    <td>: <span>{{auditResponse?.modifiedDate |
                            date:dateTimeDisplayFormat}}</span>
                    </td>
                </tr>
            </table>
        </div>
    </div>
    <!---------------------------------->
    <!---------------------------------->
    <!-----------Sub Heading end-------->
    <!---------------------------------->
    <!---------------------------------->

    <!-- model body start -->
    <div class="modal-body">
        <div class="auditTable">
            <table class="table table-sm table-bordered" style="overflow-x: scroll;" aria-hidden="true">
                <thead>
                    <tr class="thead-light">
                        <th><span>Field</span></th>
                        <th *ngIf="!isOldDataColumeHide"><span>Old Value</span></th>
                        <th *ngIf="!isNewDataColumeHide"><span>New Value</span></th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let auditDetail of auditDetailResponse;">
                        <td class="fieldBold">
                            <span>{{auditDetail.title}}</span>
                        </td>
                        <td *ngIf="!isOldDataColumeHide">
                            <span>{{auditDetail.oldValue
                                |auditDataTypePipe:auditDetail:booleanKeyValueMap:enumKeyValueMap}}</span>
                        </td>
                        <td *ngIf="!isNewDataColumeHide">
                            <span>{{auditDetail.newValue
                                |auditDataTypePipe:auditDetail:booleanKeyValueMap:enumKeyValueMap}}</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Modal footer with Reverse Transfer Product button -->
    <div class="modal-footer">
        <!-- Reverse Transfer Product button -->
        <button type="button" *ngIf="auditResponse?.reverseButtonDisplay && isReversePermission"
            class="btn btn-sm btn-orange ok-button" id="reverseTransferProductBtn" (click)="reverseTransferProduct()"
            [disabled]="auditResponse?.reverseButtonDisable">Reverse Action</button>
    </div>
    <!-- model body end -->
</div>