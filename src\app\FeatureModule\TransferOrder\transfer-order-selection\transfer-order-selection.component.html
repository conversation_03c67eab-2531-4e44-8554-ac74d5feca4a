<div *ngIf="transferOrderEndUserSelectionDisaplay">
    <!-- Loading section -->
    <div class="ringLoading" *ngIf="loading">
        <div class="ringLoadingDiv">
            <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
        </div>
    </div>

    <body class="bg-white">
        <div class="container-fluid" id="transferrdOrderPage">
            <form [formGroup]="transferOrderForm">
                <!-- row start -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="row" class="headerAlignment">
                            <div>
                                <label class="childFlex h5-tag">
                                    Transfer Order - Select Order Number For Transfer
                                    {{ deviceSerialNumber }}
                                </label>
                            </div>

                            <div class="childFlex">
                                <!-------------------------------------------->
                                <!-- back button div start -->
                                <button class="btn btn-sm btn-outline-secondary role-back-btn mr-3"
                                    id="tranferredOrderPageBack" (click)="back()"><i class="fa fa-reply"
                                        aria-hidden="true"></i>&nbsp;&nbsp;Back</button>
                                <!-- back button div end -->
                                <!-------------------------------------------->

                                <!-------------------------------------------->
                                <!-- Refresh button div start -->
                                <button class="btn btn-sm btn-orange" id="salesOrderRefresh"
                                    (click)="salesOrderNumber()"><em class="fa fa-refresh"></em></button>
                                <!-- Refresh button div end -->
                                <!-------------------------------------------->
                            </div>
                        </div>

                        <div>
                            <label class="text-primary">* Select the Order Number to initiate the transfer
                                process</label>
                        </div>

                        <!-- Transfer Order DropDown -->
                        <div class="row">
                            <div class="col-md-12">
                                <!-- main card start -->
                                <div class="card">
                                    <div class="card-body">
                                        <div class="card shadow">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-lg-4 col-md-6 col-sm-12">
                                                        <!-- Sales Order Number form field start -->
                                                        <div class="form-group">
                                                            <div>
                                                                <label><strong class="">Sales Order
                                                                        Number</strong></label>
                                                            </div>
                                                            <div>
                                                                <div
                                                                    class="d-flex justify-content-center align-items-center gap-1">
                                                                    <ng-multiselect-dropdown name="field_order_type"
                                                                        class="w-100" [placeholder]="''"
                                                                        id="salesOrderNumber"
                                                                        [settings]="orderTypeSetting"
                                                                        [data]="salesOrderList"
                                                                        (onSelect)="enduserSeletion($event)"
                                                                        (onDeSelect)="endUserOrderDeSelect(null)"
                                                                        (click)="onItemClickValidation('salesOrderNumber')"
                                                                        formControlName="salesOrderNumber"
                                                                        [disabled]="isSalesOrderDisabled">
                                                                    </ng-multiselect-dropdown>

                                                                    <button class="btn btn-sm btn-orange radius-50 ml-2"
                                                                        id="addSalesOrderBtn"
                                                                        (click)="openManualSalesOrderField()"
                                                                        style="border-radius: 50%;"
                                                                        [disabled]="isSalesOrderDisabled">
                                                                        <em class="fa fa-plus"></em>
                                                                    </button>
                                                                </div>
                                                                <!-- Sales Order Number form field End -->
                                                                <!-- Error message for salesOrderNumber -->
                                                                <div class="mt-1"
                                                                    *ngIf="transferOrderForm.get('salesOrderNumber').invalid && (transferOrderForm.get('salesOrderNumber').touched || transferOrderForm.get('salesOrderNumber').dirty)">
                                                                    <span class="form-err   "
                                                                        *ngIf="transferOrderForm.get('salesOrderNumber').errors?.['required']">
                                                                        {{enterSalesOrderNumber}}
                                                                    </span>
                                                                </div>

                                                                <!-- Manual Sales Order TextBox Start -->
                                                                <div *ngIf="isSalesOrderDisabled">
                                                                    <div id="salesOrderField">
                                                                        <div class="form-group">
                                                                            <div
                                                                                class="d-flex justify-content-start align-items-center g-1 mt-1">
                                                                                <input type="text"
                                                                                    class="form-control sales-order-control mt-2 w-100"
                                                                                    formControlName="manualSalesOrderNumber">
                                                                                <button
                                                                                    class="btn btn-sm btn-orange radius-50 ml-2 mt-2"
                                                                                    id="closeSalesOrderBtn"
                                                                                    style="border-radius: 50%;"
                                                                                    (click)="closeManualSalesOrderField()">
                                                                                    <em class="fa fa-close"></em>
                                                                                </button>
                                                                            </div>
                                                                        </div>
                                                                        <!-- Error message for manualSalesOrderNumber -->
                                                                        <div class="mt-2"
                                                                            *ngIf="transferOrderForm.get('manualSalesOrderNumber').invalid && (transferOrderForm.get('manualSalesOrderNumber').touched || transferOrderForm.get('manualSalesOrderNumber').dirty)">
                                                                            <span class="form-err"
                                                                                *ngIf="transferOrderForm.get('manualSalesOrderNumber').errors?.['required']">
                                                                                {{enterSalesOrderNumber}}
                                                                            </span>
                                                                            <span class="form-err"
                                                                                *ngIf="transferOrderForm.get('manualSalesOrderNumber').errors?.['maxlength']">
                                                                                {{small_maxCharactersAllowedMessage}}
                                                                            </span>
                                                                            <span class="form-err"
                                                                                *ngIf="transferOrderForm.get('manualSalesOrderNumber').errors?.['pattern']">
                                                                                {{specialCharacterErrorMessage}}
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <!-- Manual Sales Order TextBox End -->
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="col-lg-4 col-md-6 col-sm-12"
                                                        *ngIf="isSalesOrderDisabled">
                                                        <!-- Customer Name form field start -->
                                                        <div class="form-group">
                                                            <label><strong class="">Customer name</strong></label>
                                                            <input type="text" class="form-control" name="customerName"
                                                                formControlName="customerName">
                                                            <!-- Error message for customerName -->
                                                            <div
                                                                *ngIf="transferOrderForm.get('customerName').invalid && (transferOrderForm.get('customerName').touched || transferOrderForm.get('customerName').dirty)">
                                                                <span class="form-err"
                                                                    *ngIf="transferOrderForm.get('customerName').errors?.['required']">
                                                                    {{enterCustomerName}}
                                                                </span>
                                                                <span class="form-err"
                                                                    *ngIf="transferOrderForm.get('customerName').errors?.['maxlength']">
                                                                    {{maxCharactersAllowedMessage}}
                                                                </span>
                                                                <span class="form-err"
                                                                    *ngIf="transferOrderForm.get('customerName').errors?.['pattern']">
                                                                    {{specialCharacterErrorMessage}}
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <!-- Customer Name form field End -->
                                                    </div>

                                                    <!-- countries form field start -->
                                                    <div class="col-lg-4 col-md-6 col-sm-12"
                                                        *ngIf="isSalesOrderDisabled">
                                                        <div class="form-group">
                                                            <label class="form-control-label" for="field_countries"
                                                                id="label_countries"><strong>Country</strong></label>
                                                            <!-- countries form field start -->
                                                            <ng-multiselect-dropdown id="field_countries"
                                                                name="countries" [placeholder]="''"
                                                                (click)="onItemClickValidation('countries')"
                                                                formControlName="countries" [settings]="countrySetting"
                                                                [data]="countriesList">
                                                            </ng-multiselect-dropdown>
                                                            <!-- countries form field end -->
                                                            <!-- Error message for countries -->
                                                            <div class="mt-1"
                                                                *ngIf="transferOrderForm.get('countries').invalid && (transferOrderForm.get('countries').touched || transferOrderForm.get('countries').dirty)">
                                                                <span class="form-err"
                                                                    *ngIf="transferOrderForm.get('countries').errors?.['required']">
                                                                    {{countryValidMessage}}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <!-- countries form field End -->
                                                </div>

                                                <div class="row d-flex" *ngIf="isSalesOrderDisabled">
                                                    <!-- Po Number form field start -->
                                                    <div class="col-lg-4 col-md-6 col-sm-12 ">
                                                        <div class="form-group">
                                                            <label><strong class="">PO#</strong></label>
                                                            <input type="text" class="form-control ponumber"
                                                                name="poNumber" formControlName="poNumber">
                                                            <!-- Error message for poNumber -->
                                                            <div
                                                                *ngIf="transferOrderForm.get('poNumber').invalid && (transferOrderForm.get('poNumber').touched || transferOrderForm.get('poNumber').dirty)">
                                                                <span class="form-err"
                                                                    *ngIf="transferOrderForm.get('poNumber').errors?.['maxlength']">
                                                                    {{maxCharactersAllowedMessage}}
                                                                </span>
                                                                <span class="form-err"
                                                                    *ngIf="transferOrderForm.get('poNumber').errors?.['pattern']">
                                                                    {{specialCharacterErrorMessage}}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <!-- Po Number form field End -->

                                                    <div class="col-lg-4 col-md-6 col-sm-12 ">
                                                        <!-- Customer E-mail form field start -->
                                                        <div class="form-group m-0">
                                                            <label><strong class="">Customer E-mail</strong></label>
                                                            <input type="text" class="form-control ponumber"
                                                                name="customerEmail" formControlName="customerEmail">
                                                        </div>
                                                        <!-- Customer E-mail form field End -->
                                                        <div
                                                            *ngIf="(transferOrderForm.get('customerEmail').touched || transferOrderForm.get('customerEmail').dirty) && transferOrderForm.get('customerEmail').invalid ">
                                                            <!-- email pattern validation error-->
                                                            <div
                                                                *ngIf="transferOrderForm.get('customerEmail').errors['pattern'] && !(transferOrderForm.controls['customerEmail'].hasError('maxlength'))">
                                                                <span class="alert-color font-12">
                                                                    {{enterValidEmail}}</span>
                                                            </div>
                                                            <!-- customer email maxlength validation error-->
                                                            <div
                                                                *ngIf="transferOrderForm.controls['customerEmail'].hasError('maxlength')">
                                                                <span
                                                                    class="alert-color font-12">{{maxCharactersAllowedMessage}}</span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Order Record Typw form field start -->
                                                    <div class="col-lg-4 col-md-6 col-sm-12">
                                                        <label class="form-control-label" for="field_countries"
                                                            id="label_countries"><strong>Order Record
                                                                Type</strong></label>
                                                        <ng-multiselect-dropdown name="field_order_record_type"
                                                            [placeholder]="''" formControlName="orderRecordType"
                                                            [settings]="orderRecordTypeSetting"
                                                            (click)="onItemClickValidation('orderRecordType')"
                                                            [data]="orderRecordTypeList">
                                                        </ng-multiselect-dropdown>
                                                        <!-- Error message for orderRecordType -->
                                                        <div class="mt-1"
                                                            *ngIf="transferOrderForm.get('orderRecordType').invalid && (transferOrderForm.get('orderRecordType').touched || transferOrderForm.get('orderRecordType').dirty)">
                                                            <span class="form-err"
                                                                *ngIf="transferOrderForm.get('orderRecordType').errors?.['required']">
                                                                {{orderRecordTypeValidationMessage}}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <!-- Order Record Type form field End -->
                                                </div>

                                                <div class="row" *ngIf="isSalesOrderDisabled">
                                                    <div
                                                        class="col-lg-4 col-md-6 col-sm-12 d-flex align-items-center justify-content-start mt-3">
                                                        <div
                                                            class="d-flex align-items-center justify-content-start gap-1">
                                                            <!-- Device Auto Lock form field start -->
                                                            <div
                                                                class="form-group d-flex align-items-center justify-content-start gap-2">
                                                                <label><strong class="text-nowrap">Device Auto
                                                                        Lock</strong></label>
                                                                <input type="checkbox"
                                                                    class="form-control deviceAutoLock"
                                                                    name="deviceAutoLock"
                                                                    formControlName="deviceAutoLock">
                                                            </div>
                                                            <!-- Device Auto Lock form field End -->

                                                            <!-- Probe Auto Lock form field start -->
                                                            <div
                                                                class="form-group d-flex align-items-center justify-content-start gap-2 ml-3">
                                                                <label><strong class="text-nowrap">Probe Auto
                                                                        Lock</strong></label>
                                                                <input type="checkbox"
                                                                    class="form-control deviceAutoLock"
                                                                    name="probeAutoLock"
                                                                    formControlName="probeAutoLock">
                                                            </div>
                                                            <!-- Probe Auto Lock form field End -->
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Modal footer with Next button -->
                                                <div class="modal-footer">
                                                    <!-- Next Button field start -->
                                                    <button type="button"
                                                        class="btn btn-sm btn-orange ok-button px-3 py-2"
                                                        id="orderTypeNextBtn" [disabled]="transferOrderForm.invalid"
                                                        (click)="showSelectionTable()">Next</button>
                                                    <!-- Next Button field end -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- row end -->
            </form>
        </div>
    </body>
</div>