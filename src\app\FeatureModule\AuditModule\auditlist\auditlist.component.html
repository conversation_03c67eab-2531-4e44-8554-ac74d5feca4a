<!-- loading start -->
<!-- loading gif start -->
<div class="ringLoading" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!-- loading gif end -->
<!-- loading end -->


<body *ngIf="listingPageDisplay">
    <!-- row start -->
    <div class="row">

        <!--############################################################-->
        <!--Filter start-->
        <!--############################################################-->
        <div class="col-md-3 pr-0" *ngIf="(!isFilterHidden)" id="auditFilterBtn">
            <label class="col-md-12 h5-tag">Filter</label>
            <div class="card mt-3">
                <div class="card-body">
                    <app-audit-filter [isFilterComponentInitWithApicall]="isFilterComponentInitWithApicall"
                        [auditSearchRequsetBody]="auditSearchRequsetBody"
                        [listPageRefreshForbackToDetailPage]="listPageRefreshForbackToDetailPage"
                        [archivedAuditSearch]="archivedAuditSearch">
                    </app-audit-filter>
                </div>
            </div>
        </div>
        <!--############################################################-->
        <!--Filter End-->
        <!--############################################################-->

        <!--table Block Start-->
        <div [className]="(isFilterHidden)?'col-md-12 pr-0':'col-md-9 pr-0'">
            <div class="container-fluid" id="auditList">
                <!--############################################################-->
                <!--############################################################-->
                <div class="row" class="headerAlignment">
                    <!--############################################################-->
                    <!--Left Side-->
                    <!--############################################################-->
                    <div class="childFlex">
                        <!----------------------------------------------->
                        <!------------Show/hide filter-------------------->
                        <!----------------------------------------------->
                        <div class="dropdown" id="hideShowFilter">
                            <button class="btn btn-sm btn-orange mr-3 ml-0 filter-symbol" (click)="toggleFilter()"
                                id="auditListHideShowButtonaccordion">
                                <i class=" fas fa-filter" aria-hidden="true"></i>
                                &nbsp;&nbsp;{{ hideShowFilterButtonText }}
                            </button>
                        </div>
                        <!----------------------------------------------->
                        <!------------Pagnatation drp-------------------->
                        <!----------------------------------------------->
                        <div>
                            <label class="mb-0">Show entry</label>
                            <select id="auditListShowEntry" [(ngModel)]="drpselectsize"
                                class="form-control form-control-sm" (change)="changeDataSize($event)">
                                <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                                    <option [value]="dataSize">{{ dataSize }}</option>
                                </ng-template>
                            </select>
                        </div>
                    </div>
                    <!--############################################################-->
                    <!--Right Side-->
                    <!--############################################################-->
                    <div class="childFlex">
                        <!------------------------------------------------>
                        <!----------------refresh------------------------->
                        <!------------------------------------------------>
                        <div>
                            <button class="btn btn-sm btn-orange" (click)="refreshFilter()" id="refresh_auditList"><em
                                    class="fa fa-refresh"></em></button>
                        </div>
                    </div>
                </div>
                <!--############################################################-->
                <!--############################################################-->
                <!-- selected sales order start -->
                <div>Total {{totalRecord}} Audits Record(s)</div>
                <!-- selected sales order end -->

                <!-------------------------------------------->
                <!-------------------------------------------->
                <!-- sales order table start -->
                <!-------------------------------------------->
                <!-------------------------------------------->
                <div class="commonTable">
                    <table class="table table-sm table-bordered" style="overflow-x: scroll;" aria-hidden="true">
                        <!--###########################################-->
                        <!-- table header Start -->
                        <!--###########################################-->
                        <thead>
                            <tr class="thead-light">
                                <th><span>Unique Id</span></th>
                                <th><span>Modules</span></th>
                                <th><span>Module Action</span></th>
                                <th><span>Modified By</span></th>
                                <th><span>Modified Date & Time</span></th>
                                <th><span>Activity</span></th>
                            </tr>
                        </thead>
                        <!--###########################################-->
                        <!-- table body start -->
                        <!--###########################################-->
                        <tbody>
                            <tr *ngFor="let auditResponse of auditListResponse;">
                                <ng-template
                                    [ngIf]="(auditResponse | moduleDetailPageDisplayPermissionCheckPipe:modulePermissionMapping ) && auditResponse?.modulePrimaryKey > auditSystemPrimeryId">
                                    <td (click)="openViewDetailPage(auditResponse?.module,auditResponse?.modulePrimaryKey,auditResponse?.moduleUniqueKey,auditResponse.action)"
                                        id="auditToDeatil">
                                        <span class="spanunderline">{{auditResponse?.moduleUniqueKey}}</span>
                                    </td>
                                </ng-template>
                                <ng-template
                                    [ngIf]="!(auditResponse | moduleDetailPageDisplayPermissionCheckPipe:modulePermissionMapping ) || auditResponse?.modulePrimaryKey == auditSystemPrimeryId">
                                    <td>
                                        <span>{{auditResponse?.moduleUniqueKey}}</span>
                                    </td>
                                </ng-template>
                                <td><span>{{auditResponse?.module}}</span></td>
                                <td><span>{{auditResponse?.action}}</span></td>
                                <td><span>{{auditResponse?.modifiedBy}}</span></td>
                                <td><span>{{auditResponse?.modifiedDate | date:dateTimeDisplayFormat}}</span></td>
                                <td>
                                    <span>
                                        <ng-template [ngIf]="auditResponse?.viewDetail">
                                            <div class="spanunderline" (click)="auditViewDetail(auditResponse)"
                                                id="auditViewDetail">
                                                View Activity</div>
                                        </ng-template>
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                        <!--###########################################-->
                        <!-- table body end -->
                        <!--###########################################-->
                    </table>
                </div>
                <!-------------------------------------------->
                <!-------------------------------------------->
                <!-- sales order table end -->
                <!-------------------------------------------->
                <!-------------------------------------------->

                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
                <!--pagination Start-->
                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
                <div>
                    <div>Showing {{totalRecordDisplay}} out of {{totalRecord}} Audits Record(s)</div>
                    <div class="float-right">
                        <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="itemsPerPage"
                            id="audit-pagination" [maxSize]="5" [rotate]="true" [boundaryLinks]="true"
                            (pageChange)="loadPage(page)">
                        </ngb-pagination>
                    </div>
                </div>
                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
                <!--pagination end-->
                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
            </div>
        </div>
        <!--table Block End-->
    </div>
    <!-- row end -->
</body>


<!--Device detail start-->
<div *ngIf="viewDetailPageDisplay[auditModuleEnum.DEVICE]">
    <app-device-detail [deviceIdInput]="moduleDetailId" [resource]="listAuditResource"
        (showDevice)="openViewDetailPage(null,null,null,null)">
    </app-device-detail>
</div>
<!--Device detail End-->

<!--Probe detail start-->
<div *ngIf="viewDetailPageDisplay[auditModuleEnum.PROBE]">
    <app-ots-probes-detail [probeId]="moduleDetailId" [resource]="listAuditResource"
        (showOtsProbe)="openViewDetailPage(null,null,null,null)"></app-ots-probes-detail>
</div>
<!--Probe detail End-->

<!--User detail start-->
<div *ngIf="viewDetailPageDisplay[auditModuleEnum.USER]">
    <app-user-detail [userId]="moduleDetailId" (singleuser)="openViewDetailPage(null,null,null,null)"></app-user-detail>
</div>
<!--User detail End-->

<!--Role detail start-->
<div *ngIf="viewDetailPageDisplay[auditModuleEnum.ROLE]">
    <app-role-detail (showRoleList)="openViewDetailPage(null,null,null,null)"
        [roleId]="moduleDetailId"></app-role-detail>
</div>
<!--Role detail End-->


<!--Sales Order detail start-->
<div *ngIf="viewDetailPageDisplay[auditModuleEnum.SALES_ORDER]">
    <app-sales-order-detail (showSalesOrderList)="openViewDetailPage(null,null,null,null)"
        [salesOrderId]="moduleDetailId">
    </app-sales-order-detail>
</div>
<!--Sales Order detail End-->

<!--Kit Detail  start-->
<div *ngIf="viewDetailPageDisplay[auditModuleEnum.KIT_MANAGEMENT]">
    <div *ngIf="brigeKitManagementPermission && bridgeKitManagementAction.includes(auditAction)">
        <app-kit-management-detail (showKitList)="openViewDetailPage(null,null,null,null)"
            [kitId]="moduleDetailId"></app-kit-management-detail>
    </div>
    <div *ngIf="otsKitManagementPermission && otsKitManagementAction.includes(auditAction)">
        <app-ots-kit-management-detail (otskitListPage)="openViewDetailPage(null,null,null,null)"
            [kitId]="moduleDetailId"></app-ots-kit-management-detail>
    </div>
</div>
<!--Kit Detail  end-->

<!--Probe Config Group Detail start-->
<div *ngIf="viewDetailPageDisplay[auditModuleEnum.PROBE_CONFIG_GROUP]">
    <app-probe-config-group-detail (showListingPage)="openViewDetailPage(null,null,null,null)"
        [probeConfigGroupId]="moduleDetailId"></app-probe-config-group-detail>
</div>
<!--Probe Config Group Detail End-->