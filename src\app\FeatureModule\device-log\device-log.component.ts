import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { ITEMS_PER_PAGE, SPECIAL_CHARACTER_PATTERN, SPECIAL_CHARACTER_ERROR_MESSAGE, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH, DeviceLogListResource } from '../../app.constants';
import { DeviceLog } from '../../model/Logs/DeviceLog.model';
import { DeviceLogPageResponse } from '../../model/Logs/DeviceLogPageResponse.model';
import { DeviceLogsSearchRequest } from '../../model/Logs/DeviceLogsSearchRequest.model';
import { LogType } from '../../model/log-type';
import { ExceptionHandlingService } from '../../shared/ExceptionHandling.service';
import { MessageService } from '../../shared/Message.service';
import { AuthJwtService } from '../../shared/auth-jwt.service';
import { PermissionAction } from '../../shared/enum/Permission/permissionAction.enum';
import { collapseFilterTextEnum } from '../../shared/enum/collapseFilterButtonText.enum';
import { DeviceLogApiCallService } from '../../shared/Service/Device-log/device-log-api-call.service';
import { PermissionService } from '../../shared/permission.service';
import { CommonsService } from '../../shared/util/commons.service';


@Component({
  selector: 'app-device-log',
  templateUrl: './device-log.component.html',
  styleUrls: ['./device-log.component.css']
})
export class DeviceLogComponent implements OnInit {
  itemsPerPage: number;
  page: number = 0;
  previousPage: number;
  totalItems: number;
  loading: boolean = false;
  totalLogDisplay: number = 0;
  totalLog: number = 0;
  dropdownSettings: any = {};
  logTypeList: LogType[];

  logsDetail: DeviceLog[];
  deviceIdInput: number;
  displaylog: boolean = true;
  displayDeviceDetail: boolean = false;
  downloadLogFilename: Array<string> = [];
  localdownloadLogFilename: Array<string> = [];
  drpselectsize: number = ITEMS_PER_PAGE;
  isFilterHidden: boolean = false;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;
  //Text box max limit set
  textBoxMaxLength: number = SMALL_TEXTBOX_MAX_LENGTH;
  textBoxMaxCharactersAllowedMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;
  deviceLogListResource = DeviceLogListResource;

  filterForm = this.fb.group({
    logType: [],
    logDeviceId: new FormControl('', [Validators.maxLength(this.textBoxMaxLength), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    logSerialNumber: new FormControl('', [Validators.maxLength(this.textBoxMaxLength), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    logExamId: new FormControl('', [Validators.maxLength(this.textBoxMaxLength), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    logFrom: new FormControl(''),
    logTo: new FormControl('')
  });

  // show entry selection
  dataSizes: string[] = [];

  //permission
  downloadDeviceLogPermission: boolean = false;
  deviceReaderPermission: boolean = false;

  //maxDate
  maxdate: Date = new Date();


  constructor(
    private fb: FormBuilder,
    private logDetailService: DeviceLogApiCallService,
    protected router: Router,
    private message: MessageService,
    private permissionService: PermissionService,
    private toste: ToastrService,
    private exceptionService: ExceptionHandlingService,
    private commonsService: CommonsService,
    private authservice: AuthJwtService
  ) { }

  ngOnInit() {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.dataSizes = this.commonsService.accessDataSizes();
      this.displaylog = true;
      this.displayDeviceDetail = false;
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.drpselectsize = ITEMS_PER_PAGE;
      this.page = 0;
      this.previousPage = 1;
      this.getInitCall();
      this.clearFilter();
      this.setLogPermission();
    }
  }

  private setLogPermission(): void {
    this.downloadDeviceLogPermission = this.permissionService.getDeviceLogPermission(PermissionAction.DOWNLOAD_DEVICE_LOG_ACTION);
    this.deviceReaderPermission = this.permissionService.getDevicePermission(PermissionAction.GET_DEVICE_ACTION);
  }

  public async getInitCall(): Promise<void> {
    this.dropdownSettings = {
      idField: 'key',
      textField: 'value',
      singleSelection: true,
      itemsShowLimit: 1,
      allowSearchFilter: false
    };
    this.logTypeList = []
    this.loading = true;
    this.logDetailService.getLogTypes()?.subscribe({
      next: (res: HttpResponse<any>) => {
        this.logTypeList = res.body;
      }, error: (error: any) => {
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  /**
   * Clear all filter
   */
  private clearAllFilter(): void {
    this.filterForm.get('logType').setValue(null);
    this.filterForm.get('logDeviceId').setValue(null);
    this.filterForm.get('logSerialNumber').setValue(null);
    this.filterForm.get('logExamId').setValue(null);
    this.filterForm.get('logFrom').setValue(null);
    this.filterForm.get('logTo').setValue(null);
    this.page = 0;
    this.downloadLogFilename = [];
  }

  /**
   * Clear all the filter and reload
   */
  public clearFilter(): void {
    this.clearAllFilter()
    this.loadAll();
  }

  /**
  * Refresh Page
  * 
  * <AUTHOR>
  */
  public refreshPage(): void {
    this.page = 0;
    this.loadAll();
  }

  /**
   * search Log Filter
   * 
   * <AUTHOR>
   */
  public searchLogFilter(): void {
    let logType = this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('logType').value) ? null : this.filterForm.get('logType').value;
    let logDeviceId = this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('logDeviceId').value) ? null : this.filterForm.get('logDeviceId').value;
    let logSerialNumber = this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('logSerialNumber').value) ? null : this.filterForm.get('logSerialNumber').value;
    let logExamId = this.commonsService.checkValueIsNullOrEmpty(this.filterForm.get('logExamId').value) ? null : this.filterForm.get('logExamId').value;
    let getStartdate = this.filterForm.get('logFrom').value;
    let logStart = (isNullOrUndefined(getStartdate) || getStartdate == "" || Number.isNaN(getStartdate)) ? null : new Date(getStartdate);
    let getEndDate = this.filterForm.get('logTo').value;
    let logEnd = (isNullOrUndefined(getEndDate) || getEndDate == "" || Number.isNaN(getEndDate)) ? null : this.commonsService.getEndTimeOfDay(new Date(getEndDate));
    this.searchLogFilterValidate(logType, logDeviceId, logSerialNumber, logExamId, logStart, logEnd);
  }

  /**
   * Validate Log Filter value
   * 
   * <AUTHOR>
   * @param logType 
   * @param logDeviceId 
   * @param logSerialNumber 
   * @param logExamId 
   * @param logStart 
   * @param logEnd 
   */
  private searchLogFilterValidate(logType: string, logDeviceId: string, logSerialNumber: string, logExamId: string, logStart: any, logEnd: any) {
    if (logType == null && logDeviceId == null && logSerialNumber == null && logExamId == null && logStart == null && logEnd == null) {
      this.toste.info(this.message.empty_filter);
    } else if (logStart == null && logEnd != null) {
      this.toste.info(this.message.empty_startdate);
    } else if (logStart != null && logEnd == null) {
      this.toste.info(this.message.empty_enddate);
    } else if (logStart != null && logEnd != null && logStart > logEnd) {
      this.toste.info(this.message.invalid_date);
    }
    else {
      this.page = 0;
      this.downloadLogFilename = [];
      this.loadAll();
    }
  }
  private logTypeValue(value: LogType[]): string {
    if (!isNullOrUndefined(value) && value.length == 1) {
      return value[0].value;
    }
    return null;
  }

  private getLogsSearchRequest(): DeviceLogsSearchRequest {
    let allFormValue = this.filterForm.value;
    let logType = this.logTypeValue(allFormValue.logType);
    this.filterForm.get('logDeviceId').setValue(this.commonsService.checkNullFieldValue(this.filterForm.get('logDeviceId').value));
    this.filterForm.get('logSerialNumber').setValue(this.commonsService.checkNullFieldValue(this.filterForm.get('logSerialNumber').value));
    this.filterForm.get('logExamId').setValue(this.commonsService.checkNullFieldValue(this.filterForm.get('logExamId').value));
    let logStartDate = this.commonsService.checkValueIsNullOrEmpty(allFormValue.logFrom) ? null : new Date(allFormValue.logFrom).getTime();
    let logEndDate = this.commonsService.checkValueIsNullOrEmpty(allFormValue.logTo) ? null : this.commonsService.getEndTimeOfDay(new Date(allFormValue.logTo)).getTime();
    return new DeviceLogsSearchRequest(
      logType,
      this.filterForm.get('logDeviceId').value,
      this.filterForm.get('logSerialNumber').value,
      this.filterForm.get('logExamId').value,
      logStartDate,
      logEndDate
    );
  }

  public loadAll(): void {
    this.loading = true;
    if (this.filterForm.invalid) {
      this.filterForm.reset();
    }
    this.logDetailService
      .getLogDetail(
        this.getLogsSearchRequest(),
        {
          page: this.page - 1,
          size: this.itemsPerPage,
        }
      )
      ?.subscribe({
        next: (res: HttpResponse<DeviceLogPageResponse>) => {
          if (res.status != 200 || res.body == null) {
            this.logsDetail = [];
            this.loading = false;
            this.totalLogDisplay = 0;
            this.totalLog = 0;
          } else {
            this.paginateLogs(res.body);
            this.setLocalFileName(res.body.content)
          }
        },
        error: (error: HttpErrorResponse) => {
          this.loading = false;
          this.exceptionService.customErrorMessage(error);
        }
      });
  }
  setLocalFileName(fileList: DeviceLog[]) {
    this.localdownloadLogFilename = [];
    for (let index in fileList) {
      this.localdownloadLogFilename.push(fileList[index]["fileName"]);
    }
    this.defaultSelectAll();
  }
  changeDataSize(datasize): void {
    this.loading = true;
    this.itemsPerPage = datasize.target.value;
    this.loadAll();
  }

  protected paginateLogs(data: any): void {
    this.totalItems = parseInt(data.totalElements, 10);
    this.logsDetail = data.content;
    this.page = data.number + 1;
    this.totalLog = data.totalElements;
    this.totalLogDisplay = data.numberOfElements;
    this.loading = false;
  }

  loadPage(page: number): void {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.transition();
    }
  }

  transition(): void {
    this.router.navigate(['/modules']);
    this.loadAll();
  }

  deviceDetailModel(deviceIdPk: number): void {
    this.deviceIdInput = deviceIdPk;
    this.displaylog = false;
    this.displayDeviceDetail = true;
  }

  showLog(): void {
    this.displaylog = true;
    this.displayDeviceDetail = false;
    this.downloadLogFilename = [];
    this.loadAll();
  }

  setDownloadLogFileName(event, fileName: string): void {
    if (event.target.checked) {
      this.downloadLogFilename.push(fileName);
    }
    else {
      this.downloadLogFilename.splice((this.downloadLogFilename.indexOf(fileName)), 1);
    }
    this.defaultSelectAll();
  }

  deSelectLogCheckBox() {
    let chkseleclallLog = (<HTMLInputElement[]><any>document.getElementsByName("chkseleclallLog"));
    chkseleclallLog[0].checked = false;
    let deviceLogCheckox = (<HTMLInputElement[]><any>document.getElementsByName("deviceLog[]"));
    let deviceLogLength = deviceLogCheckox.length;
    for (let index = 0; index < deviceLogLength; index++) {
      deviceLogCheckox[index].checked = false;
    }
  }

  downloadLogs(): void {
    if (this.downloadLogFilename.length > 0) {
      this.loading = true;
      this.logDetailService
        .getLogFileUrl(
          {
            fileNames: this.downloadLogFilename
          }
        )
        ?.subscribe({
          next: (res: HttpResponse<any>) => {
            let token = this.logDetailService.getToken();
            this.logDetailService.downloadMyFile(res.body['url'] + token, "logs.zip");
            this.downloadLogFilename = [];
            this.deSelectLogCheckBox();
            this.loading = false;
          },
          error: (error: HttpErrorResponse) => {
            this.exceptionService.customErrorMessage(error);
            this.deSelectLogCheckBox();
            this.loading = false;
          }
        });
    } else {
      this.toste.info("Please Select Log(s)")
    }

  }



  downloadMyFile(filename: any, attachmentUrl: any) {
    const link = document.createElement('a');
    link.setAttribute('target', '_blank');
    link.setAttribute('href', attachmentUrl);
    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
  }

  defaultSelectLog(name: string): boolean {
    let index = this.downloadLogFilename.findIndex(id => id == name);

    if (index >= 0) {

      return true;
    }
    else {

      return false;
    }

  }

  defaultSelectAll(): void {
    let res: boolean = false;
    for (let index in this.localdownloadLogFilename) {
      let filename = this.localdownloadLogFilename[index];
      let deviceIndex = this.downloadLogFilename.findIndex(name => name == filename);

      if (deviceIndex < 0) {
        res = false;
        break;
      }
      else {
        res = true;
      }

    }
    const selectDevice = document.getElementById("selectLog") as HTMLInputElement;
    if (selectDevice) {
      selectDevice.checked = res;
    }
  }

  selectAllLog(event): void {
    let logCheckox = (<HTMLInputElement[]><any>document.getElementsByName("deviceLog[]"));

    let l = logCheckox.length;
    if (event.target.checked) {
      for (let i = 0; i < l; i++) {
        logCheckox[i].checked = true;
      }
      for (let index in this.localdownloadLogFilename) {
        let filename = this.localdownloadLogFilename[index];
        let memberindex = this.downloadLogFilename.findIndex(name => name == filename);
        if (memberindex < 0) {
          this.downloadLogFilename.push(this.localdownloadLogFilename[index]);
        }
      }
    }
    else {
      for (let i = 0; i < l; i++) {
        logCheckox[i].checked = false;
      }
      for (let index in this.localdownloadLogFilename) {
        let removeFilename = this.localdownloadLogFilename[index];
        let memberIndexRemove = this.downloadLogFilename.findIndex(name => name == removeFilename);
        this.downloadLogFilename.splice(memberIndexRemove, 1);
      }
    }
  }

  /**
  * Toggle Filter
  * 
  */
  public toggleFilter(): void {
    this.isFilterHidden = !this.isFilterHidden;
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }
}
