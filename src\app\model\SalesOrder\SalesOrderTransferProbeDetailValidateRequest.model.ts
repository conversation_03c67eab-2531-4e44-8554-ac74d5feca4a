import { ConfigMappingRequest } from "../probe/ConfigMappingRequest.model";
import { SalesOrderTransferProductDetailValidateRequest } from "./SalesOrderTransferProductDetailValidateRequest.model";

export class SalesOrderTransferProbeDetailValidateRequest extends SalesOrderTransferProductDetailValidateRequest {
    probeId: number;
    configMappingRequest: ConfigMappingRequest;

    constructor(sourceSopmId: number, destinationSopmId: number, probeId: number, configMappingRequest: ConfigMappingRequest) {
        super(sourceSopmId, destinationSopmId);
        this.probeId = probeId;
        this.configMappingRequest = configMappingRequest;
    }
}