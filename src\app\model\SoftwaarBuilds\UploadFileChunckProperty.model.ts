export class UploadFileChunckProperty {
    totalBytesRemaining: number;
    currentFilePointer: number;
    maxBlockSize: number;
    attachmentFile: File;
    blockIdPrefix: string;
    blockIds: Array<any>;

    constructor($totalBytesRemaining: number, $currentFilePointer: number, $maxBlockSize: number, $attachmentFile: File, $blockIdPrefix: string, $blockIds: Array<any>) {
        this.totalBytesRemaining = $totalBytesRemaining;
        this.currentFilePointer = $currentFilePointer;
        this.maxBlockSize = $maxBlockSize;
        this.attachmentFile = $attachmentFile;
        this.blockIdPrefix = $blockIdPrefix;
        this.blockIds = $blockIds;
    }

}