import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DeleteSoftwareBuildDialogComponent } from './delete-software-build-dialog.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

describe('DeleteInventoryDialogComponent', () => {
  let component: DeleteSoftwareBuildDialogComponent;
  let fixture: ComponentFixture<DeleteSoftwareBuildDialogComponent>;
  let mockActiveModal: jasmine.SpyObj<NgbActiveModal>;

  beforeEach(async () => {
    // Create a mock for NgbActiveModal
    mockActiveModal = jasmine.createSpyObj('NgbActiveModal', ['close', 'dismiss']);

    await TestBed.configureTestingModule({
      declarations: [DeleteSoftwareBuildDialogComponent],
      providers: [NgbActiveModal, { provide: NgbActiveModal, useValue: mockActiveModal }]
    })
      .compileComponents();

    fixture = TestBed.createComponent(DeleteSoftwareBuildDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });


  it('should call decline and close the modal with false', () => {
    // Act
    component.decline();

    // Assert
    expect(mockActiveModal.close).toHaveBeenCalledWith(false);
  });

  it('should call dismiss and dismiss the modal', () => {
    // Act
    component.dismiss();

    // Assert
    expect(mockActiveModal.dismiss).toHaveBeenCalled();
  });
});
