import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { OtsKitManagementFilterComponent } from './ots-kit-management-filter.component';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';

describe('OtsKitManagementFilterComponent', () => {
  let component: OtsKitManagementFilterComponent;
  let fixture: ComponentFixture<OtsKitManagementFilterComponent>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);

    await TestBed.configureTestingModule({
      declarations: [OtsKitManagementFilterComponent],
      imports: [NgMultiSelectDropDownModule.forRoot(),
        ReactiveFormsModule],
      providers: [
        SessionStorageService,
        HidePermissionNamePipe,
        LocalStorageService,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        ConfirmDialogService,
        commonsProviders(toastrServiceMock),
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(OtsKitManagementFilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
