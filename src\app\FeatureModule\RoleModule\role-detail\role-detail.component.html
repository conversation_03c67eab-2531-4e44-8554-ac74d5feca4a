<!------------------------------------------->
<!-- loadin start -->
<!------------------------------------------->
<div class="ringLoading" *ngIf="loading">
  <div class="ringLoadingDiv">
    <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
  </div>
</div>
<!------------------------------------------->
<!-- loadin end -->
<!------------------------------------------->

<body class="bg-white">
  <!------------------------------------------->
  <!-- container fluid start -->
  <!------------------------------------------->
  <div class="container-fluid">
    <div class="row">
      <div class="col-md-12">
        <!-------------------------------------------->
        <!-------------------------------------------->
        <!-------------------------------------------->
        <div class="row" class="headerAlignment">
          <label class="childFlex h5-tag">Role Details</label>
          <div class="childFlex">

            <!-------------------------------------------->
            <!---------operation dropdown start----->
            <ng-template [ngIf]="operationsList.length > 1">
              <select id="roleOperation" class="form-control form-control-sm mr-3"
                (change)="changeOperation($any($event.target)?.value)">
                <ng-template ngFor let-operation [ngForOf]="operationsList">
                  <option [value]="operation">{{ operation }}</option>
                </ng-template>
              </select>
            </ng-template>
            <!--operation dropdown end  -->
            <!-------------------------------------------->

            <!-------------------------------------------->
            <!-- back button div start -->
            <button class="btn btn-sm btn-outline-secondary role-back-btn" (click)="back()"><i class="fa fa-reply"
                aria-hidden="true"></i>&nbsp;&nbsp;Back</button>
            <!-- back button div end -->
            <!-------------------------------------------->

            <!-- refresh button start -->
            <button class="btn btn-sm btn-orange ml-2" id="roleDetailRefresh" (click)="refreshRoleDetailPage()"><em
                class="fa fa-refresh"></em></button>
            <!-- refresh button end -->

          </div>
        </div>
        <!-------------------------------------------->
        <!-------------------------------------------->
        <!-------------------------------------------->
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-body">
                <div class="card shadow">
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-3">
                        <div class="form-group">
                          <label><strong class="">Name</strong></label>
                          <input type="text" class="form-control" name="serialNumber" [value]="roleResponse?.name"
                            readonly>
                        </div>
                      </div>

                      <div class="col-md-9">
                        <div class="form-group">
                          <label><strong class="">Description</strong></label>
                          <input type="text" class="form-control commonEllips" name="probeType"
                            [value]="roleResponse?.description" readonly>
                        </div>
                      </div>

                      <!--Module start-->
                      <div class="col-md-12">
                        <div class="form-group">
                          <label><strong class="">Module(s)</strong></label>
                          <div class="detailListItem">
                            <span>{{roleResponse?.permissions | getPermissionModuleName | printListPipe}}</span>
                          </div>
                        </div>
                      </div>
                      <!--Module End-->

                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-------------------------------------------->
        <!-------------------------------------------->
        <!-------------------------------------------->
        <div class="row mt-2">
          <div class="col-md-12">
            <div class="card">
              <div class="card-body">
                <div class="card shadow">
                  <div class="card-body">
                    <div class="container">
                      <label class="mb-3 h5-tag"><span>Permissions</span></label>
                      <!------------------------------------------->
                      <!-- table start -->
                      <!------------------------------------------->
                      <table class="table table-sm table-bordered" aria-hidden="true">
                        <thead>
                          <tr class="thead-light">
                            <th>Name</th>
                            <th>Description</th>
                          </tr>
                        </thead>
                        <tbody *ngIf="roleResponse != null">
                          <ng-template ngFor let-permisionObj
                            [ngForOf]="roleResponse.permissions | hidePermissionNamePipe">
                            <tr>
                              <td>{{permisionObj?.name}}</td>
                              <td>{{permisionObj?.description}}</td>
                            </tr>
                          </ng-template>
                        </tbody>
                      </table>
                      <!------------------------------------------->
                      <!-- table end -->
                      <!------------------------------------------->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>
    <!-- row end -->
  </div>
  <!------------------------------------------->
  <!-- container fluid end -->
  <!------------------------------------------->
</body>