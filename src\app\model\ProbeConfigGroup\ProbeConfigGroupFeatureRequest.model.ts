import { ValidityEnum } from "src/app/shared/enum/ValidityEnum.enum";

export class ProbeConfigGroupFeatureRequest {
    featureId: number;
    probeTypeMasterFeatureMasterMappingId: number;
    validity: ValidityEnum;

    constructor($featureId: number, $probeTypeMasterFeatureMasterMappingId: number, $validity: ValidityEnum) {
        this.featureId = $featureId;
        this.probeTypeMasterFeatureMasterMappingId = $probeTypeMasterFeatureMasterMappingId;
        this.validity = $validity;
    }
}
