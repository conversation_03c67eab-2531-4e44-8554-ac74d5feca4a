import { HttpErrorResponse, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { throwError } from 'rxjs';
import { AuditModuleWithActionResponse } from 'src/app/model/Audit/AuditModuleWithActionResponse.model';
import { AuditDetailResponse } from 'src/app/model/Audit/auditDetailResponse.model';
import { AuditListPageResponse } from 'src/app/model/Audit/auditListPageResponse';
import { AuditSearchRequsetBody } from 'src/app/model/Audit/auditSearchRequsetBody';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { CommonsService } from '../../util/commons.service';
import { AuditApiCallService } from './audit-api-call.service';
import { KeyValueMappingServiceService } from '../../util/key-value-mapping-service.service';
import { PrintListPipe } from '../../pipes/printList.pipe';
import { LocalStorageService } from 'ngx-webstorage';

describe('AuditApiCallService', () => {
  let service: AuditApiCallService;
  let httpMock: HttpTestingController;
  let exceptionServiceSpy: jasmine.SpyObj<ExceptionHandlingService>;
  let configInjectServiceSpy: jasmine.SpyObj<ConfigInjectService>;
  let commonsServiceSpy: jasmine.SpyObj<CommonsService>;

  beforeEach(() => {
    exceptionServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    configInjectServiceSpy = jasmine.createSpyObj('ConfigInjectService', ['getServerApiUrl']);
    commonsServiceSpy = jasmine.createSpyObj('CommonsService', ['handleError']);

    configInjectServiceSpy.getServerApiUrl.and.returnValue('http://example.com/');

    TestBed.configureTestingModule({
      imports: [],
      providers: [
        AuditApiCallService,
        KeyValueMappingServiceService,
        PrintListPipe,
        LocalStorageService,
        { provide: ExceptionHandlingService, useValue: exceptionServiceSpy },
        { provide: ConfigInjectService, useValue: configInjectServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting()
      ]
    });

    service = TestBed.inject(AuditApiCallService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should fetch audit module list', async () => {
    const mockResponse: AuditModuleWithActionResponse[] = [new AuditModuleWithActionResponse(null, null, null, null)];

    const promise = service.getAuditModuleList();
    const req = httpMock.expectOne('http://example.com/api/audit/module/actions');
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);

    await expectAsync(promise).toBeResolvedTo(mockResponse);
  });

  it('should handle error in getAuditModuleList', async () => {
    const errorResponse = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });

    const promise = service.getAuditModuleList();
    const req = httpMock.expectOne('http://example.com/api/audit/module/actions');
    req.flush({}, errorResponse);

    await expectAsync(promise).toBeResolvedTo([]);
    expect(exceptionServiceSpy.customErrorMessage).toHaveBeenCalledWith(jasmine.objectContaining({
      status: 500,
      statusText: 'Server Error',
      message: jasmine.stringMatching(/Http failure response for .*: 500 Server Error/),
      url: jasmine.any(String), // Allows any string URL
      error: jasmine.any(Object) // Allows any object instead of null
    }));

  });

  it('should fetch audit list', () => {
    const mockRequestBody: AuditSearchRequsetBody = new AuditSearchRequsetBody(null, null, null, null, null, null);
    const mockResponse: AuditListPageResponse = new AuditListPageResponse(null, 1, false, 10, 10, true, null, 10, 0, false, []);

    service.getAuditList(mockRequestBody, {}, false).subscribe(response => {
      expect(response.body).toEqual(mockResponse);
    });

    const req = httpMock.expectOne('http://example.com/api/audit/search?isArchivedData=false');
    expect(req.request.method).toBe('POST');
    req.flush(mockResponse);
  });

  it('should fetch audit detail', () => {
    const mockResponse: AuditDetailResponse[] = [new AuditDetailResponse(null, null, null, null, null, null)];
    const auditId = 1;

    service.getAuditDetail(auditId, false).subscribe(response => {
      expect(response.body).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`http://example.com/api/audit/details/${auditId}?isArchivedData=false`);
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);
  });

  it('should handle error in getAuditList', () => {
    const mockRequestBody: AuditSearchRequsetBody = new AuditSearchRequsetBody(null, null, null, null, null, null);
    const errorResponse = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
    commonsServiceSpy.handleError.and.returnValue(throwError(() => errorResponse));

    service.getAuditList(mockRequestBody, {}, false).subscribe({
      error: (error) => {
        expect(error).toEqual(errorResponse);
      }
    });

    const req = httpMock.expectOne('http://example.com/api/audit/search?isArchivedData=false');
    expect(req.request.method).toBe('POST');
    req.flush({}, errorResponse);
  });
});
