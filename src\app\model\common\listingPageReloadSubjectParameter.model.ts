export class ListingPageReloadSubjectParameter {
    isReloadData: boolean;
    isDefaultPageNumber: boolean;
    isClearFilter: boolean;
    isMovePrevPage: boolean;
    isOtherAction: boolean;


    constructor($isReloadData: boolean, $isDefaultPageNumber: boolean, $isClearFilter: boolean, $isMovePrevPage: boolean,
        $isOtherAction: boolean = false) {
        this.isReloadData = $isReloadData;
        this.isDefaultPageNumber = $isDefaultPageNumber;
        this.isClearFilter = $isClearFilter;
        this.isMovePrevPage = $isMovePrevPage;
        this.isOtherAction = $isOtherAction;
    }

}