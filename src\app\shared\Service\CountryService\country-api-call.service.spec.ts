import { HttpClient, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { throwError } from 'rxjs';
import { CountryPageResponse } from 'src/app/model/Country/CountryPageResponse.model';
import { CountryRequestBody } from 'src/app/model/Country/CountryRequestBody.model';
import { CreateCountryRequest } from 'src/app/model/Country/CreateUpdateCountry/CreateUpdateCountryRequest.model';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { CommonsService } from '../../util/commons.service';
import { CountryApiCallService } from './country-api-call.service';

describe('CountryApiCallService', () => {
    let service: CountryApiCallService;
    let httpMock: HttpTestingController;
    let configInjectServiceMock: jasmine.SpyObj<ConfigInjectService>;
    let commonsServiceMock: jasmine.SpyObj<CommonsService>;
    let mockServerUrl = 'http://test-api.com/';

    beforeEach(() => {
        // Create spies for the injected services
        configInjectServiceMock = jasmine.createSpyObj('ConfigInjectService', ['getServerApiUrl']);
        commonsServiceMock = jasmine.createSpyObj('CommonsService', ['handleError']);

        // Set up the mock return value for getServerApiUrl
        configInjectServiceMock.getServerApiUrl.and.returnValue(mockServerUrl);

        // Set up the handleError mock to return an observable that can be subscribed to
        commonsServiceMock.handleError.and.callFake((error) => {
            return throwError(() => new Error('Test error'));
        });

        TestBed.configureTestingModule({
            imports: [],
            providers: [
                CountryApiCallService,
                { provide: ConfigInjectService, useValue: configInjectServiceMock },
                { provide: CommonsService, useValue: commonsServiceMock },
                provideHttpClient(withInterceptorsFromDi()),
                provideHttpClientTesting()
            ]
        });

        service = TestBed.inject(CountryApiCallService);
        httpMock = TestBed.inject(HttpTestingController);
    });

    afterEach(() => {
        // Verify that there are no outstanding requests
        httpMock.verify();
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    describe('getCountryList', () => {
        it('should send a POST request with correct URL and body', () => {
            // Arrange
            const mockRequestBody: CountryRequestBody = new CountryRequestBody("xyz", "xyz")
            const mockReq = { page: 0, size: 10 };
            const mockResponse: CountryPageResponse = new CountryPageResponse(null, 1, false, 10, 10, true, null, 10, 0, false)

            // Act
            service.getCountryList(mockRequestBody, mockReq).subscribe(response => {
                expect(response.body).toEqual(mockResponse);
            });

            // Assert
            const req = httpMock.expectOne(`${mockServerUrl}api/country/search?page=0&size=10`);
            expect(req.request.method).toBe('POST');
            expect(req.request.body).toEqual(mockRequestBody);

            // Respond with mock data
            req.flush(mockResponse);
        });

        it('should handle errors through CommonsService.handleError', () => {
            // Arrange
            const mockRequestBody: CountryRequestBody = new CountryRequestBody("xyz", "xyz")
            const mockReq = { page: 0, size: 10 };

            // Act
            service.getCountryList(mockRequestBody, mockReq).subscribe({
                error: error => {
                    // Assert
                    expect(error).toBeTruthy();
                    expect(commonsServiceMock.handleError).toHaveBeenCalled();
                }
            });

            // Simulate a network error
            const req = httpMock.expectOne(`${mockServerUrl}api/country/search?page=0&size=10`);
            req.error(new ProgressEvent('error'));
        });
    });

    describe('createCountry', () => {
        it('should send a POST request with correct URL and body', () => {
            // Arrange
            const mockCountryRequest: CreateCountryRequest = new CreateCountryRequest('Test Country', [1, 2, 3])

            const mockResponse: SuccessMessageResponse = new SuccessMessageResponse('Country created successfully')

            // Act
            service.createCountry(mockCountryRequest).subscribe(response => {
                expect(response.body).toEqual(mockResponse);
            });

            // Assert
            const req = httpMock.expectOne(`${mockServerUrl}api/country`);
            expect(req.request.method).toBe('POST');
            expect(req.request.body).toEqual(mockCountryRequest);

            // Respond with mock data
            req.flush(mockResponse);
        });

        it('should handle errors through CommonsService.handleError', () => {
            // Arrange
            const mockCountryRequest: CreateCountryRequest = new CreateCountryRequest('Test Country', [1, 2, 3])

            // Act
            service.createCountry(mockCountryRequest).subscribe({
                error: error => {
                    // Assert
                    expect(error).toBeTruthy();
                    expect(commonsServiceMock.handleError).toHaveBeenCalled();
                }
            });

            // Simulate a network error
            const req = httpMock.expectOne(`${mockServerUrl}api/country`);
            req.error(new ProgressEvent('Network error'));
        });
    });

    describe('deleteCountry', () => {
        it('should send a DELETE request with correct URL and countryIds', () => {
            // Arrange
            const mockCountryIds = [1, 2, 3];
            const mockResponse: SuccessMessageResponse = new SuccessMessageResponse('Countries deleted successfully')
            // Act
            service.deleteCountry(mockCountryIds).subscribe(response => {
                expect(response.body).toEqual(mockResponse);
            });

            // Assert
            const req = httpMock.expectOne(`${mockServerUrl}api/country/${mockCountryIds}`);
            expect(req.request.method).toBe('DELETE');

            // Respond with mock data
            req.flush(mockResponse);
        });

        it('should handle errors through CommonsService.handleError', () => {
            // Arrange
            const mockCountryIds = [1, 2, 3];

            // Act
            service.deleteCountry(mockCountryIds).subscribe({
                error: error => {
                    // Assert
                    expect(error).toBeTruthy();
                    expect(commonsServiceMock.handleError).toHaveBeenCalled();
                }
            });

            // Simulate a network error
            const req = httpMock.expectOne(`${mockServerUrl}api/country/${mockCountryIds}`);
            req.error(new ProgressEvent('Network error'));
        });

        it('should handle empty array of countryIds', () => {
            // Arrange
            const mockCountryIds: number[] = [];
            const mockResponse: SuccessMessageResponse = new SuccessMessageResponse('No countries to delete')
            // Act
            service.deleteCountry(mockCountryIds).subscribe(response => {
                expect(response.body).toEqual(mockResponse);
            });

            // Assert
            const req = httpMock.expectOne(`${mockServerUrl}api/country/${mockCountryIds}`);
            expect(req.request.method).toBe('DELETE');

            // Respond with mock data
            req.flush(mockResponse);
        });
    });

    describe('Service initialization', () => {
        it('should initialize with correct base URLs from ConfigInjectService', () => {
            // We already verified in beforeEach that configInjectServiceMock.getServerApiUrl was called
            expect(configInjectServiceMock.getServerApiUrl).toHaveBeenCalled();

            // Create a new instance to ensure constructor logic is executed
            const newService = new CountryApiCallService(
                TestBed.inject(HttpClient),
                configInjectServiceMock,
                commonsServiceMock
            );

            // Verify the URL is constructed correctly (accessing private property for testing)
            // We need to use any to access private members for testing
            expect((newService as any).serverApiUrl).toBe(mockServerUrl);
            expect((newService as any).countryBaseUrl).toBe(`${mockServerUrl}api/`);
            expect((newService as any).countryPostfix).toBe('country');
        });
    });
});