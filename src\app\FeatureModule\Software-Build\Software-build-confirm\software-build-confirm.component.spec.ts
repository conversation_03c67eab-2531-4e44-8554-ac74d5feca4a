import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SoftwareBuildConfirmComponent } from './software-build-confirm.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

describe('InventoryOperationsComponent', () => {
  let component: SoftwareBuildConfirmComponent;
  let fixture: ComponentFixture<SoftwareBuildConfirmComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [SoftwareBuildConfirmComponent],
      providers: [NgbActiveModal]
    })
      .compileComponents();

    fixture = TestBed.createComponent(SoftwareBuildConfirmComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
