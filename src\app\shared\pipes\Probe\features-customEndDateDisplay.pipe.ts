import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { LicensesRequest } from 'src/app/model/probe/multiProbe/LicensesRequest.model';
import { EndDateOptions } from '../../enum/endDateOptions.enum';
import { ConfigBaseMappingRequest } from 'src/app/model/probe/ConfigBaseMappingRequest.model';

@Pipe({
    name: 'featuresCustomEndDateDisplayPipe'
})
export class FeaturesCustomEndDateDisplayPipe implements PipeTransform {

    transform(features: Array<LicensesRequest> | Array<ConfigBaseMappingRequest>, featureId: number, reloadPipe: boolean): boolean {
        if (!isNullOrUndefined(features) && reloadPipe) {
            let filterData = features.filter(obj => obj.id == featureId);
            if (filterData.length == 1 && !isNullOrUndefined(filterData[0].endDateUi) && filterData[0].endDateUi == EndDateOptions.CUSTOMDATE) {
                return true;
            }
        }
        return false;
    }

}
