import { Pageable } from "../common/pageable.model";
import { PageResponse } from "../common/PageResponse.model";
import { Sort } from "../common/sort.model";
import { AuditListResponse } from "./AuditListResponse";

export class AuditListPageResponse extends PageResponse {
    content: Array<AuditListResponse>;

    constructor( //NOSONAR
        pageable: Pageable,
        totalPages: number,
        last: boolean,
        totalElements: number,
        numberOfElements: number,
        first: boolean,
        sort: Sort,
        size: number,
        number: number,
        empty: boolean,
        content: Array<AuditListResponse>
    ) {
        super(
            pageable,
            totalPages,
            last,
            totalElements,
            numberOfElements,
            first,
            sort,
            size,
            number,
            empty
        );
        this.content = content;
    }
}
