import { HttpClient, HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { firstValueFrom } from 'rxjs';
import { PresetDetailBaseResponse } from 'src/app/model/Presets/PresetDetailBaseResponse.model';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { ConfigInjectService } from '../../InjectService/config-inject.service';

@Injectable({
  providedIn: 'root'
})
export class PresetApiService {

  public presetUrl = this.configInjectService.getServerApiUrl() + 'api/probes/';

  constructor(private configInjectService: ConfigInjectService,
    private http: HttpClient,
    private exceptionService: ExceptionHandlingService) { }

  /**
   * Get Probe Presets
   * 
   * <AUTHOR>
   * @returns 
   */
  public async getProbePresetsList(): Promise<Array<PresetDetailBaseResponse>> {
    let presetListResponseList: Array<PresetDetailBaseResponse> = [];
    try {
      const res: HttpResponse<Array<PresetDetailBaseResponse>> = await firstValueFrom(this.http.get<Array<PresetDetailBaseResponse>>(`${this.presetUrl}presets`, { observe: 'response' }));
      presetListResponseList = res.body;
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        this.exceptionService.customErrorMessage(error);
      }
      presetListResponseList = [];
    }
    return presetListResponseList;
  }
}
