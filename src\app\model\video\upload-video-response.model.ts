export class SubTitles {
    public name: string;
    public url: string;
    public subTitleChanged: boolean;

    constructor($name: string, $url: string, $subTitleChanged: boolean) {
        this.name = $name;
        this.url = $url;
        this.subTitleChanged = $subTitleChanged;
    }
}
export class UploadVideoResponse {
    public videoUrl: string;
    public videoChanged: boolean;
    public thumbnailUrl: string;
    public thumbnailChanged: boolean;
    public subTitles: SubTitles[];
    public upload: boolean;

    constructor($videoUrl: string, $videoChanged: boolean, $thumbnailUrl: string, $thumbnailChanged: boolean, $subTitles: SubTitles[], $upload: boolean) {
        this.videoUrl = $videoUrl;
        this.videoChanged = $videoChanged;
        this.thumbnailUrl = $thumbnailUrl;
        this.thumbnailChanged = $thumbnailChanged;
        this.subTitles = $subTitles;
        this.upload = $upload;
    }
}
