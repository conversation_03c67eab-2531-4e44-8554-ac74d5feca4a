import { EndDateOptions } from "src/app/shared/enum/endDateOptions.enum";

export class ConfigBaseMappingRequest {
    id: number;
    name: string;
    displayName: string;
    startDate: number;
    endDate: number;
    trial: boolean;
    enable: boolean;
    endDateUi: EndDateOptions;


    constructor($id: number, $startDate: number, $endDate: number, $trial: boolean, $enable: boolean) {
        this.id = $id;
        this.startDate = $startDate;
        this.endDate = $endDate;
        this.trial = $trial;
        this.enable = $enable
    }

}