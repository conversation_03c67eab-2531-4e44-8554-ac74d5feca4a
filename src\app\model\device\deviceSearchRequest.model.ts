import { ProductStatusEnum } from "src/app/shared/enum/Common/ProductStatus.enum";

export class DeviceSearchRequest {
    packageVersions: Array<string>;
    status: string;
    deviceType: string;
    deviceId: string;
    deviceSerialNo: string;
    customerName: string;
    countryIds: Array<number>;
    deviceLockStatus: boolean;
    isEditable: boolean;
    salesOrderNumbers: Array<string>;
    productStatus: Array<ProductStatusEnum>;

    constructor($packageVersions: Array<string>, $status: string, $deviceType: string, $deviceId: string,//NOSONAR
        $deviceSerialNo: string, $customerName: string, $countryIds: Array<number>, $deviceLockStatus: boolean, $isEditable: boolean,
        $salesOrderNumbers: Array<string>, $productStatus: Array<ProductStatusEnum>,) {
        this.packageVersions = $packageVersions;
        this.status = $status;
        this.deviceType = $deviceType;
        this.deviceId = $deviceId;
        this.deviceSerialNo = $deviceSerialNo;
        this.customerName = $customerName;
        this.countryIds = $countryIds;
        this.deviceLockStatus = $deviceLockStatus;
        this.isEditable = $isEditable;
        this.salesOrderNumbers = $salesOrderNumbers;
        this.productStatus = $productStatus;
    }


}
