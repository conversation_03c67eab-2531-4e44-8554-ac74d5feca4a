import { DatePipe } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { ProbeConfigGroupFilterComponent } from './probe-config-group-filter.component';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';

describe('ProbeConfigGroupFilterComponent', () => {
  let component: ProbeConfigGroupFilterComponent;
  let fixture: ComponentFixture<ProbeConfigGroupFilterComponent>;

  beforeEach(async () => {
    const toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);

    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    await TestBed.configureTestingModule({
      declarations: [ProbeConfigGroupFilterComponent],
      imports: [NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, FormsModule],
      providers: [
        SessionStorageService,
        DatePipe,
        HidePermissionNamePipe,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(ProbeConfigGroupFilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
