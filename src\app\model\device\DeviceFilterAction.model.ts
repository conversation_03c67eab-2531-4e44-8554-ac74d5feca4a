import { ListingPageReloadSubjectParameter } from "../common/listingPageReloadSubjectParameter.model";
import { DeviceSearchRequest } from "./deviceSearchRequest.model";

/**
 * Device Filter Action Model for communication between filter and listing components
 * Following the pattern used in sales-order and other modules
 *
 * <AUTHOR>
 */
export class DeviceFilterAction {
    listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter;
    deviceSearchRequest: DeviceSearchRequest;

    constructor($listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, $deviceSearchRequest: DeviceSearchRequest) {
        this.listingPageReloadSubjectParameter = $listingPageReloadSubjectParameter;
        this.deviceSearchRequest = $deviceSearchRequest;
    }
}