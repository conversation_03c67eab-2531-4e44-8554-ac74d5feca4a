import { HttpClient, HttpResponse } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { CreateJsonRequest } from 'src/app/model/video/create-json-request.model';
import { DownloadJsonResponse } from 'src/app/model/video/download-json-response.model';
import { DownloadVideoResponse } from 'src/app/model/video/download-video-response.model';
import { Jsonlist } from 'src/app/model/video/jsonlist.model';
import { UploadVideoRequest } from 'src/app/model/video/upload-video-request.model';
import { VideoSearchRequest } from 'src/app/model/video/video-search-request.model';
import { VideoListPagableResponse } from 'src/app/model/video/videoListPagableResponse.model';
import { API_BASE_URL } from '../config';
import { CommonsService } from '../util/commons.service';
import { createRequestOption } from '../util/request-util';
import { UploadVideoResponse } from 'src/app/model/video/upload-video-response.model';

@Injectable({
  providedIn: 'root'
})
export class VideoService {

  public videoServerUrl = this.SERVER_API_URL + 'api/video';
  public getVideoServerUrl = this.SERVER_API_URL + 'api/getVideo';
  public deviceMasters = this.SERVER_API_URL + 'api/deviceMasters';

  constructor(
    private httpClient: HttpClient,
    private commonsService: CommonsService,
    @Inject(API_BASE_URL) public SERVER_API_URL: string) { }

  public getVideoList(videoSearchRequest?: VideoSearchRequest, req?: any): Observable<HttpResponse<VideoListPagableResponse>> {
    const options = createRequestOption(req);
    return this.httpClient.post<VideoListPagableResponse>(this.getVideoServerUrl, videoSearchRequest, { params: options, observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  public uploadVideo(uploadVideoDialogRequest?: UploadVideoRequest, req?: any): Observable<HttpResponse<UploadVideoResponse>> {
    const options = createRequestOption(req);
    return this.httpClient.post<UploadVideoResponse>(this.videoServerUrl, uploadVideoDialogRequest, { params: options, observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  public getVideo(videoId: number): Observable<HttpResponse<UploadVideoRequest>> {
    return this.httpClient.get<UploadVideoRequest>(this.getVideoServerUrl + `/${videoId}`, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  public updateVideo(updateVideoDialogRequest?: UploadVideoRequest, videoId?: number, req?: any): Observable<HttpResponse<UploadVideoResponse>> {
    const options = createRequestOption(req);
    return this.httpClient.put<UploadVideoResponse>(this.videoServerUrl + `/${videoId}`, updateVideoDialogRequest, { params: options, observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  public createJson(createJsonDialogRequest?: CreateJsonRequest): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.httpClient.post<SuccessMessageResponse>(this.videoServerUrl + '/json', createJsonDialogRequest, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  public updateJson(updateJsonDialogRequest?: CreateJsonRequest, jsonId?: number): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.httpClient.put<SuccessMessageResponse>(this.videoServerUrl + `/json/${jsonId}`, updateJsonDialogRequest, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  public downloadVideoZipFile(videoId: number): Observable<HttpResponse<DownloadVideoResponse>> {
    return this.httpClient.get<DownloadVideoResponse>(this.getVideoServerUrl + `/${videoId}/download`, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  public downloadJSONFile(jsonId: number): Observable<HttpResponse<DownloadJsonResponse>> {
    return this.httpClient.get<DownloadJsonResponse>(this.getVideoServerUrl + `/json/download/${jsonId}`, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  public deleteVideo(videoId: any): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.httpClient.delete<SuccessMessageResponse>(this.videoServerUrl + `/${videoId}`, { observe: 'response' });
  }

  public deleteJson(jsonId: any): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.httpClient.delete<SuccessMessageResponse>(this.videoServerUrl + `/json/${jsonId}`, { observe: 'response' });
  }

  public getListofJsonVersions(): Observable<HttpResponse<Array<Jsonlist>>> {
    return this.httpClient.get<Array<Jsonlist>>(this.getVideoServerUrl + '/software-builds/json', { observe: 'response' });
  }

}
