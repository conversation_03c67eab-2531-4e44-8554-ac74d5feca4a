.progress-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    max-width: 600px;
    margin: 0 auto;
}

#progressContainer .progress-line {
    flex: 1;
    height: 3px;
    background-color: transparent;
    position: relative;
    margin-bottom: 24px;
    max-width: 100px;
    border-top: 2px dashed #e0e0e0;
    margin-left: -18px;
    margin-right: -18px;
}

#progressContainer .loading-line {
    position: absolute;
    top: -2px;
    left: -18px;
    height: 2px;
    width: calc(30% + 18px);
    background-color: #F79423;
    border-radius: 0;
}

#progressContainer .loading-line:not(.completed) {
    animation: loading 1.5s infinite ease-in-out;
    box-shadow: 0 0 15px rgba(0, 123, 255, 0.5);
}

#progressContainer .loading-line.completed {
    width: calc(100% + 36px);
    transition: width 0.3s ease-in-out;
    border-top: 2px solid #F79423;
    background: none;
}

@keyframes loading {
    0% {
        left: -30%;
        width: 30%;
        background-color: #F79423;
    }

    50% {
        background-color: rgb(248, 162, 65);
    }

    100% {
        left: 100%;
        width: 30%;
        background-color: #F79423;
    }
}

#progressContainer .step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #fff;
    border: 2px solid #F79423;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
}

#progressContainer .step-icon .svg1 {
    width: 50%;
}

#progressContainer .step-icon.inactive {
    border-color: #e0e0e0;
    background-color: #f5f5f5;
}

#progressContainer.step-icon.completed {
    border-color: #F79423;
    background-color: #F79423;
}

#progressContainer .step-icon.completed svg {
    display: block;
    width: 32px;
    height: 32px;
}

#progressContainer .step-icon.completed svg .circle-background {
    fill: #F79423;
    stroke: #F79423;
}

#progressContainer .step-icon.completed svg .checkmark {
    stroke: white;
    stroke-dasharray: 50;
    stroke-dashoffset: 50;
    animation: drawCheckmark 0.5s ease-out forwards;
}

#progressContainer .step-icon.completed svg .outer-circle {
    stroke: rgba(255, 255, 255, 0.3);
}

@keyframes drawCheckmark {
    to {
        stroke-dashoffset: 0;
    }
}

#progressContainer .step-icon.inactive svg .checkmark {
    stroke: #e0e0e0;
}

#progressContainer .step-icon.inactive svg .outer-circle {
    stroke: #e0e0e0;
}

#progressContainer .progress-line.inactive {
    border-top-color: #e0e0e0;
}

#progressContainer .active .step-icon {
    background-color: #F79423;
    color: white;
}

#progressContainer .progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 -1px;
}

#progressContainer .step-label {
    margin-top: 8px;
    font-size: 14px;
}

#progressContainer .step-label.inactive {
    color: #808080;
}

#progressContainer .horizontal-divider {
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, #e0e0e0, transparent);
    margin: 30px 0;
}

#progressContainer .action-container {
    text-align: center;
    padding: 20px 0;
}

#progressContainer .step-icon.active {
    border-color: #F79423;
    background-color: #F79423;
}

#progressContainer .step-icon.active svg .checkmark {
    stroke: white;
}