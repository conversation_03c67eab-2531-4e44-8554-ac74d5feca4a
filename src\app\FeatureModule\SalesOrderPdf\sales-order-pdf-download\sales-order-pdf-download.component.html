<div>
  <!------------------------------------->
  <!--------------Title Start------------>
  <!------------------------------------->
  <div class="modal-header">
    <label class="modal-title">Download Sales Order Letter</label>
  </div>
  <!------------------------------------->
  <!--------------Title End------------>
  <!------------------------------------->

  <!------------------------------------->
  <!-- dialog body - start -->
  <!------------------------------------->
  <div class="modal-body" id="salesOrderPdf">
    <form [formGroup]="salesOrderForm" id="salesOrderForm">
      <div>
        <table class="w-table" aria-hidden="true">

          <tr>
            <th class="upload-title w-title pb-2">
              <span class="pr-2">Sales Order Number</span>
            </th>
            <td class="w-control pb-2">
              <!-- sales order number formfield - start -->
              <ng-multiselect-dropdown id="salesOrderNumberField" name="salesOrderNumber" [placeholder]="''"
                [settings]="dropdownSettingsForSalesOrderNumber" [data]="salesOrderNumberList"
                formControlName="salesOrderNumber" class="devicePageDeviceType">
              </ng-multiselect-dropdown>
            </td>
          </tr>
        </table>
      </div>
    </form>
  </div>
  <!------------------------------------->
  <!-- dialog buttons - start -->
  <!------------------------------------->
  <div class="modal-footer mt-4">
    <button type="button" tabindex="-1" class="btn btn-sm btn-outline-secondary" (click)="decline()">Cancel</button>
    <button type="button" class="btn btn-sm btn-orange" id="downloadBtn" (click)="downloadSalesOrderPdfLetter()"
      [disabled]="salesOrderForm.invalid">Download</button>
  </div>
</div>