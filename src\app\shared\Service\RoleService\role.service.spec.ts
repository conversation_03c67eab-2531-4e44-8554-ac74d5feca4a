import { TestBed } from '@angular/core/testing';

import { RoleService } from './role.service';
import { LocalStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { CommonsService } from '../../util/commons.service';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

describe('RoleService', () => {
  let service: RoleService;
  let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;


  beforeEach(() => {
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        RoleService,
        CommonsService,
        { provide: LocalStorageService, useValue: localStorageServiceMock }, // Provide mock LocalStorageService
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting(),
      ]
    });
    service = TestBed.inject(RoleService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
