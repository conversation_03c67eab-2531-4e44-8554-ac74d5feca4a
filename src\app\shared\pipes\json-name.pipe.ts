import { Pipe, PipeTransform } from "@angular/core";
import { Jsonlist } from "src/app/model/video/jsonlist.model";
import { isNullOrUndefined } from 'is-what';

@Pipe({
    name: "jsonDisplayName"
})
export class JsonNamePipe implements PipeTransform {

    transform(json: Jsonlist) {
        if (isNullOrUndefined(json)) {
            return null;
        } else {
            return json.version;
        }
    }
}