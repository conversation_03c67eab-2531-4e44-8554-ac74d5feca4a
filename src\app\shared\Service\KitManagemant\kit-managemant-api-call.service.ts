import { HttpClient, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { firstValueFrom, Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { KitManagemantSearchRequestBody } from 'src/app/model/KitManagement/KitManagemantSearchRequestBody.model';
import { KitManagementDetailResponse } from 'src/app/model/KitManagement/KitManagementDetailResponse.model';
import { KitManagementPageResponse } from 'src/app/model/KitManagement/KitManagementPageResponse.model';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { CommonsService } from '../../util/commons.service';
import { createRequestOption } from '../../util/request-util';

@Injectable({
  providedIn: 'root'
})
export class KitManagemantApiCallService {

  private serverApiUrl = this.configInjectService.getServerApiUrl();
  public kitBaseUrl = this.serverApiUrl + 'api/kitManagement/bridge-world/';

  constructor(private http: HttpClient,
    private commonsService: CommonsService,
    private configInjectService: ConfigInjectService) { }

  /**
   * Get Kit List
   * 
   * <AUTHOR>
   * @param requestBody 
   * @param req 
   * @returns 
   */
  public getKitList(requestBody: KitManagemantSearchRequestBody, req: any): Observable<HttpResponse<KitManagementPageResponse>> {
    const options = createRequestOption(req);
    return this.http.post<KitManagementPageResponse>(this.kitBaseUrl + "search", requestBody, { params: options, observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Get Kit Detail 
   * 
   * @param kitId 
   * @returns 
   */
  public getKitDetail(kitId: number): Observable<HttpResponse<KitManagementDetailResponse>> {
    return this.http.get<KitManagementDetailResponse>(this.kitBaseUrl + kitId, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  public async getKitRevVersion(): Promise<string> {
    const res: HttpResponse<string> = await firstValueFrom(this.http.get<string>(`${this.kitBaseUrl}revVersion`, { observe: 'response' }));
    return res.body;
  }

}
