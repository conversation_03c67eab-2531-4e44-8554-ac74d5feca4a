@import '../../../../../src/assets/css/custom_style.css';

#features_group_add_update .col_border {
    border: 1px solid #c9c9c9;
}

#features_group_add_update .middleText {
    flex-direction: column;
    display: flex;
    justify-content: center;
}

#features_group_add_update .checboxLineHeight {
    line-height: 24px;
}

#features_group_add_update .radioButtonLineHeight {
    line-height: 22px;
    white-space: nowrap;
}

#add_update_probe_feature_id .form_dropdown{
    height: 38px;
    padding-top: 6px;
    padding-bottom: 6px;
}
#add_update_probe_feature_id .grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
    margin-left: 20px;
  }