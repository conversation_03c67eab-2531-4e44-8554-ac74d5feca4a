<!--####################################################-->
<!--Filter Start-->
<!--####################################################-->
<form id="roleFilterform" role="form" class="form" [formGroup]="filterKitForm">

  <!-- countries form field start -->
  <div class="form-group country-form-group">
    <label class="form-control-label" for="field_countries" id="label_countries"><strong>Country</strong></label>
    <ng-multiselect-dropdown id="field_countries" name="countries" [placeholder]="''" formControlName="countries"
      [settings]="countrySetting" [data]="countriesList">
    </ng-multiselect-dropdown>
  </div>
  <!-- countries form field end -->

  <!-----------Kit Part Number start-------------->
  <div class="form-group">
    <label class="form-control-label" for="field_Kit_Part_Number"><strong>Kit Part Number</strong></label>
    <input class="form-control" type="text" formControlName="kitPartNumber" />
    <div *ngIf="(filterKitForm.get('kitPartNumber').touched || filterKitForm.get('kitPartNumber').dirty) && 
    filterKitForm.get('kitPartNumber').invalid">
      <div *ngIf="filterKitForm.get('kitPartNumber').errors['maxlength']" class="pb-2">
        <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
      </div>
      <div *ngIf="filterKitForm.get('kitPartNumber').errors['pattern']" class="pb-2">
        <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
      </div>
    </div>
  </div>
  <!-----------Kit Part Number end-------------->

  <!-----------Bridge Part Number start-------------->
  <div class="form-group">
    <label class="form-control-label" for="field_Bridge_Part_Number"><strong>Bridge Part Number</strong></label>
    <input class="form-control" type="text" formControlName="bridgePartNumber" />
    <div *ngIf="(filterKitForm.get('bridgePartNumber').touched || filterKitForm.get('bridgePartNumber').dirty) && 
      filterKitForm.get('bridgePartNumber').invalid">
      <div *ngIf="filterKitForm.get('bridgePartNumber').errors['maxlength']" class="pb-2">
        <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
      </div>
      <div *ngIf="filterKitForm.get('bridgePartNumber').errors['pattern']" class="pb-2">
        <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
      </div>
    </div>
  </div>
  <!-----------Kit Part Number end-------------->


  <!-- Software Languages form field start -->
  <div class="form-group country-form-group">
    <label class="form-control-label" for="field_countries" id="label_countries"><strong>Software
        Languages</strong></label>
    <ng-multiselect-dropdown id="field_countries" name="countries" [placeholder]="''" formControlName="softwareLanguage"
      [settings]="languagesSetting" [data]="languagesList">
    </ng-multiselect-dropdown>
  </div>
  <!-- Software Languages form field end -->

  <!-----------Video Version start-------------->
  <div class="form-group">
    <label class="form-control-label" for="field_video_version"><strong>Video Version</strong></label>
    <input class="form-control" type="text" formControlName="videoVersion" />

    <div *ngIf="(filterKitForm.get('videoVersion').touched || filterKitForm.get('videoVersion').dirty) && 
    filterKitForm.get('videoVersion').invalid">
      <div *ngIf="filterKitForm.get('videoVersion').errors['maxlength']" class="pb-2">
        <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
      </div>
      <div *ngIf="filterKitForm.get('videoVersion').errors['pattern']" class="pb-2">
        <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
      </div>
    </div>
  </div>
  <!-----------Video Version end-------------->

  <!-----------Software Version start-------------->
  <div class="form-group">
    <label class="form-control-label" for="field_Software_Version"><strong>Software Version</strong></label>
    <input class="form-control" type="text" formControlName="buildVersion" />

    <div *ngIf="(filterKitForm.get('buildVersion').touched || filterKitForm.get('buildVersion').dirty) && 
    filterKitForm.get('buildVersion').invalid">
      <div *ngIf="filterKitForm.get('buildVersion').errors['maxlength']" class="pb-2">
        <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
      </div>
      <div *ngIf="filterKitForm.get('buildVersion').errors['pattern']" class="pb-2">
        <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
      </div>
    </div>
  </div>
  <!-----------Software Version end-------------->


  <hr class="mt-1 mb-2">
  <!--####################################################-->
  <!---------Action Button Start------->
  <!--####################################################-->
  <div class="">
    <button class="btn btn-sm btn-orange mr-3" (click)="searchData()" [disabled]="filterKitForm.invalid"
      id="bridgeKitFilterSearch">Search</button>
    <button class="btn btn-sm btn-orange" (click)="clearFilter(defaultListingPageReloadSubjectParameter)">Clear</button>
  </div>
  <!--####################################################-->
  <!---------Action Button End------->
  <!--####################################################-->
</form>
<!--####################################################-->
<!--Filter End-->
<!--####################################################-->