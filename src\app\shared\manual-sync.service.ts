import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ManualSyncComponent } from '../FeatureModule/SalesOrderModule/manual-sync/manual-sync.component';
import { SalesOrderSchedulerManualSyncTimeResponse } from '../model/SalesOrder/SalesOrderSchedulerManuleSyncTimeResponse.model';

@Injectable({
  providedIn: 'root'
})
export class ManualSyncService {
  constructor(
    private modalService: NgbModal,
    protected http: HttpClient,
  ) { }

  /**
  * Opens a confirmation modal for manual sync.
  * @param title - The title of the modal.
  * @param salesOrderSchedulerManualSyncTimeResponse - The response data for manual sync.
  * @param btnOkText - The text for the OK button (default is 'Upload').
  * @returns A promise that resolves to a boolean indicating the result of the modal.
  */
  public confirm(
    title: string,
    salesOrderSchedulerManualSyncTimeResponse: SalesOrderSchedulerManualSyncTimeResponse,
    btnOkText: string = 'Upload'): Promise<boolean> {
    // Open the modal with specified options
    const modalRef = this.modalService.open(ManualSyncComponent, {
      windowClass: "modal fade",
      backdrop: 'static', // Prevents closing on outside click
      keyboard: false // Prevents closing with the Escape key
    });

    // Set the component instance properties
    modalRef.componentInstance.title = title;
    modalRef.componentInstance.btnOkText = btnOkText;
    modalRef.componentInstance.salesOrderSchedulerManualSyncTimeResponse = salesOrderSchedulerManualSyncTimeResponse;

    // Return the result of the modal
    return modalRef.result;
  }

}
