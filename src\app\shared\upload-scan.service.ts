import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { UploadSoftwareBuildDialogComponent } from '../FeatureModule/Software-Build/upload-software-build-dialog/upload-software-build-dialog.component';
import { API_BASE_URL } from './config';

@Injectable()
export class UploadScanService {

  constructor(
    private modalService: NgbModal,
    protected http: HttpClient,
    @Inject(API_BASE_URL) public SERVER_API_URL: string
  ) { }

  public confirm(
    title: string,
    message: string,
    countryList: any[],
    jsonVersionList: any[],
    btnOkText: string = 'Upload',
    btnCancelText: string = 'Cancel',
    dialogSize: 'sm' | 'lg' = 'sm'): Promise<boolean> {
    const modalRef = this.modalService.open(UploadSoftwareBuildDialogComponent, {
      windowClass: "modal fade",
      backdrop: 'static', // Prevents closing on outside click
      keyboard: false // Prevents closing with the Escape key
    });
    modalRef.componentInstance.title = title;
    modalRef.componentInstance.message = message;
    modalRef.componentInstance.btnOkText = btnOkText;
    modalRef.componentInstance.btnCancelText = btnCancelText;
    modalRef.componentInstance.countryList = countryList;
    modalRef.componentInstance.jsonVersionList = jsonVersionList;

    return modalRef.result;
  }

}
