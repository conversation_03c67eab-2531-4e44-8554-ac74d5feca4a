import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ChangeDetectorRef, Component, EventEmitter, OnInit, Output } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, ValidatorFn, Validators } from '@angular/forms';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { AddMultipleProbeResource, CancelBtn, CountryValidMessage, DeleteProbeConfirmationConfirmBtn, DeleteProbeConfirmationHeader, DeleteProbeConfirmationMessage, EMAIL_VALIDATION_PATTERN, EnterCustomerName, EnterQRCode, EnterSalesOrderNumber, EnterValidEmail, EnterValidSerialNumber, Find_Letter_Pattern, OREDER_RECORD_TYPE_MANDATORY, PROBE_ALREADY_EXIEST, ProbeConflictErrorMessageTimeOut, ProbeSuccessMessage, ProbeTypeMessage, SERIAL_NUMBER_PATTERN, SerialNumberExists, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH, SPECIAL_CHARACTER_PATTERN, SPECIAL_CHARACTER_ERROR_MESSAGE, MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, MAXIMUM_TEXTBOX_LENGTH, VIOLATION_UNIQUE_KEY_CONSTRAINT } from 'src/app/app.constants';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';
import { ConfigBaseMappingRequest } from 'src/app/model/probe/ConfigBaseMappingRequest.model';
import { AddUpdateMultiProbeResponse } from 'src/app/model/probe/multiProbe/AddUpdateMultiProbeResponse.model';
import { ProbeFeatureRequest } from 'src/app/model/probe/multiProbe/ProbeFeatureRequest.model';
import { ProbeRequest } from 'src/app/model/probe/multiProbe/ProbeRequest.model';
import { ProbeFeatureResponse } from 'src/app/model/probe/ProbeFeatureResponse.model';
import { ProbePresetResponse } from 'src/app/model/probe/ProbePresetResponse.model';
import { ProbeTypeResponse } from 'src/app/model/probe/probeTypeResponse.model';
import { BasicSalesOrderDetailResponse } from 'src/app/model/SalesOrder/BasicSalesOrderDetailResponse.model';
import { EndDateOptions } from 'src/app/shared/enum/endDateOptions.enum';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { ProbeConfigType } from 'src/app/shared/enum/ProbeConfigType.enum';
import { ProbeTypeEnum } from 'src/app/shared/enum/ProbeType.enum';
import { ValidityEnum } from 'src/app/shared/enum/ValidityEnum.enum';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { ProbeService } from 'src/app/shared/Service/ProbeService/probe.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { UpdateAssociationService } from 'src/app/shared/update-association.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { DownloadService } from 'src/app/shared/util/download.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { ValidationService } from 'src/app/shared/util/validation.service';

@Component({
  selector: 'app-create-update-multiple-probe',
  templateUrl: './create-update-multiple-probe.component.html',
  styleUrls: ['./create-update-multiple-probe.component.css']
})
export class CreateUpdateMultipleProbeComponent implements OnInit {

  @Output("showOtsProbe") showOtsProbe = new EventEmitter;

  loading: boolean = true;

  requiredValidator: ValidatorFn = Validators.required;

  salesOrderNumberList: string[] = [];
  isSalesOrderDisabled: boolean = false;
  salesOrderDropdownSettings: any = {};
  countrySetting: MultiSelectDropdownSettings = null;
  orderRecordTypeSetting: MultiSelectDropdownSettings = null;
  countriesList: CountryListResponse[] = [];
  orderRecordTypeList: Array<string> = [];
  probeTypeEnum: typeof ProbeTypeEnum = ProbeTypeEnum;
  isDisabled: boolean = false;

  //Error Message display
  enterSalesOrderNumber: string = EnterSalesOrderNumber;
  enterQRCode: string = EnterQRCode;
  probeTypeMessage: string = ProbeTypeMessage;
  enterValidSerialNumber = EnterValidSerialNumber;
  serialNumberexists: string = SerialNumberExists;
  enterValidEmail: string = EnterValidEmail;
  maxCharactersAllowedMessage: string = MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  small_maxCharactersAllowedMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  enterCustomerName: string = EnterCustomerName;
  countryValidMessage: string = CountryValidMessage;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;
  orderRecordTypeMessage: string = OREDER_RECORD_TYPE_MANDATORY;
  countryId: number = null;


  probeFeaturesForm: FormGroup = new FormGroup({
    serialNumber: new FormControl('', [this.requiredValidator,
    this.commonsService.removeSpacesThrowError(),
    this.validationService.cannotContainSpace,
    Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH),
    this.distinctSerialNumberValidate(this),
    this.validationService.serialNumberStartWithValidate(this),
    Validators.pattern(SERIAL_NUMBER_PATTERN)]),
    probeType: new FormControl('', [this.requiredValidator])
  });

  probeDetailForm = new FormGroup({
    salesOrderNumber: new FormControl(null, [this.requiredValidator, this.commonsService.removeSpacesThrowError()]),
    manualSalesOrderNumber: new FormControl(null, [this.commonsService.removeSpacesThrowError(), Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    countries: new FormControl([], [this.requiredValidator]),
    orderRecordType: new FormControl([], [this.requiredValidator]),
    customerName: new FormControl(null, [this.requiredValidator, this.commonsService.removeSpacesThrowError(), Validators.maxLength(MAXIMUM_TEXTBOX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    poNumber: new FormControl(null, [this.commonsService.removeSpacesThrowError(), Validators.maxLength(MAXIMUM_TEXTBOX_LENGTH)]),
    customerEmail: new FormControl(null, [Validators.pattern(EMAIL_VALIDATION_PATTERN), Validators.maxLength(MAXIMUM_TEXTBOX_LENGTH)]),
    deviceAutoLock: new FormControl(false, []),
    probeAutoLock: new FormControl(false, [])
  })


  //Probe Type Response set
  probeTypeResponse: ProbeTypeResponse[] = [];
  probeTypePrefixList: Array<string> = [];
  featuresList: Array<ProbeFeatureResponse> = [];
  presetsList: Array<ProbePresetResponse> = [];

  //Based on probeType features set
  currentFeatures: Array<ProbeFeatureResponse> = [];
  currentPresets: Array<ProbePresetResponse> = [];
  featuresRequestList: Array<ConfigBaseMappingRequest> = [];
  presetsRequestList: Array<ConfigBaseMappingRequest> = [];


  //Final probe Feature Request
  probeFeatureRequest: ProbeFeatureRequest = null;
  probeFeatureDownload: boolean = false;

  //single Probe Request 
  probeObject: ProbeRequest = null;
  probeObjectForOlddata: ProbeRequest = null;

  //Probe form hide/show
  addProdeMode: boolean = false;
  editProbeMode: boolean = false;

  //Default Date
  defaultStartDate = new Date().getTime();
  defaultEndDateOptions = EndDateOptions.UNLIMITED;
  oneYearEndDateOptions = EndDateOptions.ONE_YEAR;
  unlimitedEndDateOptions = EndDateOptions.UNLIMITED;
  customEndDateOptions = EndDateOptions.CUSTOMDATE;
  validityPerpetual = ValidityEnum.PERPETUAL;
  validityOneYear = ValidityEnum.ONE_YEAR;
  featureProbeConfigType = ProbeConfigType.FEATURE;
  presetProbeConfigType = ProbeConfigType.PRESET;
  reloadPipe: boolean = true;

  //subject
  subscriptionForisloading: Subscription;

  //permission
  setReminderOptionDisplayPermission: boolean = false;

  //setReminderDisabled
  isReminderDisabled: boolean = true;

  constructor(
    private updateAssociationService: UpdateAssociationService,
    private probeService: ProbeService,
    private commonsService: CommonsService,
    private cdr: ChangeDetectorRef,
    private probeApiService: ProbeApiService,
    private exceptionService: ExceptionHandlingService,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private downloadService: DownloadService,
    private toste: ToastrService,
    private permissionService: PermissionService,
    private salesOrderApiCallService: SalesOrderApiCallService,
    private validationService: ValidationService,
    private countryCacheService: CountryCacheService,
  ) { }

  public ngOnInit(): void {
    this.probeFeatureRequest = new ProbeFeatureRequest(null, null, null, null, null, null, [], true, false, false);
    this.salesOrderDropdownSettings = this.multiSelectDropDownSettingService.getSalesOrderNumberDrpSetting(true, null, null, false);
    this.getInitData();
    this.subjectInit();
  }

  /**
   * Subscription for loading status
   */
  private subjectInit(): void {
    this.subscriptionForisloading = this.downloadService.getisLoadingSubject().subscribe((res: boolean) => {
      this.setLoading(res);
    });
  }

  ngOnDestroy() {
    if (this.subscriptionForisloading != undefined) {
      this.subscriptionForisloading.unsubscribe();
    }
  }

  private async getInitData() {
    this.loading = true;
    this.setReminderOptionDisplayPermission = this.permissionService.getProbPermission(PermissionAction.ADD_PROBE_SET_REMINDER_OPTIONS_DISPLAY);
    this.salesOrderNumberList = await this.salesOrderApiCallService.getSalesOrderNumberList();
    this.countrySetting = this.multiSelectDropDownSettingService.getCountryDrpSetting(true, false);
    this.orderRecordTypeSetting = this.multiSelectDropDownSettingService.getCreateOrderRecordTypeDrpSetting();
    this.countriesList = await this.countryCacheService.getCountryListFromCache();
    this.orderRecordTypeList = await this.salesOrderApiCallService.getOrderRecordNumberList(false);
    this.probeTypeResponse = await this.probeApiService.getprobeTypeResponseList(true);
    this.probeTypePrefixList = this.probeTypeResponse.filter(probe => probe.prefix != null).map(probe => probe.prefix.toUpperCase());
    this.setDefaultCurrentFeatures();
    this.setDefaultCurrentPresets();
    this.featuresList = await this.probeApiService.getFeaturesList();
    this.presetsList = await this.probeApiService.getPresetsList();
    this.featuresRequestList = this.probeService.getFeaturesListForCreate(this.featuresList);
    this.presetsRequestList = this.probeService.getFeaturesListForCreate(this.presetsList);
    this.loading = false;
  }

  /**
   * set default features
   */
  private setDefaultCurrentFeatures(): void {
    this.currentFeatures = JSON.parse(JSON.stringify(this.probeTypeResponse[0].features));
  }

  /**
  * set default presets
  */
  private setDefaultCurrentPresets(): void {
    this.currentPresets = JSON.parse(JSON.stringify(this.probeTypeResponse[0].presets));
  }


  /**
   * field mark as touched
   * @param fieldName 
   */
  public onItemClickValidation(fieldName: string): void {
    if (this.probeDetailForm.invalid) {
      this.probeDetailForm.get(fieldName).markAsTouched();
    }
  }

  /**
   * hide/show loading 
   * 
   * <AUTHOR>
   * @param status 
   */
  private setLoading(status: boolean): void {
    this.loading = status;
  }

  /**
   * select sales order value from dropdown list
   * 
   * <AUTHOR>
   * @param value 
   */
  public onDropdownSelect(value: any): void {
    this.setLoading(true);
    this.salesOrderApiCallService.getBasicSalesOrderDetails(value).subscribe({
      next: (response: HttpResponse<BasicSalesOrderDetailResponse>) => {
        let customrDetail: BasicSalesOrderDetailResponse = response.body;
        this.isDisabled = customrDetail.salesForceOrder;
        this.probeDetailForm.get("customerEmail").setValue(customrDetail.customerEmail);
        this.probeDetailForm.get("customerName").setValue(customrDetail.customerName);
        this.probeDetailForm.get("poNumber").setValue(customrDetail.poNumber);
        this.probeDetailForm.get("countries").setValue(this.commonsService.getDropDownValue(this.countriesList, [customrDetail.countryId]));
        this.probeDetailForm.get("countries").markAsTouched();
        this.probeDetailForm.get("orderRecordType").setValue([customrDetail.orderRecordType]);
        this.probeDetailForm.get("customerName").disable();
        if (this.probeDetailForm.get("poNumber").value != null) {
          this.probeDetailForm.get("poNumber").disable();
        } else {
          this.probeDetailForm.get("poNumber").enable();
        }
        this.probeDetailForm.get("deviceAutoLock").setValue(customrDetail.deviceAutoLock);
        this.probeDetailForm.get("probeAutoLock").setValue(customrDetail.probeAutoLock);
        this.countryId = customrDetail.countryId;
        this.setLoading(false);
      }, error: (error: HttpErrorResponse) => {
        this.setLoading(false);
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  /**
  * unselect sales order value from dropdown list
  * 
  * <AUTHOR>
  */
  public onDropdownDeSelect(): void {
    this.probeDetailForm.reset();
    this.isDisabled = false;
    this.removeDisabledAttributeOfFields();
  }

  /**
   * remove disabled attribute of fields
   * 
   * <AUTHOR>
   */
  private removeDisabledAttributeOfFields(): void {
    this.probeDetailForm.get("customerName").enable();
    this.probeDetailForm.get("poNumber").enable();
  }

  /**
  * open sales order number text field
  * 
  * <AUTHOR>
  */
  public openManualSalesOrderField(): void {
    this.isSalesOrderDisabled = true;
    document.getElementById("salesOrderField").style.display = "block";
    document.getElementById("addSalesOrderBtn").setAttribute("disabled", "disabled");
    this.probeDetailForm.reset();
    this.isDisabled = false;
    this.removeDisabledAttributeOfFields();
    this.setOrRemoveValidator(this.probeDetailForm.get("salesOrderNumber"), false);
    this.setOrRemoveValidator(this.probeDetailForm.get("manualSalesOrderNumber"), true);
  }

  /**
   * close sales order number text field
   * 
   * <AUTHOR>
   */
  public closeManualSalesOrderField(): void {
    this.isSalesOrderDisabled = false;
    document.getElementById("salesOrderField").style.display = "none";
    document.getElementById("addSalesOrderBtn").removeAttribute("disabled");
    this.probeDetailForm.reset();
    this.isDisabled = false;
    this.setOrRemoveValidator(this.probeDetailForm.get("manualSalesOrderNumber"), false);
    this.setOrRemoveValidator(this.probeDetailForm.get("salesOrderNumber"), true);
  }

  /**
  * set or remove validator of form field
  * 
  * <AUTHOR>
  * @param control 
  * @param isRequired 
  */
  private setOrRemoveValidator(control: AbstractControl, isRequired: boolean): void {
    let validators = [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), this.commonsService.removeSpacesThrowError()];
    if (isRequired) {
      validators.push(this.requiredValidator);
    }
    control.setValidators(validators);
    control.updateValueAndValidity();
  }

  /**
   * Goto Listing Page
   * <AUTHOR>
   */
  public back(): void {
    this.toste.clear();
    this.showOtsProbe.emit();
  }

  /**
   * Reload the pipe for checkbox and radio button checked/unchecked
   * <AUTHOR>
   */
  public reloadUiWithPipe(): void {
    this.reloadPipe = false;
    this.cdr.detectChanges();
    this.reloadPipe = true;
    this.cdr.detectChanges();
  }

  /**
   * set fetuares based on select probeType
   * 
   * @param probeTypeId 
   */
  public updateCurrentFeatures(probeTypeId: number, featuresList: boolean, isResetSerialNumber: boolean): void {
    let updateFeatures: ProbeTypeResponse[] = this.probeTypeResponse.filter(obj => obj.probeTypeId == probeTypeId);
    if (updateFeatures.length == 1) {
      this.currentFeatures = updateFeatures[0].features;
      this.currentPresets = updateFeatures[0].presets;
      this.setProbeType(updateFeatures[0].displayName, updateFeatures[0].probeTypeId, featuresList);
      if (isResetSerialNumber) {
        this.setSerialNumber(null);
      }
    } else {
      this.currentFeatures = null;
      this.currentPresets = null;
    }
  }



  /**
   * set serial number
   * <AUTHOR>
   * @param serialNumberValue 
   */
  private setSerialNumber(serialNumberValue: string) {
    let serialNumber: any = this.commonsService.checkNullFieldValue(serialNumberValue);
    if (!isNullOrUndefined(serialNumber)) {
      this.probeObject.serialNumber = serialNumber;
    } else {
      this.probeObject.serialNumber = null;
    }
    this.probeFeaturesForm.get('serialNumber').setValue(this.probeObject.serialNumber);
  }


  /**
   * set serial number in probe Object
   * 
   * <AUTHOR>
   * @param serialNumberValue 
   */
  public updateSerialNumber(serialNumberValue: string): void {
    this.setSerialNumber(serialNumberValue);
    //Auto Select Probe type
    let probeTypeResponse = this.probeService.getProbeTypeDropdownValue(this.probeObject.serialNumber, this.probeTypeResponse);
    if (!isNullOrUndefined(probeTypeResponse)) {
      let isResetFeatures = this.probeFeaturesForm.get('probeType').value != probeTypeResponse.probeTypeId;
      this.updateCurrentFeatures(probeTypeResponse.probeTypeId, isResetFeatures, false);
    }
  }

  /**
   * set probe Type in probe Object
   * 
   * <AUTHOR>
   * 
   * @param probeTypeValue 
   * @param id 
   */
  public setProbeType(probeTypeValue: string, id: number, isResetFeatures: boolean): void {
    let control = this.probeFeaturesForm.get('probeType');
    let probeTypeEnum = this.commonsService.getEnumKey(ProbeTypeEnum, probeTypeValue);
    this.probeObject.probeType = probeTypeEnum;
    if (isResetFeatures) {
      this.probeObject.features = this.probeService.getConfigListForAssign(this.featuresList, this.currentFeatures, this.featureProbeConfigType);
      this.probeObject.presets = this.probeService.getConfigListForAssign(this.presetsList, this.currentPresets, this.presetProbeConfigType);

    }
    control.setValue(id);
    this.updateValidatation();
    this.updateReminderForUserAction();

  }

  /**
   * Update Validatation
   * <AUTHOR>
   */
  public updateValidatation(): void {
    let control = this.probeFeaturesForm.get('probeType');
    if (control.value == "-1") {
      control.setErrors({ required: true });
      control.markAsDirty();
    }
  }

  /**
   * set Remindar in probe object
   * 
   * <AUTHOR>
   * @param isReminder 
   */
  public setIsReminder(isReminder: boolean): void {
    this.probeObject.reminder = isReminder;
  }

  /**
     * set Remindar 
     * 
     * <AUTHOR>
     */
  public setReminderDisabled(): void {
    this.isReminderDisabled = this.probeObject.features.filter(obj => obj.enable).length == 0 && this.probeObject.presets.filter(obj => obj.enable).length == 0;
  }

  /**
   * features checkbox enebale/disable feature
   * startDate = new date
   * endDate = default set based on validity
   * 
   * <AUTHOR> 
   * @param featureId 
   * @param isFeatureEnable 
   * @param startDate 
   * @param featuresBaseResponse 
   */
  public onChangeFeatures(featureId: number, isFeatureEnable: boolean, startDate: number, featuresBaseResponse: ProbeFeatureResponse): void {
    let index = this.getFetaureIndex(featureId);
    if (index != -1) {
      this.probeObject.features[index].enable = isFeatureEnable;
      if (isFeatureEnable) {
        let endDate: EndDateOptions = this.commonsService.getFeatureDefalutValidty(featuresBaseResponse);
        this.probeObject.features[index].startDate = startDate;
        this.probeObject.features[index].endDate = this.commonsService.getEndDateConvertion(endDate, null);
        this.probeObject.features[index].endDateUi = endDate;
      } else {
        this.probeObject.features[index].startDate = null;
        this.probeObject.features[index].endDate = null;
        this.probeObject.features[index].endDateUi = null;
      }
    }
    this.updateReminderForUserAction();
  }

  /**
  * preset checkbox enebale/disable preset
  * startDate = new date
  * endDate = default set based on validity
  * 
  * <AUTHOR> 
  * @param presetId 
  * @param isPresetEnable 
  * @param startDate 
  * @param presetsBaseResponse 
  */
  public onChangePresets(presetId: number, isPresetEnable: boolean, startDate: number, probePresetResponse: ProbePresetResponse): void {
    let index = this.getPresetsIndex(presetId);
    if (index != -1) {
      this.probeObject.presets[index].enable = isPresetEnable;
      if (isPresetEnable) {
        let endDate: EndDateOptions = this.commonsService.getFeatureDefalutValidty(probePresetResponse);
        this.probeObject.presets[index].startDate = startDate;
        this.probeObject.presets[index].endDate = this.commonsService.getEndDateConvertion(endDate, null);
        this.probeObject.presets[index].endDateUi = endDate;
      } else {
        this.probeObject.presets[index].startDate = null;
        this.probeObject.presets[index].endDate = null;
        this.probeObject.presets[index].endDateUi = null;
      }
    }
    this.updateReminderForUserAction();
  }

  /**
   * Update the Remainder options 
   * Note :If user select 12 month any Features then setReminder select
   * <AUTHOR>
   */
  public updateReminderForUserAction(): void {
    this.setReminderDisabled();
    this.setIsReminder(this.probeService.setReminderForUserActionForAssignConfig(this.probeObject));
    this.reloadUiWithPipe();
  }

  /**
   * Features radion button options 12 month and Unlimited based on selection end date set
   * 
   * <AUTHOR>
   * @param featureId 
   * @param startDate 
   * @param endDate 
   */
  public onChangeFeaturesEndDate(featureId: number, startDate: number, endDate: EndDateOptions, customEndDate: Date): void {
    let index = this.getFetaureIndex(featureId);
    if (index != -1) {
      this.probeObject.features[index].enable = true;
      this.probeObject.features[index].startDate = startDate;
      this.probeObject.features[index].endDateUi = endDate;
      this.probeObject.features[index].endDate = this.commonsService.getEndDateConvertion(endDate, customEndDate);
    }
    this.updateReminderForUserAction();
  }

  /**
   * presets radion button options 12 month and Unlimited based on selection end date set
   * 
   * <AUTHOR>
   * @param presetId 
   * @param startDate 
   * @param endDate 
   */
  public onChangePresetsEndDateForUpdate(PresetId: number, startDate: number, endDateOptions: EndDateOptions, customEndDate: Date): void {
    let index = this.getPresetsIndex(PresetId);
    if (index != -1) {
      this.probeObject.presets[index].enable = true;
      this.probeObject.presets[index].startDate = startDate;
      this.probeObject.presets[index].endDateUi = endDateOptions;
      this.probeObject.presets[index].endDate = this.commonsService.getEndDateConvertion(endDateOptions, customEndDate);
    }
    this.updateReminderForUserAction();
  }

  /**
   * Get Index from probeObject.features based on feature Id
   * 
   * <AUTHOR>
   * @param featureId 
   * @returns 
   */
  public getFetaureIndex(featureId: number): number {
    return this.probeObject.features.findIndex(obj => obj.id == featureId);
  }

  /**
* Get Index from probeObject.presets based on preset Id
* 
* <AUTHOR>
* @param presetId 
* @returns 
*/
  public getPresetsIndex(presetId: number): number {
    return this.probeObject.presets.findIndex(obj => obj.id == presetId);
  }

  /**
   * Form Add Mode on
   * 
   * <AUTHOR>
   */
  public addProbeModeOn(): void {
    this.addProdeMode = true;
    this.editProbeMode = false;
    this.setDefaultCurrentFeatures();
    this.setDefaultCurrentPresets();
    this.setReminderDisabled();
  }

  /**
   * Form Edit Mode On
   * 
   * <AUTHOR>
   */
  public editProbeModeOn(): void {
    this.addProdeMode = false;
    this.editProbeMode = true;
  }

  /**
   * Close Probe Form 
   * 
   * <AUTHOR>
   */
  public closeAddEditMode(): void {
    this.addProdeMode = false;
    this.editProbeMode = false;
    this.probeObject = null;
    this.probeObjectForOlddata = null;
  }

  /**
   * edit mode and click close button then add old data and close edit mode
   * <AUTHOR>
   */
  public closeEditMode() {
    if (!isNullOrUndefined(this.probeObjectForOlddata)) {
      this.probeFeatureRequest.probes.push(JSON.parse(JSON.stringify(this.probeObjectForOlddata)));
    }
    this.closeAddEditMode();
  }

  /**
   * set probe Features Form data
   * 
   * <AUTHOR>
   * @param serialNumber 
   * @param probeType 
   */
  public setProbeFormData(serialNumber: string, probeType: number) {
    this.probeFeaturesForm.get('serialNumber').setValue(serialNumber);
    this.probeFeaturesForm.get('probeType').setValue(probeType);
  }


  /**
   * Open Add Probe Form
   * <AUTHOR>
   */
  public openAddNewProbForm(): void {
    this.probeFeaturesForm.reset();
    let probeTypeEnum = this.commonsService.getEnumKey(ProbeTypeEnum, this.probeTypeResponse[0].displayName);
    this.probeObject = new ProbeRequest(JSON.parse(JSON.stringify(this.featuresRequestList),), JSON.parse(JSON.stringify(this.presetsRequestList),), false, null, probeTypeEnum, null);
    this.setProbeFormData(null, this.probeTypeResponse[0].probeTypeId);
    this.addProbeModeOn();
  }

  /**
   * Add a new prob in probeFeatureRequest
   * 
   * <AUTHOR>
   */
  public addNewProb(): void {
    this.probeFeatureRequest.probes.push(JSON.parse(JSON.stringify(this.truncateSerialNumber(this.probeObject))));
    this.closeAddEditMode();
  }

  /**
   * Add a new prob in probeFeatureRequest
   * 
   * <AUTHOR>
   */
  public editProbAndSave(): void {
    this.probeFeatureRequest.probes.push(JSON.parse(JSON.stringify(this.truncateSerialNumber(this.probeObject))));
    this.closeAddEditMode();
  }

  /**
   * 
   * Truncate serial number
   * 
   * <AUTHOR>
   * @param probeObject 
   * @returns 
   */
  public truncateSerialNumber(probeObject: ProbeRequest): ProbeRequest {
    let serialNumber: any = probeObject.serialNumber;
    if (!isNullOrUndefined(serialNumber)) {
      let indexForSerialNumber = serialNumber.indexOf(serialNumber.match(Find_Letter_Pattern));
      probeObject.serialNumber = serialNumber.substr(indexForSerialNumber);
    }
    return probeObject;
  }

  /**
   * Edit Button Click (set form data and hide add button and display Save button)
   * 
   * <AUTHOR>
   */
  public editModeOpen(probeIndex: number): void {
    this.editProbeModeOn();
    this.probeObject = JSON.parse(JSON.stringify(this.probeFeatureRequest.probes[probeIndex]));
    this.probeObject.errorMessage = null;
    this.probeObjectForOlddata = JSON.parse(JSON.stringify(this.probeFeatureRequest.probes[probeIndex]));
    this.probeFeatureRequest.probes.splice(probeIndex, 1);
    let probeType = this.probeTypeResponse.filter(obj => obj.displayName == ProbeTypeEnum[this.probeObject.probeType])[0];
    this.setProbeFormData(this.probeObject.serialNumber, probeType.probeTypeId);
    this.updateCurrentFeatures(probeType.probeTypeId, false, false);
    this.setReminderDisabled();
  }

  /**
   * Delete Probe only Ui side
   * 
   * <AUTHOR>
   */
  public deleteProbe(probeIndex: number): void {
    this.updateAssociationService.openUpdateAssociationModel(
      DeleteProbeConfirmationHeader, DeleteProbeConfirmationMessage,
      DeleteProbeConfirmationConfirmBtn, CancelBtn
    ).then(response => {
      if (response) {
        this.toste.clear();
        this.probeFeatureRequest.probes.splice(probeIndex, 1);
      }
    }, () => { });
  }

  /**
   * Download flag set for download prob files or not
   * 
   * <AUTHOR>
   * @param isDownload 
   */
  public onChangeDownloadLicense(isDownload: boolean): void {
    this.probeFeatureDownload = isDownload;
  }

  /**
   * Save All Probes Api call
   * 
   * <AUTHOR>
   */
  public multiprobeSave(): void {
    this.setLoading(true);
    let salesOrder = (!isNullOrUndefined(this.probeDetailForm.get('salesOrderNumber').value) ? this.probeDetailForm.get('salesOrderNumber').value[0] : this.probeDetailForm.get('manualSalesOrderNumber').value);
    let country = this.commonsService.checkNullFieldValue(this.probeDetailForm.get("countries").value);
    let orderRecordType = this.commonsService.checkNullFieldValue(this.probeDetailForm.get("orderRecordType").value);
    this.probeFeatureRequest.salesOrderNumber = this.commonsService.checkNullFieldValue(salesOrder);
    this.probeFeatureRequest.customerEmail = this.commonsService.checkNullFieldValue(this.probeDetailForm.get('customerEmail').value);
    this.probeFeatureRequest.customerName = this.commonsService.checkNullFieldValue(this.probeDetailForm.get('customerName').value);
    this.probeFeatureRequest.poNumber = this.commonsService.checkNullFieldValue(this.probeDetailForm.get('poNumber').value);
    this.probeFeatureRequest.deviceAutoLock = this.commonsService.checkNullFieldValue(this.probeDetailForm.get('deviceAutoLock').value);
    this.probeFeatureRequest.probeAutoLock = this.commonsService.checkNullFieldValue(this.probeDetailForm.get('probeAutoLock').value);
    this.probeFeatureRequest.orderRecordType = orderRecordType.length > 0 ? orderRecordType[0] : null;
    if (country.length > 0) {
      this.probeFeatureRequest.countryId = country[0].id;
    }
    this.callMultiprobeSaveAPI(this.probeFeatureRequest);
  }

  /**
   * Subscribe probe add api and call download feature license
   */
  private callMultiprobeSaveAPI(finalProbeFeatureRequest: ProbeFeatureRequest): void {
    this.probeApiService.saveMutiprobe(finalProbeFeatureRequest).subscribe(
      {
        next: (response: HttpResponse<AddUpdateMultiProbeResponse>) => {
          if (response.body.error) {
            this.probeFeatureRequest.probes.forEach(probe => {
              // Find the matching probe in the response based on serialNumber
              const matchingProbe = response.body.probes.find(s => s.serialNumber === probe.serialNumber);
              // If a matching probe is found, set the errorMessage
              if (matchingProbe) {
                probe.errorMessage = matchingProbe.errorMessage;
              }
            });
          }
          if (!response.body.error) {
            this.back();
            this.toste.success(ProbeSuccessMessage);
          }
          if (this.probeFeatureDownload) {
            let probeIdList = []
            response.body.probes.forEach(probe => {
              probeIdList.push(probe.probeId);
            });
            this.downloadProbes(probeIdList)
          }
          this.setLoading(false);
        }, error: (error: HttpErrorResponse) => {
          this.setLoading(false);
          if (error.status == 409) {
            if (error.error["errorMessage"].includes(VIOLATION_UNIQUE_KEY_CONSTRAINT)) {
              this.toste.error(PROBE_ALREADY_EXIEST);
            } else {
              this.toste.error(error.error["errorMessage"], null, {
                timeOut: ProbeConflictErrorMessageTimeOut
              });
            }
          } else if (error.status == 412) {
            this.toste.info(error.message);
          } else {
            this.exceptionService.customErrorMessage(error);
          }
        }
      });
  }
  /**
  * Checks Error in Add Probe Table
  */
  public errorInAddProbeRequest(): boolean {
    return this.probeFeatureRequest.probes.some(probeFeatureRequest => probeFeatureRequest.errorMessage != null)
  }

  /**
  * Download Probe Feature License
  */
  public async downloadProbes(probeIdList: Array<number>): Promise<void> {
    await this.probeApiService.dowloadSasUriofFeatureLicenseAsync(probeIdList, AddMultipleProbeResource);
  }

  /**
   * Check serialNumber with local probe data (probeFeatureRequest)
   * 
   * <AUTHOR>
   * @param componentVariable -> Access all the component Variable
   * @returns 
   */
  public distinctSerialNumberValidate(componentVariable: any): ValidatorFn {
    return function validate(control: FormControl) {
      if (!isNullOrUndefined(control) && !isNullOrUndefined(control.value) && (typeof control.value == "string" && control.value.replace(/\s/g, '').length > 0)) {
        let controlValue: any = control.value;
        if (!isNullOrUndefined(controlValue)) {
          let index = controlValue.indexOf(controlValue.match(Find_Letter_Pattern));
          //Get Probe List in probeFeatureRequest (local data)
          let probeList = isNullOrUndefined(componentVariable.probeFeatureRequest) ? [] : JSON.parse(JSON.stringify(componentVariable.probeFeatureRequest.probes));
          let filterData = probeList?.filter(obj => {
            let indexForSerialNumber = obj.serialNumber.indexOf(obj.serialNumber.match(Find_Letter_Pattern));
            if (obj.serialNumber.substr(indexForSerialNumber).toLowerCase() == control.value.substr(index).toLowerCase()) {
              return obj;
            }
          });
          if (filterData.length > 0) {
            return { serialNumberexists: true };
          }
        }
      }
      return null;
    };
  }
}
