import { Pageable } from "../common/pageable.model";
import { PageResponse } from "../common/PageResponse.model";
import { Sort } from "../common/sort.model";
import { UserSearchResponse } from "./UserSearchResponse";

export class UserListPageResponse extends PageResponse {
    content: Array<UserSearchResponse>;

    constructor(pageable: Pageable, totalPages: number, last: boolean, totalElements: number,//NOSONAR
        numberOfElements: number, first: boolean, sort: Sort, size: number, number: number,
        empty: boolean, content: Array<UserSearchResponse>) {
        super(pageable, totalPages, last, totalElements, numberOfElements, first, sort, size, number, empty);
        this.content = content;
    }

}