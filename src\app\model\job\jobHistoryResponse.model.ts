import { FirmwareResponse } from "./firmwareResponse.model";
import { StatusHistory } from "./statusHistory.model";

export class JobHistoryResponse {
    deviceId: string;
    jobId: string;
    jobType: string;
    statusHistory: Array<StatusHistory>;
    firmwareResponse: FirmwareResponse;
    jsonVersion: string;

    constructor(
        deviceId: string,
        jobId: string,
        jobType: string,
        statusHistory: Array<StatusHistory>,
        firmwareResponse: FirmwareResponse,
        jsonVersion: string
    ) {
        this.deviceId = deviceId;
        this.jobId = jobId;
        this.jobType = jobType;
        this.statusHistory = statusHistory;
        this.firmwareResponse = firmwareResponse;
        this.jsonVersion = jsonVersion;
    }
}
