import { ComponentFixture, TestBed } from '@angular/core/testing';

import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { PermissionService } from 'src/app/shared/permission.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { KitManagementModuleComponent } from './kit-management-module.component';

describe('KitManagementModuleComponent', () => {
  let component: KitManagementModuleComponent;
  let fixture: ComponentFixture<KitManagementModuleComponent>;
  let mockAuthJwtService: jasmine.SpyObj<AuthJwtService>;
  let mockPermissionService: jasmine.SpyObj<PermissionService>;

  beforeEach(async () => {
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    mockAuthJwtService = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    mockPermissionService = jasmine.createSpyObj('PermissionService', ['getKitManagementPermission']);


    await TestBed.configureTestingModule({
      declarations: [KitManagementModuleComponent],
      imports: [],
      providers: [
        SessionStorageService,
        { provide: AuthJwtService, useValue: mockAuthJwtService },
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: PermissionService, useValue: mockPermissionService },
        commonsProviders(null)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(KitManagementModuleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should navigate to login if not authenticated', () => {
      // **Arrange: Mock authentication service to return false**
      // Simulate the scenario where the user is not authenticated
      mockAuthJwtService.isAuthenticate.and.returnValue(false);

      // **Act: Initialize the component**
      // Trigger `ngOnInit` lifecycle method
      component.ngOnInit();

      // **Assert: Verify redirection to login**
      // Ensure `loginNavigate` method was called
      expect(mockAuthJwtService.loginNavigate).toHaveBeenCalled();
    });

    it('should set permissions and display defaults if authenticated', () => {
      // **Arrange: Mock authentication and permissions**
      // Simulate an authenticated user
      mockAuthJwtService.isAuthenticate.and.returnValue(true);

      // Mock permissions for Bridge Kit and OTS Kit Management
      mockPermissionService.getKitManagementPermission
        .withArgs(PermissionAction.GET_BRIDGE_KIT_MANAGEMENT_ACTION)
        .and.returnValue(true); // Bridge permissions granted

      mockPermissionService.getKitManagementPermission
        .withArgs(PermissionAction.GET_OTS_KIT_MANAGEMENT_ACTION)
        .and.returnValue(false); // OTS permissions denied

      // **Act: Initialize the component**
      // Trigger `ngOnInit` lifecycle method
      component.ngOnInit();

      // **Assert: Verify permissions and default display states**
      expect(component.bridgeKitDisplayPermissions).toBe(true); // Bridge permission should be true
      expect(component.otsKitDisplayPermissions).toBe(false); // OTS permission should be false
      expect(component.bridgeWorldListDisplay).toBe(true); // Default display should show Bridge World
      expect(component.otsWorldListDisplay).toBe(false); // OTS World should not be displayed
    });
  });

  describe('setDefaultPageDisplay', () => {
    it('should display Bridge World list if bridgeKitDisplayPermissions is true', () => {
      // **Arrange: Set up the component's permissions**
      component.bridgeKitDisplayPermissions = true;

      // **Act: Call the method to set the default display**
      component.setDefalutPageDisplay();

      // **Assert: Verify display states**
      expect(component.bridgeWorldListDisplay).toBe(true); // Bridge World should be displayed
      expect(component.otsWorldListDisplay).toBe(false); // OTS World should not be displayed
    });

    it('should display OTS World list if bridgeKitDisplayPermissions is false', () => {
      // **Arrange: Set up the component's permissions**
      component.bridgeKitDisplayPermissions = false;

      // **Act: Call the method to set the default display**
      component.setDefalutPageDisplay();

      // **Assert: Verify display states**
      expect(component.bridgeWorldListDisplay).toBe(false); // Bridge World should not be displayed
      expect(component.otsWorldListDisplay).toBe(true); // OTS World should be displayed
    });
  });

  describe('showBridgeWorldListDisplay', () => {
    it('should show only Bridge World list', () => {
      // **Act: Call the method to show Bridge World list**
      component.showBridgeWorldListDisplay();

      // **Assert: Verify display states**
      expect(component.bridgeWorldListDisplay).toBe(true); // Bridge World should be displayed
      expect(component.otsWorldListDisplay).toBe(false); // OTS World should not be displayed
    });
  });

  describe('showOtsWorldListDisplay', () => {
    it('should show only OTS World list', () => {
      // **Act: Call the method to show OTS World list**
      component.showOtsWorldListDisplay();

      // **Assert: Verify display states**
      expect(component.bridgeWorldListDisplay).toBe(false); // Bridge World should not be displayed
      expect(component.otsWorldListDisplay).toBe(true); // OTS World should be displayed
    });
  });

});