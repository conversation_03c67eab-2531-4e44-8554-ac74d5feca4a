<!------------------------------------>
<!------------------------------------>
<!--

    Component Name : uplaod-video-dialog
    Description : Upload Videos and related contents.
    Functionality : Create record of video in which upload video,
                    Upload thumbnail and upload subtitles files
                    related to a language and its.
                   
    Validations : Title - required
                          Must contain at least one character
                  Duration - required
                            in hh:mm:ss format only
                  uploadVideo - required
                                Only Mp4 file type allowed
                  uploadThumbnail - required
                                    Only jpg file type allowed
                  uploadSubtitles - allow Multiple Selection of files
                                    Only srt file type allowed
                                    srtTitle - required
    ** All uploaded File names must be same

-->
<!------------------------------------>
<!------------------------------------>
<!--Model - start-->
<!-- loading - start -->
<div class="ringLoading" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!-- loading - end -->
<!------------------------------------>
<!-- Model Header - start -->
<div class="modal-header">
    <label class="modal-title"><strong>{{ title }}</strong></label>
</div>
<!-- Model Header - end -->
<!------------------------------------>
<!------------------------------------>
<!--Model Body - start-->
<div class="modal-body" id="uploadVideoDialog">
    <div>
        <form enctype="multipart/form-data" name='uploadvideofile' [formGroup]="uploadVideoForm"
            class="upload-video-form">
            <!-- Title Field - start -->
            <div class="form-group mb-4">
                <div class="row">
                    <div class="col-md-2">
                        <span class="video-upload-title">Title *</span>
                    </div>
                    <div class="col-md-10">
                        <input type="text" tabindex="-1" id="title" name="title" formControlName="Title"
                            class="form-control" autocomplete="off" required />
                        <div
                            *ngIf="(uploadVideoForm.get('Title').touched || uploadVideoForm.get('Title').dirty) && uploadVideoForm.get('Title').invalid ">
                            <div *ngIf="uploadVideoForm.get('Title').errors['required']" class="validation">
                                Enter Valid Title
                            </div>
                            <div *ngIf="uploadVideoForm.get('Title').errors['maxlength']" class="validation">
                                <span class="alert-color font-12">{{textBoxMaxCharactersAllowedMessage}}</span>
                            </div>
                            <div *ngIf="uploadVideoForm.get('Title').errors['pattern']" class="validation">
                                <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Title Field - end -->
            <!-- Duration Field - start -->
            <div class="form-group mb-4">
                <div class="row">
                    <div class="col-md-7">
                        <span class="video-upload-title">Duration* (number only, e.g., 3:05)</span>
                    </div>
                    <div class="col-md-5">
                        <input type="text" tabindex="-1" id="duration" name="duration" formControlName="duration"
                            class="form-control" autocomplete="off" required />
                        <div
                            *ngIf="(uploadVideoForm.get('duration').touched || uploadVideoForm.get('duration').dirty) && uploadVideoForm.get('duration').invalid ">
                            <div *ngIf="uploadVideoForm.get('duration').errors['required'] || uploadVideoForm.get('duration').invalid"
                                class="validation">
                                Enter Valid Duration
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Duration Field - end -->
            <!-- upload video Field - start -->
            <div class="form-group mb-2">
                <div class="video-upload-title mb-2">
                    <span>Upload video file (.mp4 Only)*</span>
                </div>
                <div>
                    <div>
                        <input #videoFileInput [hidden]="videoFileName!=null" type="file" id="videofile"
                            name="videofile" accept=".mp4" formControlName="videoFileSelect"
                            (change)="handleVideoFileInput($event)" tabindex="-1" class="form-control-file" required />
                        <mat-chip-list *ngIf="videoFileName!=null">
                            <mat-chip [title]="videoFileName" [selectable]="selectable" [removable]="removable"
                                (removed)="removeSrt(uploadVideoForm,videoFileName,'videoFile',null)">
                                {{ videoFileName }}
                                <mat-icon matChipRemove *ngIf="removable && isEditable">cancel</mat-icon>
                            </mat-chip>
                        </mat-chip-list>
                    </div>
                    <div
                        *ngIf="(uploadVideoForm.get('videoFileSelect').touched || uploadVideoForm.get('videoFileSelect').dirty) && uploadVideoForm.get('videoFileSelect').invalid">
                        <div *ngIf="uploadVideoForm.get('videoFileSelect').errors['required'] || uploadVideoForm.get('videoFileSelect').invalid"
                            class="validation">
                            Upload Video File
                        </div>
                    </div>
                    <div *ngIf="validVideoFileType != null" class="validation">
                        {{ validVideoFileType }}
                    </div>
                </div>
            </div>
            <!-- upload video Field - end -->
            <!-- upload thumbnail Field - start -->
            <div class="form-group mb-2">
                <div class="video-upload-title mb-2">
                    <span>Upload thumbnail file (.jpg Only)*</span>
                </div>
                <div>
                    <div>
                        <input #thumbnailFileInput [hidden]="thumbnailFileName!=null" type="file" id="thumbnailfile"
                            name="thumbnailfile" accept=".jpg" formControlName="thumbnailFileSelect"
                            (change)="handleThumbnailFileInput($event)" tabindex="-1" class="form-control-file"
                            required />
                        <mat-chip-list *ngIf="thumbnailFileName!=null">
                            <mat-chip [title]="thumbnailFileName" [selectable]="selectable" [removable]="removable"
                                (removed)="removeSrt(uploadVideoForm,thumbnailFileName,'thumbnailFile',null)">
                                {{ thumbnailFileName }}
                                <mat-icon matChipRemove *ngIf="removable && isEditable">cancel</mat-icon>
                            </mat-chip>
                        </mat-chip-list>
                    </div>
                    <div
                        *ngIf="(uploadVideoForm.get('thumbnailFileSelect').touched || uploadVideoForm.get('thumbnailFileSelect').dirty) && uploadVideoForm.get('thumbnailFileSelect').invalid ">
                        <div *ngIf="uploadVideoForm.get('thumbnailFileSelect').errors['required'] || uploadVideoForm.get('thumbnailFileSelect').invalid"
                            class="validation">
                            Upload Thumbnail File
                        </div>
                    </div>
                    <div *ngIf="validThumbnailFileType != null" class="validation">
                        {{ validThumbnailFileType }}
                    </div>
                </div>
            </div>
            <!-- upload thumbnail Field - end -->
            <!-- upload subtitles Field - start -->
            <div class="mb-2">
                <div class="video-upload-title mb-2">
                    <span>Upload subtitled languages (.srt Only)</span>
                </div>
                <div formArrayName="srtFilesList">
                    <div
                        *ngFor="let srt of uploadVideoForm.get('srtFilesList')['controls'];let srtFilesListIndex=index">
                        <div class="ml-4" [formGroupName]="srtFilesListIndex">
                            <!-- subtitle matchip  - start -->
                            <mat-chip-list [name]="'subtitleFile'+srtFilesListIndex">
                                <mat-chip [title]="srt.value.subtitleFile" class="mb-2" [selectable]="selectable"
                                    [removable]="removable"
                                    (removed)="removeSrt(uploadVideoForm,srt.value,'srtFilesList',srtFilesListIndex)">
                                    {{srt.value.subtitleFile}}
                                    <mat-icon matChipRemove *ngIf="removable && isEditable">cancel</mat-icon>
                                </mat-chip>
                            </mat-chip-list>
                            <!-- subtitle matchip  - end -->
                            <!-- subtitle title Field - start -->
                            <div class="form-group">
                                <div class="row">
                                    <div class="col-md-2">
                                        <span class="video-upload-title">Title *</span>
                                    </div>
                                    <div class="col-md-10">
                                        <input type="text" class="form-control" [name]="'title'+srtFilesListIndex"
                                            [id]="'title'" class="form-control" formControlName="title" tabindex="-1"
                                            autocomplete="off" required />
                                        <div
                                            *ngIf="(srt.get('title').touched || srt.get('title').dirty) && srt.get('title').invalid ">
                                            <div *ngIf="srt.get('title').errors['required'] || srt.get('title').invalid"
                                                class="validation">
                                                Enter Valid Title
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- subtitle title Field - end -->
                        </div>
                    </div>
                </div>
                <!-- select subtitle Field - start -->
                <div class="form-group">
                    <input #srtFileInput type="file" id="srtfile" name="srtfile" accept=".srt"
                        formControlName="srtFileSelect" (change)="handleSrtFileInput($event)" tabindex="-1"
                        class="form-control-file" multiple />
                    <div *ngIf="validSrtFileType != null" class="validation">
                        {{ validSrtFileType }}
                    </div>
                </div>
                <!-- select subtitle Field - end -->
            </div>
            <!-- upload subtitles Field - end -->
            <!------------------------------------>
            <!------------------------------------>
            <!-- upload notes Field - start -->
            <div class="form-group mb-2">
                <div class="row">
                    <div class="col-md-2">
                        <span class="video-upload-title">Notes</span>
                    </div>
                    <div class="col-md-10">
                        <textarea formControlName="notes" style="resize: none;" rows="3" class="form-control"
                            tabindex="-1"></textarea>
                        <div
                            *ngIf="(uploadVideoForm.get('notes').touched || uploadVideoForm.get('notes').dirty) && uploadVideoForm.get('notes').invalid ">
                            <div *ngIf="uploadVideoForm.get('notes').errors['maxlength']" class="validation">
                                <span class="alert-color font-12">{{textAreaMaxLengthMessage}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- upload notes Field - end -->
            <!------------------------------------>
            <!------------------------------------>
        </form>
    </div>
</div>
<!--Model Body - end-->
<!------------------------------------>
<!------------------------------------>
<!--Footer Buttons - start-->
<div class="modal-footer upload-video-footer">
    <button type="button" class="btn btn-sm btn-outline-secondary" (click)="decline()" id="declineVideo">{{
        btnCancelText }}</button>
    <button type="button" class="btn btn-sm btn-orange" [disabled]="disableBtn && !uploadVideoForm.valid"
        id="uploadVideoBtn" (click)="accept()">{{ btnOkText }}</button>
</div>
<!--Footer Buttons - end-->
<!------------------------------------>
<!--Model - end-->