import { EndDateOptions } from "src/app/shared/enum/endDateOptions.enum";

export class LicensesRequest {
    id: number;
    name: string;
    displayName: string;
    startDate: number;
    endDate: number;
    trial: boolean;
    enable: boolean;
    endDateUi: EndDateOptions;
    partNumberCode: string;

    constructor($id: number, $name: string, $startDate: number, $endDate: number, $trial: boolean, $enable: boolean,) {
        this.id = $id;
        this.name = $name;
        this.startDate = $startDate;
        this.endDate = $endDate;
        this.trial = $trial;
        this.enable = $enable;
    }

}