import { ListingPageReloadSubjectParameter } from "../common/listingPageReloadSubjectParameter.model";
import { KitManagemantSearchRequestBody } from "./KitManagemantSearchRequestBody.model";

export class KitManagemantFilterAction {
    listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter;
    roleRequestBody: KitManagemantSearchRequestBody;


    constructor($listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, $roleRequestBody: KitManagemantSearchRequestBody) {
        this.listingPageReloadSubjectParameter = $listingPageReloadSubjectParameter;
        this.roleRequestBody = $roleRequestBody;
    }

}