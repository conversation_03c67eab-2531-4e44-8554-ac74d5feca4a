import { Pipe, PipeTransform } from "@angular/core";
import { isNullOrUndefined } from "is-what";
import { BooleanKeyValueMapping } from "src/app/model/common/BooleanKeyValueMapping.model";

@Pipe({
    name: 'booleanKeyValueMappingDisplayNamePipe'
})
export class BooleanKeyValueMappingDisplayNamePipe implements PipeTransform {

    /**
     * Return Boolean value Display Name
     * 
     * @param status 
     * @param productStatusList 
     * @returns 
     */
    transform(status: boolean, productStatusList: Array<BooleanKeyValueMapping>): string {
        if (!isNullOrUndefined(status) && !isNullOrUndefined(productStatusList) && productStatusList.length > 0) {
            let filterList = productStatusList.filter((obj) => obj.value == status);
            return filterList.length == 1 ? filterList[0].key : null;
        }
        return null;
    }

}