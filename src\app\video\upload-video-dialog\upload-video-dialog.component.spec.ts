import { ElementRef } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { UploadVideoRequest } from 'src/app/model/video/upload-video-request.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { VideoService } from 'src/app/shared/videoservice/video.service';
import { commonsProviders, testErrorHandling } from 'src/app/Tesing-Helper/test-utils';
import { UploadVideoDialogComponent } from './upload-video-dialog.component';

describe('UploadVideoDialogComponent', () => {
  let component: UploadVideoDialogComponent;
  let fixture: ComponentFixture<UploadVideoDialogComponent>;
  let exceptionHandlingService: ExceptionHandlingService;
  let videoApiCallServicespy: jasmine.SpyObj<VideoService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;

  const editVideoResponse: UploadVideoRequest = new UploadVideoRequest(
    "Getting started v2.2",
    "13:00",
    "Video1_v2-3.mp4",
    36917361,
    false,
    "Video1_v2-3.jpg",
    214980,
    false,
    [],
    "test121",
    1
  )


  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj<ToastrService>('ToastrService', ['success', 'error', 'warning', 'info']);
    // Correct SpyObj creation for VideoService
    videoApiCallServicespy = jasmine.createSpyObj<VideoService>('VideoService', [
      'getVideo',
      'updateVideo',
      'uploadVideo'
    ]);

    await TestBed.configureTestingModule({
      declarations: [UploadVideoDialogComponent],
      imports: [ReactiveFormsModule],
      providers: [
        NgbActiveModal,
        SessionStorageService,
        ConfirmDialogService,
        LocalStorageService,
        { provide: VideoService, useValue: videoApiCallServicespy },
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(UploadVideoDialogComponent);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });



  it('should handle error in getVideo', () => {
    testErrorHandling(videoApiCallServicespy.getVideo, () => component.getVideo(1), exceptionHandlingService, toastrServiceMock, fixture);
  });

  it('should handle error in updateVideo when videoZipFileId is set', () => {
    component.videoZipFileId = 1;
    testErrorHandling(videoApiCallServicespy.updateVideo, () => component.accept(), exceptionHandlingService, toastrServiceMock, fixture);
  });

  it('should handle error in uploadVideo when videoZipFileId is null', () => {
    component.videoZipFileId = null;
    testErrorHandling(videoApiCallServicespy.uploadVideo, () => component.accept(), exceptionHandlingService, toastrServiceMock, fixture);

    // Additional test: Reset file input
    const mockFileInput = { nativeElement: { value: 'dummy-file.png' } } as ElementRef;
    component.reset(mockFileInput);
    expect(mockFileInput.nativeElement.value).toBe('');
  });

  it('should handle error in uploadFilesToBlob (uploadVideo case)', () => {
    testErrorHandling(videoApiCallServicespy.uploadVideo, () => component.uploadFilesToBlob(editVideoResponse, null, false), exceptionHandlingService, toastrServiceMock, fixture);
  });

  it('should handle error in uploadFilesToBlob (updateVideo case)', () => {
    testErrorHandling(videoApiCallServicespy.updateVideo, () => component.uploadFilesToBlob(editVideoResponse, null, true), exceptionHandlingService, toastrServiceMock, fixture);
  });
});
