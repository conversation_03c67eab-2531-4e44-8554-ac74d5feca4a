import { ProductStatusEnum } from "src/app/shared/enum/Common/ProductStatus.enum";
import { ProbeBaseResponse } from "./ProbeBaseResponse.model";
import { LicensesRequest } from "./multiProbe/LicensesRequest.model";

export class ProbeDetailWithConfig extends ProbeBaseResponse {
    features: Array<LicensesRequest>;
    presets: Array<LicensesRequest>;
    productStatus: ProductStatusEnum;

    constructor( //NOSONAR
        id: number,
        type: string,
        serialNumber: string,
        partNumber: string,
        probeVersion: string,
        lastConnectedTime: number,
        licenseDate: number,
        reminder: boolean,
        licenseModifiedBy: string,
        countryId: number,
        customerName: string,
        customerEmail: string,
        salesOrderNumber: string,
        modifiedDate: number,
        country: string,
        locked: boolean,
        editable: boolean,
        poNumber: string,
        deviceAutoLock: boolean,
        probeAutoLock: boolean,
        features: Array<LicensesRequest>,
        presets: Array<LicensesRequest>,
        productStatus: ProductStatusEnum,
        orderRecordType: string
    ) {
        super(
            id,
            type,
            serialNumber,
            partNumber,
            probeVersion,
            lastConnectedTime,
            licenseDate,
            reminder,
            licenseModifiedBy,
            countryId,
            customerName,
            customerEmail,
            salesOrderNumber,
            modifiedDate,
            country,
            locked,
            editable,
            poNumber,
            deviceAutoLock,
            probeAutoLock,
            orderRecordType
        );
        this.features = features;
        this.presets = presets;
        this.productStatus = productStatus;
    }
}