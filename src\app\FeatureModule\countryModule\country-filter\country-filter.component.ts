import { Component, Input } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { isUndefined } from 'is-what';
import { Subscription } from 'rxjs';
import { SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH, SPECIAL_CHARACTER_PATTERN, SPECIAL_CHARACTER_ERROR_MESSAGE } from 'src/app/app.constants';
import { CountryFilterAction } from 'src/app/model/Country/CountryFilterAction.model';
import { CountryRequestBody } from 'src/app/model/Country/CountryRequestBody.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CountryService } from 'src/app/shared/Service/CountryService/country.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { KeyValueMappingServiceService } from 'src/app/shared/util/key-value-mapping-service.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
@Component({
  selector: 'app-country-filter',
  templateUrl: './country-filter.component.html',
  styleUrls: ['./country-filter.component.css']
})
export class CountryFilterComponent {
  @Input("isFilterComponentInitWithApicall") isFilterComponentInitWithApicall: boolean;
  @Input("countryRequestBody") countryRequestBody: CountryRequestBody;

  filterCountryForm = new FormGroup({
    countryName: new FormControl('', [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    languageName: new FormControl('', [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
  })
  defaultListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
  subscriptionForRefeshList: Subscription;

  //MaxLength Message
  small_textBoxMaxLengthMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;



  constructor(private commonOperationService: CommonOperationsService,
    private commonService: CommonsService,
    private countryService: CountryService,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private keyValueMappingServiceService: KeyValueMappingServiceService) { }


  public ngOnInit(): void {
    this.onInitSubject();
    this.setFilterValue();
    if (this.isFilterComponentInitWithApicall) {
      this.clearFilter(this.defaultListingPageReloadSubjectParameter);
    }
  }

  /**
  * Set Filter value 
  * 
  * Note : if user hide and show filter then set data in storage
  * 
  * <AUTHOR>
  */
  public setFilterValue() {
    if (this.countryRequestBody != null) {
      this.filterCountryForm.get('countryName').setValue(this.countryRequestBody.country);
      this.filterCountryForm.get('languageName').setValue(this.countryRequestBody.language);
    }
  }

  public onInitSubject(): void {
    /**
     * Role Detail Page Refresh After some Action Like Create or Update or Delete Role
     * <AUTHOR>
     */
    this.subscriptionForRefeshList = this.countryService.getCountryRefreshSubject().subscribe((listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter) => {
      if (listingPageReloadSubjectParameter.isReloadData) {
        if (listingPageReloadSubjectParameter.isClearFilter) {
          this.clearFilter(listingPageReloadSubjectParameter);
        } else {
          //page change 1,2,3
          this.countryListPageRefresh(listingPageReloadSubjectParameter);
        }
      }
    });
  }

  /**
   * Clear All filter and Reload Data
   * <AUTHOR>
   */
  public clearFilter(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    this.clearAllFilter();
    this.countryListPageRefresh(listingPageReloadSubjectParameter);
  }

  /**
   * Clear All filter
   * <AUTHOR>
   */
  public clearAllFilter(): void {
    this.filterCountryForm.get('countryName').setValue("");
    this.filterCountryForm.get('languageName').setValue("");
  }


  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForRefeshList)) { this.subscriptionForRefeshList.unsubscribe() }
  }


  public searchData() {
    let allFormValue = this.filterCountryForm.value;
    this.filterCountryForm.get('countryName').setValue(this.commonService.checkNullFieldValue(allFormValue.countryName));
    this.filterCountryForm.get('languageName').setValue(this.commonService.checkNullFieldValue(allFormValue.languageName));
    if (this.filterCountryForm.invalid ||
      (this.commonService.checkValueIsNullOrEmpty(allFormValue.countryName) &&
        this.commonService.checkValueIsNullOrEmpty(allFormValue.languageName))) {
      this.commonOperationService.showEmptyFilterTosteMessge();
    } else {
      this.countryListPageRefresh(this.defaultListingPageReloadSubjectParameter);

    }
  }

  /**
   * Get Filter Data and pass to Listing page and Reload Page 
   * <AUTHOR>
   */
  public countryListPageRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    if (this.filterCountryForm.invalid) {
      this.filterCountryForm.reset();
    }
    let allFormValue = this.filterCountryForm.value;
    let countryRequestBody = new CountryRequestBody(allFormValue.countryName, allFormValue.languageName);
    let countryFilterAction = new CountryFilterAction(listingPageReloadSubjectParameter, countryRequestBody);
    this.countryService.callCountryListFilterRequestParameterSubject(countryFilterAction);
  }
}
