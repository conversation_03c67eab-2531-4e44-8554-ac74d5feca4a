@import '../../../../assets/css/custom_style.css';

#deleteExam:focus {
    outline: 0px;
    border: 0px;
}

.modal-title i,
.modal-title em {
    color: #c70e24;
    float: left;
    font-size: 24px;
    margin-top: 0;
}

#updateFeatureModel .feature-block {
    height: 50vh;
}

#updateFeatureModel .ml-30 {
    margin-left: 30px;
}

#updateFeatureModel .validation {
    color: #c70e24;
    font-size: 11px;
}

#updateFeatureModel .feature-expire-model {
    color: #919199;
    font-size: 14px;
}

#updateFeatureModel .update-feature-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    float: unset;
}

#updateFeatureModel .custom-checkbox .custom-control-label {
    line-height: 23px;
}

#updateFeatureModel .custom-radio .custom-control-label {
    line-height: 22px;
}

#mainSection {
    max-height: 80vh;
    overflow-y: auto;
    overflow-x: hidden;
}

#updateFeatureModel .border-license {
    border-left: 1px solid #6c757d;
    border-bottom: 1px solid #6c757d;
}