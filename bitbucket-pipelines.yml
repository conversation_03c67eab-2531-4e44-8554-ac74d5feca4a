#  Template NodeJS build

#  This template allows you to validate your NodeJS code.
#  The workflow allows running tests and code linting on the default branch.

image: node:20

pipelines:
  default:
    - parallel:
        - step:
            name: Build and Test
            caches:
              - node
            script:
              # Install dependencies
              - npm install -g @angular/cli@latest
              
              # Add Google Chrome repository and install Chrome
              - apt-get update
              - apt-get install -y wget curl gnupg
              - wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add -
              - echo "deb [arch=amd64] https://dl.google.com/linux/chrome/deb/ stable main" | tee -a /etc/apt/sources.list.d/google-chrome.list
              - apt-get update
              - apt-get install -y google-chrome-stable
              
              # Set the CHROME_BIN environment variable
              - export CHROME_BIN=/usr/bin/google-chrome
              
              # Install npm dependencies
              - npm install
              
              # Run tests with code coverage
              - ng test --code-coverage --watch=false
              
              # Build the production version
              - npm run build -- --configuration production
