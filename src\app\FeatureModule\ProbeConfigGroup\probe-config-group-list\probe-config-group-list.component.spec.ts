import { DatePipe } from '@angular/common';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { Abdomen, AiFast, AutoDoppler, AutoEf, AutoPreset, Bladder, CwDoppler, Heart, Lungs_Torso, Msk, Nerve, PwDoppler, TDI, Trio20 } from 'src/app/Tesing-Helper/ProbeConfigGrouoInfo';
import { commonsProviders, testAuthentication, testDropdownInteraction, testPagination, testToggleFilter } from 'src/app/Tesing-Helper/test-utils';
import { INTERNAL_SERVER_ERROR, ITEMS_PER_PAGE } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { ProbeConfigGroupDeatilResponse } from 'src/app/model/ProbeConfigGroup/ProbeConfigGroupDeatilResponse.model';
import { ProbeConfigGroupPageResponse } from 'src/app/model/ProbeConfigGroup/probeConfigGroupPageResponse.model';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { ProbeFeatureResponse } from 'src/app/model/probe/ProbeFeatureResponse.model';
import { ProbeTypeResponse } from 'src/app/model/probe/probeTypeResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { PresetApiService } from 'src/app/shared/Service/PresetService/preset-api.service';
import { ProbeConfigGroupApiCallService } from 'src/app/shared/Service/ProbeConfigGroup/probe-config-group-api-call.service';
import { ProbeConfigGroupService } from 'src/app/shared/Service/ProbeConfigGroup/probe-config-group.service';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { ValidityEnum } from 'src/app/shared/enum/ValidityEnum.enum';
import { PermissionService } from 'src/app/shared/permission.service';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { FeatureAndPresetInformationDisplayPipe } from 'src/app/shared/pipes/Common/FeatureAndPresetInformationDisplayPipe.pipe';
import { ProbeConfigGroupCheckBoxPipe } from 'src/app/shared/pipes/Probe Config Group/probe-config-group-checkbox.pipe';
import { FeatureValidityOptionHideShowPipe } from 'src/app/shared/pipes/Probe/feature-validity-option-hide-show.pipe';
import { FeaturesBaseResponseDisplayPipe } from 'src/app/shared/pipes/Probe/features-base-response-display.pipe';
import { FeaturesValidityPartNumberDisplayPipe } from 'src/app/shared/pipes/Probe/features-validity-partNumber-Display.pipe';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { DisableLicenseCheckBoxpipe } from 'src/app/shared/pipes/disable-license-check-box.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { KeyValueMappingServiceService } from 'src/app/shared/util/key-value-mapping-service.service';
import { AddOrUpdateProbeConfigGroupComponent } from '../add-or-update-probe-config-group/add-or-update-probe-config-group.component';
import { ProbeConfigGroupDetailComponent } from '../probe-config-group-detail/probe-config-group-detail.component';
import { ProbeConfigGroupFilterComponent } from '../probe-config-group-filter/probe-config-group-filter.component';
import { ProbeConfigGroupListComponent } from './probe-config-group-list.component';

describe('ProbeConfigGroupListComponent', () => {
  let component: ProbeConfigGroupListComponent;
  let fixture: ComponentFixture<ProbeConfigGroupListComponent>;
  let exceptionHandlingService: ExceptionHandlingService;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let authServiceSpy: jasmine.SpyObj<AuthJwtService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let probeApiCallServiceSpy: jasmine.SpyObj<ProbeApiService>;
  let presetApiCallServiceSpy: jasmine.SpyObj<PresetApiService>;
  let probeConfigGroupApiCallServiceSpy: jasmine.SpyObj<ProbeConfigGroupApiCallService>;
  let probeConfigGroupServiceSpy: ProbeConfigGroupService;
  let confirmDialogServiceMock: jasmine.SpyObj<ConfirmDialogService>;

  let pwDroplerFeature = {
    "featureId": 1,
    "displayName": "PW Doppler",
    "partNumbers": [{
      "partNumber": "P007786-001",
      "validity": "PERPETUAL",
      "probeTypeMasterFeatureMasterMappingId": 3,
      "default": false,
      "allowedToEdit": true
    }, {
      "partNumber": "P007786-002",
      "validity": "ONE_YEAR",
      "probeTypeMasterFeatureMasterMappingId": 10,
      "default": false,
      "allowedToEdit": true
    }]
  }

  let probeConfigGroupListResponse: ProbeConfigGroupPageResponse = {
    "content": [{
      "id": 3,
      "partNumberCode": "PCG-T3",
      "probeType": "TORSO3",
      "description": "Probe Config group with T3 probe type",
      "modifiedDate": 1726133180607,
      "features": [AiFast],
      "presets": [Lungs_Torso, Heart, Abdomen]
    }, {
      "id": 4,
      "partNumberCode": "PCG-T1U",
      "probeType": "TORSO1_USB",
      "description": "PCG-T1U",
      "modifiedDate": 1726133235902,
      "features": [PwDoppler],
      "presets": [Heart]
    }, {
      "id": 5,
      "partNumberCode": "PCG-T1U-1",
      "probeType": "TORSO1_USB",
      "description": "",
      "modifiedDate": 1726133794955,
      "features": [CwDoppler],
      "presets": [Bladder]
    }, {
      "id": 6,
      "partNumberCode": "PCG-T1",
      "probeType": "TORSO1",
      "description": "",
      "modifiedDate": 1726133833872,
      "features": [AiFast],
      "presets": [Lungs_Torso, Heart, Abdomen]
    }, {
      "id": 16,
      "partNumberCode": "PCG-111",
      "probeType": "TORSO1_USB",
      "description": "",
      "modifiedDate": 1726812380514,
      "features": [AutoEf, CwDoppler, AutoDoppler, Trio20, AutoPreset, AiFast, TDI, PwDoppler],
      "presets": [Lungs_Torso, Heart, Abdomen, Bladder]
    }, {
      "id": 19,
      "partNumberCode": "PCG-111-1",
      "probeType": "LEXSA",
      "description": "",
      "modifiedDate": 1726813244398,
      "features": [PwDoppler],
      "presets": []
    }, {
      "id": 26,
      "partNumberCode": "P901155-001",
      "probeType": "LEXSA",
      "description": "",
      "modifiedDate": 1727332994919,
      "features": [PwDoppler],
      "presets": [Msk]
    }, {
      "id": 27,
      "partNumberCode": "PCG-TORSO1",
      "probeType": "TORSO1_USB",
      "description": "",
      "modifiedDate": 1727341952114,
      "features": [AutoEf, CwDoppler, AutoDoppler, Trio20, AutoPreset, AiFast, TDI, PwDoppler],
      "presets": [Lungs_Torso, Heart, Abdomen, Bladder]
    }, {
      "id": 29,
      "partNumberCode": "Test-1",
      "probeType": "TORSO1_USB",
      "description": "",
      "modifiedDate": 1727343695663,
      "features": [AiFast],
      "presets": [Heart]
    }, {
      "id": 30,
      "partNumberCode": "test-ocr",
      "probeType": "TORSO1_USB",
      "description": "",
      "modifiedDate": 1727679664055,
      "features": [PwDoppler],
      "presets": [Lungs_Torso, Heart, Abdomen, Bladder]
    }],
    "pageable": {
      "pageNumber": 0,
      "pageSize": 10,
      "sort": {
        "empty": true,
        "sorted": false,
        "unsorted": true
      },
      "offset": 0,
      "paged": true,
      "unpaged": false
    },
    "last": false,
    "totalPages": 2,
    "totalElements": 20,
    "size": 10,
    "number": 0,
    "sort": {
      "empty": true,
      "sorted": false,
      "unsorted": true
    },
    "first": true,
    "numberOfElements": 10,
    "empty": false
  }
  let getFeaturesResponse: ProbeFeatureResponse[] = [
    {
      id: 2,
      featureId: null,
      partNumbers: [], // Ensure this is an empty array of strings
      name: '', // Provide an empty string as a default for `name`
      displayName: 'CW Doppler',
    },
    {
      id: 3,
      featureId: null,
      partNumbers: [],
      name: '',
      displayName: 'Auto EF',
    },
    {
      id: 9,
      featureId: null,
      partNumbers: [],
      name: '',
      displayName: 'Trio 2.0 (Educational)',
    },
    {
      id: 8,
      featureId: null,
      partNumbers: [],
      name: '',
      displayName: 'Auto Doppler',
    },
    {
      id: 4,
      featureId: null,
      partNumbers: [],
      name: '',
      displayName: 'TDI',
    },
    {
      id: 7,
      featureId: null,
      partNumbers: [],
      name: '',
      displayName: 'Auto Preset',
    },
    {
      id: 6,
      featureId: null,
      partNumbers: [],
      name: '',
      displayName: 'Trio 2.0',
    },
    {
      id: 1,
      featureId: null,
      partNumbers: [],
      name: '',
      displayName: 'PW Doppler',
    },
    {
      id: 5,
      featureId: null,
      partNumbers: [],
      name: '',
      displayName: 'AI Fast',
    },
  ];
  let probeTypeResponse = [{
    "probeTypeId": 4,
    "displayName": "Torso1, USB",
    "prefix": "T1A",
    "features": [pwDroplerFeature, {
      "featureId": 2,
      "displayName": "CW Doppler",
      "partNumbers": [{
        "partNumber": "P007791-001",
        "validity": "PERPETUAL",
        "probeTypeMasterFeatureMasterMappingId": 4,
        "default": false,
        "allowedToEdit": true
      }, {
        "partNumber": "P007791-002",
        "validity": "ONE_YEAR",
        "probeTypeMasterFeatureMasterMappingId": 11,
        "default": false,
        "allowedToEdit": true
      }]
    }, {
        "featureId": 3,
        "displayName": "Auto EF",
        "partNumbers": [{
          "partNumber": "P007788-001",
          "validity": "PERPETUAL",
          "probeTypeMasterFeatureMasterMappingId": 5,
          "default": false,
          "allowedToEdit": true
        }, {
          "partNumber": "P007788-002",
          "validity": "ONE_YEAR",
          "probeTypeMasterFeatureMasterMappingId": 12,
          "default": false,
          "allowedToEdit": true
        }]
      }, {
        "featureId": 4,
        "displayName": "TDI",
        "partNumbers": [{
          "partNumber": "P007787-001",
          "validity": "PERPETUAL",
          "probeTypeMasterFeatureMasterMappingId": 6,
          "default": false,
          "allowedToEdit": true
        }, {
          "partNumber": "P007787-002",
          "validity": "ONE_YEAR",
          "probeTypeMasterFeatureMasterMappingId": 13,
          "default": false,
          "allowedToEdit": true
        }]
      }, {
        "featureId": 5,
        "displayName": "AI Fast",
        "partNumbers": [{
          "partNumber": "P007790-001",
          "validity": "PERPETUAL",
          "probeTypeMasterFeatureMasterMappingId": 7,
          "default": false,
          "allowedToEdit": true
        }, {
          "partNumber": "P007790-002",
          "validity": "ONE_YEAR",
          "probeTypeMasterFeatureMasterMappingId": 14,
          "default": false,
          "allowedToEdit": true
        }]
      }, {
        "featureId": 6,
        "displayName": "Trio 2.0",
        "partNumbers": [{
          "partNumber": "P008432-001",
          "validity": "PERPETUAL",
          "probeTypeMasterFeatureMasterMappingId": 8,
          "default": false,
          "allowedToEdit": true
        }, {
          "partNumber": "P008432-002",
          "validity": "ONE_YEAR",
          "probeTypeMasterFeatureMasterMappingId": 15,
          "default": false,
          "allowedToEdit": true
        }]
      }, {
        "featureId": 7,
        "displayName": "Auto Preset",
        "partNumbers": [{
          "partNumber": "P008332-001",
          "validity": "PERPETUAL",
          "probeTypeMasterFeatureMasterMappingId": 17,
          "default": false,
          "allowedToEdit": true
        }, {
          "partNumber": "P008332-002",
          "validity": "ONE_YEAR",
          "probeTypeMasterFeatureMasterMappingId": 18,
          "default": false,
          "allowedToEdit": true
        }]
      }, {
        "featureId": 8,
        "displayName": "Auto Doppler",
        "partNumbers": [{
          "partNumber": "P008331-001",
          "validity": "PERPETUAL",
          "probeTypeMasterFeatureMasterMappingId": 19,
          "default": false,
          "allowedToEdit": true
        }, {
          "partNumber": "P008331-002",
          "validity": "ONE_YEAR",
          "probeTypeMasterFeatureMasterMappingId": 20,
          "default": false,
          "allowedToEdit": true
        }]
      }, {
        "featureId": 9,
        "displayName": "Trio 2.0 (Educational)",
        "partNumbers": [{
          "partNumber": "P007789-001",
          "validity": "PERPETUAL",
          "probeTypeMasterFeatureMasterMappingId": 25,
          "default": false,
          "allowedToEdit": true
        }, {
          "partNumber": "P007789-002",
          "validity": "ONE_YEAR",
          "probeTypeMasterFeatureMasterMappingId": 26,
          "default": false,
          "allowedToEdit": true
        }]
      }],
    "presets": [{
      "presetId": 1,
      "displayName": "Heart",
      "partNumbers": [{
        "partNumber": "P008420-001",
        "validity": "PERPETUAL",
        "probeTypeMasterPresetMasterMappingId": 91,
        "default": false,
        "allowedToEdit": true
      }]
    }, {
      "presetId": 2,
      "displayName": "Lungs Torso",
      "partNumbers": [{
        "partNumber": "P008421-001",
        "validity": "PERPETUAL",
        "probeTypeMasterPresetMasterMappingId": 92,
        "default": false,
        "allowedToEdit": true
      }]
    }, {
      "presetId": 3,
      "displayName": "Abdomen",
      "partNumbers": [{
        "partNumber": "P008422-001",
        "validity": "PERPETUAL",
        "probeTypeMasterPresetMasterMappingId": 93,
        "default": false,
        "allowedToEdit": true
      }]
    }, {
      "presetId": 4,
      "displayName": "Bladder",
      "partNumbers": [{
        "partNumber": "P008423-001",
        "validity": "PERPETUAL",
        "probeTypeMasterPresetMasterMappingId": 94,
        "default": false,
        "allowedToEdit": true
      }]
    }, {
      "presetId": 5,
      "displayName": "Ob",
      "partNumbers": [{
        "partNumber": "P008490-001",
        "validity": "PERPETUAL",
        "probeTypeMasterPresetMasterMappingId": 95,
        "default": false,
        "allowedToEdit": true
      }]
    }, {
      "presetId": 9,
      "displayName": "Gyn",
      "partNumbers": [{
        "partNumber": "P008491-001",
        "validity": "PERPETUAL",
        "probeTypeMasterPresetMasterMappingId": 96,
        "default": false,
        "allowedToEdit": true
      }]
    }]
  }, {
    "probeTypeId": 3,
    "displayName": "Lexsa",
    "prefix": "L1A",
    "features": [pwDroplerFeature],
    "presets": [{
      "presetId": 6,
      "displayName": "Msk",
      "partNumbers": [{
        "partNumber": "P008424-001",
        "validity": "PERPETUAL",
        "probeTypeMasterPresetMasterMappingId": 98,
        "default": false,
        "allowedToEdit": true
      }]
    }, {
      "presetId": 7,
      "displayName": "Nerve",
      "partNumbers": [{
        "partNumber": "P008425-001",
        "validity": "PERPETUAL",
        "probeTypeMasterPresetMasterMappingId": 99,
        "default": false,
        "allowedToEdit": true
      }]
    }, {
      "presetId": 8,
      "displayName": "Vascular",
      "partNumbers": [{
        "partNumber": "P008426-001",
        "validity": "PERPETUAL",
        "probeTypeMasterPresetMasterMappingId": 100,
        "default": false,
        "allowedToEdit": true
      }]
    }, {
      "presetId": 10,
      "displayName": "Lungs Lexsa",
      "partNumbers": [{
        "partNumber": "P008427-001",
        "validity": "PERPETUAL",
        "probeTypeMasterPresetMasterMappingId": 97,
        "default": false,
        "allowedToEdit": true
      }]
    }]
  }, {
    "probeTypeId": 1,
    "displayName": "Torso1",
    "prefix": "T1B",
    "features": [{
      "featureId": 5,
      "displayName": "AI Fast",
      "partNumbers": [{
        "partNumber": "P007651-001",
        "validity": "PERPETUAL",
        "probeTypeMasterFeatureMasterMappingId": 1,
        "default": false,
        "allowedToEdit": true
      }]
    }],
    "presets": [{
      "presetId": 1,
      "displayName": "Heart",
      "partNumbers": [{
        "partNumber": null,
        "validity": "PERPETUAL",
        "probeTypeMasterPresetMasterMappingId": 102,
        "default": true,
        "allowedToEdit": false
      }]
    }, {
      "presetId": 2,
      "displayName": "Lungs Torso",
      "partNumbers": [{
        "partNumber": null,
        "validity": "PERPETUAL",
        "probeTypeMasterPresetMasterMappingId": 103,
        "default": true,
        "allowedToEdit": false
      }]
    }, {
      "presetId": 3,
      "displayName": "Abdomen",
      "partNumbers": [{
        "partNumber": null,
        "validity": "PERPETUAL",
        "probeTypeMasterPresetMasterMappingId": 104,
        "default": true,
        "allowedToEdit": false
      }]
    }]
  }, {
    "probeTypeId": 2,
    "displayName": "Torso3",
    "prefix": "T3B",
    "features": [{
      "featureId": 5,
      "displayName": "AI Fast",
      "partNumbers": [{
        "partNumber": "P007651-002",
        "validity": "PERPETUAL",
        "probeTypeMasterFeatureMasterMappingId": 2,
        "default": false,
        "allowedToEdit": true
      }]
    }],
    "presets": [{
      "presetId": 1,
      "displayName": "Heart",
      "partNumbers": [{
        "partNumber": null,
        "validity": "PERPETUAL",
        "probeTypeMasterPresetMasterMappingId": 107,
        "default": true,
        "allowedToEdit": false
      }]
    }, {
      "presetId": 2,
      "displayName": "Lungs Torso",
      "partNumbers": [{
        "partNumber": null,
        "validity": "PERPETUAL",
        "probeTypeMasterPresetMasterMappingId": 108,
        "default": true,
        "allowedToEdit": false
      }]
    }, {
      "presetId": 3,
      "displayName": "Abdomen",
      "partNumbers": [{
        "partNumber": null,
        "validity": "PERPETUAL",
        "probeTypeMasterPresetMasterMappingId": 109,
        "default": true,
        "allowedToEdit": false
      }]
    }]
  }]
  let getPresetsListResponse = [{
    "id": 1,
    "displayName": "Heart"
  }, {
    "id": 7,
    "displayName": "Nerve"
  }, {
    "id": 10,
    "displayName": "Lungs Lexsa"
  }, {
    "id": 5,
    "displayName": "Ob"
  }, {
    "id": 6,
    "displayName": "Msk"
  }, {
    "id": 2,
    "displayName": "Lungs Torso"
  }, {
    "id": 4,
    "displayName": "Bladder"
  }, {
    "id": 8,
    "displayName": "Vascular"
  }, {
    "id": 9,
    "displayName": "Gyn"
  }, {
    "id": 3,
    "displayName": "Abdomen"
  }]
  let pcgDetailResponse: ProbeConfigGroupDeatilResponse = {
    "id": 65,
    "partNumberCode": "PCG-L1",
    "probeType": "LEXSA",
    "description": "",
    "modifiedDate": 1727345458462,
    "createdDate": 1727345458462,
    "features": [PwDoppler],
    "presets": [Nerve, Msk]
  }

  let pwdropler = {
    "featureId": 1,
    "displayName": "PW Doppler",
    "id": 1,
    "name": "Torso1, USB",
    "partNumbers": [{
      "partNumber": "P007786-001",
      validity: ValidityEnum.PERPETUAL,
      "probeTypeMasterFeatureMasterMappingId": 3,
      "default": false,
      "allowedToEdit": true
    }, {
      "partNumber": "P007786-002",
      validity: ValidityEnum.ONE_YEAR,
      "probeTypeMasterFeatureMasterMappingId": 10,
      "default": false,
      "allowedToEdit": true
    }]
  }

  let getprobeTypeResponseListResponse: Array<ProbeTypeResponse> = [{
    "probeTypeId": 4,
    "displayName": "Torso1, USB",
    "name": "Torso1, USB",
    "prefix": "T1A",
    "features": [pwdropler, {
      "featureId": 2,
      "displayName": "CW Doppler",
      "id": 1,
      "name": "Torso1, USB",
      "partNumbers": [{
        "partNumber": "P007791-001",
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterFeatureMasterMappingId": 4,
        "default": false,
        "allowedToEdit": true
      }, {
        "partNumber": "P007791-002",
        validity: ValidityEnum.ONE_YEAR,
        "probeTypeMasterFeatureMasterMappingId": 11,
        "default": false,
        "allowedToEdit": true
      }]
    }, {
        "featureId": 3,
        "displayName": "Auto EF",
        "id": 1,
        "name": "Torso1, USB",
        "partNumbers": [{
          "partNumber": "P007788-001",
          validity: ValidityEnum.PERPETUAL,
          "probeTypeMasterFeatureMasterMappingId": 5,
          "default": false,
          "allowedToEdit": true
        }, {
          "partNumber": "P007788-002",
          validity: ValidityEnum.ONE_YEAR,
          "probeTypeMasterFeatureMasterMappingId": 12,
          "default": false,
          "allowedToEdit": true
        }]
      }, {
        "featureId": 4,
        "id": 1,
        "name": "Torso1, USB",
        "displayName": "TDI",
        "partNumbers": [{
          "partNumber": "P007787-001",
          validity: ValidityEnum.PERPETUAL,
          "probeTypeMasterFeatureMasterMappingId": 6,
          "default": false,
          "allowedToEdit": true
        }, {
          "partNumber": "P007787-002",
          validity: ValidityEnum.ONE_YEAR,
          "probeTypeMasterFeatureMasterMappingId": 13,
          "default": false,
          "allowedToEdit": true
        }]
      }, {
        "featureId": 5,
        "id": 1,
        "name": "Torso1, USB",
        "displayName": "AI Fast",
        "partNumbers": [{
          "partNumber": "P007790-001",
          validity: ValidityEnum.PERPETUAL,
          "probeTypeMasterFeatureMasterMappingId": 7,
          "default": false,
          "allowedToEdit": true
        }, {
          "partNumber": "P007790-002",
          validity: ValidityEnum.ONE_YEAR,
          "probeTypeMasterFeatureMasterMappingId": 14,
          "default": false,
          "allowedToEdit": true
        }]
      }, {
        "featureId": 6,
        "id": 1,
        "name": "Torso1, USB",
        "displayName": "Trio 2.0",
        "partNumbers": [{
          "partNumber": "P008432-001",
          validity: ValidityEnum.PERPETUAL,
          "probeTypeMasterFeatureMasterMappingId": 8,
          "default": false,
          "allowedToEdit": true
        }, {
          "partNumber": "P008432-002",
          validity: ValidityEnum.ONE_YEAR,
          "probeTypeMasterFeatureMasterMappingId": 15,
          "default": false,
          "allowedToEdit": true
        }]
      }, {
        "featureId": 7,
        "id": 1,
        "name": "Torso1, USB",
        "displayName": "Auto Preset",
        "partNumbers": [{
          "partNumber": "P008332-001",
          validity: ValidityEnum.PERPETUAL,
          "probeTypeMasterFeatureMasterMappingId": 17,
          "default": false,
          "allowedToEdit": true
        }, {
          "partNumber": "P008332-002",
          validity: ValidityEnum.ONE_YEAR,
          "probeTypeMasterFeatureMasterMappingId": 18,
          "default": false,
          "allowedToEdit": true
        }]
      }, {
        "featureId": 8,
        "id": 1,
        "name": "Torso1, USB",
        "displayName": "Auto Doppler",
        "partNumbers": [{
          "partNumber": "P008331-001",
          validity: ValidityEnum.PERPETUAL,
          "probeTypeMasterFeatureMasterMappingId": 19,
          "default": false,
          "allowedToEdit": true
        }, {
          "partNumber": "P008331-002",
          validity: ValidityEnum.ONE_YEAR,
          "probeTypeMasterFeatureMasterMappingId": 20,
          "default": false,
          "allowedToEdit": true
        }]
      }, {
        "featureId": 9,
        "id": 1,
        "name": "Torso1, USB",
        "displayName": "Trio 2.0 (Educational)",
        "partNumbers": [{
          "partNumber": "P007789-001",
          validity: ValidityEnum.PERPETUAL,
          "probeTypeMasterFeatureMasterMappingId": 25,
          "default": false,
          "allowedToEdit": true
        }, {
          "partNumber": "P007789-002",
          validity: ValidityEnum.ONE_YEAR,
          "probeTypeMasterFeatureMasterMappingId": 26,
          "default": false,
          "allowedToEdit": true
        }]
      }],
    "presets": [{
      "presetId": 1,
      "id": 1,
      "name": "Torso1, USB",
      "displayName": "Heart",
      "partNumbers": [{
        "partNumber": "P008420-001",
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterPresetMasterMappingId": 91,
        "default": false,
        "allowedToEdit": true
      }]
    }, {
      "presetId": 2,
      "id": 1,
      "name": "Torso1, USB",
      "displayName": "Lungs Torso",
      "partNumbers": [{
        "partNumber": "P008421-001",
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterPresetMasterMappingId": 92,
        "default": false,
        "allowedToEdit": true
      }]
    }, {
      "presetId": 3,
      "id": 1,
      "name": "Torso1, USB",
      "displayName": "Abdomen",
      "partNumbers": [{
        "partNumber": "P008422-001",
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterPresetMasterMappingId": 93,
        "default": false,
        "allowedToEdit": true
      }]
    }, {
      "presetId": 4,
      "id": 1,
      "name": "Torso1, USB",
      "displayName": "Bladder",
      "partNumbers": [{
        "partNumber": "P008423-001",
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterPresetMasterMappingId": 94,
        "default": false,
        "allowedToEdit": true
      }]
    }, {
      "presetId": 5,
      "id": 1,
      "name": "Torso1, USB",
      "displayName": "Ob",
      "partNumbers": [{
        "partNumber": "P008490-001",
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterPresetMasterMappingId": 95,
        "default": false,
        "allowedToEdit": true
      }]
    }, {
      "presetId": 9,
      "id": 1,
      "name": "Torso1, USB",
      "displayName": "Gyn",
      "partNumbers": [{
        "partNumber": "P008491-001",
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterPresetMasterMappingId": 96,
        "default": false,
        "allowedToEdit": true
      }]
    }]
  }, {
    "probeTypeId": 3,
    "displayName": "Lexsa",
    "name": "Torso1, USB",
    "prefix": "L1A",
    "features": [pwdropler],
    "presets": [{
      "presetId": 6,
      "id": 1,
      "name": "Torso1, USB",
      "displayName": "Msk",
      "partNumbers": [{
        "partNumber": "P008424-001",
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterPresetMasterMappingId": 98,
        "default": false,
        "allowedToEdit": true
      }]
    }, {
      "presetId": 7,
      "id": 1,
      "name": "Torso1, USB",
      "displayName": "Nerve",
      "partNumbers": [{
        "partNumber": "P008425-001",
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterPresetMasterMappingId": 99,
        "default": false,
        "allowedToEdit": true
      }]
    }, {
      "presetId": 8,
      "id": 1,
      "name": "Torso1, USB",
      "displayName": "Vascular",
      "partNumbers": [{
        "partNumber": "P008426-001",
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterPresetMasterMappingId": 100,
        "default": false,
        "allowedToEdit": true
      }]
    }, {
      "presetId": 10,
      "id": 1,
      "name": "Torso1, USB",
      "displayName": "Lungs Lexsa",
      "partNumbers": [{
        "partNumber": "P008427-001",
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterPresetMasterMappingId": 97,
        "default": false,
        "allowedToEdit": true
      }]
    }]
  }, {
    "probeTypeId": 1,
    "displayName": "Torso1",
    "name": "Torso1, USB",
    "prefix": "T1B",
    "features": [{
      "featureId": 5,
      "id": 1,
      "name": "Torso1, USB",
      "displayName": "AI Fast",
      "partNumbers": [{
        "partNumber": "P007651-001",
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterFeatureMasterMappingId": 1,
        "default": false,
        "allowedToEdit": true
      }]
    }],
    "presets": [{
      "presetId": 1,
      "id": 1,
      "name": "Torso1, USB",
      "displayName": "Heart",
      "partNumbers": [{
        "partNumber": null,
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterPresetMasterMappingId": 102,
        "default": true,
        "allowedToEdit": false
      }]
    }, {
      "presetId": 2,
      "id": 1,
      "name": "Torso1, USB",
      "displayName": "Lungs Torso",
      "partNumbers": [{
        "partNumber": null,
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterPresetMasterMappingId": 103,
        "default": true,
        "allowedToEdit": false
      }]
    }, {
      "presetId": 3,
      "id": 1,
      "name": "Torso1, USB",
      "displayName": "Abdomen",
      "partNumbers": [{
        "partNumber": null,
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterPresetMasterMappingId": 104,
        "default": true,
        "allowedToEdit": false
      }]
    }]
  }, {
    "probeTypeId": 2,
    "displayName": "Torso3",
    "name": "Torso1, USB",
    "prefix": "T3B",
    "features": [{
      "featureId": 5,
      "id": 1,
      "name": "Torso1, USB",
      "displayName": "AI Fast",
      "partNumbers": [{
        "partNumber": "P007651-002",
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterFeatureMasterMappingId": 2,
        "default": false,
        "allowedToEdit": true
      }]
    }],
    "presets": [{
      "presetId": 1,
      "id": 1,
      "name": "Torso1, USB",
      "displayName": "Heart",
      "partNumbers": [{
        "partNumber": null,
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterPresetMasterMappingId": 107,
        "default": true,
        "allowedToEdit": false
      }]
    }, {
      "presetId": 2,
      "id": 1,
      "name": "Torso1, USB",
      "displayName": "Lungs Torso",
      "partNumbers": [{
        "partNumber": null,
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterPresetMasterMappingId": 108,
        "default": true,
        "allowedToEdit": false
      }]
    }, {
      "presetId": 3,
      "id": 1,
      "name": "Torso1, USB",
      "displayName": "Abdomen",
      "partNumbers": [{
        "partNumber": null,
        validity: ValidityEnum.PERPETUAL,
        "probeTypeMasterPresetMasterMappingId": 109,
        "default": true,
        "allowedToEdit": false
      }]
    }]
  }]
  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['confirm']);
    authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getProbeConfigGroupPermission']);
    probeApiCallServiceSpy = jasmine.createSpyObj('ProbeApiService', ['getFeaturesList', 'getprobeTypeResponseList']);
    presetApiCallServiceSpy = jasmine.createSpyObj('PresetApiService', ['getProbePresetsList']);
    probeConfigGroupApiCallServiceSpy = jasmine.createSpyObj('ProbeConfigGroupApiCallService', ['getProbeConfigGroupList', 'deleteProbeConfigGroup', 'getProbeConfigGroupDetails', 'addOrUpdateProbeConfigGroup']);


    await TestBed.configureTestingModule({
      declarations: [
        ProbeConfigGroupListComponent,
        ProbeConfigGroupFilterComponent,
        ProbeConfigGroupDetailComponent,
        EnumMappingDisplayNamePipe,
        FeatureAndPresetInformationDisplayPipe,
        AddOrUpdateProbeConfigGroupComponent,
        FeaturesBaseResponseDisplayPipe,
        FeatureValidityOptionHideShowPipe,
        ProbeConfigGroupCheckBoxPipe,
        DisableLicenseCheckBoxpipe,
        FeaturesValidityPartNumberDisplayPipe
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA] // Use schemas if you have external custom elements
      ,
      imports: [NgbModule,
        NgMultiSelectDropDownModule.forRoot(),
        ReactiveFormsModule,
        FormsModule],
      providers: [
        AuthJwtService,
        DatePipe,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        SessionStorageService,
        CommonsService,
        CommonOperationsService,
        RoleApiCallService,
        HidePermissionNamePipe,
        KeyValueMappingServiceService,
        PrintListPipe,
        EnumMappingDisplayNamePipe,
        FeatureAndPresetInformationDisplayPipe,
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: AuthJwtService, useValue: authServiceSpy },
        { provide: ProbeApiService, useValue: probeApiCallServiceSpy },
        { provide: PresetApiService, useValue: presetApiCallServiceSpy },
        { provide: ProbeConfigGroupApiCallService, useValue: probeConfigGroupApiCallServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ProbeConfigGroupListComponent);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    probeConfigGroupServiceSpy = TestBed.inject(ProbeConfigGroupService);

    // Mock additional API calls for Language and Country lists
    probeApiCallServiceSpy.getFeaturesList?.and.returnValue(
      Promise.resolve(getFeaturesResponse)
    );
    presetApiCallServiceSpy.getProbePresetsList?.and.returnValue(
      Promise.resolve(getPresetsListResponse)
    );
    probeApiCallServiceSpy.getprobeTypeResponseList?.and.returnValue(
      Promise.resolve(getprobeTypeResponseListResponse)
    );
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {

    it('should initialize the component when the user is authenticated', async () => {

      // Arrange: Mock `isAuthenticate` and permission services to return valid data
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      permissionServiceSpy.getProbeConfigGroupPermission?.and.returnValue(true);

      // Act: Call `ngOnInit` and wait for asynchronous operations to complete
      component.ngOnInit();
      fixture.detectChanges();
      await fixture.whenStable();

      testAuthentication(authServiceSpy, component, fixture);

      // Test dropdown interaction
      testDropdownInteraction(fixture, component, '#probeConfigGroupListShowEntry');

      // Test pagination
      testPagination(fixture, component, '#pcgList-pagination', 2);
    });

    // Test: Toggles filter visibility and updates the button text
    it('should toggle filter visibility and update the button text', () => {
      // Use the generic utility to test the toggle behavior
      testToggleFilter(component);
    });


    it('should update the refresh button text based on filter visibility', async () => {
      // Arrange: Mock `isAuthenticate` and permission services to return valid data
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      permissionServiceSpy.getProbeConfigGroupPermission?.and.returnValue(true);

      // Act: Call `ngOnInit` and wait for asynchronous operations to complete
      component.ngOnInit();
      fixture.detectChanges();
      await fixture.whenStable();

      spyOn(component, 'refreshFilter')?.and.callThrough();
      const button = fixture.nativeElement.querySelector('#refresh_pcgList');
      button?.click();
      expect(component.refreshFilter).toHaveBeenCalled();
    });

    // Test: Displays an error message when an internal server error occurs
    it('should display an error message when an internal server error occurs', () => {
      // Arrange: Simulate a 500 Internal Server Error
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
      probeConfigGroupApiCallServiceSpy.getProbeConfigGroupList?.and.returnValue(throwError(() => mockError));
      spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

      // Act: Trigger the data loading method
      component.loadAll(null);

      // Assert: Verify the error handling logic is triggered
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
    });

    // Test: Handles an error response when loading data
    it('should handle error response in the LoadAll method', () => {
      // Arrange: Simulate a 500 status response with no body
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      probeConfigGroupApiCallServiceSpy.getProbeConfigGroupList?.and.returnValue(of(new HttpResponse<ProbeConfigGroupPageResponse>({
        body: null,
        status: 500,
        statusText: 'OK',
      })));

      // Act: Call `loadAll` to attempt data loading
      component.loadAll(null);

      // Assert: Verify no data is loaded and error state is managed
      expect(component.probeConfigGroupListResponse.length).toEqual(0);
      expect(component.totalRecordDisplay).toEqual(0);
      expect(component.totalRecord).toEqual(0);
      expect(component.loading).toBeFalse();
    });
  });

  it('should initialize form controls on ngOnInit', async () => {
    // **Arrange: Setup the required dependencies and initial state**
    // Simulate user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);

    // Simulate user permissions for accessing Probe Config Group functionality
    permissionServiceSpy.getProbeConfigGroupPermission?.and.returnValue(true);

    // Mock API call to fetch the Probe Config Group list and return a successful response
    probeConfigGroupApiCallServiceSpy.getProbeConfigGroupList?.and.returnValue(of(new HttpResponse<ProbeConfigGroupPageResponse>({
      body: probeConfigGroupListResponse, // Mocked response data
      status: 200, // Simulate a successful API response
    })));

    // Spy on the relevant service and component methods to track their invocation
    spyOn(probeConfigGroupServiceSpy, 'callProbeConfigGroupListFilterRequestParameterSubject')?.and.callThrough();
    spyOn(component, 'loadAll')?.and.callThrough();

    // Initialize the component (triggers ngOnInit and sets up component state)
    component.ngOnInit();
    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution

    // Access the child filter component instance for validation
    const filterComponent = fixture.debugElement.query(By.directive(ProbeConfigGroupFilterComponent)).componentInstance;

    // **Assert: Validate the state of the filter component**
    // Ensure the child filter component is properly rendered and initialized
    expect(filterComponent).toBeTruthy();

    // Verify that the form controls in the filter component are initialized with default values
    expect(filterComponent.probeConfigGroupForm?.get('partNumber').value).toBe(''); // Default value for `partNumber`
    expect(filterComponent.probeConfigGroupForm?.get('probeType').value).toEqual([]); // Default value for `probeType`
    expect(filterComponent.probeConfigGroupForm?.get('featureType').value).toEqual([]); // Default value for `featureType`
    expect(filterComponent.probeConfigGroupForm?.get('presetType').value).toEqual([]); // Default value for `presetType`

    // **Act: Trigger Angular lifecycle methods and finalize change detection**
    fixture.detectChanges(); // Run initial change detection
    await fixture.whenStable(); // Wait for asynchronous operations to complete

    // **Additional Assertions: Validate component's internal state**
    // Verify permissions and pagination-related properties
    expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE); // Items per page should match the configured constant
    expect(component.drpselectsize).toBe(ITEMS_PER_PAGE); // Dropdown size should match the configured constant
    expect(component.previousPage).toBe(1); // Default page should be the first page
    expect(component.dataSizes).toEqual(['10', '25', '50', '100']); // Data sizes for pagination dropdown

    // Verify that the service method was called to initialize filter parameters
    expect(probeConfigGroupServiceSpy.callProbeConfigGroupListFilterRequestParameterSubject).toHaveBeenCalled();

    // Assert that the component state reflects the API response data
    expect(component.totalItems).toEqual(probeConfigGroupListResponse.totalElements); // Total items should match the API response
    expect(component.probeConfigGroupListResponse).toEqual(probeConfigGroupListResponse.content); // List should populate with the response content
    expect(component.totalRecord).toEqual(probeConfigGroupListResponse.totalElements); // Total records should match the response
    expect(component.totalRecordDisplay).toEqual(probeConfigGroupListResponse.numberOfElements); // Displayed records should match the response
  });


  it("should handle 'Select All' checkbox functionality", async () => {
    // **Arrange: Setup the required dependencies and initial state**
    // Mock user authentication and permissions
    authServiceSpy.isAuthenticate?.and.returnValue(true); // Simulate an authenticated user
    permissionServiceSpy.getProbeConfigGroupPermission?.and.returnValue(true); // Simulate permission grant for Probe Config Group

    // Mock the confirmation dialog to return a positive response
    confirmDialogServiceMock.confirm.and.returnValue(Promise.resolve(true));

    // Mock API response for fetching the Probe Config Group list
    probeConfigGroupApiCallServiceSpy.getProbeConfigGroupList?.and.returnValue(of(new HttpResponse<ProbeConfigGroupPageResponse>({
      body: probeConfigGroupListResponse, // Mocked response data
      status: 200, // Successful API call
      statusText: 'OK',
    })));

    // Mock API response for deleting a Probe Config Group
    probeConfigGroupApiCallServiceSpy.deleteProbeConfigGroup?.and.returnValue(of(new HttpResponse<SuccessMessageResponse>({
      body: { message: 'Probe Config Group deleted successfully.' }, // Mocked delete success message
      status: 200, // Successful deletion
      statusText: 'OK',
    })));

    // Spy on critical component methods to validate their invocation
    spyOn(component, 'loadAll')?.and.callThrough(); // Track calls to the `loadAll` method
    spyOn(component, 'selectAllItem')?.and.callThrough(); // Track calls to the `selectAllItem` method

    // Set up the initial state by assigning the checkbox element ID
    component.selectAllCheckboxId = 'selectAllProbeConfigGroup';

    // Initialize the component (triggers ngOnInit)
    component.ngOnInit();
    fixture.detectChanges(); // Trigger initial change detection

    // Wait for asynchronous operations to stabilize
    await fixture.whenStable();

    // **Act: Simulate user interaction**
    // Simulate clicking the "Select All" checkbox
    const selectAllCheckbox = fixture.nativeElement.querySelector('#selectAllProbeConfigGroup');
    selectAllCheckbox?.click(); // User selects all items

    // **Assert: Verify the behavior for 'Select All'**
    // Ensure the `selectAllItem` method is called with `true` when the checkbox is clicked
    expect(component.selectAllItem).toHaveBeenCalledWith(true);

    // Simulate unchecking the "Select All" checkbox
    selectAllCheckbox?.click(); // User deselects all items
    fixture.detectChanges();

    // Wait for asynchronous operations to stabilize
    await fixture.whenStable();

    // **Act: Simulate selection of an individual checkbox**
    // Find a specific checkbox element and simulate its selection
    const individualCheckbox = fixture.debugElement.query(By.css('#probeConfigGroup5probeConfigGroup')).nativeElement as HTMLInputElement;
    individualCheckbox.click(); // User selects one item
    fixture.detectChanges();

    // Wait for asynchronous operations to stabilize
    await fixture.whenStable();

    // **Act: Simulate clicking the "Delete" button**
    // Find the delete button and simulate a click
    const deleteButton = fixture.debugElement.query(By.css('#pcgDeleteListing')).nativeElement as HTMLInputElement;
    deleteButton.click(); // User confirms deletion
    fixture.detectChanges();

    // Wait for asynchronous operations to stabilize
    await fixture.whenStable();

    // **Assert: Verify successful deletion message**
    // Ensure the success message is displayed to the user
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Probe Config Group deleted successfully.');
  });

  it("select one checkbox", async () => {
    // **Arrange: Set up necessary dependencies and initial state**

    // Mock user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);
    permissionServiceSpy.getProbeConfigGroupPermission?.and.returnValue(true);
    confirmDialogServiceMock.confirm.and.returnValue(Promise.resolve(true));

    // Mock API calls for Probe Config Group List and Detail, returning successful responses
    probeConfigGroupApiCallServiceSpy.getProbeConfigGroupList?.and.returnValue(of(new HttpResponse<ProbeConfigGroupPageResponse>({
      body: probeConfigGroupListResponse,
      status: 200,
      statusText: 'OK',
    })));
    probeConfigGroupApiCallServiceSpy.getProbeConfigGroupDetails?.and.returnValue(
      of(new HttpResponse<ProbeConfigGroupDeatilResponse>({
        body: pcgDetailResponse,
        status: 200,
        statusText: 'OK',
      }))
    );

    // Mock API call for deleting a Probe Config Group with a successful response
    probeConfigGroupApiCallServiceSpy.addOrUpdateProbeConfigGroup?.and.returnValue(
      of(new HttpResponse<SuccessMessageResponse>({
        body: { message: 'Probe Config Group successfully Created.' }, // Success message
        status: 200,
        statusText: 'OK',
      }))
    );


    // Mock API call for delete operation to return an error
    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
    probeConfigGroupApiCallServiceSpy.deleteProbeConfigGroup?.and.returnValue(throwError(() => mockError));

    // Spy on critical component methods to ensure they are called during the test
    spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();
    spyOn(component, 'loadAll')?.and.callThrough();
    spyOn(component, 'selectCheckbox')?.and.callThrough();

    // Set the initial state for the component
    component.chkPreFix = 'probeConfigGroup'; // Prefix for checkbox IDs

    // Trigger component initialization (calls ngOnInit)
    component.ngOnInit();
    // Wait for asynchronous tasks to stabilize
    await fixture.whenStable();
    fixture.detectChanges(); // Trigger change detection

    // Get the delete DebugElement
    const deleteButtonDebugEl = fixture.debugElement.query(By.css('#pcgDeleteListing'));
    // Get the native element
    const deleteButtonNativeEl = deleteButtonDebugEl.nativeElement as HTMLInputElement;
    // Simulate the click
    deleteButtonNativeEl.click();

    expect(toastrServiceMock.info).toHaveBeenCalledWith('Please Select a Probe Feature Group');

    // Arrange: Select the checkbox element by ID
    // Get the checkbox DebugElement
    const checkboxDebugEl = fixture.debugElement.query(By.css('#probeConfigGroup5probeConfigGroup'));
    // Get the native element
    const checkboxNativeEl = checkboxDebugEl.nativeElement as HTMLInputElement;

    // Simulate the click
    checkboxNativeEl.click();

    // Assert if the checkbox is checked
    expect(checkboxNativeEl.checked).toBeTrue();
    // **Assert: Verify the initial checkbox selection**
    expect(component.selectCheckbox).toHaveBeenCalled(); // Validate method call
    expect(component.selectedProbeConfigGroupIdList.length).toEqual(1); // Confirm one item is selected

    deleteButtonNativeEl.click();

    fixture.detectChanges();
    await fixture.whenStable();

    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
    // Act: Simulate another click to deselect the checkbox
    checkboxNativeEl.click();

    // Assert: Verify the checkbox is deselected
    expect(component.selectedProbeConfigGroupIdList.length).toEqual(0); // No items should remain selected

    // **Verify interactions with child components**
    // Access the Probe Config Group Filter component
    const pcgfilterDebugElement = fixture.debugElement.query(By.css('app-probe-config-group-filter'));
    const pcgfilterComponent = pcgfilterDebugElement.componentInstance;

    // Assert: Ensure the filter component is rendered
    expect(pcgfilterComponent).toBeTruthy();

    // Simulate a filter search without selecting criteria
    const searchFilterButton = fixture.nativeElement.querySelector('#pcgFilterSearch');
    searchFilterButton?.click(); // Trigger search filter action
    await fixture.whenStable();
    fixture.detectChanges();

    // Assert: Confirm appropriate info message is displayed
    expect(toastrServiceMock.info).toHaveBeenCalledWith("Please Select Filter To Search");

    // Simulate navigation to the detail view
    const selectElementDetails = fixture.nativeElement.querySelector('#pcgListToDeatil');
    selectElementDetails?.click();

    // Assert: Verify display transitions between list and detail views
    expect(component.showProbeConfigGroupForListingPage).toBeFalsy();
    expect(component.showProbeConfigGroupForAddUpdatePage).toBeFalsy();
    expect(component.showProbeConfigGroupForDetailPage).toBeTruthy();

    fixture.detectChanges();
    await fixture.whenStable();

    // Access the Probe Config Group Detail component
    const probeConfigDetailDebugElement = fixture.debugElement.query(By.css('app-probe-config-group-detail'));
    const probeConfigDetailDetailComponent = probeConfigDetailDebugElement.componentInstance;

    // Assert: Verify detail component state
    expect(probeConfigDetailDetailComponent.probeConfigGroupDetailResponse).toEqual(pcgDetailResponse);
    expect(probeConfigDetailDetailComponent).toBeTruthy();

    // Simulate refresh action on the detail component
    spyOn(probeConfigDetailDetailComponent, 'refreshPCGDetailPage').and.callThrough();
    const otsDetailRefreshButton = fixture.nativeElement.querySelector('#pcgDetailRefresh');
    otsDetailRefreshButton?.click();

    // Assert: Ensure the refresh method is called
    expect(probeConfigDetailDetailComponent.refreshPCGDetailPage).toHaveBeenCalled();

    fixture.detectChanges();
    await fixture.whenStable();

    // Simulate navigation back to the list view
    component.isFilterHidden = true;
    const pcgDetailBackButton = fixture.nativeElement.querySelector('#pcgDetailBack');
    pcgDetailBackButton?.click();

    // Assert: Verify list view is displayed and filters are reset
    expect(component.listPageRefreshForbackToDetailPage).toBeTruthy();
    expect(component.showProbeConfigGroupForListingPage).toBeTruthy();
    expect(component.isFilterComponentInitWithApicall).toBeFalsy();
    expect(component.showProbeConfigGroupForDetailPage).toBeFalsy();
    expect(component.showProbeConfigGroupForAddUpdatePage).toBeFalsy();
    expect(component.selectedProbeConfigGroupIdList.length).toEqual(0);

    selectElementDetails?.click();
    spyOn(probeConfigDetailDetailComponent, 'deleteProbeConfigGroup').and.callThrough();
    const otsDetaildeleteButton = fixture.nativeElement.querySelector('#pcgDeleteDetail');

    otsDetaildeleteButton?.click();

    fixture.detectChanges();
    await fixture.whenStable();

    expect(probeConfigDetailDetailComponent.deleteProbeConfigGroup).toHaveBeenCalled();

    // Assert: Verify that error handling methods were called
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
    pcgDetailBackButton?.click();
    pcgfilterComponent.ngOnInit();

    fixture.detectChanges();
    await fixture.whenStable();

    const pcgAddButton = fixture.nativeElement.querySelector('#AddProbeConfigGroup');
    pcgAddButton?.click();

    expect(component.showProbeConfigGroupForListingPage).toBeFalsy();
    expect(component.showProbeConfigGroupForDetailPage).toBeFalsy();
    expect(component.showProbeConfigGroupForAddUpdatePage).toBeTruthy();

    fixture.detectChanges();
    await fixture.whenStable();

    // Access the Add Probe Config Group component
    const addProbeConfigGroupDebugElement = fixture.debugElement.query(By.css('app-add-or-update-probe-config-group'));
    const addProbeConfigGroupComponent = addProbeConfigGroupDebugElement.componentInstance;

    fixture.detectChanges();
    await fixture.whenStable();

    addProbeConfigGroupComponent.probeTypeGroupResponse = probeTypeResponse;
    const selectElement = fixture.nativeElement.querySelector('#addProbeConfigGroupDrp');
    expect(selectElement?.options.length).toBe(5);

    selectElement.value = selectElement?.options[3].value;
    selectElement.dispatchEvent(new Event('change'));

    fixture.detectChanges();
    await fixture.whenStable();

    selectElement.value = selectElement?.options[1].value;
    selectElement.dispatchEvent(new Event('change'));

    fixture.detectChanges();
    await fixture.whenStable();

    const onchangePresetChangeButton = fixture.nativeElement.querySelector('#Perpetual_Heart_1');
    const onchangefeatureChangeButton = fixture.nativeElement.querySelector('#Perpetual_TDI_4');
    const onchangefeature12ChangeButton = fixture.nativeElement.querySelector('#month_TDI_4');

    onchangePresetChangeButton?.click();
    fixture.detectChanges();
    await fixture.whenStable();
    onchangefeatureChangeButton?.click();
    onchangePresetChangeButton?.click();

    fixture.detectChanges();
    await fixture.whenStable();
    onchangefeature12ChangeButton?.click();

    fixture.detectChanges();
    await fixture.whenStable();
    onchangefeature12ChangeButton?.click();
    onchangePresetChangeButton?.click();

    addProbeConfigGroupComponent.probeConfigGroupForm.get('partNumber').setValue("P1A-12345");

    addProbeConfigGroupComponent.saveData();

    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.showProbeConfigGroupForListingPage).toBeTruthy();
    expect(component.showProbeConfigGroupForDetailPage).toBeFalsy();
    expect(component.showProbeConfigGroupForAddUpdatePage).toBeFalsy();

  });


  it("should delete a selected item and verify its removal", async () => {
    // **Arrange: Set up necessary dependencies and initial state**

    // Mock user authentication to simulate a logged-in user
    authServiceSpy.isAuthenticate?.and.returnValue(true);

    // Simulate user permissions for accessing Probe Config Group functionality
    permissionServiceSpy.getProbeConfigGroupPermission?.and.returnValue(true);

    // Simulate confirmation dialog response as 'true' (user confirms the delete action)
    confirmDialogServiceMock.confirm.and.returnValue(Promise.resolve(true));

    // Mock API call for fetching Probe Config Group List with a successful response
    probeConfigGroupApiCallServiceSpy.getProbeConfigGroupList?.and.returnValue(
      of(new HttpResponse<ProbeConfigGroupPageResponse>({
        body: probeConfigGroupListResponse, // Predefined mock data for the list
        status: 200,
        statusText: 'OK',
      }))
    );

    // Mock API call for fetching Probe Config Group Details with a successful response
    probeConfigGroupApiCallServiceSpy.getProbeConfigGroupDetails?.and.returnValue(
      of(new HttpResponse<ProbeConfigGroupDeatilResponse>({
        body: pcgDetailResponse, // Predefined mock data for the detail view
        status: 200,
        statusText: 'OK',
      }))
    );

    // Mock API call for deleting a Probe Config Group with a successful response
    probeConfigGroupApiCallServiceSpy.deleteProbeConfigGroup?.and.returnValue(
      of(new HttpResponse<SuccessMessageResponse>({
        body: { message: 'Probe Config Group deleted successfully.' }, // Success message
        status: 200,
        statusText: 'OK',
      }))
    );

    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });

    // Mock the API call to return an error response
    probeConfigGroupApiCallServiceSpy.addOrUpdateProbeConfigGroup?.and.returnValue(throwError(() => mockError));

    // Spy on the `loadAll` method to ensure it is called during the test
    spyOn(component, 'loadAll')?.and.callThrough();

    // Spy on the `CustomerrorMessage` method of `exceptionHandlingService` to verify error handling
    spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

    // Trigger component initialization (calls ngOnInit)
    component.ngOnInit();
    // Wait for asynchronous tasks to stabilize
    await fixture.whenStable();
    fixture.detectChanges(); // Trigger initial change detection

    // **Act: Simulate user interactions**

    // Simulate navigation to the detail view of a Probe Config Group
    const selectElementDetails = fixture.nativeElement.querySelector('#pcgListToDeatil');
    selectElementDetails?.click();

    // **Assert: Verify navigation to detail view**
    expect(component.showProbeConfigGroupForListingPage).toBeFalsy(); // List view should not be displayed
    expect(component.showProbeConfigGroupForAddUpdatePage).toBeFalsy(); // Add/Update page should not be displayed
    expect(component.showProbeConfigGroupForDetailPage).toBeTruthy(); // Detail view should be displayed

    fixture.detectChanges();
    await fixture.whenStable();

    // Access the detail component instance for assertions
    const probeConfigDetailDebugElement = fixture.debugElement.query(By.css('app-probe-config-group-detail'));
    const probeConfigDetailComponent = probeConfigDetailDebugElement?.componentInstance;

    // Assert: Verify the detail component's state
    expect(probeConfigDetailComponent?.probeConfigGroupDetailResponse).toEqual(pcgDetailResponse);
    expect(probeConfigDetailComponent).toBeTruthy(); // Detail component should be rendered

    // Spy on the `deleteRoleOperation` method to confirm it is called
    spyOn(probeConfigDetailComponent, 'deleteProbeConfigGroup').and.callThrough();

    // Simulate clicking the delete button in the detail view
    const otsDetailDeleteButton = fixture.nativeElement.querySelector('#pcgDeleteDetail');
    otsDetailDeleteButton?.click();

    fixture.detectChanges();
    await fixture.whenStable();

    // Assert: Verify the delete operation is triggered
    expect(probeConfigDetailComponent.deleteProbeConfigGroup).toHaveBeenCalled();

    // Assert: Confirm success message is displayed after deletion
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Probe Config Group deleted successfully.');

    // **Assert: Verify navigation back to the listing page**
    expect(component.listPageRefreshForbackToDetailPage).toBeTruthy(); // Ensure list page is refreshed
    expect(component.showProbeConfigGroupForListingPage).toBeTruthy(); // List view should be displayed
    expect(component.isFilterComponentInitWithApicall).toBeFalsy(); // Filter component should not be initialized
    expect(component.showProbeConfigGroupForDetailPage).toBeFalsy(); // Detail view should not be displayed
    expect(component.showProbeConfigGroupForAddUpdatePage).toBeFalsy(); // Add/Update page should not be displayed
    expect(component.selectedProbeConfigGroupIdList.length).toEqual(0); // No items should remain selected

    fixture.detectChanges();
    await fixture.whenStable();

    const pcgAddButton = fixture.nativeElement.querySelector('#AddProbeConfigGroup');
    pcgAddButton?.click();

    expect(component.showProbeConfigGroupForListingPage).toBeFalsy();
    expect(component.showProbeConfigGroupForDetailPage).toBeFalsy();
    expect(component.showProbeConfigGroupForAddUpdatePage).toBeTruthy();

    fixture.detectChanges();
    await fixture.whenStable();

    // Access the Add Probe Config Group component
    const addProbeConfigGroupDebugElement = fixture.debugElement.query(By.css('app-add-or-update-probe-config-group'));
    const addProbeConfigGroupComponent = addProbeConfigGroupDebugElement.componentInstance;

    fixture.detectChanges();
    await fixture.whenStable();

    addProbeConfigGroupComponent.probeTypeGroupResponse = probeTypeResponse;
    const selectElement = fixture.nativeElement.querySelector('#addProbeConfigGroupDrp');
    expect(selectElement?.options.length).toBe(5);

    fixture.detectChanges();
    await fixture.whenStable();

    selectElement.value = selectElement?.options[1].value;
    selectElement.dispatchEvent(new Event('change'));

    fixture.detectChanges();
    await fixture.whenStable();


    addProbeConfigGroupComponent.probeConfigGroupForm.get('partNumber').setValue("P1A-12345");

    addProbeConfigGroupComponent.saveData();

    fixture.detectChanges();
    await fixture.whenStable();

    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();

    // Ensure the toastr service displays an error message for `INTERNAL_SERVER_ERROR`
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
  });

});
