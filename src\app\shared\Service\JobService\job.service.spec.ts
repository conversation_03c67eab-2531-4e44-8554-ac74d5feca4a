import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { FirmwareResponse } from '../../../model/job/firmwareResponse.model';
import { JobHistoryResponse } from '../../../model/job/jobHistoryResponse.model';
import { StatusHistory } from '../../../model/job/statusHistory.model';
import { IJobchange } from '../../../model/jobchange.model';
import { API_BASE_URL } from '../../config';
import { JobService } from './job.service';

describe('JobService', () => {
  let service: JobService;
  let httpMock: HttpTestingController;
  const baseUrl = 'http://example.com/api';

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        JobService,
        { provide: API_BASE_URL, useValue: baseUrl },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting()
      ]
    });

    service = TestBed.inject(JobService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify(); // Verify that no unmatched requests are outstanding
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service.resourceUrl).toBe(baseUrl + 'api/jobs/');
  });

  describe('getJobType', () => {
    it('should return job types', () => {
      const mockTypes = ['type1', 'type2', 'type3'];

      service.getJobType().subscribe(response => {
        expect(response.body).toEqual(mockTypes);
        expect(response.status).toBe(200);
      });

      const req = httpMock.expectOne(`${service.resourceUrl}types`);
      expect(req.request.method).toBe('GET');
      req.flush(mockTypes, { status: 200, statusText: 'OK' });
    });
  });

  describe('getJobStatus', () => {
    it('should return job statuses', () => {
      const mockStatuses = ['status1', 'status2', 'status3'];

      service.getJobStatus().subscribe(response => {
        expect(response.body).toEqual(mockStatuses);
        expect(response.status).toBe(200);
      });

      const req = httpMock.expectOne(`${service.resourceUrl}status`);
      expect(req.request.method).toBe('GET');
      req.flush(mockStatuses, { status: 200, statusText: 'OK' });
    });
  });

  describe('getJobList', () => {
    it('should return job list based on filter criteria', () => {
      const mockFilterData = {
        packageVersions: ['pkg1', 'pkg2'],
        probeVersions: ['probe1'],
        handleVersions: ['handle1', 'handle2'],
        connectionState: 'ACTIVE',
        jobDeviceId: '12345',
        drpJobTypes: 'UPDATE',
        jobStatus: 'COMPLETED'
      };

      const mockParams = { page: 0, size: 10 };

      const mockResponse: IJobchange = null

      service.getJobList(mockFilterData, mockParams).subscribe(response => {
        expect(response.body).toEqual(mockResponse);
        expect(response.status).toBe(200);
      });

      const req = httpMock.expectOne(request =>
        request.url === `${service.resourceUrl}search` &&
        request.method === 'POST'
      );

      expect(req.request.body).toEqual({
        packageVersions: mockFilterData.packageVersions,
        probeVersions: mockFilterData.probeVersions,
        handleVersions: mockFilterData.handleVersions,
        status: mockFilterData.connectionState,
        deviceId: mockFilterData.jobDeviceId,
        jobType: mockFilterData.drpJobTypes,
        jobScheduleStatus: mockFilterData.jobStatus
      });

      req.flush(mockResponse, { status: 200, statusText: 'OK' });
    });
  });

  describe('getJobHistory', () => {
    it('should return job history for a given job schedule status ID', () => {
      const jobScheduleStatusId = 123;

      const mockResponse: JobHistoryResponse = new JobHistoryResponse(null, null, null, [new StatusHistory(null, null)], new FirmwareResponse(null, null), null)

      service.getJobHistory(jobScheduleStatusId).subscribe(response => {
        expect(response.body).toEqual(mockResponse);
        expect(response.status).toBe(200);
      });

      const req = httpMock.expectOne(`${service.resourceUrl}jobScheduleStatus/${jobScheduleStatusId}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse, { status: 200, statusText: 'OK' });
    });
  });

  // Additional error handling tests
  describe('error handling', () => {
    it('should handle error when getting job types', () => {
      service.getJobType().subscribe({
        next: () => fail('should have failed with a 404 error'),
        error: (error) => {
          expect(error.status).toBe(404);
          expect(error.statusText).toBe('Not Found');
        }
      });

      const req = httpMock.expectOne(`${service.resourceUrl}types`);
      req.flush('Not found', { status: 404, statusText: 'Not Found' });
    });

    it('should handle error when getting job list', () => {
      const mockFilterData = {
        packageVersions: [],
        probeVersions: [],
        handleVersions: [],
        connectionState: '',
        jobDeviceId: '',
        drpJobTypes: '',
        jobStatus: ''
      };

      service.getJobList(mockFilterData).subscribe({
        next: () => fail('should have failed with a 500 error'),
        error: (error) => {
          expect(error.status).toBe(500);
          expect(error.statusText).toBe('Server Error');
        }
      });

      const req = httpMock.expectOne(`${service.resourceUrl}search`);
      req.flush('Server error', { status: 500, statusText: 'Server Error' });
    });
  });
});