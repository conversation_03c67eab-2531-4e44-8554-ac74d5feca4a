import { ListingPageReloadSubjectParameter } from "../common/listingPageReloadSubjectParameter.model";
import { AuditSearchRequsetBody } from "./auditSearchRequsetBody";

export class AuditFilterAction {
    listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter;
    auditSearchRequsetBody: AuditSearchRequsetBody;
    archivedAuditDataSearch: boolean;

    constructor($listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, $auditSearchRequsetBody: AuditSearchRequsetBody, $archivedAuditDataSearch: boolean) {
        this.listingPageReloadSubjectParameter = $listingPageReloadSubjectParameter;
        this.auditSearchRequsetBody = $auditSearchRequsetBody;
        this.archivedAuditDataSearch = $archivedAuditDataSearch;
    }

}