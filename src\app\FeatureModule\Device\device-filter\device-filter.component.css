/* Device Filter Component Styles */
@import '../../../../../src/assets/css/custom_style.css';

/* Inherit styles from parent device component */
/* Additional filter-specific styles can be added here if needed */

#deviceFilter .form-group {
    margin-bottom: 1rem;
}

#deviceFilter .form-control-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

#deviceFilter .alert-color {
    color: #dc3545;
}

#deviceFilter .font-12 {
    font-size: 12px;
}

#deviceFilter .h5-tag {
    font-size: 1.25rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

#deviceFilter .card {
    border: 1px solid rgba(0, 0, 0, .125);
    border-radius: 0.25rem;
}

#deviceFilter .devicePageDeviceType,
#deviceFilter .multiselectdropdown {
    width: 100%;
}