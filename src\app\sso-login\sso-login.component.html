<!-- name : login screen component -->
<!-- description : use for user login in to system -->
<!-- loading - start -->
<div class="ringLoading" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!-- loading - end -->
<!-- login screen - start -->
<div id="loginscreen" *ngIf="isLoginPopupDisplay">
    <div class="wrapper fadeInDown">
        <div id="formContent">
            <!-- Tabs Titles -->

            <!-- Icon -->
            <div class="fadeIn first">
                <span class="logo-echonous"></span>
            </div>
            <!-- signIn button -->
            <div>
                <p>Log in to continue with:</p>
                <div class="logoButton" (click)="loginWithMicrosoft()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21">
                        <path fill="#f25022" d="M1 1h9v9H1z" />
                        <path fill="#00a4ef" d="M1 11h9v9H1z" />
                        <path fill="#7fba00" d="M11 1h9v9h-9z" />
                        <path fill="#ffb900" d="M11 11h9v9h-9z" />
                    </svg>
                    <button type="button" class="btn"> Microsoft</button>
                </div>

                <div class="textalign cust-label mt-3">
                    <span>{{rdmVersion}}</span>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- login screen - end -->