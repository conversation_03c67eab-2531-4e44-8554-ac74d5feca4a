import { Component, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-delete-video-json-confirmation-dialog',
  templateUrl: './delete-video-json-confirmation-dialog.component.html',
  styleUrls: ['./delete-video-json-confirmation-dialog.component.css']
})
export class DeleteVideoJsonConfirmationDialogComponent {

  @Input('title') title;
  @Input('message') message;
  @Input('btnOkText') btnOkText;
  @Input('btnCancelText') btnCancelText;

  constructor(private activeModal: NgbActiveModal) { }

  public accept(): void {
    this.activeModal.close(true);
  }

  public close(): void {
    this.activeModal.close(false);
  }

  public dismiss(): void {
    this.activeModal.dismiss();
  }

}
