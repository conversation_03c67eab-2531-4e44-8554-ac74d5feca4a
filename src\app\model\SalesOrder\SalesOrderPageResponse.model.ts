import { Pageable } from "../common/pageable.model";
import { PageResponse } from "../common/PageResponse.model";
import { Sort } from "../sort.model";
import { SalesOrderResponse } from "./SalesOrderResponse.model";

export class SalesOrderPageResponse extends PageResponse {
    content: Array<SalesOrderResponse>;

    constructor( //NOSONAR
        pageable: Pageable,
        totalPages: number,
        last: boolean,
        totalElements: number,
        numberOfElements: number,
        first: boolean,
        sort: Sort,
        size: number,
        number: number,
        empty: boolean,
        content: Array<SalesOrderResponse>
    ) {
        super(pageable, totalPages, last, totalElements, numberOfElements, first, sort, size, number, empty);
        this.content = content;
    }
}
