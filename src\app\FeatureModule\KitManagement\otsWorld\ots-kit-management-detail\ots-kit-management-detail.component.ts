import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { DateTimeDisplayFormat, OTS_KIT_MANAGEMANT_DELETE } from 'src/app/app.constants';
import { OTSKitManagementDetailResponse } from 'src/app/model/KitManagement/otsWorld/OTSKitManagementDetailResponse.model';
import { EnableFeaturesResponse } from 'src/app/model/SalesOrder/EnableFeaturesResponse.model';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { ValidityEnum } from 'src/app/shared/enum/ValidityEnum.enum';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { OtsKitManagemantApiCallService } from 'src/app/shared/Service/KitManagemant/ots-kit-managemant-api-call.service';

@Component({
  selector: 'app-ots-kit-management-detail',
  templateUrl: './ots-kit-management-detail.component.html',
  styleUrl: './ots-kit-management-detail.component.css'
})
export class OtsKitManagementDetailComponent implements OnInit {


  @Output("otskitListPage") otskitListPage = new EventEmitter();
  @Input("kitId") kitId: number;

  loading: boolean = false;

  otsKitManagementDetailResponse: OTSKitManagementDetailResponse;

  //Module Id used For Move In Detail page
  moduleDetailId = null;

  back() {
    this.otskitListPage.emit();
  }
  feature1: Array<EnableFeaturesResponse> = [];
  feature2: Array<EnableFeaturesResponse> = [];


  validity = ValidityEnum;

  otsDetailPageDisplay: boolean = true;
  featureDetailPageDisplay: boolean = false;

  //Probe Config Group Permission
  probeConfigGroupPermission: boolean = false;

  constructor(
    private exceptionService: ExceptionHandlingService,
    private otsKitManagemantApiCallService: OtsKitManagemantApiCallService,
    private toste: ToastrService,
    private permissionService: PermissionService) { }

  ngOnInit(): void {
    this.getKitDetail();
    this.probeConfigGroupPermission = this.permissionService.getProbeConfigGroupPermission(PermissionAction.GET_CONFIG_GROUP_ACTION);
  }

  dateTimeDisplayFormat: string = DateTimeDisplayFormat;

  featureValidityEnum = ValidityEnum;

  /**
   * Get Kit Details
   * 
   * <AUTHOR>
   */
  private getKitDetail(): void {
    this.setLoadingStatus(true);
    this.otsKitManagemantApiCallService.getOTSKitDetail(this.kitId)?.subscribe({
      next: (res: HttpResponse<OTSKitManagementDetailResponse>) => {
        if (res.status == 200) {
          this.otsKitManagementDetailResponse = res.body;
          this.setLoadingStatus(false);
        } else {
          this.setLoadingStatus(false);
          this.toste.info(OTS_KIT_MANAGEMANT_DELETE);
          this.back();
        }
      }, error: (error: HttpErrorResponse) => {
        this.setLoadingStatus(false);
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  showFeature(id: number): void {
    this.moduleDetailId = id
    this.otsDetailPageDisplay = false;
    this.featureDetailPageDisplay = true;
  }

  showOtsdetail(): void {
    this.otsDetailPageDisplay = true;
    this.featureDetailPageDisplay = false;
  }

  /**
 * Loading Display
 * 
 * <AUTHOR>
 * 
 * @param status 
 */
  private setLoadingStatus(status: boolean): void {
    this.loading = status;
  }

  /**
  * Refresh Probe Detail Page Data
  * 
  * <AUTHOR> 
  */
  public refreshOtskitDetailPage(): void {
    this.getKitDetail();
  }
}
