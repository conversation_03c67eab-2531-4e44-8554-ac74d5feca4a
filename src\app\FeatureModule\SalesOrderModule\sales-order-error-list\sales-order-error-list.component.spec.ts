import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { commonsProviders, testAuthentication, testDropdownInteraction, testPagination, testToggleFilter } from 'src/app/Tesing-Helper/test-utils';
import { INTERNAL_SERVER_ERROR, ITEMS_PER_PAGE } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { SalesOrderFailedPageResponse } from 'src/app/model/SalesOrder/SalesOrderFailedPageResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { SalesOrderService } from 'src/app/shared/Service/SalesOrderService/sales-order.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { SalesOrderErrorFilterComponent } from '../sales-order-error-filter/sales-order-error-filter.component';
import { SalesOrderErrorListComponent } from './sales-order-error-list.component';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';

describe('SalesOrderErrorListComponent', () => {
  let component: SalesOrderErrorListComponent;
  let fixture: ComponentFixture<SalesOrderErrorListComponent>;
  let authServiceSpy: jasmine.SpyObj<AuthJwtService>;
  let salesOrderServiceSpy: SalesOrderService;
  let confirmDialogServiceMock: jasmine.SpyObj<ConfirmDialogService>
  let salesOrderApiCallServicespy: jasmine.SpyObj<SalesOrderApiCallService>;
  let exceptionHandlingService: ExceptionHandlingService;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;

  let salesOrderFailList: SalesOrderFailedPageResponse = {
    "content": [{
      "id": 49255,
      "salesOrderNumber": "00001334",
      "message": "Sales order is not allowed to update as one of the product status is in progress or configured.",
      "lastSyncDate": 1732520700056
    }, {
      "id": 49254,
      "salesOrderNumber": "00001326",
      "message": "License P007935-001 is not associated with Probe / Bridge Kit / OTS Kit.",
      "lastSyncDate": 1729728720000
    }, {
      "id": 49253,
      "salesOrderNumber": "00001325",
      "message": "License P008500-001 is not associated with Probe / Bridge Kit / OTS Kit.",
      "lastSyncDate": 1729728540000
    }, {
      "id": 49251,
      "salesOrderNumber": "00001323",
      "message": "License P008500-001 is not associated with Probe / Bridge Kit / OTS Kit.",
      "lastSyncDate": 1729668960000
    }, {
      "id": 49246,
      "salesOrderNumber": "00001308",
      "message": "Quantity mismatch for the license P007786-002 and product of hierarchy no - 1",
      "lastSyncDate": 1729586880000
    }, {
      "id": 49245,
      "salesOrderNumber": "00001307",
      "message": "License P007787-001 is not associated with Probe / Bridge Kit / OTS Kit.",
      "lastSyncDate": 1729586520000
    }, {
      "id": 49242,
      "salesOrderNumber": "00001305",
      "message": "License P007787-002 is not associated with Probe / Bridge Kit / OTS Kit.",
      "lastSyncDate": 1729585980000
    }, {
      "id": 49243,
      "salesOrderNumber": "00001304",
      "message": "License P007935-001 is not associated with Probe / Bridge Kit / OTS Kit.",
      "lastSyncDate": 1729585980000
    }, {
      "id": 49244,
      "salesOrderNumber": "00001306",
      "message": "License P008432-002 is not associated with Probe / Bridge Kit / OTS Kit.",
      "lastSyncDate": 1729585980000
    }, {
      "id": 49241,
      "salesOrderNumber": "00001303",
      "message": "License P007935-001 is not associated with Probe / Bridge Kit / OTS Kit.",
      "lastSyncDate": 1729585440000
    }],
    "pageable": {
      "pageNumber": 0,
      "pageSize": 10,
      "sort": {
        "empty": true,
        "sorted": false,
        "unsorted": true
      },
      "offset": 0,
      "paged": true,
      "unpaged": false
    },
    "last": false,
    "totalPages": 13,
    "totalElements": 126,
    "size": 10,
    "number": 0,
    "sort": {
      "empty": true,
      "sorted": false,
      "unsorted": true
    },
    "first": true,
    "numberOfElements": 10,
    "empty": false
  }

  beforeEach(async () => {
    confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    salesOrderApiCallServicespy = jasmine.createSpyObj('SalesOrderApiCallService', ['getSalesOrderFailedList', 'getSalesOrderSchedulerSyncTime']);
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');

    await TestBed.configureTestingModule({
      declarations: [
        SalesOrderErrorListComponent,
        SalesOrderErrorFilterComponent
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      imports: [FormsModule,
        NgbPaginationModule,
        ReactiveFormsModule],
      providers: [
        AuthJwtService,
        SessionStorageService,
        CommonOperationsService,
        RoleApiCallService,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        HidePermissionNamePipe,
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServicespy },
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: AuthJwtService, useValue: authServiceSpy },
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(SalesOrderErrorListComponent);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    salesOrderServiceSpy = TestBed.inject(SalesOrderService);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {

    it('should initialize the component when the user is authenticated', async () => {

      // Arrange: Set up spies and mocks
      authServiceSpy.isAuthenticate?.and.returnValue(true);

      // Act: Initialize component and simulate actions
      component.ngOnInit();
      fixture.detectChanges();

      spyOn(component, 'refreshFilter')?.and.callThrough();
      const button = fixture.nativeElement.querySelector('#refresh_salesOrderErrorList');
      expect(button).toBeTruthy();
      button?.click();
      expect(component.refreshFilter).toHaveBeenCalled();

      testAuthentication(authServiceSpy, component, fixture);

      // Test dropdown interaction
      testDropdownInteraction(fixture, component, '#salesOrderErrorListShowEntry');

      // Test pagination
      testPagination(fixture, component, '#salesOrderErrotList-pagination', 2);
    });

    it('should toggle filter visibility and update the button text', () => {
      // Use the generic utility to test the toggle behavior
      testToggleFilter(component);
    });

    it('should update the refresh button text based on filter visibility', () => {
      authServiceSpy.isAuthenticate?.and.returnValue(true);

      // Act: Initialize component and simulate actions
      component.ngOnInit();
      fixture.detectChanges();

      spyOn(component, 'refreshFilter')?.and.callThrough();
      const button = fixture.nativeElement.querySelector('#refresh_salesOrderErrorList');
      expect(button).toBeTruthy();
      button?.click();
      expect(component.refreshFilter).toHaveBeenCalled();
    });

    // Test: Displays an error message when an internal server error occurs
    it('should display an error message when an internal server error occurs', () => {
      // Arrange: Simulate a 500 Internal Server Error
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
      salesOrderApiCallServicespy.getSalesOrderFailedList?.and.returnValue(throwError(() => mockError));
      spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

      // Act: Trigger the data loading method
      component.loadAll(null);

      // Assert: Verify the error handling logic is triggered
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
    });

    // Test: Handles an error response when loading data
    it('should handle error response in the LoadAll method', () => {
      // Arrange: Simulate a 500 status response with no body
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      salesOrderApiCallServicespy.getSalesOrderFailedList?.and.returnValue(of(new HttpResponse<SalesOrderFailedPageResponse>({
        body: null,
        status: 500,
        statusText: 'OK',
      })));

      // Act: Call `loadAll` to attempt data loading
      component.loadAll(null);

      // Assert: Verify no data is loaded and error state is managed
      expect(component.salesOrderFailedResponse.length).toEqual(0);
      expect(component.totalRecordDisplay).toEqual(0);
      expect(component.totalRecord).toEqual(0);
      expect(component.loading).toBeFalse();
    });
  });

  it('should initialize form controls on ngOnInit', async () => {
    // **Arrange: Setup the required dependencies and initial state**
    // Simulate user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);

    // Mock API call to fetch the sales Order Fail List and return a successful response
    salesOrderApiCallServicespy.getSalesOrderFailedList?.and.returnValue(of(new HttpResponse<SalesOrderFailedPageResponse>({
      body: salesOrderFailList,
      status: 200,
      statusText: 'OK',
    })));

    // Spy on the relevant service and component methods to track their invocation
    spyOn(salesOrderServiceSpy, 'callSalesOrderFailedListFilterRequestParameterSubject')?.and.callThrough();
    spyOn(component, 'loadAll')?.and.callThrough();

    // Initialize the component (triggers ngOnInit and sets up component state)
    component.ngOnInit();
    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution

    // Access the child filter component instance for validation
    const filterComponent = fixture.debugElement.query(By.directive(SalesOrderErrorFilterComponent)).componentInstance;

    // **Assert: Validate the state of the filter component**
    // Ensure the child filter component is properly rendered and initialized
    expect(filterComponent).toBeTruthy();

    // Verify that the form controls in the filter component are initialized with default values
    expect(filterComponent.filterSalesOrderFailedForm?.get('salesOrderNumber').value).toBe(''); // Default value for `partNumber`

    // **Act: Trigger Angular lifecycle methods and finalize change detection**
    fixture.detectChanges(); // Run initial change detection
    await fixture.whenStable(); // Wait for asynchronous operations to complete

    // **Additional Assertions: Validate component's internal state**
    // Verify permissions and pagination-related properties
    expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE); // Items per page should match the configured constant
    expect(component.drpselectsize).toBe(ITEMS_PER_PAGE); // Dropdown size should match the configured constant
    expect(component.previousPage).toBe(1); // Default page should be the first page
    expect(component.dataSizes).toEqual(['10', '25', '50', '100']); // Data sizes for pagination dropdown
    component.hideShowSyncAndErrorListingEmit(false)
    // Verify that the service method was called to initialize filter parameters
    expect(salesOrderServiceSpy.callSalesOrderFailedListFilterRequestParameterSubject).toHaveBeenCalled();
    // Assert that the component state reflects the API response data
    expect(component.totalItems).toEqual(salesOrderFailList.totalElements); // Total items should match the API response
    expect(component.salesOrderFailedResponse).toEqual(salesOrderFailList.content); // List should populate with the response content
    expect(component.totalRecord).toEqual(salesOrderFailList.totalElements); // Total records should match the response
    expect(component.totalRecordDisplay).toEqual(salesOrderFailList.numberOfElements); // Displayed records should match the response
  });

  it("should display search results on list", async () => {
    // Arrange: Set up spies and mocks
    authServiceSpy.isAuthenticate?.and.returnValue(true);

    salesOrderApiCallServicespy.getSalesOrderFailedList?.and.returnValue(of(new HttpResponse<SalesOrderFailedPageResponse>({
      body: salesOrderFailList,
      status: 200,
      statusText: 'OK',
    })));

    spyOn(salesOrderServiceSpy, 'callSalesOrderFailedListFilterRequestParameterSubject')?.and.callThrough();

    component.salesOrderFaieldSearchRequestBody = { salesOrderNumber: "0000314" };

    // Spy on the `loadAll` method
    const loadAllSpy = spyOn(component, 'loadAll').and.callThrough();

    // Act: Initialize component and simulate actions
    component.ngOnInit();
    fixture.detectChanges();

    const filterComponent = fixture.debugElement.query(By.directive(SalesOrderErrorFilterComponent)).componentInstance;
    expect(filterComponent).toBeTruthy();

    const searchBtn = fixture.nativeElement.querySelector('#salesOrderFailSearchBtn');
    searchBtn?.click();

    expect(toastrServiceMock.info).toHaveBeenCalledWith("Please Select Filter To Search");

    filterComponent.filterSalesOrderFailedForm?.get('salesOrderNumber').setValue("0000314");

    searchBtn?.click();

    fixture.detectChanges(); // Finalize change detection
    await fixture.whenStable(); // Wait for asynchronous tasks

    // Assert: Validate the last call to `loadAll`
    const lastCallArgs = loadAllSpy.calls.mostRecent()?.args;
    expect(lastCallArgs[0]).toEqual(jasmine.objectContaining({ salesOrderNumber: "0000314" }));

    // Ensure the form value matches the set value
    expect(filterComponent.filterSalesOrderFailedForm?.get('salesOrderNumber').value).toEqual("0000314");
  });
});
