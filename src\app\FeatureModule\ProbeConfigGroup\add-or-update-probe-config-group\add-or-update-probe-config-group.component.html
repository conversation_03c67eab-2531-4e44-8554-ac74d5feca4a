<!------------------------------------------->
<!--loading start-->
<!------------------------------------------->
<div class="ringLoading" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!------------------------------------------->
<!--loading end-->
<!------------------------------------------->
<!------------------------------------------->
<!--Body start-->
<!------------------------------------------->

<body class="bg-white">
    <!--container start-->
    <div class="container-fluid" id="add_update_probe_feature_id">
        <!--Row start-->
        <div class="row">
            <div class="col-md-12">
                <!--header  Start-->
                <div class="row">
                    <label class="col-md-6 h5-tag">Add Probe Config Group</label>
                    <div class="ml-auto col-md-6 text-right mb-3">
                        <!--Back button start-->
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary mr-3" (click)="back()"><i
                                    class="fa fa-reply" aria-hidden="true"></i>&nbsp;&nbsp;Back</button>
                        </div>
                        <!--Back button end-->
                    </div>
                </div>
                <!--header  end-->
                <!--Body start-->
                <div class="row">
                    <div class="col-md-12">
                        <!--card start-->
                        <div class="card">
                            <!--Card body start-->
                            <div class="card-body">
                                <div class="card shadow">
                                    <div class="card-body">
                                        <!------------------------------------------>
                                        <!--Form start-->
                                        <!------------------------------------------>
                                        <form id="addUpadteProbeConfigGroupFilterform" class="firm"
                                            [formGroup]="probeConfigGroupForm">
                                            <!------------------------------------>
                                            <!------------------------------------>
                                            <div class="row">

                                                <!--Part Number start-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label><strong class="">Part Number</strong></label>
                                                        <input class="form-control" type="text"
                                                            formControlName="partNumber" />
                                                        <div *ngIf="
                                                             (probeConfigGroupForm.get('partNumber').touched ||
                                                              probeConfigGroupForm.get('partNumber').dirty) &&
                                                              probeConfigGroupForm.get('partNumber').invalid
                                                            ">
                                                            <span
                                                                *ngIf="(probeConfigGroupForm.get('partNumber').errors && probeConfigGroupForm.get('partNumber').errors['required'])"
                                                                class="text-danger">{{partNumberRequire}}</span>
                                                            <div
                                                                *ngIf="probeConfigGroupForm.get('partNumber').errors['maxlength']">
                                                                <span class="alert-color font-12">{{
                                                                    small_textBoxMaxLengthMessage
                                                                    }}</span>
                                                            </div>
                                                            <div
                                                                *ngIf="probeConfigGroupForm.get('partNumber').errors['pattern']">
                                                                <span class="alert-color font-12">{{
                                                                    specialCharacterErrorMessage
                                                                    }}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--Part Number end-->

                                                <!--Probe Type start-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label><strong class="">Probe Type</strong></label>
                                                        <select class="form-control form-control-sm form_dropdown"
                                                            id="addProbeConfigGroupDrp" formControlName="probeType"
                                                            (change)="updateCurrentProbeConfigGroup($any($event.target)?.value)">
                                                            <option value="">Select Probe Type</option>
                                                            <option *ngFor="let probeType of probeTypeGroupResponse"
                                                                [value]="probeType.probeTypeId">{{ probeType.displayName
                                                                }}
                                                            </option>
                                                        </select>
                                                        <div
                                                            *ngIf="probeConfigGroupForm.get('probeType').invalid && (probeConfigGroupForm.get('probeType').dirty || probeConfigGroupForm.get('probeType').touched)">
                                                            <span class="alert-color font-12">Probe Type is
                                                                required.</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--Probe Type End-->
                                            </div>

                                            <!------------------------------------>
                                            <!------------------------------------>



                                            <!------------------------------------>
                                            <!------------------------------------>
                                            <div class="row mx-0 form-group" id="features_group_add_update"
                                                *ngIf="currentFeatureGroup != null">
                                                <div class="col-3 col_border middleText">
                                                    <strong class="">Configure Presets:</strong>
                                                </div>
                                                <div class="col-9 p-0">
                                                    <div class="row m-0">
                                                        <ng-template [ngIf]="currentPresetGroup.length > 0">
                                                            <ng-template ngFor let-presetGroupObject
                                                                [ngForOf]="currentPresetGroup">
                                                                <div
                                                                    class="col-xl-4 col-lg-4 col-md-4 col-sm-6 col_border p-1">
                                                                    <label>{{presetGroupObject |
                                                                        featuresBaseResponseDisplayPipe}}</label>
                                                                    <div class="grid-container">
                                                                        <ng-template
                                                                            [ngIf]="presetGroupObject | featureValidityOptionHideShow : validityPerpetual">
                                                                            <div class="custom-control custom-radio">
                                                                                <input type="checkbox"
                                                                                    class="custom-control-input"
                                                                                    [id]="'Perpetual_'+presetGroupObject.displayName+'_'+presetGroupObject.presetId.toString()"
                                                                                    [name]="presetGroupObject.displayName+'_'+presetGroupObject.presetId.toString()"
                                                                                    [checked]="selectedPresets | probeConfigGroupCheckBoxPipe:presetGroupObject.presetId:validityPerpetual:presetProbeConfigType:reloadPipePreset"
                                                                                    (click)="onPresetChange(presetGroupObject.presetId,presetGroupObject.partNumbers,validityPerpetual)"
                                                                                    [disabled]="!(presetGroupObject.partNumbers |disableLicenseCheckBoxpipe:validityPerpetual)">
                                                                                <label
                                                                                    class="custom-control-label radioButtonLineHeight"
                                                                                    [for]="'Perpetual_'+presetGroupObject.displayName+'_'+presetGroupObject.presetId.toString()">{{presetGroupObject
                                                                                    |featuresValidityPartNumberDisplayPipe:validityPerpetual}}</label>
                                                                            </div>
                                                                        </ng-template>
                                                                        <!-- ------------------------------------- -->
                                                                        <ng-template
                                                                            [ngIf]="presetGroupObject |featureValidityOptionHideShow : validityOneYear">
                                                                            <div class="custom-control custom-radio">
                                                                                <input type="checkbox"
                                                                                    class="custom-control-input"
                                                                                    [id]="'month_'+presetGroupObject.displayName+'_'+presetGroupObject.presetId.toString()"
                                                                                    [name]="presetGroupObject.displayName+'_'+presetGroupObject.presetId.toString()"
                                                                                    [checked]="selectedPresets | probeConfigGroupCheckBoxPipe:presetGroupObject.presetId:validityOneYear:presetProbeConfigType:reloadPipePreset"
                                                                                    (click)="onPresetChange(presetGroupObject.presetId,presetGroupObject.partNumbers,validityOneYear)"
                                                                                    [disabled]="!(presetGroupObject.partNumbers |disableLicenseCheckBoxpipe:validityOneYear)">
                                                                                <label
                                                                                    class="custom-control-label radioButtonLineHeight"
                                                                                    [for]="'month_'+presetGroupObject.displayName+'_'+presetGroupObject.presetId.toString()">{{presetGroupObject
                                                                                    |featuresValidityPartNumberDisplayPipe:validityOneYear}}</label>
                                                                            </div>
                                                                        </ng-template>
                                                                    </div>
                                                                </div>
                                                            </ng-template>
                                                        </ng-template>
                                                        <ng-template [ngIf]="currentFeatureGroup.length == 0">
                                                            <div class="col-12 col_border p-3"><span>No Presets
                                                                    available to
                                                                    configure</span></div>
                                                        </ng-template>
                                                    </div>
                                                </div>
                                            </div>
                                            <!------------------------------------>
                                            <!------------------------------------>

                                            <div class="row mx-0 form-group" id="features_group_add_update"
                                                *ngIf="currentFeatureGroup != null">
                                                <div class="col-3 col_border middleText">
                                                    <strong class="">Configure Features:</strong>
                                                </div>
                                                <div class="col-9 p-0">
                                                    <div class="row m-0">
                                                        <ng-template [ngIf]="currentFeatureGroup.length > 0">
                                                            <ng-template ngFor let-featureGroupObject
                                                                [ngForOf]="currentFeatureGroup">
                                                                <div
                                                                    class="col-xl-4 col-lg-4 col-md-4 col-sm-6 col_border p-1">
                                                                    <label>{{featureGroupObject |
                                                                        featuresBaseResponseDisplayPipe}}</label>
                                                                    <div class="grid-container">
                                                                        <ng-template
                                                                            [ngIf]="featureGroupObject | featureValidityOptionHideShow : validityPerpetual">
                                                                            <div class="custom-control custom-radio">
                                                                                <input type="checkbox"
                                                                                    class="custom-control-input"
                                                                                    [id]="'Perpetual_'+featureGroupObject.displayName+'_'+featureGroupObject.featureId.toString()"
                                                                                    [name]="featureGroupObject.displayName+'_'+featureGroupObject.featureId.toString()"
                                                                                    [checked]="selectedFeatures | probeConfigGroupCheckBoxPipe:featureGroupObject.featureId:validityPerpetual:featureProbeConfigType:reloadPipeFeature"
                                                                                    (click)="onFeatureChange(featureGroupObject.featureId,featureGroupObject.partNumbers,validityPerpetual)"
                                                                                    [disabled]="!(featureGroupObject.partNumbers|disableLicenseCheckBoxpipe:validityPerpetual)">
                                                                                <label
                                                                                    class="custom-control-label radioButtonLineHeight"
                                                                                    [for]="'Perpetual_'+featureGroupObject.displayName+'_'+featureGroupObject.featureId.toString()">{{featureGroupObject
                                                                                    |featuresValidityPartNumberDisplayPipe:validityPerpetual}}</label>
                                                                            </div>
                                                                        </ng-template>
                                                                        <!-- ------------------------------------- -->
                                                                        <ng-template
                                                                            [ngIf]="featureGroupObject |featureValidityOptionHideShow : validityOneYear">
                                                                            <div class="custom-control custom-radio">
                                                                                <input type="checkbox"
                                                                                    class="custom-control-input"
                                                                                    [id]="'month_'+featureGroupObject.displayName+'_'+featureGroupObject.featureId.toString()"
                                                                                    [name]="featureGroupObject.displayName+'_'+featureGroupObject.featureId.toString()"
                                                                                    [checked]="selectedFeatures | probeConfigGroupCheckBoxPipe:featureGroupObject.featureId:validityOneYear:featureProbeConfigType:reloadPipeFeature"
                                                                                    (click)="onFeatureChange(featureGroupObject.featureId,featureGroupObject.partNumbers,validityOneYear)"
                                                                                    [disabled]="!(featureGroupObject.partNumbers|disableLicenseCheckBoxpipe:validityOneYear)">
                                                                                <label
                                                                                    class="custom-control-label radioButtonLineHeight"
                                                                                    [for]="'month_'+featureGroupObject.displayName+'_'+featureGroupObject.featureId.toString()">{{featureGroupObject
                                                                                    |featuresValidityPartNumberDisplayPipe:validityOneYear}}</label>
                                                                            </div>
                                                                        </ng-template>
                                                                    </div>
                                                                </div>
                                                            </ng-template>
                                                        </ng-template>
                                                        <ng-template [ngIf]="currentFeatureGroup.length == 0">
                                                            <div class="col-12 col_border p-3"><span>No feature
                                                                    available to
                                                                    configure</span></div>
                                                        </ng-template>
                                                    </div>
                                                </div>
                                            </div>

                                            <!------------------------------------>
                                            <!------------------------------------>
                                            <div class="row">
                                                <!--Part Number start-->
                                                <div class="col-md-12">
                                                    <div class="form-group">
                                                        <label><strong class="">Description</strong></label>
                                                        <textarea tabindex="-1" class="form-control textAreaResize"
                                                            rows="3" autocomplete="off"
                                                            formControlName="description"></textarea>
                                                        <div *ngIf="
                                                            (probeConfigGroupForm.get('description').touched ||
                                                             probeConfigGroupForm.get('description').dirty) &&
                                                             probeConfigGroupForm.get('description').invalid
                                                           ">
                                                            <div
                                                                *ngIf="probeConfigGroupForm.get('description').errors['maxlength']">
                                                                <span class="alert-color font-12">{{
                                                                    textAreaMaxCharactersAllowedMessage
                                                                    }}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--Part Number end-->
                                            </div>

                                        </form>
                                        <!------------------------------------------>
                                        <!------------------------------------------>
                                        <!--form end-->
                                        <!------------------------------------------>
                                        <!------------------------------------------>
                                    </div>
                                </div>

                                <hr class="mt-4">
                                <!--Update user start-->
                                <div class="ml-auto text-right">
                                    <button class="btn btn-orange btn-sm"
                                        [disabled]="!(probeConfigGroupForm.valid&&(selectedFeatures.length>0||selectedPresets.length>0))"
                                        (click)="saveData()">Save</button>
                                </div>
                                <!--Update user end-->


                            </div>
                            <!--Card body end-->
                        </div>
                        <!--card end-->
                    </div>


                </div>
                <!--Body end-->
            </div>
        </div>
        <!--Row end-->
    </div>
    <!--container end-->
</body>
<!------------------------------------------->
<!--Body end-->
<!------------------------------------------->