import { TestBed } from '@angular/core/testing';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { AuthJwtService } from '../../auth-jwt.service';
import { API_BASE_URL } from '../../config';
import { DeviceLogsSearchRequest } from '../../../model/Logs/DeviceLogsSearchRequest.model';
import { DeviceLogApiCallService } from './device-log-api-call.service';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { DeviceLogPageResponse } from '../../../model/Logs/DeviceLogPageResponse.model';

describe('DeviceLogApiCallService', () => {
    let service: DeviceLogApiCallService;
    let httpMock: HttpTestingController;
    let authServiceSpy: jasmine.SpyObj<AuthJwtService>;
    const API_URL = 'http://example.com/api/';

    beforeEach(() => {
        const authSpy = jasmine.createSpyObj('AuthJwtService', ['getToken']);
        TestBed.configureTestingModule({
            imports: [],
            providers: [
                DeviceLogApiCallService,
                { provide: API_BASE_URL, useValue: API_URL },
                { provide: AuthJwtService, useValue: authSpy },
                provideHttpClient(withInterceptorsFromDi()),
                provideHttpClientTesting()
            ]
        });
        service = TestBed.inject(DeviceLogApiCallService);
        httpMock = TestBed.inject(HttpTestingController);
        authServiceSpy = TestBed.inject(AuthJwtService) as jasmine.SpyObj<AuthJwtService>;
    });

    afterEach(() => {
        httpMock.verify();
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should call getLogDetail with POST', () => {
        const requestPayload: DeviceLogsSearchRequest = null;
        const dummyResponse: DeviceLogPageResponse = null;

        service.getLogDetail(requestPayload).subscribe((res) => {
            expect(res.body).toEqual(dummyResponse);
        });

        const req = httpMock.expectOne(`${API_URL}api/log`);
        expect(req.request.method).toBe('POST');
        req.flush(dummyResponse);
    });

    it('should call getLogFileUrl with POST', () => {
        const fileName = 'logfile.txt';
        const dummyResponse = { url: 'download-link' };

        service.getLogFileUrl(fileName).subscribe((res) => {
            expect(res.body).toEqual(dummyResponse);
        });

        const req = httpMock.expectOne(`${API_URL}api/log/download/url`);
        expect(req.request.method).toBe('POST');
        expect(req.request.body).toEqual(fileName);
        req.flush(dummyResponse);
    });

    it('should call getLogTypes with GET', () => {
        const dummyLogTypes = ['INFO', 'ERROR'];

        service.getLogTypes().subscribe((res) => {
            expect(res.body).toEqual(dummyLogTypes);
        });

        const req = httpMock.expectOne(`${API_URL}api/log/logtypes`);
        expect(req.request.method).toBe('GET');
        req.flush(dummyLogTypes);
    });

    it('should call downloadMyFile and create anchor element', () => {
        const attachmentUrl = 'log/download/file.txt';
        const filename = 'file.txt';

        const anchor = document.createElement('a');
        spyOn(anchor, 'click');
        spyOn(anchor, 'remove');

        const createElementSpy = spyOn(document, 'createElement').and.returnValue(anchor);
        const appendChildSpy = spyOn(document.body, 'appendChild').and.callThrough();

        service.downloadMyFile(attachmentUrl, filename);

        expect(createElementSpy).toHaveBeenCalledWith('a');
        expect(anchor.getAttribute('href')).not.toBeNull();
        expect(appendChildSpy).toHaveBeenCalledWith(anchor);
        expect(anchor.click).toHaveBeenCalled();
        expect(anchor.remove).toHaveBeenCalled();
    });


    it('should return token from getToken', () => {
        authServiceSpy.getToken.and.returnValue('mockToken');
        const result = service.getToken();
        expect(result).toBe('&Authorization=Bearer mockToken');
        expect(authServiceSpy.getToken).toHaveBeenCalled();
    });
});
