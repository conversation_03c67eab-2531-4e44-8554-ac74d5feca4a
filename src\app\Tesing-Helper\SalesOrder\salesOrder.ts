import { AssociatedConfigLicence } from "src/app/model/associated-config-licence.model";
import { LicensesRequest } from "src/app/model/probe/multiProbe/LicensesRequest.model";
import { SalesOrderBridgeDetailResponse } from "src/app/model/SalesOrder/SalesOrderBridgeDetailResponse.model";
import { SalesOrderBridgeDetailValidateResponse } from "src/app/model/SalesOrder/SalesOrderBridgeDetailValidateResponse.model";
import { SalesOrderProbeDetailResponse } from "src/app/model/SalesOrder/SalesOrderProbeDetailResponse.model";
import { SalesOrderProbeDetailValidateResponse } from "src/app/model/SalesOrder/SalesOrderProbeDetailValidateResponse.model";
import { SalesOrderProduct } from "src/app/model/SalesOrder/SalesOrderProduct.model";
import { SalesOrderTransferProductDetailValidateResponse } from "src/app/model/SalesOrder/SalesOrderTransferProductDetailValidateResponse.model";
import { TransferOrderSelectionProductResponse } from "src/app/model/SalesOrder/TransferOrderSelectionProductResponse.model";
import { TransferOrderSerialNumber } from "src/app/model/SalesOrder/TransferOrderSerialNumber.model";
import { TransferredSalesOrder } from "src/app/model/SalesOrder/TransferredSalesOrder.model";
import { ProductConfigStatus } from "src/app/shared/enum/SalesOrder/ProductConfigStatus.enum";

export function createSalesOrderProbeDetailResponse(sopmId: number, probeType: string, bridgeKitPartNumberCode: any, otsKitPartNumberCode: any, productCode: string, entitySerialNumber: string, entityPk: number, entityStatus: ProductConfigStatus, probeConfigGroupPartNumberCodes: Array<string>, associatedFeatures: Array<AssociatedConfigLicence>, associatedPresets: Array<AssociatedConfigLicence>, enableFeatures: Array<LicensesRequest>, enablePresets: Array<LicensesRequest>, transferredSalesOrderNumber: string, reminder: boolean): SalesOrderProbeDetailResponse { //NOSONAR
    return new SalesOrderProbeDetailResponse(sopmId, probeType, bridgeKitPartNumberCode, otsKitPartNumberCode, productCode, entitySerialNumber, entityPk, entityStatus, probeConfigGroupPartNumberCodes, associatedFeatures, associatedPresets, enableFeatures, enablePresets, transferredSalesOrderNumber, reminder);
}

export function createSalesOrderBridgeDetailResponse(sopmId: number, bridgeKitPartNumberCode: string, productCode: string, entitySerialNumber: string | null, entityPk: number | null, entityStatus: ProductConfigStatus, tranferredSalesOrderNumber: string): SalesOrderBridgeDetailResponse {
    return new SalesOrderBridgeDetailResponse(sopmId, bridgeKitPartNumberCode, productCode, entitySerialNumber, entityPk, entityStatus, tranferredSalesOrderNumber);
}

const transferOrderProbe1Response = createSalesOrderProbeDetailResponse(64493, "Lexsa", null, null, "P006709-062", "L1A-1234", 4227, ProductConfigStatus.CONFIGURED, null, [], [], [], [], null, false);
const transferOrderProbe2Response = createSalesOrderProbeDetailResponse(64495, "Torso1, USB", null, null, "P007785-005", "T1A-1234", 4228, ProductConfigStatus.CONFIGURED, null, [], [], [], [], null, false);
const transferOrderBridge1Response = createSalesOrderBridgeDetailResponse(64496, null, "P006030-185", "maitri-test-function-audit49", 85, ProductConfigStatus.CONFIGURED, null);
const transferOrderBridge2Response = createSalesOrderBridgeDetailResponse(64497, null, "P006030-198", "maitri-test-function-audit48", 85, ProductConfigStatus.CONFIGURED, null);
const transferOrderBridge3Response = createSalesOrderBridgeDetailResponse(64494, null, "P006030-195", null, null, ProductConfigStatus.NOT_CONFIGURED, null);
const transferOrderProduct: SalesOrderProduct = new SalesOrderProduct([transferOrderProbe1Response, transferOrderProbe2Response], [transferOrderBridge1Response, transferOrderBridge2Response, transferOrderBridge3Response])



const transferredOrderProbe1Response = createSalesOrderProbeDetailResponse(64507, "Lexsa", null, null, "P006709-062", null, 4227, ProductConfigStatus.NOT_CONFIGURED, null, [], [], [], [], null, false);
const transferredOrderProbe2Response = createSalesOrderProbeDetailResponse(64506, "Torso1, USB", null, null, "P007785-005", null, 4228, ProductConfigStatus.NOT_CONFIGURED, null, [], [], [], [], null, false);
const transferredOrredBridge1Response = createSalesOrderBridgeDetailResponse(64503, null, null, null, null, ProductConfigStatus.NOT_CONFIGURED, null);
const transferredOrderBridge2Response = createSalesOrderBridgeDetailResponse(64504, null, null, null, null, ProductConfigStatus.NOT_CONFIGURED, null);
const transferredOrderBridge3Response = createSalesOrderBridgeDetailResponse(64505, null, null, null, null, ProductConfigStatus.NOT_CONFIGURED, null);
const transferredOrderredProduct: SalesOrderProduct = new SalesOrderProduct([transferredOrderProbe1Response, transferredOrderProbe2Response], [transferredOrredBridge1Response, transferredOrderBridge2Response, transferredOrderBridge3Response]);

export const transferOrderResponse: TransferOrderSelectionProductResponse = new TransferOrderSelectionProductResponse(5777, "00001578", transferOrderProduct, "United States");
export const transferredOrderredResponse: TransferOrderSelectionProductResponse = new TransferOrderSelectionProductResponse(5778, "00001577", transferredOrderredProduct, "Nepal");

export const salesOrderProbeDetailValidateResponse = new SalesOrderProbeDetailValidateResponse(64506, "Torso1, USB", null, null, "P007785-005", null, 4228, ProductConfigStatus.NOT_CONFIGURED, null, [], [], [], [], null, false);
export const salesOrderBridgeDetailValidateResponse = new SalesOrderBridgeDetailValidateResponse(64507, null, "P007785-005", null, null, ProductConfigStatus.NOT_CONFIGURED, null);

export const salesOrderTransferProductDetailValidateResponse = new SalesOrderTransferProductDetailValidateResponse([salesOrderProbeDetailValidateResponse], [salesOrderBridgeDetailValidateResponse]);
export const destinationSalesOrder = new TransferredSalesOrder(1, "salesOrder", salesOrderTransferProductDetailValidateResponse, "India");
export const transferSalesOrderProbeSerialNumbers = new Map<string, Array<TransferOrderSerialNumber>>([
    ["Torso1, USB", [new TransferOrderSerialNumber(64495, "T1A-1234")]],
    ["Lexsa", [new TransferOrderSerialNumber(64493, "L1A-1234")]]
]);