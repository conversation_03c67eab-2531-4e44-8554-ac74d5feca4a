import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { EnumMapping } from 'src/app/model/common/EnumMapping.model';
import { PrintListPipe } from '../printList.pipe';

@Pipe({
    name: 'enumMappingDisplayNamePipe'
})
export class EnumMappingDisplayNamePipe implements PipeTransform {

    constructor(private printListPipe: PrintListPipe) { }

    /**
     * <AUTHOR>
     * @param enumValue 
     * @param enumMapping 
     * @returns 
     */
    transform(enumValue: any, enumMapping: Array<EnumMapping>): string {
        if (!isNullOrUndefined(enumValue) && !isNullOrUndefined(enumMapping) && enumMapping.length > 0) {
            let filterList = enumMapping.filter((obj) => obj.key == enumValue);
            if (filterList.length > 0) {
                let enumValues: Array<string> = filterList.map(obj => obj.value);
                return this.printListPipe.transform(enumValues);
            }
        }
        return null;
    }

}
