import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ProductConfigStatus } from '../../enum/SalesOrder/ProductConfigStatus.enum';

@Pipe({
    name: 'salesOrderBridgeViewDetailButtonDisplayPipe'
})
export class SalesOrderBridgeViewDetailButtonDisplayPipe implements PipeTransform {

    //view detail Show
    viewDetailButtonShowStatusList: Array<string> = [ProductConfigStatus.CONFIGURED];

    public transform(productEntityId: number, productStatus: ProductConfigStatus): boolean {
        if (!isNullOrUndefined(productEntityId) && !isNullOrUndefined(productStatus)) {
            return this.viewDetailButtonShowStatusList.includes(ProductConfigStatus[productStatus]);
        }
        return false;
    }

}
