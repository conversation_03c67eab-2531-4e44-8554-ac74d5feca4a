import { HttpBackend, HttpClient, HttpHeaders, HttpResponse } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { SoftwareBuildListResponse } from 'src/app/model/SoftwaarBuilds/SoftwareBuildListResponse.model';
import { SoftwareBuildSearchRequestBody } from 'src/app/model/SoftwaarBuilds/SoftwareBuildSearchRequestBody';
import { AttachmentDownloadResponseModel } from 'src/app/model/SoftwaarBuilds/attachment-download-response.model';
import { UploadPackageRequest } from 'src/app/model/upload.package.request';
import { UploadPackageResponse } from 'src/app/model/upload.package.response';
import { API_BASE_URL } from 'src/app/shared/config';
import { createRequestOption } from 'src/app/shared/util/request-util';

type EntityArrayResponseType = HttpResponse<SoftwareBuildListResponse[]>;

@Injectable({
  providedIn: 'root'
})
export class SoftwareBuildApiCallService {

  public resourceUrlList = this.SERVER_API_URL + 'api/software-builds';
  public resourceUrl = this.SERVER_API_URL + 'api/inventory';

  private https: HttpClient;

  constructor(protected http: HttpClient, @Inject(API_BASE_URL) public SERVER_API_URL: string, httpBackend: HttpBackend) { this.https = new HttpClient(httpBackend); }

  inventoryList(filterData: SoftwareBuildSearchRequestBody, req?: any): Observable<EntityArrayResponseType> {
    const options = createRequestOption(req);
    return this.http.post<SoftwareBuildListResponse[]>(this.resourceUrlList + '/search', filterData, { params: options, observe: 'response' });
  }

  deleteSoftwearBuild(id: number): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.delete<SuccessMessageResponse>(this.resourceUrl + '/' + id, { observe: 'response' });
  }

  getAttachmentUrl(id: number, prm?: any): Observable<HttpResponse<AttachmentDownloadResponseModel>> {
    const options = createRequestOption(prm);
    return this.http.get<AttachmentDownloadResponseModel>(this.resourceUrl + '/getSignedURL/' + id, { params: options, observe: 'response' });
  }

  pushFileToStorage(requestData: UploadPackageRequest, prm?: any): Observable<HttpResponse<UploadPackageResponse>> {
    const options = createRequestOption(prm);
    return this.http.post<UploadPackageResponse>(this.resourceUrl + '/upload', requestData, { params: options, observe: 'response' });
  }

  updateFirmwareUploadStatus(requestData: UploadPackageRequest, prm?: any) {
    const options = createRequestOption(prm);
    return this.http.put(this.resourceUrl + '/upload', requestData, { params: options, observe: 'response' });
  }

  /**
   * Upload avatar image
   * 
   * <AUTHOR>
   * @param file 
   * @param fileUrl 
   */
  public uploadFileToStorage(file: File, fileUrl: string): Observable<HttpResponse<any>> {
    let headers = new HttpHeaders();
    headers = headers.set('x-ms-blob-type', 'BlockBlob');
    return this.https.put(fileUrl, file, { observe: 'response', headers: headers });
  }

  /**
   * Upload avatar image's final call
   * 
   * <AUTHOR>
   * @param fileData 
   * @param fileUrl 
   * @param fileType 
   */
  public commitFileToStorage(fileData: any, fileUrl: string, fileType: string): Observable<HttpResponse<any>> {
    let headers = new HttpHeaders();
    headers = headers.set('x-ms-blob-content-type', fileType);
    return this.https.put(fileUrl, fileData, { observe: 'response', headers: headers });
  }

  /**
   * Map Inventories with Device Type 
   * 
   * <AUTHOR>
   * @param inventoryId 
   * @param prm 
   */
  public mapInventoryWithDeviceType(inventoryId: number[], prm?: any): Observable<HttpResponse<SuccessMessageResponse>> {
    const options = createRequestOption(prm);
    return this.http.put<SuccessMessageResponse>(this.resourceUrl + '/' + inventoryId, {}, { params: options, observe: 'response' });
  }

  /**
   * Mark Inventories as Active/Inactive 
   * 
   * <AUTHOR>
   * @param inventoryId 
   * @param prm 
   */
  public markInventoriesActiveInactive(inventoryId: number[], prm?: any): Observable<HttpResponse<SuccessMessageResponse>> {
    const options = createRequestOption(prm);
    return this.http.put<SuccessMessageResponse>(this.resourceUrl + '/' + inventoryId, {}, { params: options, observe: 'response' });
  }

  public updateInventory(inventoryId: number, prm: any): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.put<SuccessMessageResponse>(this.resourceUrl + '/' + inventoryId, prm, { observe: 'response' });
  }

}
