import { Injectable } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { Subject } from 'rxjs';
import { DetailSalesOrderResource, ListSalesOrderResource } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { SalesOrderFilterAction } from 'src/app/model/SalesOrder/SalesFilterAction.model';
import { SalesOrderFaieldSearchRequestBody } from 'src/app/model/SalesOrder/SalesOrderFaieldSearchRequestBody.model';
import { SalesOrderFailedFilterAction } from 'src/app/model/SalesOrder/SalesOrderFailedFilterAction.model';
import { SalesOrderSearchRequestBody } from 'src/app/model/SalesOrder/SalesOrderSearchRequestBody.model';

@Injectable({
  providedIn: 'root'
})
export class SalesOrderService {


  /**
  * Country List
  */
  private countriesList: CountryListResponse[] = [];

  /**
   * Order Record Type List
   */
  private orderRecordTypeList: any[] = [];

  //Sales Order list filter
  private salesOrderListFilterRequestParameterSubject = new Subject<SalesOrderFilterAction>();

  //Refresh Sales Order Faliled List 
  private salesOrderFaieldListRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //Refresh Sales Order Faliled List
  private salesOrderListRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //Sales Order Faliled list filter
  private salesOrderFailedListFilterRequestParameterSubject = new Subject<SalesOrderFailedFilterAction>();

  //Refresh Sales Order Detail page
  private salesOrderDetailRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  /**
   * Set Country
   */
  public setCountryList(countriesList: CountryListResponse[]) {
    this.countriesList = countriesList;
  }

  /**
   * Get Country
   */
  public getCountryList() {
    return this.countriesList;
  }

  /**
   * Set Order Record Type List
   */
  public setOrderRecordTypeList(orderRecordTypeList: any[]) {
    this.orderRecordTypeList = orderRecordTypeList;
  }

  /**
   * Get Order Record Type List
   */
  public getOrderRecordTypeList() {
    return this.orderRecordTypeList;
  }


  /**
   * Sales Order List Page Refresh After some Action
   * <AUTHOR>
   * @returns ListingPageReloadSubjectParameter
   */
  public getSalesOrderListRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.salesOrderListRefreshSubject;
  }



  /**
   * Sales Order Detail Page Refresh After some Action
   * <AUTHOR>
   * @returns ListingPageReloadSubjectParameter
   */
  public getSalesOrderDetailRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.salesOrderDetailRefreshSubject;
  }

  /**
   * Sales Order List Page Refresh After some Action Like Serch parameter add
   * Note : Clear All filter and refresh page 
   * <AUTHOR>
   * @returns SalesOrderList 
   */
  public getSalesOrderListFilterRequestParameterSubject(): Subject<SalesOrderFilterAction> {
    return this.salesOrderListFilterRequestParameterSubject;
  }

  public callSalesOrderListFilterRequestParameterSubject(salesOrderFilterAction: SalesOrderFilterAction): void {
    this.salesOrderListFilterRequestParameterSubject.next(salesOrderFilterAction);
  }



  /**
   * This function call the subject for reload the page data
   *  Note : (ListSalesOrderResource) -> Filter page subject call -> Listing page subject call
   * clear all filter after page data Reload
   * <AUTHOR>
   * @param isReloadData -> false means move to prev page for DetailSalesOrderResource.
   * @param resourceName 
   */
  public callRefreshPageSubject(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, resourceName: string, isFilterHidden: boolean, salesOrderSearchRequestBodyApply: SalesOrderSearchRequestBody): void {
    if (resourceName == ListSalesOrderResource) {
      if (isFilterHidden) {
        let salesOrderRequestBody = new SalesOrderSearchRequestBody(null, null, null, [], null, null, null, []);
        if (!isNullOrUndefined(salesOrderSearchRequestBodyApply) && !listingPageReloadSubjectParameter.isClearFilter) {
          salesOrderRequestBody = salesOrderSearchRequestBodyApply;
        }
        let salesOrderFilterAction = new SalesOrderFilterAction(listingPageReloadSubjectParameter, salesOrderRequestBody);
        this.callSalesOrderListFilterRequestParameterSubject(salesOrderFilterAction);
      } else {
        this.salesOrderListRefreshSubject.next(listingPageReloadSubjectParameter);
      }
    } else if (resourceName == DetailSalesOrderResource) {
      this.salesOrderDetailRefreshSubject.next(listingPageReloadSubjectParameter);
    }
  }

  /*--------------------------------------------------------- */
  /**  Failed Sales Order List */
  /*--------------------------------------------------------- */

  /**
   * Sales Order Failed List Page Refresh After some Action
   * <AUTHOR>
   * @returns ListingPageReloadSubjectParameter
   */
  public getSalesOrderFailedListRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.salesOrderFaieldListRefreshSubject;
  }

  /**
   * Sales Order Failed List Page Refresh After some Action Like Serch parameter add
   * Note : Clear All filter and refresh page 
   * @returns 
   */
  public getSalesOrderFailedListFilterRequestParameterSubject(): Subject<SalesOrderFailedFilterAction> {
    return this.salesOrderFailedListFilterRequestParameterSubject;
  }

  public callSalesOrderFailedListFilterRequestParameterSubject(salesOrderFailedFilterAction: SalesOrderFailedFilterAction): void {
    this.salesOrderFailedListFilterRequestParameterSubject.next(salesOrderFailedFilterAction);
  }

  /**
   * This function call the subject for reload the page data
   *  Note : (ListSalesOrderResource) -> Filter page subject call -> Listing page subject call
   * clear all filter after page data Reload
   * <AUTHOR>
   * @param isReloadData -> false means move to prev page for DetailSalesOrderResource.
   * @param resourceName 
   */
  public callRefreshPageSubjectForFaildOrder(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, isFilterHidden: boolean): void {
    if (isFilterHidden) {
      let salesOrderFailedRequestBody = new SalesOrderFaieldSearchRequestBody(null);
      let salesOrderFailedFilterAction = new SalesOrderFailedFilterAction(listingPageReloadSubjectParameter, salesOrderFailedRequestBody);
      this.callSalesOrderFailedListFilterRequestParameterSubject(salesOrderFailedFilterAction);
    } else {
      this.salesOrderFaieldListRefreshSubject.next(listingPageReloadSubjectParameter);
    }
  }


}

