<!------------------------------------------->
<!--loading start-->
<!------------------------------------------->
<div class="ringLoading" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!------------------------------------------->
<!--loading end-->
<!------------------------------------------->

<!------------------------------------------->
<!--body start-->
<!------------------------------------------->

<body class="bg-white" *ngIf="userDetailShow">

    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <!--header  start-->
                <div class="row">
                    <label class="col-md-6 h5-tag">User Detail</label>
                    <div class="ml-auto col-md-6 text-right mb-3">
                        <!--Button div start-->
                        <!--back button-->
                        <!--update button-->
                        <div class="dropdown">
                            <!-- back button start -->
                            <button class="btn btn-sm btn-outline-secondary mr-3" (click)="back()"
                                id="userDetailPageBackBtn"><i class="fa fa-reply"
                                    aria-hidden="true"></i>&nbsp;&nbsp;Back</button>
                            <!-- back button end -->

                            <!-- update button start -->
                            <button class="btn btn-sm btn-orange" (click)="updateUserShow()"
                                *ngIf="updateUserPermission" id="userDetailPageUpdateBtn"><em
                                    class="fa fa-edit"></em>&nbsp;&nbsp;Update</button>
                            <!-- update button end -->

                            <!-- refresh button start -->
                            <button class="btn btn-sm btn-orange ml-2" (click)="refreshUserDetailPage()"
                                id="userDetailPageRefreshBtn"><em class="fa fa-refresh"></em></button>
                            <!-- refresh button end -->

                        </div>
                        <!--Button div end-->
                    </div>
                </div>
                <!--header  end-->
                <!--Body start-->
                <div class="row">
                    <div class="col-md-12">
                        <!--card start-->
                        <div class="card">
                            <!--card body start-->
                            <div class="card-body">
                                <div class="card shadow">
                                    <div class="card-body">
                                        <!--Last Logged start-->
                                        <div class="row">
                                            <label class="col-md-6 h6-tag">Last Logged in
                                                {{userResponse?.lastLoggedIn | date:'MM/dd/yyyy, h:mm:ss a'}}</label>
                                        </div>
                                        <!--Last Logged end-->
                                        <hr>

                                        <div class="row">
                                            <!--First Name start-->
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label><strong class="">First Name</strong></label>
                                                    <input type="text" class="form-control" id="" placeholder=""
                                                        [value]="userResponse?.firstName" disabled>
                                                </div>
                                            </div>
                                            <!--First Name end-->
                                            <!--Last Name start-->
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label><strong class="">Last Name</strong></label>
                                                    <input type="text" class="form-control" id="" placeholder=""
                                                        [value]="userResponse?.lastName" disabled>
                                                </div>
                                            </div>
                                            <!--Last Name end-->
                                            <!--Email start-->
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label><strong class="">E-mail</strong></label>
                                                    <input type="text" class="form-control" id="" placeholder=""
                                                        [value]="userResponse?.email" disabled>
                                                </div>
                                            </div>
                                            <!--Email end-->
                                            <!--Login Name start-->
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label><strong class="">Login Name</strong></label>
                                                    <input type="text" class="form-control" id="" placeholder=""
                                                        [value]="userResponse?.login" disabled>
                                                </div>
                                            </div>
                                            <!--Login Name end-->
                                            <!--Role start-->
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label><strong class="">Role</strong></label>
                                                    <input type="text" class="form-control commonEllips" id="Role"
                                                        placeholder="" [value]="userResponse?.userRoles | printListPipe"
                                                        disabled>
                                                </div>
                                            </div>
                                            <!--Role end-->
                                            <!--date added start-->
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label><strong class="">Date & Time Added</strong></label>
                                                    <input type="text" class="form-control" id="" placeholder=""
                                                        [value]="userResponse?.createdDate | date:dateDisplayFormat"
                                                        disabled>
                                                </div>
                                            </div>
                                            <!--date added end-->

                                            <!--date added start-->
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label><strong class="">Modified Date & Time</strong></label>
                                                    <input type="text" class="form-control" id="Modified" placeholder=""
                                                        [value]="userResponse?.modifiedDate | date:dateDisplayFormat"
                                                        disabled>
                                                </div>
                                            </div>
                                            <!--date added end-->

                                            <!--Module start-->
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label><strong class="">Module(s)</strong></label>
                                                    <div class="detailListItem">
                                                        <span>{{userResponse?.modules | printListPipe}}</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--Module End-->
                                        </div>
                                        <!-- second row start -->

                                        <div class="row" *ngIf="userResponse != null && 
                                        userResponse?.countryMasters != null && 
                                        userResponse?.countryMasters.length > 0">
                                            <div class="col-md-12">
                                                <div class="card">
                                                    <div class="card-body">
                                                        <div class="card shadow">
                                                            <div class="card-body">
                                                                <div class="container">
                                                                    <table class="table table-sm table-bordered"
                                                                        aria-hidden="true">
                                                                        <thead>
                                                                            <tr class="thead-light">
                                                                                <th>Country</th>
                                                                            </tr>

                                                                        </thead>
                                                                        <tbody>
                                                                            <tr
                                                                                *ngFor='let country of userResponse?.countryMasters'>
                                                                                <td>{{country?.country}}</td>
                                                                            </tr>

                                                                        </tbody>

                                                                    </table>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- second row end -->

                                    </div>
                                </div>


                            </div>
                            <!--card body end-->
                        </div>
                        <!--card end-->
                    </div>
                </div>
                <!--Body end-->

            </div>
        </div>
    </div>
</body>
<!------------------------------------------->
<!--body end-->
<!------------------------------------------->
<!--Update user Components start-->
<div *ngIf="userUpdateShow">
    <app-update-user [userId]="userId" (viewmemberlist)="userShow()"></app-update-user>
</div>
<!--Update user Components end-->