<!-- loading start -->
<!-- loading gif start -->
<div class="ringLoading" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!-- loading gif end -->
<!-- loading end -->

<body class="bg-white">

    <!-- main container start -->
    <div class="container-fluid">
        <!-- row start -->
        <div class="row">
            <div class="col-md-12">
                <!-------------------------------->
                <!-- header row start -->
                <!-------------------------------->
                <div class="row headerAlignment">
                    <!--  header start -->
                    <label class="childFlex h5-tag">Brige Kit Management Detail&nbsp;-&nbsp;<span
                            *ngIf="KitRevVersionResponse != null && KitRevVersionResponse != null">Rev
                            version {{KitRevVersionResponse}}</span></label>
                    <!--  header end -->

                    <div class="childFlex">
                        <!-- back button start -->
                        <button class="btn btn-sm btn-outline-secondary device-back-btn" (click)="back()"
                            id="bridgeDetailBack"><i class="fa fa-reply"
                                aria-hidden="true"></i>&nbsp;&nbsp;Back</button>
                        <!-- back button end -->

                        <!-- refresh button start -->
                        <button class="btn btn-sm btn-orange ml-2" (click)="refreshBridgekitDetailPage()"
                            id="bridgeDetailRefresh"><em class="fa fa-refresh"></em></button>
                        <!-- refresh button end -->
                    </div>
                </div>
                <!-------------------------------->
                <!--  detail header row end -->
                <!---######################################################################-->
                <!-------------------------------->
                <!-- kitManagementDetailResponse detail fields row start -->
                <!-------------------------------->
                <div class="row">
                    <div class="col-md-12">
                        <!-------------------------------->
                        <!-- main card start -->
                        <!-------------------------------->
                        <div class="card">
                            <div class="card-body">
                                <div class="card shadow">
                                    <div class="card-body">
                                        <div class="row">
                                            <!-------------------------------->
                                            <!-------------------------------->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">Country</strong></label>
                                                    <input type="text" [value]="kitManagementDetailResponse?.country"
                                                        class="form-control" name="Country" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">Kit Part Number</strong></label>
                                                    <input type="text"
                                                        [value]="kitManagementDetailResponse?.kitPartNumber | kitPartnumberDisplayPipe:kitManagementDetailResponse?.dummyKitPartNumber"
                                                        class="form-control" name="KitPartNumber" readonly>
                                                </div>
                                            </div>


                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">Software Language</strong></label>
                                                    <input type="text" [value]="kitManagementDetailResponse?.language"
                                                        class="form-control" name="Language" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">Video Version</strong></label>
                                                    <input type="text"
                                                        [value]="kitManagementDetailResponse?.videoVersion"
                                                        class="form-control" name="VideoVersion" readonly>
                                                </div>
                                            </div>

                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">Software Version</strong></label>
                                                    <input type="text"
                                                        [value]="kitManagementDetailResponse?.softWareVersion"
                                                        class="form-control" name="SoftwareVersion" readonly>
                                                </div>
                                            </div>

                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">Software Part Number</strong></label>
                                                    <input type="text"
                                                        [value]="kitManagementDetailResponse?.softWarePartNumber"
                                                        class="form-control" name="SoftwarePartNumber" readonly>
                                                </div>
                                            </div>

                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">Last Modified Date & Time</strong></label>
                                                    <input type="text"
                                                        [value]="kitManagementDetailResponse?.modifiedDate | date:'MMM d, y, h:mm:ss a'"
                                                        class="form-control" name="LastModifiedTime" readonly>
                                                </div>
                                            </div>

                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label><strong class="">Description</strong></label>
                                                    <textarea type="text"
                                                        [value]="kitManagementDetailResponse?.description" rows="3"
                                                        class="form-control textAreaResize" name="Description"
                                                        readonly></textarea>
                                                </div>
                                            </div>
                                            <!-------------------------------->
                                            <!-------------------------------->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-------------------------------->
                        <!-- main card start -->
                        <!-------------------------------->
                    </div>
                </div>
                <!-------------------------------->
                <!-- kitManagementDetailResponse detail fields row end -->
                <!-------------------------------->
                <!---######################################################################-->
                <div class="row">
                    <!-------------------------------->
                    <!-- main card start -->
                    <!-------------------------------->
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="card shadow">
                                    <div class="card-body ml-2">
                                        <label class="h6-tag mb-3"><u>Bridge Detail</u></label>

                                        <div class="row" *ngIf="kitManagementDetailResponse?.bridgePartNumber !=null">
                                            <!-------------------------------->
                                            <!-------------------------------->
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label><strong class="">Part Number</strong></label>
                                                    <input type="text"
                                                        [value]="kitManagementDetailResponse?.bridgePartNumber"
                                                        class="form-control w-75" name="bridgeDetailCode" readonly>
                                                </div>
                                            </div>
                                            <!-------------------------------->
                                            <!-------------------------------->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-------------------------------->
                    <!-- main card end -->
                    <!-------------------------------->
                </div>
                <!---######################################################################-->
                <div class="row"
                    *ngIf="kitManagementDetailResponse?.probePartNumbers && kitManagementDetailResponse?.probePartNumbers.length > 0">
                    <div class="col-md-12">
                        <!-------------------------------->
                        <!-- main card start -->
                        <!-------------------------------->
                        <div class="card">
                            <div class="card-body">
                                <div class="card shadow">
                                    <div class="card-body">
                                        <div class="row tablePadding">
                                            <div class="col-md-9">
                                                <!-------------------------------->
                                                <!-------------------------------->
                                                <label class="h6-tag mb-3"><u>Probe Details</u></label>
                                                <table class="table table-sm table-bordered w-50" aria-hidden="true">
                                                    <thead>
                                                        <tr class="thead-light">
                                                            <th>Sr. No.</th>
                                                            <th>Part Number</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <ng-template ngFor let-probeResponseObj
                                                            [ngForOf]="kitManagementDetailResponse?.probePartNumbers"
                                                            let-probeIndex="index">
                                                            <tr>
                                                                <td>{{(probeIndex+1)}}</td>
                                                                <td>{{probeResponseObj}}</td>
                                                            </tr>
                                                        </ng-template>
                                                    </tbody>
                                                </table>
                                                <!-------------------------------->
                                                <!-------------------------------->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-------------------------------->
                        <!-- main card start -->
                        <!-------------------------------->
                    </div>
                </div>

            </div>
        </div>
    </div>
</body>