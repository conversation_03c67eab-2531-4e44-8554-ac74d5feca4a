export class ManualSalesOrderResponse {
    salesOrderId: number;
    countryName: string;
    deviceAutoLock: boolean;
    probeAutoLock: boolean;
    orderRecordType: string;
    auditActionEnum: string;

    constructor(
        salesOrderId: number,
        countryName: string,
        deviceAutoLock: boolean,
        probeAutoLock: boolean,
        orderRecordType: string,
        auditActionEnum: string
    ) {
        this.salesOrderId = salesOrderId;
        this.countryName = countryName;
        this.deviceAutoLock = deviceAutoLock;
        this.probeAutoLock = probeAutoLock;
        this.orderRecordType = orderRecordType;
        this.auditActionEnum = auditActionEnum;
    }
}