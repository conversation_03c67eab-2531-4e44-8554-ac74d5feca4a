import { Pageable } from "./pageable.model";
import { Sort } from "./sort.model";

export class PageResponse {
    pageable: Pageable;
    totalPages: number;
    last: boolean;
    totalElements: number;
    numberOfElements: number;
    first: boolean;
    sort: Sort;
    size: number;
    number: number;
    empty: boolean;

    constructor( //NOSONAR
        pageable: Pageable,
        totalPages: number,
        last: boolean,
        totalElements: number,
        numberOfElements: number,
        first: boolean,
        sort: Sort,
        size: number,
        number: number,
        empty: boolean
    ) {
        this.pageable = pageable;
        this.totalPages = totalPages;
        this.last = last;
        this.totalElements = totalElements;
        this.numberOfElements = numberOfElements;
        this.first = first;
        this.sort = sort;
        this.size = size;
        this.number = number;
        this.empty = empty;
    }
}
