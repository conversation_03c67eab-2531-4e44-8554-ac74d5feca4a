<!------------------------------------>
<!------------------------------------>
<!-- Component Name : video -->
<!-- Description : Video and Json Listing, seraching -->
<!-- Functionality : Videos and Json Listing with pagination
                    Searching based on videoId and jsonVer searchfield
                    Edit video files and json and its note
                    Delete Video and Json
                    Download Video zip and Json files
                    Upload Video, Thumbnail and Subtitle files
                    Create Json from selected Videos
                    Expand - collapse filter -->
<!-- Validations : video delete is not allowed if it is relation in with exting json file
                  all buttons are disabled while creating json file -->
<!------------------------------------>
<!------------------------------------>
<div class="ringLoading" *ngIf="loading">
  <div class="ringLoadingDiv">
    <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
  </div>
</div>

<div class="row">
  <!------------------------------------------->
  <!--Filter start-->
  <!------------------------------------------->
  <div class="col-md-3 pr-0" *ngIf="(!isFilterHidden)">
    <label class="col-md-12 h5-tag">Filter</label>
    <div class="card mt-3">
      <div class="card-body">
        <form id="filter-form" [formGroup]="videoFilterForm" role="form" class="form">
          <!-- Filter VideoId Field - start -->
          <div class="form-group">
            <label class="form-control-label" for="filter_videoId"><strong>Video Title / JSON Title</strong></label>
            <input class="form-control" type="text" formControlName="videoTitleJsonTitle" id="filter_videoId"
              autocomplete="off" />
            <div
              *ngIf="(videoFilterForm.get('videoTitleJsonTitle').touched || videoFilterForm.get('videoTitleJsonTitle').dirty) && videoFilterForm.get('videoTitleJsonTitle').invalid ">
              <div *ngIf="videoFilterForm.get('videoTitleJsonTitle').errors['maxlength']" class="validation">
                <span class="alert-color font-12">{{textBoxMaxCharactersAllowedMessage}}</span>
              </div>
              <div *ngIf="videoFilterForm.get('videoTitleJsonTitle').errors['pattern']" class="validation">
                <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
              </div>
            </div>
          </div>
          <!-- Filter VideoId Field - end -->
          <!-- Filter jsonVer Field - start -->
          <div class="form-group">
            <label class="form-control-label" for="filter_jsonVer"><strong>Video Id / JSON Version</strong></label>
            <input class="form-control" type="text" formControlName="videoIdJsonVer" id="filter_jsonVer"
              autocomplete="off" />
            <div
              *ngIf="(videoFilterForm.get('videoIdJsonVer').touched || videoFilterForm.get('videoIdJsonVer').dirty) && videoFilterForm.get('videoIdJsonVer').invalid ">
              <div *ngIf="videoFilterForm.get('videoIdJsonVer').errors['maxlength']" class="validation">
                <span class="alert-color font-12">{{textBoxMaxCharactersAllowedMessage}}</span>
              </div>
              <div *ngIf="videoFilterForm.get('videoIdJsonVer').errors['pattern']" class="validation">
                <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
              </div>
            </div>
          </div>
          <!-- Filter jsonVer Field - end -->
          <hr class="mt-1 mb-2">
          <div class="">
            <button class="btn btn-sm btn-orange mr-3" (click)="searchVideoFilter()" id="serachFilterButton"
              [disabled]="(!isSelectColumnHidden) || videoFilterForm.invalid?'disabled':null">Search</button>
            <button class="btn btn-sm btn-orange" (click)="clearFilter()"
              [disabled]="(!isSelectColumnHidden)?'disabled':null">Clear</button>
          </div>
        </form>
      </div>
    </div>
  </div>
  <!------------------------------------------->
  <!--Filter End-->
  <!------------------------------------------->
  <div [className]="(isFilterHidden)?'col-md-12 pr-0':'col-md-9 pr-0'">

    <div class="container-fluid">
      <!------------------------------------------->
      <!--view data member Start-->
      <!------------------------------------------->
      <div>
        <div class="row" class="headerAlignment">
          <!--------------------------------------->
          <!--Left Side-->
          <!--------------------------------------->
          <div class="childFlex">
            <!-- hide Show FilterButton start -->
            <div class="dropdown" id="videoDetailHideShowFilter">
              <button class="btn btn-sm btn-orange mr-3 ml-0 filter-symbol" (click)="toggleFilter()"
                [id]="(isFilterHidden)?'Show Filter':'Hide Filter'">
                <i class="fas fa-filter" aria-hidden="true" [id]="(isFilterHidden)?'Show Filter':'Hide Filter'"></i>
                &nbsp;&nbsp;{{ hideShowFilterButtonText }}
              </button>
            </div>
            <!-- hide Show FilterButton end -->
            <!-- show entry div start -->
            <div>
              <label class="mb-0">Show entry</label>
              <!-- selection of page entry start -->
              <select id="videoDetailShowEntry" [(ngModel)]="drpselectsize" class="form-control form-control-sm"
                (change)="changeDataSize($event)">
                <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                  <option [value]="dataSize">{{ dataSize }}</option>
                </ng-template>
              </select>
              <!-- selection of page entry end -->
            </div>
          </div>
          <!--------------------------------------->
          <!--Right Side-->
          <!--------------------------------------->
          <div class="childFlex">
            <!-- show entry div end -->

            <div *ngIf="addVideoPermission">
              <!------------ Create Json / Cancel Selecton - button - start ------------->
              <button class="btn btn-sm btn-orange mr-3" (click)="toggleSelectVideoRows($event)"
                *ngIf="videoIdListcollect.length==0"
                [id]="(isSelectColumnHidden)?'Create JSON File':'Cancel JSON Creation'">
                <em class="fa fa-plus" *ngIf="isSelectColumnHidden"></em>&nbsp;&nbsp;{{ HideShowSelectColumnButtonText
                }}
              </button>
              <!------------ Create Json / Cancel Selecton - button - end ------------->
              <!------------ Confirm Selecton - button - start ------------->
              <button *ngIf="videoIdListcollect.length>0" class="btn btn-sm btn-orange mr-3" id="AddJsonVideo"
                (click)="addOrUpdateJSONFile()">
                Confirm Selection</button>
              <!------------ Confirm Selecton - button - end ------------->
              <!------------ Add Video - button - start ------------->
              <button class="btn btn-sm btn-orange mr-3" (click)="uploadOrUpdateVideo()" id="uploadVideo"
                [disabled]="(!isSelectColumnHidden)?'disabled':null"><em class="fa fa-plus"></em>&nbsp;&nbsp;Add
                Video</button>
              <!------------ Add Video - button - end ------------->
            </div>
            <div> <!--Refresh button add start-->
              <button class="btn btn-sm btn-orange" (click)="clearFilter()"><em class="fa fa-refresh"
                  id="refresh_VideoList"></em></button>
              <!--Refresh button add end-->
            </div>
          </div>
        </div>
        <!--view data video Start-->
        <div>Total {{totalMember}} Videos/JSONs <p *ngIf="videoIdListcollect.length>0">
            <strong>{{videoIdListcollect.length}} Videos/JSONs selected</strong>
          </p>
        </div>
        <!-- Videos / Json list - start -->
        <div class="video-table">
          <table class="table table-sm table-bordered table-scroll" aria-hidden="true">
            <thead>
              <tr class="thead-light">
                <th *ngIf="(!isSelectColumnHidden)">
                  <span>Select</span>
                </th>
                <th><span class="text_nowrap">Video Title / JSON Title</span></th>
                <th><span class="text_nowrap">Video Id / JSON Version</span></th>
                <th><span class="text_nowrap">Effective Date & Time</span></th>
                <th><span>Attachment</span></th>
                <th><span>Notes</span></th>
                <th *ngIf="updateVideoPermission"><span>Edit</span></th>
                <th *ngIf="deleteVideoPermission"><span>Delete</span></th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let video of videosList;">
                <td *ngIf="(!isSelectColumnHidden)">
                  <div class="custom-control custom-checkbox" *ngIf="video.jsonId==null">
                    <input type="checkbox" class="custom-control-input" [checked]="defaultSelectVideo(video.videoId)"
                      name="memberdata[]" id="parentid{{video.parentId}}" (change)="videoIdList($event,video.videoId)">
                    <label class="custom-control-label" for="parentid{{video.parentId}}"></label>
                  </div>
                </td>
                <td><span>{{ video.title }}</span></td>
                <td><span>{{ video.name }}</span></td>
                <td><span>{{ video.date | date:"MM/dd/yyyy, h:mm:ss a" }}</span></td>
                <td>
                  <span>{{ video.attachment }}</span>
                  <button class="btn btn-sm" style="float: right;"
                    (click)="(video.jsonId==null)?downloadZipFile(video.videoId, video.attachment):downloadJSONFile(video.jsonId, video.attachment)"
                    id="downloadZipFile" [disabled]="(!isSelectColumnHidden)?'disabled':null">
                    <span class="Pointer video-filter"><em class="fa fa-download"></em></span>
                  </button>
                </td>
                <td class="maxWidth"><span>{{ video.notes }}</span></td>
                <td *ngIf="updateVideoPermission">
                  <button class="btn btn-sm" id="editVideoJson"
                    (click)="(video.jsonId==null)?uploadOrUpdateVideo(video.videoId, video.editable):addOrUpdateJSONFile(video)"
                    [disabled]="(!isSelectColumnHidden)?'disabled':null" style="text-align: center;">
                    <span class="Pointer video-filter"><em class="fa fa-pencil-alt"></em></span>
                  </button>
                </td>
                <td *ngIf="deleteVideoPermission">
                  <button class="btn btn-sm" id="deleteVideoAndJson{{video.videoId}}"
                    (click)="(video.jsonId==null)?deleteVideo(video.videoId):deleteJson(video.jsonId)"
                    [disabled]="(!isSelectColumnHidden)?'disabled':null" style="text-align: center;">
                    <em class="fas fa-trash-alt"></em>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <!-- Videos / Json list - end -->
        <div>
          <div>Showing {{totalMemberDisplay}} out of {{totalMember}} Videos/JSONs</div>
          <div class="float-right">
            <!--------Pagination - start----------->
            <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="itemsPerPage" [maxSize]="5"
              id="video-pagination" [rotate]="true" [boundaryLinks]="true" (pageChange)="loadPage(page)">
            </ngb-pagination>
            <!--------Pagination - end----------->
          </div>
        </div>
      </div>
      <!------------------------------------------->
      <!--view data video End-->
      <!------------------------------------------->

      <!--flud tab 9 row-->
    </div>
  </div>
</div>
<!--flud tab 9 row-->