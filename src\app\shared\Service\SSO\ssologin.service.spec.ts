import { TestBed } from '@angular/core/testing';
import { SSOLoginService } from './ssologin.service';
import { ConfigInjectService } from '../../InjectService/config-inject.service';

describe('SSOLoginService', () => {
  let service: SSOLoginService;
  let configServiceSpy: jasmine.SpyObj<ConfigInjectService>;

  beforeEach(() => {
    const spy = jasmine.createSpyObj('ConfigInjectService', ['getSSOServerApiUrl', 'getSSORegistrationId']);

    TestBed.configureTestingModule({
      providers: [
        { provide: ConfigInjectService, useValue: spy }
      ]
    });

    configServiceSpy = TestBed.inject(ConfigInjectService) as jasmine.SpyObj<ConfigInjectService>;
    configServiceSpy.getSSOServerApiUrl.and.returnValue('https://sso.example.com/');
    configServiceSpy.getSSORegistrationId.and.returnValue('microsoft');

    service = TestBed.inject(SSOLoginService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

});
