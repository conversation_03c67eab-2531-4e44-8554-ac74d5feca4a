import { CountryListResponse } from "../Country/CountryListResponse.model";

export class UserResponse {
    id: number;
    login: string;
    firstName: string;
    lastName: string;
    email: string;
    createdDate: number;
    modifiedDate: number;
    createdBy: number;
    modifiedBy: number;
    lastLoggedIn: number;
    userRoles: Array<string>;
    countryMasters: Array<CountryListResponse>;
    deleted: boolean;
    modules: Array<string>;


    constructor($id: number, $login: string, $firstName: string, $lastName: string,//NOSONAR
        $email: string, $createdDate: number, $modifiedDate: number,
        $createdBy: number, $modifiedBy: number, $lastLoggedIn: number,
        $userRoles: Array<string>, $countryMasters: Array<CountryListResponse>,
        $deleted: boolean, $modules: Array<string>) {
        this.id = $id;
        this.login = $login;
        this.firstName = $firstName;
        this.lastName = $lastName;
        this.email = $email;
        this.createdDate = $createdDate;
        this.modifiedDate = $modifiedDate;
        this.createdBy = $createdBy;
        this.modifiedBy = $modifiedBy;
        this.lastLoggedIn = $lastLoggedIn;
        this.userRoles = $userRoles;
        this.countryMasters = $countryMasters;
        this.deleted = $deleted;
        this.modules = $modules;
    }

}