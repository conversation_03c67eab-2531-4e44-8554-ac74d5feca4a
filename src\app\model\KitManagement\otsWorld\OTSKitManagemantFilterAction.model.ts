import { ListingPageReloadSubjectParameter } from "../../common/listingPageReloadSubjectParameter.model";
import { OTSKitManagemantSearchRequestBody } from "./OTSKitManagemantSearchRequestBody.model";

export class OTSKitManagemantFilterAction {
    listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter;
    otsKitManagemantSearchRequestBody: OTSKitManagemantSearchRequestBody;


    constructor($listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, $otsKitManagemantSearchRequestBody: OTSKitManagemantSearchRequestBody) {
        this.listingPageReloadSubjectParameter = $listingPageReloadSubjectParameter;
        this.otsKitManagemantSearchRequestBody = $otsKitManagemantSearchRequestBody;
    }

}