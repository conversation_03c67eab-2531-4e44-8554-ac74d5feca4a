import { BaseResponse } from "./common/BaseResponse.model";

export class MultiSelectDropDownRequest extends BaseResponse {

    isDisabled: boolean;

    /**
     * Creates an instance of MultiSelectDropDownRequest model.
     * 
     * <AUTHOR>
     * @param $id 
     * @param $name 
     * @param $displayName 
     * @param $isDisabled 
     */
    constructor($id: number, $name: string, $displayName: string, $isDisabled: boolean) {
        super($id, $name, $displayName);
        this.isDisabled = $isDisabled;
    }

}