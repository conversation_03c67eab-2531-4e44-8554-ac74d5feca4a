import { Inject, Injectable } from '@angular/core';
import { API_BASE_URL, API_SSO_BASE_URL, RDM_ENVIRONMENT, RDM_VERSION, SSO_REGISTRATION_ID, } from '../config';

@Injectable({
  providedIn: 'root'
})
export class ConfigInjectService {

  serverApiUrl: string;
  ssoServerApiUrl: string;
  ssoRegistrationId: string;
  rdmVersion: string;
  rdmEnvironment: string;

  constructor(@Inject(API_BASE_URL) public apiBaseUrl: string,
    @Inject(RDM_VERSION) public rdmFrontendVersion: string,
    @Inject(API_SSO_BASE_URL) public ssoBaseUrl: string,
    @Inject(SSO_REGISTRATION_ID) public registrationIdForSSO: string,
    @Inject(RDM_ENVIRONMENT) public rdmEnviromentstage: string) {
    this.serverApiUrl = apiBaseUrl;
    this.rdmVersion = rdmFrontendVersion;
    this.ssoServerApiUrl = ssoBaseUrl;
    this.ssoRegistrationId = registrationIdForSSO;
    this.rdmEnvironment = rdmEnviromentstage;
  }

  /**
   * Read Json Base Path in config.json
   * <AUTHOR>
   * @returns base Url
   */
  public getServerApiUrl(): string {
    return this.serverApiUrl;
  }

  /**
   * Get Rdm version
   * 
   * <AUTHOR>
   * @returns 
   */
  public getRDMVersion(): string {
    return this.rdmVersion;
  }

  /**
   * SSO Server API URL
   * 
   * <AUTHOR>
   * @returns 
   */
  public getSSOServerApiUrl(): string {
    return this.ssoServerApiUrl;
  }

  /**
   * SSO Registration Id
   * 
   * <AUTHOR>
   * @returns 
   */
  public getSSORegistrationId(): string {
    return this.ssoRegistrationId;
  }


  /**
    * Get Rdm Environment
    * 
    * <AUTHOR>
    * @returns 
    */
  public getRDMEnvironment(): string {
    return this.rdmEnvironment;
  }

}

