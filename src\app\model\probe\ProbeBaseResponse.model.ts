export class ProbeBaseResponse {
    id: number;
    type: string;
    serialNumber: string;
    partNumber: string;
    probeVersion: string;
    lastConnectedTime: number;
    licenseDate: number;
    reminder: boolean;
    licenseModifiedBy: string;
    countryId: number;
    customerName: string;
    customerEmail: string;
    salesOrderNumber: string;
    salesOrderId: number;
    modifiedDate: number;
    orderRecordType: string;
    country: string;
    locked: boolean;
    editable: boolean;
    poNumber: string;
    deviceAutoLock: boolean;
    probeAutoLock: boolean;
    soStatus: string;

    constructor(//NOSONAR
        id: number,
        type: string,
        serialNumber: string,
        partNumber: string,
        probeVersion: string,
        lastConnectedTime: number,
        licenseDate: number,
        reminder: boolean,
        licenseModifiedBy: string,
        countryId: number,
        customerName: string,
        customerEmail: string,
        salesOrderNumber: string,
        modifiedDate: number,
        country: string,
        locked: boolean,
        editable: boolean,
        poNumber: string,
        deviceAutoLock: boolean,
        probeAutoLock: boolean,
        orderRecordType: string
    ) {
        this.id = id;
        this.type = type;
        this.serialNumber = serialNumber;
        this.partNumber = partNumber;
        this.probeVersion = probeVersion;
        this.lastConnectedTime = lastConnectedTime;
        this.licenseDate = licenseDate;
        this.reminder = reminder;
        this.licenseModifiedBy = licenseModifiedBy;
        this.countryId = countryId;
        this.customerName = customerName;
        this.customerEmail = customerEmail;
        this.salesOrderNumber = salesOrderNumber;
        this.modifiedDate = modifiedDate;
        this.country = country;
        this.locked = locked;
        this.editable = editable;
        this.poNumber = poNumber;
        this.deviceAutoLock = deviceAutoLock;
        this.probeAutoLock = probeAutoLock;
        this.orderRecordType = orderRecordType;
    }
}