import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, ValidatorFn, Validators } from '@angular/forms';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { CountryValidMessage, EMAIL_VALIDATION_PATTERN, EnterCustomerName, EnterSalesOrderNumber, EnterValidEmail, MANUAL_ORDER_CREATED, OREDER_RECORD_TYPE_MANDATORY, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH, SPECIAL_CHARACTER_PATTERN, SPECIAL_CHARACTER_ERROR_MESSAGE, MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, MAXIMUM_TEXTBOX_LENGTH } from 'src/app/app.constants';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { TransferProductDetails } from 'src/app/model/device/TransferProductDetails.model';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';
import { BasicSalesOrderDetailResponse } from 'src/app/model/SalesOrder/BasicSalesOrderDetailResponse.model';
import { ManualSalesOrderResponse } from 'src/app/model/SalesOrder/ManualSalesOrderResponse.model';
import { TranferOrderSelection } from 'src/app/model/SalesOrder/TranferOrderSelection.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';

@Component({
  selector: 'app-transfer-order-selection',
  templateUrl: './transfer-order-selection.component.html',
  styleUrl: './transfer-order-selection.component.css',
  encapsulation: ViewEncapsulation.None
})
export class TransferOrderSelectionComponent implements OnInit {
  @Input("transferProductDetail") transferProductDetail: TransferProductDetails;
  @Output("backToDetailPage") backToDetailPage = new EventEmitter;
  @Output() showTranferOrder = new EventEmitter<[boolean, boolean, boolean]>();
  @Output() destinationSalesOrderIdChange = new EventEmitter<number>();

  //multi Select DropDown Setting
  orderTypeSetting: MultiSelectDropdownSettings = null;
  countrySetting: MultiSelectDropdownSettings = null;
  orderRecordTypeSetting: MultiSelectDropdownSettings = null;

  requiredValidator: ValidatorFn = Validators.required;
  maxLengthValidator: ValidatorFn = Validators.maxLength(MAXIMUM_TEXTBOX_LENGTH);
  emailValidatorPattern: ValidatorFn = Validators.pattern(EMAIL_VALIDATION_PATTERN);

  destinationSalesOrderId: number = null;
  loading: boolean = false;

  transferOrderEndUserSelectionDisaplay: boolean = true;
  transferOrderSelectionDisplay: boolean = false;

  isSalesOrderDisabled: boolean = false;

  endUserOrderId: number = null;
  salesOrderList: Array<TranferOrderSelection> = [];
  deviceSerialNumber: string;
  countriesList: CountryListResponse[] = [];
  orderRecordTypeList: Array<string> = [];
  //Error Message display
  enterSalesOrderNumber: string = EnterSalesOrderNumber;
  small_maxCharactersAllowedMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;
  enterCustomerName: string = EnterCustomerName;
  maxCharactersAllowedMessage: string = MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  countryValidMessage: string = CountryValidMessage;
  orderRecordTypeValidationMessage: string = OREDER_RECORD_TYPE_MANDATORY;
  enterValidEmail: string = EnterValidEmail;

  constructor(
    private readonly multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private readonly salesOrderApiCallService: SalesOrderApiCallService,
    private readonly commonsService: CommonsService,
    private readonly countryCacheService: CountryCacheService,
    private readonly exceptionService: ExceptionHandlingService,
    private readonly toastrService: ToastrService
  ) { }

  transferOrderForm = new FormGroup({
    salesOrderNumber: new FormControl([], [this.requiredValidator, this.commonsService.removeSpacesThrowError()]),
    manualSalesOrderNumber: new FormControl('', [this.commonsService.removeSpacesThrowError(), Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    countries: new FormControl(null, []),
    customerName: new FormControl('', [this.commonsService.removeSpacesThrowError(), Validators.maxLength(MAXIMUM_TEXTBOX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    poNumber: new FormControl('', [this.commonsService.removeSpacesThrowError(), Validators.maxLength(MAXIMUM_TEXTBOX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    customerEmail: new FormControl(null, [this.emailValidatorPattern, this.maxLengthValidator]),
    deviceAutoLock: new FormControl(false, []),
    probeAutoLock: new FormControl(false, []),
    orderRecordType: new FormControl(null, []),
  });

  /**
  * Lifecycle hook - Initializes component data
  * Fetches dropdown settings and transfer order records from API
  */
  public ngOnInit(): void {
    this.loading = true;
    this.orderTypeSetting = this.multiSelectDropDownSettingService.getTransferOrderSelectionDrpSetting();
    this.countrySetting = this.multiSelectDropDownSettingService.getCountryDrpSetting(true, false);
    this.orderRecordTypeSetting = this.multiSelectDropDownSettingService.getCreateOrderRecordTypeDrpSetting();
    this.deviceSerialNumber = isNullOrUndefined(this.transferProductDetail?.serialNumber) ? "" : " - " + this.transferProductDetail?.serialNumber;
    this.initData();
  }

  /**
  * Order Record Type Data
  * 
  * <AUTHOR>
  */
  public async initData(): Promise<void> {
    this.countriesList = await this.countryCacheService.getCountryListFromCache();
    this.orderRecordTypeList = (await this.salesOrderApiCallService.getOrderRecordNumberList(true));
    await this.salesOrderNumber();
    this.loading = false;
  }

  /**
  * Fetches sales order numbers for transfer orders
  * If no sales orders are found, navigates back to the detail page
  * <AUTHOR>
  * @returns {Promise<void>}
  */
  public async salesOrderNumber(): Promise<void> {
    this.salesOrderList = await this.salesOrderApiCallService.getTranferOrderList();
    if (isNullOrUndefined(this.salesOrderList)) {
      this.back();
    }
  }

  /**
  * field mark as touched
  * @param fieldName 
  */
  public onItemClickValidation(fieldName: string): void {
    if (this.transferOrderForm.invalid) {
      this.transferOrderForm.get(fieldName).markAsTouched();
    }
  }

  /**
  * Sales Order Details page
  * 
  * <AUTHOR>
  */
  public back(): void {
    this.backToDetailPage.emit();
  }

  /**
  * Toggles visibility of the selection tables
  */
  public showSelectionTable(): void {
    if (this.isSalesOrderDisabled) {
      this.accept();
    } else {
      this.destinationSalesOrderIdChange.emit(this.destinationSalesOrderId);
      this.showTranferOrder.emit([false, true, false]);
    }
  }

  /**
  * Handles selection of an end-user order
  * @param selectedItem - The selected item containing order details
  */
  public enduserSeletion(selectedItem: any): void {
    this.destinationSalesOrderId = selectedItem.id;
  }

  /**
  * Handles deselection of an end-user order
  * @param selectedItem - The deselected item
  */
  public endUserOrderDeSelect(selectedItem: any): void {
    this.destinationSalesOrderId = selectedItem;
  }

  /**
  * open sales order number text field
  * 
  * <AUTHOR>
  */
  public openManualSalesOrderField(): void {
    this.isSalesOrderDisabled = true;
    this.transferOrderForm.reset();
    this.setOrRemoveValidator(this.transferOrderForm.get("salesOrderNumber"), false);
    this.setOrRemoveValidator(this.transferOrderForm.get("manualSalesOrderNumber"), true);
    this.setOrRemoveValidator(this.transferOrderForm.get("countries"), true);
    this.setOrRemoveValidator(this.transferOrderForm.get("customerName"), true);
    this.setOrRemoveValidator(this.transferOrderForm.get("orderRecordType"), true);
  }

  /**
  * open sales order number text field
  * 
  * <AUTHOR>
  */
  public closeManualSalesOrderField(): void {
    this.isSalesOrderDisabled = false;
    this.transferOrderForm.reset();
    this.setOrRemoveValidator(this.transferOrderForm.get("salesOrderNumber"), true);
    this.setOrRemoveValidator(this.transferOrderForm.get("manualSalesOrderNumber"), false);
    this.setOrRemoveValidator(this.transferOrderForm.get("countries"), false);
    this.setOrRemoveValidator(this.transferOrderForm.get("customerName"), false);
    this.setOrRemoveValidator(this.transferOrderForm.get("orderRecordType"), false);
  }


  /**
  * set or remove validator of form field
  * 
  * <AUTHOR>
  * @param control 
  * @param isRequired 
  */
  private setOrRemoveValidator(control: AbstractControl, isRequired: boolean): void {
    let validators = [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), this.commonsService.removeSpacesThrowError()];
    if (isRequired) {
      validators.push(this.requiredValidator);
    }
    control.setValidators(validators);
    control.updateValueAndValidity();
  }

  /**
  * submit customer association form
  * 
  * <AUTHOR>
  */
  public accept(): void {
    let salesOrderNumber: string = this.commonsService.checkNullFieldValue(!isNullOrUndefined(this.transferOrderForm.value?.salesOrderNumber) ? this.transferOrderForm.value?.salesOrderNumber[0] : this.transferOrderForm.value?.manualSalesOrderNumber);
    let country = this.commonsService.checkNullFieldValue(this.transferOrderForm.value.countries);
    let basicSalesOrderDetailResponse: BasicSalesOrderDetailResponse = new BasicSalesOrderDetailResponse(salesOrderNumber,
      this.commonsService.checkNullFieldValue(this.transferOrderForm.value.customerName),
      this.commonsService.checkNullFieldValue(this.transferOrderForm.value.customerEmail),
      (country != null && country.length == 1) ? country[0].id : [],
      this.commonsService.checkNullFieldValue(this.transferOrderForm.value.poNumber),
      this.transferOrderForm.value.deviceAutoLock,
      this.transferOrderForm.value.probeAutoLock,
      this.transferOrderForm.value.orderRecordType[0]);

    this.createManualSalesOrder(basicSalesOrderDetailResponse);
  }

  /**
  * Create Manual Sales Order
  * 
  * @param datasetdialog - The selected item containing Manual Order details
  * <AUTHOR>
  */
  private createManualSalesOrder(datasetdialog: BasicSalesOrderDetailResponse): void {
    this.loading = true;
    this.salesOrderApiCallService.createManualSalesOrder(datasetdialog)?.subscribe({
      next: (res: HttpResponse<ManualSalesOrderResponse>) => {
        if (res?.status === 200) {
          this.loading = false;
          this.destinationSalesOrderId = res.body?.salesOrderId;
          this.toastrService.success(MANUAL_ORDER_CREATED);
          this.destinationSalesOrderIdChange.emit(this?.destinationSalesOrderId);
          this.showTranferOrder.emit([false, true, false]);
        }
      },
      error: (error: HttpErrorResponse) => {
        this.exceptionService.customErrorMessage(error);
        this.loading = false;
      }
    });
  }

}