<!-- loading start -->
<div class="ringLoading" *ngIf="loading">
    <!-- loading gif start -->
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
    <!-- loading gif end -->
</div>
<!-- loading end -->

<body class="bg-white" *ngIf="deviceDetailDisplay">

    <!-- main container start -->
    <div class="container-fluid">
        <!-- row start -->
        <div class="row">
            <div class="col-md-12">
                <!-- device detail header row start -->
                <div class="row headerAlignment">
                    <!-- device detail header start -->
                    <label class="childFlex h5-tag">Device Detail</label>
                    <!-- device detail header end -->

                    <div class="childFlex">
                        <!-- Device operation dropdown start -->
                        <!-- selection of Device operation start -->
                        <select id="deviceOperation" class="form-control form-control-sm mr-3"
                            (change)="changeDeviceOperation($event)" *ngIf="deviceOperations.length > 1">
                            <ng-template ngFor let-deviceOperation [ngForOf]="deviceOperations">
                                <option [value]="deviceOperation">
                                    {{deviceOperation }}
                                </option>
                            </ng-template>
                        </select>
                        <!-- selection of Device operation end -->
                        <!--Device operation dropdown end  -->
                        <!-- back button start -->
                        <button class="btn btn-sm btn-outline-secondary device-back-btn" (click)="back()"><i
                                class="fa fa-reply" aria-hidden="true"></i>&nbsp;&nbsp;{{backBtnText}}</button>
                        <!-- back button end -->
                        <!-- refresh button start -->
                        <button class="btn btn-sm btn-orange ml-2" (click)="refreshDeviceDetailPage()"
                            id="refreshDeviceDetailPage"><em class="fa fa-refresh"></em></button>
                        <!-- refresh button end -->
                    </div>
                </div>
                <!-- device detail header row end -->
                <!-- device detail fields row start -->
                <div class="row">
                    <div class="col-md-12">
                        <!-- main card start -->
                        <div class="card">
                            <div class="card-body">
                                <div class="card shadow">
                                    <div class="card-body">

                                        <div class="row">
                                            <!-- hardware Id field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{hwId}}</strong></label>
                                                    <!-- hardware field input start -->
                                                    <input type="text" class="form-control" name="deviceId"
                                                        [value]="deviceDetailResponse?.deviceId" required readonly>
                                                    <!-- hardware field input end -->


                                                </div>
                                            </div>

                                            <!----------------------------->
                                            <!-- macAddress  field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{macAddress}}</strong></label>
                                                    <input type="text" class="form-control" name="macAddress"
                                                        [value]="deviceDetailResponse?.macAddress" readonly>

                                                </div>
                                            </div>
                                            <!-- macAddress  field end -->
                                            <!----------------------------->

                                            <!-- hardware Id field end -->
                                            <!-- connection state field start -->
                                            <div class="col-md-3">
                                                <!-- conection state form group -->
                                                <div class="form-group">
                                                    <label><strong class="">{{connectionState}}</strong></label>
                                                    <!-- connnection state inout start -->
                                                    <input type="text" class="form-control" name="connectionState"
                                                        [value]="deviceDetailResponse?.connectionState" readonly>
                                                    <!-- connnection state inout end -->

                                                </div>
                                            </div>
                                            <!-- connection state field end -->
                                            <!-- created date field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{createDateAndTime}}</strong></label>
                                                    <!-- created date input start -->
                                                    <input type="text" class="form-control" name="createdDate"
                                                        [value]="deviceDetailResponse?.createdDate | date:'MMM d, y, h:mm:ss a'"
                                                        readonly>
                                                    <!-- created date input end -->



                                                </div>
                                            </div>
                                            <!-- created date field end -->
                                            <!-- modified date field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{modifyDateAndTime}}</strong></label>
                                                    <!-- modified date input start -->
                                                    <input type="text" class="form-control" name="modifiedDate"
                                                        [value]="deviceDetailResponse?.modifiedDate |date:'MMM d, y, h:mm:ss a'"
                                                        readonly>
                                                    <!-- modified date input end -->
                                                </div>
                                            </div>
                                            <!-- modified date field end -->

                                            <!-- last checkIn time field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{lastCheckinDataAndType}}</strong></label>
                                                    <!-- last checkIn time input start -->
                                                    <input type="text" class="form-control" name="lastCheckInTime"
                                                        [value]="deviceDetailResponse?.lastCheckInTime | date:'MMM d, y, h:mm:ss a'"
                                                        readonly>
                                                    <!-- last checkIn time input end -->
                                                </div>
                                            </div>
                                            <!-- last checkIn time field end -->

                                            <!-- time zone field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{timeZone}}</strong></label>
                                                    <!-- time zone input start -->
                                                    <input type="text" class="form-control" name="timeZone"
                                                        [value]="deviceDetailResponse?.timezone" readonly>
                                                    <!-- time zone input end -->
                                                </div>
                                            </div>
                                            <!-- time zone field end -->
                                            <!-- device type field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{deviceType}}</strong></label>
                                                    <!-- device type input start -->
                                                    <input type="text" class="form-control" name="lastCheckInTime"
                                                        [value]="deviceDetailResponse?.deviceType | deviceTypeName"
                                                        readonly>
                                                    <!-- device type input end -->
                                                </div>
                                            </div>
                                            <!-- device type field end -->

                                            <!-- system software version field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{systemSwVerstion}}</strong></label>
                                                    <!-- system software version input start -->
                                                    <input type="text" class="form-control" name="packageVersion"
                                                        [value]="deviceDetailResponse?.packageVersion" readonly>
                                                    <!-- system software version input end -->
                                                </div>
                                            </div>
                                            <!-- system software version field end -->
                                            <!-- probe version field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{probeVersion}}</strong></label>
                                                    <!-- probe version input start -->
                                                    <input type="text" class="form-control" name="probeVersion"
                                                        [value]="deviceDetailResponse?.probeVersion" readonly>
                                                    <!-- probe version input start -->
                                                </div>
                                            </div>
                                            <!-- probe version field end -->
                                            <!-- handle version field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{handleVersion}}</strong></label>
                                                    <!-- handle version input start -->
                                                    <input type="text" class="form-control" name="handleVersion"
                                                        [value]="deviceDetailResponse?.handleVersion" readonly>
                                                    <!-- handle version input end -->
                                                </div>
                                            </div>
                                            <!-- handle version field end -->
                                            <!-- pims field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{pimsVersion}}</strong></label>
                                                    <input type="text" class="form-control" name="pimsDbVersion"
                                                        [value]="deviceDetailResponse?.pimsDbVersion" required readonly>
                                                </div>
                                            </div>
                                            <!-- pims field end -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{settingVersion}}</strong></label>
                                                    <input type="text" class="form-control" name="settingsDbVersion"
                                                        [value]="deviceDetailResponse?.settingsDbVersion" required
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{upsVersion}}</strong></label>
                                                    <input type="text" class="form-control" name="thorDbVersion"
                                                        [value]="deviceDetailResponse?.thorDbVersion" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{appVersion}}</strong></label>
                                                    <input type="text" class="form-control"
                                                        name="androidApplicationVersion"
                                                        [value]="deviceDetailResponse?.androidApplicationVersion"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{jsonVersion}}</strong></label>
                                                    <input type="text" class="form-control" name="jsonVersion"
                                                        [value]="deviceDetailResponse?.jsonVersion" readonly>
                                                </div>
                                            </div>
                                            <!-- country field -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{country}}</strong></label>
                                                    <input type="text" class="form-control" name="country"
                                                        [value]="deviceDetailResponse?.country" readonly>
                                                </div>
                                            </div>

                                            <!-- Order Record Type field -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{orderRecordType}}</strong></label>
                                                    <input type="text" class="form-control" name="orderRecordType"
                                                        [value]="deviceDetailResponse?.orderRecordType" readonly>
                                                </div>
                                            </div>

                                            <!-- device serial no field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{serialNo}}</strong></label>
                                                    <input type="text" class="form-control" name="deviceSerialNo"
                                                        [value]="deviceDetailResponse?.deviceSerialNo" required
                                                        readonly>
                                                </div>
                                            </div>
                                            <!-- device serial no field end -->
                                            <!-- device part no field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{partNo}}</strong></label>
                                                    <input type="text" class="form-control" name="devicePartNo"
                                                        [value]="deviceDetailResponse?.devicePartNo" required readonly>
                                                </div>
                                            </div>
                                            <!-- device serial no field end -->

                                            <!-- sales order number field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{salesOrderNumber}}</strong></label>
                                                    <input type="text" class="form-control" name="soNumber"
                                                        [value]="deviceDetailResponse?.salesOrderNumber" readonly>
                                                </div>
                                            </div>
                                            <!-- sales order number field end -->

                                            <!-- po number field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{po_no}}</strong></label>
                                                    <input type="text" class="form-control" name="poNumber"
                                                        [value]="deviceDetailResponse?.poNumber" readonly>
                                                </div>
                                            </div>
                                            <!-- po number field end -->

                                            <!-- customer Name field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{customerName}}</strong></label>
                                                    <input type="text" class="form-control" name="customerName"
                                                        [value]="deviceDetailResponse?.customerName" readonly>
                                                </div>
                                            </div>
                                            <!-- customer Name field end -->

                                            <!-- customer email field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{customerEmail}}</strong></label>
                                                    <input type="text" class="form-control" name="customerEmail"
                                                        [value]="deviceDetailResponse?.customerEmail" readonly>
                                                </div>
                                            </div>
                                            <!-- customer email field end -->

                                            <!------------------------------------>
                                            <!-- Enabled / Disabled field start -->
                                            <!------------------------------------>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{status}}</strong></label>
                                                    <input type="text" class="form-control" name="productStatus"
                                                        [value]="deviceDetailResponse?.productStatus | enumMappingDisplayNamePipe:productStatusList"
                                                        required readonly>
                                                </div>
                                            </div>
                                            <!------------------------------------>
                                            <!-- Enabled / Disabled field end -->
                                            <!------------------------------------>

                                            <!------------------------------------>
                                            <!-- Lock / Unlock field start -->
                                            <!------------------------------------>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{locked}}</strong></label>
                                                    <input type="text" class="form-control" name="Locked"
                                                        [value]="deviceDetailResponse?.locked | booleanKeyValueMappingDisplayNamePipe:lockUnlockStatus"
                                                        required readonly>
                                                </div>
                                            </div>
                                            <!------------------------------------>
                                            <!-- Lock / Unlock field end -->
                                            <!------------------------------------>

                                            <!------------------------------------>
                                            <!-- Edit Enable / Disable field start -->
                                            <!------------------------------------>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{editable}}</strong></label>
                                                    <!-- device Editable input start -->
                                                    <input type="text" class="form-control" name="Edit"
                                                        [value]="deviceDetailResponse?.editable | booleanKeyValueMappingDisplayNamePipe:editEnableDisableStatus"
                                                        readonly>
                                                    <!-- device Editable input end -->
                                                </div>
                                            </div>
                                            <!------------------------------------>
                                            <!-- Edit Enable / Disable field start -->
                                            <!------------------------------------>
                                        </div>
                                        <div class="row"
                                            *ngIf="((deviceDetailResponse?.deviceType | deviceTypeName) == 'Test Device') && updateDeviceTypePermission">
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{releaseVersion}}</strong></label>
                                                    <select id="releaseVersion" class="form-control"
                                                        (change)="changeReleaseVersion($event)">
                                                        <option value="-1">Select
                                                            Release Version</option>
                                                        <option *ngFor="let item of releaseVersions" [value]="item.id"
                                                            [selected]="item.id == selectedReleaseVersion">
                                                            {{item.itemNumber}}</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group" style="margin-top: 1.7rem;">
                                                    <button class="btn btn-sm btn-orange mr-3 form-control"
                                                        id="assignButton" (click)="assignReleaseVersion()"
                                                        [disabled]="btnReleaseVersionDisable">
                                                        {{assignReleaseVersionBtnText}}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <!--form end-->
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- main card end -->
                    </div>


                </div>
                <!-- device detail fields row end -->
            </div>
        </div>
        <!-- row end -->
    </div>
    <!-- main container end -->
</body>


<!------------------------------------------------------------->
<!----------- Tranfer Order Module Start ---------------------->
<!------------------------------------------------------------->
<ng-template [ngIf]="transferOrderSelectionDisaplay">
    <app-transfer-order-module (showTranferOrder)="transferOrderSelectionToggle(true,false)"
        [transferProductDetail]="deviceDetailsTrasferProduct">
    </app-transfer-order-module>
</ng-template>
<!------------------------------------------------------------>
<!--------- Tranfer Order Module End-------------------------->
<!------------------------------------------------------------>