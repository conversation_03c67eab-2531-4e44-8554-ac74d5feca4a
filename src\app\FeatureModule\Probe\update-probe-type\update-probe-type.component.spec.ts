import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { of, throwError } from 'rxjs';
import { ProbeTypeResponse } from 'src/app/model/probe/probeTypeResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { UpdateProbeTypeComponent } from './update-probe-type.component';

describe('UpdateProbeTypeComponent', () => {
  let component: UpdateProbeTypeComponent;
  let fixture: ComponentFixture<UpdateProbeTypeComponent>;

  let probeApiServiceSpy: jasmine.SpyObj<ProbeApiService>;
  let toastrSpy: jasmine.SpyObj<ToastrService>;
  let exceptionServiceSpy: jasmine.SpyObj<ExceptionHandlingService>;
  let commonOperationsServiceSpy: jasmine.SpyObj<CommonOperationsService>;
  let modalSpy: jasmine.SpyObj<NgbActiveModal>;

  beforeEach(async () => {
    probeApiServiceSpy = jasmine.createSpyObj('ProbeApiService', [
      'getprobeTypeResponseList',
      'probeTypeUpdate',
    ]);
    toastrSpy = jasmine.createSpyObj('ToastrService', ['success']);
    exceptionServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    commonOperationsServiceSpy = jasmine.createSpyObj('CommonOperationsService', ['callCommonLoadingSubject']);
    modalSpy = jasmine.createSpyObj('NgbActiveModal', ['close']);

    await TestBed.configureTestingModule({
      declarations: [UpdateProbeTypeComponent],
      imports: [ReactiveFormsModule],
      providers: [
        { provide: ProbeApiService, useValue: probeApiServiceSpy },
        { provide: ToastrService, useValue: toastrSpy },
        { provide: ExceptionHandlingService, useValue: exceptionServiceSpy },
        { provide: CommonOperationsService, useValue: commonOperationsServiceSpy },
        CommonsService,
        { provide: NgbActiveModal, useValue: modalSpy },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(UpdateProbeTypeComponent);
    component = fixture.componentInstance;
    component.probeType = 'TYPE_1';
    component.probeIdList = [1, 2];
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call initCall on ngOnInit and populate form', fakeAsync(() => {
    const response: ProbeTypeResponse[] = [
      { probeTypeId: 1, displayName: 'TYPE_1' } as ProbeTypeResponse,
      { probeTypeId: 2, displayName: 'TYPE_2' } as ProbeTypeResponse
    ];

    probeApiServiceSpy.getprobeTypeResponseList.and.resolveTo(response);

    component.ngOnInit();
    tick();

    expect(component.probeTypeResponse.length).toBe(2);
    expect(component.probeForm.get('probeType').value).toBe('1');
    expect(probeApiServiceSpy.getprobeTypeResponseList).toHaveBeenCalled();
  }));

  it('should validate and mark form dirty if probeType is -1', () => {
    component.probeForm.get('probeType').setValue('-1');
    component.updateValidatation();

    const errors = component.probeForm.get('probeType').errors;
    expect(errors).toEqual({ required: true });
    expect(component.probeForm.get('probeType').dirty).toBeTrue();
  });

  it('should call decline and close modal with false', () => {
    component.decline();
    expect(modalSpy.close).toHaveBeenCalledWith(false);
  });

  it('should not call API if selectedType is -1 in accept()', () => {
    component.probeForm.get('probeType').setValue('-1');
    component.probeTypeResponse = [{ probeTypeId: 1, displayName: 'TYPE_1' } as ProbeTypeResponse];
    component.accept();

    expect(probeApiServiceSpy.probeTypeUpdate).not.toHaveBeenCalled();
    expect(modalSpy.close).not.toHaveBeenCalled();
  });

  it('should call API and close modal on valid accept()', () => {
    component.probeTypeResponse = [{ probeTypeId: 1, displayName: 'TYPE_1' } as ProbeTypeResponse];
    component.probeForm.get('probeType').setValue('1');
    const httpResponse = new HttpResponse({
      body: { message: 'Success' },
      status: 200,
    });

    probeApiServiceSpy.probeTypeUpdate.and.returnValue(of(httpResponse));

    component.accept();

    expect(commonOperationsServiceSpy.callCommonLoadingSubject).toHaveBeenCalledWith(true);
    expect(probeApiServiceSpy.probeTypeUpdate).toHaveBeenCalled();
    expect(toastrSpy.success).toHaveBeenCalledWith('Success');
    expect(modalSpy.close).toHaveBeenCalledWith(true);
    expect(commonOperationsServiceSpy.callCommonLoadingSubject).toHaveBeenCalledWith(false);
  });

  it('should handle API error in accept()', () => {
    component.probeTypeResponse = [{ probeTypeId: 1, displayName: 'TYPE_1' } as ProbeTypeResponse];
    component.probeForm.get('probeType').setValue('1');

    const error = new HttpErrorResponse({ status: 500, error: 'Failed' });
    probeApiServiceSpy.probeTypeUpdate.and.returnValue(throwError(() => error));

    component.accept();

    expect(commonOperationsServiceSpy.callCommonLoadingSubject).toHaveBeenCalledWith(true);
    expect(exceptionServiceSpy.customErrorMessage).toHaveBeenCalledWith(error);
    expect(commonOperationsServiceSpy.callCommonLoadingSubject).toHaveBeenCalledWith(false);
  });
});
