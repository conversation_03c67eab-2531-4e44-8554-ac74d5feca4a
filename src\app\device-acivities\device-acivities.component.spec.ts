import { DatePipe } from '@angular/common';
import { HttpResponse } from '@angular/common/http';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { packageVersionResponse } from '../Tesing-Helper/TestDeviceInfo';
import { commonsProviders, testDropdownInteraction } from '../Tesing-Helper/test-utils';
import { INTERNAL_SERVER_ERROR, ITEMS_PER_PAGE } from '../app.constants';
import { ConfirmDialogService } from '../FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { JobDetailComponent } from '../job-detail/job-detail.component';
import { IJobchange } from '../model/jobchange.model';
import { ExceptionHandlingService } from '../shared/ExceptionHandling.service';
import { AuthJwtService } from '../shared/auth-jwt.service';
import { DeviceService } from '../shared/device.service';
import { JobService } from '../shared/Service/JobService/job.service';
import { PermissionService } from '../shared/permission.service';
import { PrintListPipe } from '../shared/pipes/printList.pipe';
import { CommonsService } from '../shared/util/commons.service';
import { DownloadService } from '../shared/util/download.service';
import { KeyValueMappingServiceService } from '../shared/util/key-value-mapping-service.service';
import { MultiSelectDropDownSettingService } from '../shared/util/multi-select-drop-down-setting.service';
import { DeviceAcivitiesComponent } from './device-acivities.component';

describe('DeviceAcivitiesComponent', () => {
  let component: DeviceAcivitiesComponent;
  let fixture: ComponentFixture<DeviceAcivitiesComponent>;
  let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;
  let authServiceMock: jasmine.SpyObj<AuthJwtService>;
  let permissionsServiceMock: jasmine.SpyObj<PermissionService>
  let multiSelectDropDownSettingService: MultiSelectDropDownSettingService;
  let keyValueMappingServiceService: KeyValueMappingServiceService;
  let deviceService: DeviceService;
  let jobService: JobService;
  let exceptionHandlingService: ExceptionHandlingService;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let commonservice: CommonsService;
  let getJobStatusMockResponse = [
    "CREATED",
    "RECEIVED",
    "DOWNLOAD_STARTED",
    "DOWNLOAD_SUCCESSFUL",
    "DOWNLOAD_FAILED",
    "DOWNLOAD_CANCELED",
    "DOWNLOAD_EXPIRED",
    "UPGRADE_STARTED",
    "UPGRADE_SUCCESSFUL",
    "UPGRADE_FAILED",
    "UPGRADE_CANCELED",
    "FINISHED",
    "ERROR",
    "EXPIRED"
  ];
  let getJobTypeMockResponse = ["VIDEO", "FIRMWARE"];

  let jobListResponse = {
    "content": [{
      "id": 97,
      "type": "FIRMWARE",
      "startDate": 1725606889996,
      "endDate": 1725617689996,
      "queryCriteria": "deviceId='292020005181800046'",
      "status": "EXPIRED",
      "createdDate": 1725606889996,
      "modifiedDate": null,
      "jobScheduleStatusId": 75,
      "deviceMasterId": "292020005181800046",
      "deviceMasterIdPk": 108,
      "createdBy": 1,
      "modifiedBy": 1
    }, {
      "id": 96,
      "type": "FIRMWARE",
      "startDate": 1725348159268,
      "endDate": 1725358959268,
      "queryCriteria": "deviceId='292020005181800046'",
      "status": "EXPIRED",
      "createdDate": 1725348159268,
      "modifiedDate": null,
      "jobScheduleStatusId": 74,
      "deviceMasterId": "292020005181800046",
      "deviceMasterIdPk": 108,
      "createdBy": 1,
      "modifiedBy": 1
    }, {
      "id": 95,
      "type": "FIRMWARE",
      "startDate": 1722330653689,
      "endDate": 1722341453689,
      "queryCriteria": "deviceId='292a20203132011733'",
      "status": "EXPIRED",
      "createdDate": 1722330653689,
      "modifiedDate": null,
      "jobScheduleStatusId": 73,
      "deviceMasterId": "292a20203132011733",
      "deviceMasterIdPk": 106,
      "createdBy": 1,
      "modifiedBy": 1
    }, {
      "id": 44,
      "type": "FIRMWARE",
      "startDate": 1699612035151,
      "endDate": 1699622835151,
      "queryCriteria": "deviceId='292a20205232013169'",
      "status": "EXPIRED",
      "createdDate": 1699612035151,
      "modifiedDate": null,
      "jobScheduleStatusId": 44,
      "deviceMasterId": "292a20205232013169",
      "deviceMasterIdPk": 2,
      "createdBy": 1,
      "modifiedBy": 1
    }, {
      "id": 43,
      "type": "FIRMWARE",
      "startDate": 1698214964124,
      "endDate": 1698225764124,
      "queryCriteria": "deviceId='292a20205232013256'",
      "status": "CREATED",
      "createdDate": 1698214964124,
      "modifiedDate": null,
      "jobScheduleStatusId": 43,
      "deviceMasterId": "292a20205232013256",
      "deviceMasterIdPk": 7,
      "createdBy": 1,
      "modifiedBy": 1
    }, {
      "id": 42,
      "type": "FIRMWARE",
      "startDate": 1698063708069,
      "endDate": 1698074508069,
      "queryCriteria": "deviceId='292a20205232013256'",
      "status": "EXPIRED",
      "createdDate": 1698063708069,
      "modifiedDate": null,
      "jobScheduleStatusId": 42,
      "deviceMasterId": "292a20205232013256",
      "deviceMasterIdPk": 7,
      "createdBy": 1,
      "modifiedBy": 1
    }, {
      "id": 41,
      "type": "FIRMWARE",
      "startDate": 1698063345811,
      "endDate": 1698074145811,
      "queryCriteria": "deviceId='292a20205232013256'",
      "status": "EXPIRED",
      "createdDate": 1698063345811,
      "modifiedDate": null,
      "jobScheduleStatusId": 41,
      "deviceMasterId": "292a20205232013256",
      "deviceMasterIdPk": 7,
      "createdBy": 1,
      "modifiedBy": 1
    }, {
      "id": 40,
      "type": "FIRMWARE",
      "startDate": 1698062263991,
      "endDate": 1698073063991,
      "queryCriteria": "deviceId='292a20205232013256'",
      "status": "EXPIRED",
      "createdDate": 1698062263991,
      "modifiedDate": null,
      "jobScheduleStatusId": 40,
      "deviceMasterId": "292a20205232013256",
      "deviceMasterIdPk": 7,
      "createdBy": 1,
      "modifiedBy": 1
    }, {
      "id": 39,
      "type": "FIRMWARE",
      "startDate": 1698061186792,
      "endDate": 1698071986792,
      "queryCriteria": "deviceId='292a20205232013256'",
      "status": "EXPIRED",
      "createdDate": 1698061186792,
      "modifiedDate": null,
      "jobScheduleStatusId": 39,
      "deviceMasterId": "292a20205232013256",
      "deviceMasterIdPk": 7,
      "createdBy": 1,
      "modifiedBy": 1
    }, {
      "id": 38,
      "type": "FIRMWARE",
      "startDate": 1698060947651,
      "endDate": 1698071747651,
      "queryCriteria": "deviceId='292a20205232013256'",
      "status": "EXPIRED",
      "createdDate": 1698060947651,
      "modifiedDate": null,
      "jobScheduleStatusId": 38,
      "deviceMasterId": "292a20205232013256",
      "deviceMasterIdPk": 7,
      "createdBy": 1,
      "modifiedBy": 1
    }],
    "pageable": {
      "pageNumber": 0,
      "pageSize": 10,
      "sort": {
        "empty": false,
        "sorted": true,
        "unsorted": false
      },
      "offset": 0,
      "paged": true,
      "unpaged": false
    },
    "last": false,
    "totalPages": 5,
    "totalElements": 47,
    "size": 10,
    "number": 0,
    "sort": {
      "empty": false,
      "sorted": true,
      "unsorted": false
    },
    "first": true,
    "numberOfElements": 10,
    "empty": false
  }

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    authServiceMock = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    permissionsServiceMock = jasmine.createSpyObj('PermissionsService', ['getDevicePermission', 'getJobPermission']);
    deviceService = jasmine.createSpyObj('DeviceService', ['getpackageVersion']);
    jobService = jasmine.createSpyObj('JobService', ['getJobType', 'getJobStatus', 'getJobList']);
    exceptionHandlingService = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    await TestBed.configureTestingModule({
      declarations: [DeviceAcivitiesComponent, JobDetailComponent],
      imports: [NgbPaginationModule, NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, FormsModule],
      providers: [
        DownloadService,
        ConfirmDialogService,
        CommonsService,
        LocalStorageService,
        DatePipe,
        { provide: AuthJwtService, useValue: authServiceMock },
        { provide: PermissionService, useValue: permissionsServiceMock },
        SessionStorageService,
        PrintListPipe,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DeviceAcivitiesComponent);
    multiSelectDropDownSettingService = TestBed.inject(MultiSelectDropDownSettingService);
    keyValueMappingServiceService = TestBed.inject(KeyValueMappingServiceService);
    deviceService = TestBed.inject(DeviceService);
    jobService = TestBed.inject(JobService);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    commonservice = TestBed.inject(CommonsService);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    // Basic test to verify that the component is created successfully
    expect(component).toBeTruthy();
  });

  it('should initialize form controls on ngOnInit', () => {
    // Arrange: Set up the mocks for dependencies and expected service responses
    authServiceMock.isAuthenticate.and.returnValue(true);
    permissionsServiceMock.getDevicePermission.and.returnValue(true); // Mocking device permissions
    permissionsServiceMock.getJobPermission.and.returnValue(true);    // Mocking job permissions

    // Mock the MultiSelectDropDownSettingService to initialize dropdown settings without calling actual service methods
    spyOn(multiSelectDropDownSettingService, 'getSystemSoftwearVersionDropdownSetting');
    spyOn(multiSelectDropDownSettingService, 'getDeviceConnectionStateDropdownSetting');
    spyOn(multiSelectDropDownSettingService, 'getJobStatusDropdownSetting');
    spyOn(multiSelectDropDownSettingService, 'getJobTypeDropdownSetting');

    // Mock keyValueMappingServiceService to provide device status options without calling the actual method
    spyOn(keyValueMappingServiceService, 'enumOptionToList');

    // Mock the deviceService to return a list of package versions
    spyOn(deviceService, 'getpackageVersion').and.returnValue(
      of(new HttpResponse({
        body: packageVersionResponse, // Mocked package response
        status: 200,
        statusText: 'OK',
      }))
    );

    // Mock jobService methods to return predefined data
    spyOn(jobService, 'getJobType').and.returnValue(of(new HttpResponse({
      body: getJobTypeMockResponse,
      status: 200,
      statusText: 'OK',
    })));
    spyOn(jobService, 'getJobStatus').and.returnValue(of(new HttpResponse({
      body: getJobStatusMockResponse,
      status: 200,
      statusText: 'OK',
    })));
    spyOn(jobService, 'getJobList').and.returnValue(of(new HttpResponse<IJobchange>({
      body: jobListResponse, // Mocked job list response
      status: 200,
      statusText: 'OK',
    })));

    // Act: Trigger ngOnInit to initialize the component and load initial data
    component.ngOnInit();

    // Assert: Confirm that all form controls were created as expected
    expect(component.filterForm.contains('packageVersions')).toBe(true);
    expect(component.filterForm.contains('connectionState')).toBe(true);
    expect(component.filterForm.contains('drpJobTypes')).toBe(true);
    expect(component.filterForm.contains('jobStatus')).toBe(true);
    expect(component.filterForm.contains('jobDeviceId')).toBe(true);

    // Assert: Confirm default display states for job details and device detail sections
    expect(component.displayJob).toBeTruthy();
    expect(component.displayDeviceDetail).toBeFalsy();
    expect(component.displayJobDetail).toBeFalsy();

    // Assert: Verify component state and pagination defaults
    expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE); // Ensure pagination is set correctly
    expect(component.drpselectsize).toBe(ITEMS_PER_PAGE);
    expect(component.previousPage).toBe(1);

    // Assert: Verify permissions were correctly set based on service responses
    expect(component.jobReaderPermission).toBeTruthy();
    expect(component.deviceReaderPermission).toBeTruthy();

    // Assert: Confirm the multi-select dropdown settings were initialized by calling the necessary methods
    expect(multiSelectDropDownSettingService.getSystemSoftwearVersionDropdownSetting).toHaveBeenCalled();
    expect(multiSelectDropDownSettingService.getDeviceConnectionStateDropdownSetting).toHaveBeenCalled();
    expect(multiSelectDropDownSettingService.getJobStatusDropdownSetting).toHaveBeenCalled();
    expect(multiSelectDropDownSettingService.getJobTypeDropdownSetting).toHaveBeenCalled();

    // Assert: Check that the package version list and job type/status lists are set up with mock data
    expect(component.pkg_v).toEqual(packageVersionResponse);
    expect(component.jobTypes).toEqual(getJobTypeMockResponse);
    expect(component.jobStatusList).toEqual(getJobStatusMockResponse);

    // Assert: Verify that form control values are initialized to null by default
    expect(component.filterForm.get('packageVersions').value).toBeNull();
    expect(component.filterForm.get('connectionState').value).toBeNull();
    expect(component.filterForm.get('jobDeviceId').value).toBeNull();
    expect(component.filterForm.get('drpJobTypes').value).toBeNull();
    expect(component.filterForm.get('jobStatus').value).toBeNull();

    // Assert: Confirm that pagination and job list details match the mocked job list response
    expect(component.page).toBe(jobListResponse.number + 1); // Verify the current page number
    expect(component.totalItems).toEqual(jobListResponse.totalElements); // Check the total items count
    expect(component.totalJobDisplay).toEqual(jobListResponse.numberOfElements); // Ensure the correct display count of jobs

    // Assert: Verify that loading is complete and set to false
    expect(component.loading).toBeFalse();
  });


  it('LoadAll method gives error', () => {
    // Arrange: Set up the mock authentication and simulate an error response from the getJobList service
    authServiceMock.isAuthenticate.and.returnValue(true);
    spyOn(jobService, 'getJobList').and.returnValue(of(new HttpResponse<IJobchange>({
      body: {},
      status: 500, // Mock HTTP status indicating a server error
      statusText: 'OK',
    })));

    // Act: Initialize component to trigger the loadAll method which calls getJobList
    component.ngOnInit();

    // Assert: Verify that no jobs are loaded when an error response is received
    expect(component.jobs.length).toEqual(0); // No jobs should be loaded
    expect(component.totalJob).toEqual(0); // Total job count should be 0
    expect(component.totalJobDisplay).toEqual(0); // Display count should be 0
    expect(component.loading).toBeFalse(); // Loading indicator should be false as loading is complete
  });

  it('should call toastrService.error with INTERNAL_SERVER_ERROR', () => {
    // Arrange: Set up authentication mock and simulate an error response for getJobList
    authServiceMock.isAuthenticate.and.returnValue(true);
    spyOn(jobService, 'getJobList').and.returnValue(
      throwError(() => ({
        status: 500, // HTTP status code indicating a server error
        statusText: 'Internal Server Error',
        message: 'Server error'
      }))
    );


    // Spy on the customErrorMessage method in exceptionHandlingService to verify error handling
    spyOn(exceptionHandlingService, 'customErrorMessage').and.callThrough();

    // Act: Trigger the component's initialization to invoke getJobList and error handling
    component.ngOnInit();

    // Assert: Ensure customErrorMessage was called for error handling
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();

    // Assert: Confirm toastrService.error was called with an INTERNAL_SERVER_ERROR message
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
  });

  it('should call loadPage on pagination page change', async () => {
    // Arrange: Set up authentication and mock the getJobList method with a valid response
    authServiceMock.isAuthenticate.and.returnValue(true);
    spyOn(component, 'loadPage').and.callThrough(); // Spy on the loadPage method to confirm it is called
    spyOn(jobService, 'getJobList').and.returnValue(of(new HttpResponse<IJobchange>({
      body: jobListResponse, // Mocked list of jobs
      status: 200, // HTTP status code for success
      statusText: 'OK',
    })));

    // Act: Initialize the component to populate initial job data
    component.ngOnInit();
    fixture.detectChanges(); // Trigger change detection to apply the initialized state

    // Await for asynchronous actions to complete
    await fixture.whenStable();

    // Simulate a pagination change to page 2
    const pagination = fixture.debugElement.query(By.css('#jobPagination')); // Locate the pagination element
    pagination.triggerEventHandler('pageChange', 2); // Trigger the page change event

    fixture.detectChanges(); // Apply changes to the DOM after pagination event

    // Assert: Confirm that loadPage method was called with the correct page number
    expect(component.loadPage).toHaveBeenCalledWith(2); // loadPage should have been called for page 2
    expect(component.previousPage).toBe(2); // Ensure previousPage is updated to reflect the current page
  });

  it('should correctly toggle the filter visibility', () => {
    // Arrange: Set the initial visibility of the filter to 'visible'
    component.isFilterHidden = false;
    component.ngOnInit(); // Initialize component settings, including filter visibility
    fixture.detectChanges(); // Ensure the DOM is updated with the current visibility state

    // Query the DOM to find the filter button (filter should be visible initially)
    let filterElement = fixture.debugElement.query(By.css('#jobFilterBtn'));
    expect(filterElement).toBeTruthy(); // Verify filter is visible at the start

    // Act: Toggle filter visibility to hide the filter
    component.toggleFilter();
    fixture.detectChanges(); // Reflect the filter visibility change in the DOM

    // Re-query the DOM to check the updated state of the filter button
    filterElement = fixture.debugElement.query(By.css('#jobFilterBtn'));
    expect(filterElement).toBeFalsy(); // Verify filter is now hidden
  });

  it('should correctly toggle the filter visibility', () => {
    // Arrange: Set the initial visibility of the filter to 'hidden'
    component.isFilterHidden = true;
    fixture.detectChanges(); // Reflect the initial state in the DOM

    // Query the DOM to find the filter button (filter should be hidden initially)
    let filterElement = fixture.debugElement.query(By.css('#jobFilterBtn'));
    expect(filterElement).toBeFalsy(); // Verify filter is hidden at the start

    // Act: Toggle filter visibility to show the filter
    component.toggleFilter();
    fixture.detectChanges(); // Update the DOM with the new visibility state

    // Re-query the DOM to confirm the filter button is now visible
    filterElement = fixture.debugElement.query(By.css('#jobFilterBtn'));
    expect(filterElement).toBeTruthy(); // Verify filter is now visible
  });

  it('should correctly search with Empty Data', () => {
    // Arrange: Query the DOM to find the search button element
    const searchButton = fixture.debugElement.query(By.css('#searchJob'));

    // Act: Simulate a click on the search button with no filter values set
    searchButton.nativeElement.click();

    // Assert: Verify that a warning is displayed when search is triggered without filter criteria
    expect(toastrServiceMock.info).toHaveBeenCalledWith("Please Select Filter To Search");
  });


  it('should correctly search with Invalid Data', () => {
    // Spy on the 'loadAll' method to ensure it is called when search is triggered
    spyOn(component, 'loadAll').and.callThrough();

    // Arrange: Set the filter form fields with invalid data values
    component.filterForm.get('packageVersions').setValue(["1.1.1.1"]);
    component.filterForm.get('connectionState').setValue(["CONNECTED"]);
    component.filterForm.get('drpJobTypes').setValue(['VIDEO']);
    component.filterForm.get('jobDeviceId').setValue('292020005181800046csdscccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccc');
    component.filterForm.get('jobStatus').setValue(["CREATED"]);

    // Act: Simulate a click on the search button to initiate the search
    const searchButton = fixture.debugElement.query(By.css('#searchJob'));
    searchButton.nativeElement.click();

    // Assert: Verify that 'loadAll' is called when search is triggered
    expect(component.loadAll).toHaveBeenCalled();

    // Assert: Ensure that invalid data is reset to null in the form after search
    expect(component.filterForm.get('packageVersions').value).toEqual(null);
    expect(component.filterForm.get('connectionState').value).toEqual(null);
    expect(component.filterForm.get('drpJobTypes').value).toEqual(null);
    expect(component.filterForm.get('jobDeviceId').value).toEqual(null);
    expect(component.filterForm.get('jobStatus').value).toEqual(null);
  });

  it('should correctly search with Valid Data', () => {
    // Spy on the 'loadAll' method to ensure it is called
    spyOn(component, 'loadAll').and.callThrough();

    // Spy on the 'getJobList' method of jobService to verify its parameters
    spyOn(jobService, 'getJobList');

    // Mock the return value of 'getSelectedValueFromEnum' to simulate a valid state
    spyOn(commonservice, 'getSelectedValueFromEnum').and.returnValue(["CONNECTED"]);

    // Arrange: Set the filter form fields with valid data values
    component.filterForm.get('packageVersions').setValue(["1.1.1.1"]);
    component.filterForm.get('connectionState').setValue(["CONNECTED"]);
    component.filterForm.get('drpJobTypes').setValue(['VIDEO']);
    component.filterForm.get('jobDeviceId').setValue('292020005181800046');
    component.filterForm.get('jobStatus').setValue(["CREATED"]);

    // Act: Simulate a click on the search button to initiate the search with valid data
    const searchButton = fixture.debugElement.query(By.css('#searchJob'));
    searchButton.nativeElement.click();

    // Assert: Verify that 'getJobList' is called with the correct parameters
    expect(jobService.getJobList).toHaveBeenCalledWith(
      {
        packageVersions: ["1.1.1.1"],
        connectionState: ["CONNECTED"],
        jobDeviceId: "292020005181800046",
        drpJobTypes: "VIDEO",
        jobStatus: ["CREATED"],
      },
      {
        page: component.page - 1,
        size: component.itemsPerPage,
      }
    );
  });

  it('should display the correct options in the dropdown change', fakeAsync(() => {

    // Mock the authentication service to return true, simulating an authenticated user
    authServiceMock.isAuthenticate.and.returnValue(true);

    // Trigger the component's ngOnInit lifecycle hook to initialize the component
    component.ngOnInit();

    // Act: Simulate asynchronous operations and update the view with any necessary data changes
    tick(); // Simulates the passage of time required for asynchronous operations (if any)
    fixture.detectChanges(); // Run change detection to ensure the view is updated with the current state

    // Test dropdown interaction
    testDropdownInteraction(fixture, component, '#jobListShowEntry');
  }));

  it('should display detail Page', async () => {
    // Mock the authentication service to return true, indicating the user is authenticated
    authServiceMock.isAuthenticate.and.returnValue(true);

    // Mock the permissions service to return true, granting the necessary device access permissions
    permissionsServiceMock.getDevicePermission.and.returnValue(true);

    // Spy on the job service's 'getJobList' method and mock the return value to simulate a successful HTTP response
    spyOn(jobService, 'getJobList').and.returnValue(of(new HttpResponse<IJobchange>({
      body: jobListResponse,  // Simulated response body containing the job list with devices
      status: 200,            // Simulated HTTP status code for success
      statusText: 'OK',       // Simulated HTTP status text
    })));

    // Trigger the component's ngOnInit lifecycle hook to initialize and load the job list data
    component.ngOnInit();

    // Run Angular's change detection to reflect any changes in the component or template after ngOnInit
    fixture.detectChanges();

    // Wait for any asynchronous tasks (like promises or observables) to complete before continuing
    await fixture.whenStable();

    // Query the DOM for the element with id '#jobDeviceId' (the element responsible for displaying device details)
    const hwIdElement = fixture.debugElement.query(By.css('#jobDeviceId'));

    // Ensure that the queried element exists in the DOM before proceeding with the test
    expect(hwIdElement).toBeTruthy();

    // Simulate a click event on the device element to display the job details
    hwIdElement.nativeElement.click();

    // Assert that after the click, the component's 'deviceIdInput' is correctly set to the first device's ID from the job list response
    expect(component.deviceIdInput).toEqual(jobListResponse.content[0].deviceMasterIdPk);

    // Assert that 'displayJobDetail' is false, meaning the job detail page is hidden
    expect(component.displayJobDetail).toBeFalsy();

    // Assert that 'displayDeviceDetail' is true, meaning the device detail page is displayed
    expect(component.displayDeviceDetail).toBeTruthy();

    // Assert that 'displayJob' is false, meaning the main job list is hidden
    expect(component.displayJob).toBeFalsy();
  });


  it('should display detail Page', async () => {
    // Mock the authentication service to return true, indicating the user is authenticated
    authServiceMock.isAuthenticate.and.returnValue(true);

    // Mock the permissions service to return true, granting the necessary job access permissions
    permissionsServiceMock.getJobPermission.and.returnValue(true);

    // Spy on the job service's 'getJobList' method and mock the return value to simulate an HTTP response
    spyOn(jobService, 'getJobList').and.returnValue(of(new HttpResponse<IJobchange>({
      body: jobListResponse,  // Simulated response body containing the job list data
      status: 200,            // Simulated HTTP status code for success
      statusText: 'OK',       // Simulated HTTP status text
    })));

    // Trigger the component's ngOnInit lifecycle hook to initialize and load data
    component.ngOnInit();

    // Run Angular's change detection to reflect any changes in the template after ngOnInit
    fixture.detectChanges();

    // Wait for any asynchronous tasks (like promises or observables) to complete before proceeding
    await fixture.whenStable();

    // Query the DOM for the element with id '#jobStatusId' (the job status element to be clicked)
    const hwIdElement = fixture.debugElement.query(By.css('#jobStatusId'));

    // Ensure that the queried element exists in the DOM before proceeding
    expect(hwIdElement).toBeTruthy();

    // Simulate a click event on the job status element to display job details
    hwIdElement.nativeElement.click();

    // Assert that after the click, the component's 'jobScheduleStatusId' is set correctly
    expect(component.jobScheduleStatusId).toEqual(jobListResponse.content[0].jobScheduleStatusId);

    // Assert that 'displayJobDetail' is true, meaning the job detail page is displayed
    expect(component.displayJobDetail).toBeTruthy();

    // Assert that 'displayDeviceDetail' is false, meaning the device detail page is hidden
    expect(component.displayDeviceDetail).toBeFalsy();

    // Assert that 'displayJob' is false, meaning the main job list is hidden
    expect(component.displayJob).toBeFalsy();
  });

  it('should display job and hide device and job details', async () => {
    // Mock the authentication service to return true, indicating the user is authenticated
    authServiceMock.isAuthenticate.and.returnValue(true);

    // Mock the permissions service to return true, granting job access permissions
    permissionsServiceMock.getJobPermission.and.returnValue(true);

    // Spy on the job service and mock the return value of 'getJobList' to simulate an HTTP response
    spyOn(jobService, 'getJobList').and.returnValue(of(new HttpResponse<IJobchange>({
      body: jobListResponse, // Simulated response body containing the job list
      status: 200,           // Simulated HTTP status code for success
      statusText: 'OK',      // Simulated HTTP status text
    })));

    // Trigger the component's ngOnInit lifecycle hook to initiate data loading
    component.ngOnInit();

    // Run Angular's change detection to reflect any changes in the template after ngOnInit
    fixture.detectChanges();

    // Wait for any asynchronous tasks (like promises or observables) to complete before proceeding
    await fixture.whenStable();

    // Query the DOM for the element with id '#jobStatusId' (the element responsible for showing job details)
    const hwIdJobElement = fixture.debugElement.query(By.css('#jobStatusId'));

    // Simulate a click event on the job status element to trigger the job display
    hwIdJobElement.nativeElement.click();

    // Trigger change detection again to apply any changes made by the click event
    fixture.detectChanges();

    // Query the DOM for the element with id '#backtoJobList' (the element responsible for going back to the job list)
    const hwIdElement = fixture.debugElement.query(By.css('#backtoJobList'));

    // Simulate a click event on the back button element to return to the job list view
    hwIdElement.nativeElement.click();

    // Assert that 'displayJobDetail' and 'displayDeviceDetail' are set to false, meaning the details are hidden
    expect(component.displayJobDetail).toBeFalsy();
    expect(component.displayDeviceDetail).toBeFalsy();

    // Assert that 'displayJob' is set to true, meaning the job list is now displayed
    expect(component.displayJob).toBeTruthy();

    const filterComponent = fixture.debugElement.query(By.directive(JobDetailComponent));
    expect(filterComponent).toBeTruthy();
  });

});
