<!-- ----------------------------------------- -->
<!-- loading start -->
<!-- ----------------------------------------- -->
<div class="ringLoading" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!-- ----------------------------------------- -->
<!-- loading end -->
<!-- ----------------------------------------- -->

<body class="bg-white">
    <!-- main container start -->
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <!-- create user header start -->
                <div class="row">
                    <label class="col-md-6 h5-tag">Create User</label>
                    <div class="ml-auto col-md-6 text-right mb-3">
                        <div class="dropdown">
                            <!-- back button start -->
                            <button class="btn btn-sm btn-outline-secondary mr-3" (click)="back()"
                                id="addUserBackBtn"><i class="fa fa-reply"
                                    aria-hidden="true"></i>&nbsp;&nbsp;Back</button>
                            <!-- back button end -->
                        </div>
                    </div>
                </div>
                <!-- create user header end -->
                <!-- user details input portion start -->
                <div class="row">
                    <div class="col-md-12">
                        <!-- main card start -->
                        <div class="card">
                            <div class="card-body">
                                <div class="card shadow">
                                    <div class="card-body">
                                        <!-- user form start -->
                                        <form [formGroup]="form">
                                            <!-- input row start -->
                                            <div class="row">
                                                <!-- first name form control start -->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <!-- first name input start -->
                                                        <label><strong class="">First Name</strong></label>
                                                        <input type="text" class="form-control" id="" name="firstName"
                                                            formControlName="firstName" required>
                                                        <!-- first name validation error start -->
                                                        <div
                                                            *ngIf="(form.get('firstName').touched || form.get('firstName').dirty) && form.get('firstName').invalid ">
                                                            <!-- required first name -->
                                                            <div *ngIf="form.get('firstName').errors['required']">
                                                                <p class="alert-color"> First Name is required</p>
                                                            </div>
                                                            <!-- required only character -->
                                                            <div
                                                                *ngIf="form.get('firstName').errors['pattern'] && !(form.get('firstName').errors['maxlength'])">
                                                                <p class="alert-color"> Enter Only Character</p>
                                                            </div>
                                                            <!---Charater limits-->
                                                            <div *ngIf="form.get('firstName').errors['maxlength']">
                                                                <p class="alert-color">
                                                                    {{textBoxMaxCharactersAllowedMessage}}</p>
                                                            </div>
                                                        </div>
                                                        <!-- first name validation error end -->
                                                        <!-- first name input end -->
                                                    </div>
                                                </div>
                                                <!-- first name form control end -->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <!-- last name input group start -->
                                                        <label><strong class="">Last Name</strong></label>
                                                        <input type="text" class="form-control" id="" name="lastName"
                                                            formControlName="lastName" required>

                                                        <div
                                                            *ngIf="(form.get('lastName').touched || form.get('lastName').dirty) && form.get('lastName').invalid ">
                                                            <div *ngIf="form.get('lastName').errors['required']">
                                                                <p class="alert-color"> Last Name is required</p>
                                                            </div>
                                                            <div
                                                                *ngIf="form.get('lastName').errors['pattern'] && !(form.get('lastName').errors['maxlength'])">
                                                                <p class="alert-color"> Enter Only Character</p>
                                                            </div>
                                                            <!---Charater limits-->
                                                            <div *ngIf="form.get('lastName').errors['maxlength']">
                                                                <p class="alert-color">
                                                                    {{textBoxMaxCharactersAllowedMessage}}</p>
                                                            </div>
                                                        </div>
                                                        <!-- last name input group end -->
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <!-- email inout start -->
                                                        <label><strong class="">E-mail</strong></label>
                                                        <input type="text" class="form-control" id="userEmail"
                                                            name="email" formControlName="email"
                                                            (input)="change($event)" required>

                                                        <div
                                                            *ngIf="(form.get('email').touched || form.get('email').dirty) && form.get('email').invalid ">
                                                            <!-- required email validation error -->
                                                            <div *ngIf="form.get('email').errors['required']">
                                                                <p class="alert-color">E-mail ID required</p>
                                                            </div>
                                                            <!-- email validation error-->
                                                            <div
                                                                *ngIf="form.get('email').errors['pattern'] && !(form.get('email').errors['maxlength'])">
                                                                <p class="alert-color"> Enter Valid E-mail </p>
                                                            </div>
                                                            <!---Charater limits-->
                                                            <div *ngIf="form.get('email').errors['maxlength']">
                                                                <p class="alert-color">
                                                                    {{textBoxMaxCharactersAllowedMessage}}</p>
                                                            </div>
                                                        </div>
                                                        <!-- email inout end -->

                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <!-- login name input start -->
                                                        <label><strong class="">Login Name</strong></label>
                                                        <input type="text" class="form-control" name="login"
                                                            [(value)]="login" readonly>
                                                        <!-- login name input end -->
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <!-- role selection start -->
                                                    <div class="form-group">
                                                        <label for="user_role"><strong>Select Role</strong></label>
                                                        <ng-multiselect-dropdown name="userRoles" [placeholder]="''"
                                                            (click)="onItemSelectValidation('userRoles')" id="user_role"
                                                            formControlName="userRoles" [settings]="roleSetting"
                                                            [data]="userRolesSelection">
                                                        </ng-multiselect-dropdown>
                                                        <!-- validation error start -->
                                                        <div
                                                            *ngIf="(form.get('userRoles').touched || form.get('userRoles').dirty) && form.get('userRoles').invalid">
                                                            <!-- required selection error strat -->
                                                            <div *ngIf="form.get('userRoles').errors['required']">
                                                                <p class="alert-color">Please select role</p>
                                                            </div>
                                                            <!-- required selection error end -->
                                                        </div>
                                                        <!-- validation error end -->
                                                    </div>
                                                    <!-- role selection end -->
                                                </div>
                                                <!-- Country selection start -->
                                                <div class="col-md-4">
                                                    <!-- Country selection start -->
                                                    <div class="form-group">
                                                        <label class="form-control-label"
                                                            for="field_Country"><strong>Country</strong></label>
                                                        <ng-multiselect-dropdown name="Country" [placeholder]="''"
                                                            (click)="onItemSelectValidation('country')"
                                                            formControlName="country" [settings]="countrySetting"
                                                            [data]="countryList">
                                                        </ng-multiselect-dropdown>
                                                        <div
                                                            *ngIf="(form.get('country').touched || form.get('country').dirty) && form.get('country').invalid">
                                                            <p class="alert-color">Atleast one country must be selected
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <!-- Country selection end -->
                                                </div>
                                                <!-- Country selection end -->
                                            </div>
                                            <!-- input row end -->
                                        </form>
                                        <!-- user form end -->
                                        <!-- card inputs end-->
                                    </div>
                                </div>
                                <hr class="mt-4">

                                <div class="ml-auto text-right">
                                    <!-- submit form button -->
                                    <button class="btn btn-orange btn-sm" (click)="createAccount()"
                                        [disabled]="!form.valid" id="creatUserBtn">Submit</button>
                                </div>

                            </div>
                            <!--Row-->
                        </div>
                        <!-- main card end -->
                    </div>
                </div>
                <!-- user details input portion end -->
            </div>
        </div>
    </div>
    <!-- main container end -->
</body>