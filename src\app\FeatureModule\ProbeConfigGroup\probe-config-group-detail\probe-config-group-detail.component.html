<!------------------------------------------->
<!--loading start-->
<!------------------------------------------->
<div class="ringLoading" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!------------------------------------------->
<!--loading end-->
<!------------------------------------------->

<!------------------------------------------->
<!--body start-->
<!------------------------------------------->

<body class="bg-white">

    <div class="container-fluid" id="probe_Feature_Group_Detail">
        <div class="row">
            <div class="col-md-12">
                <!--header  start-->
                <div class="row">
                    <label class="col-md-6 h5-tag">Probe Config Group Detail</label>
                    <div class="ml-auto col-md-6 text-right mb-3">
                        <!--Button div start-->
                        <!--back button-->
                        <div class="d-flex justify-content-end">
                            <div *ngIf="deleteProbeConfigGroupPermission">
                                <!-- delete button start -->
                                <button class="btn btn-sm btn-outline-secondary" (click)="deleteProbeConfigGroup()"
                                    id="pcgDeleteDetail">
                                    <i class="fa fa-trash" aria-hidden="true"></i>&nbsp;&nbsp;Delete
                                </button>
                                <!-- delete button end -->
                            </div>
                            <!-- back button start -->
                            <button class="btn btn-sm btn-outline-secondary ml-2" (click)="back()" id="pcgDetailBack"><i
                                    class="fa fa-reply" aria-hidden="true"></i>&nbsp;&nbsp;Back</button>
                            <!-- back button end -->

                            <!-- refresh button start -->
                            <button class="btn btn-sm btn-orange ml-2" (click)="refreshPCGDetailPage()"
                                id="pcgDetailRefresh"><em class="fa fa-refresh"></em></button>
                            <!-- refresh button end -->
                        </div>
                        <!--Button div end-->
                    </div>
                </div>
                <!--header  end-->
                <!--Body start-->
                <div class="row">
                    <div class="col-md-12">
                        <!--card start-->
                        <div class="card">
                            <!--card body start-->
                            <div class="card-body">
                                <div class="card shadow">
                                    <div class="card-body">
                                        <div class="row">

                                            <!--Part Number start-->
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label><strong class="">Part Number</strong></label>
                                                    <input type="text" class="form-control" id="" placeholder=""
                                                        [value]="probeConfigGroupDetailResponse?.partNumberCode"
                                                        disabled>
                                                </div>
                                            </div>
                                            <!--Part Number end-->

                                            <!--Probe Type start-->
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label><strong class="">Probe Type</strong></label>
                                                    <input type="text" class="form-control" id="" placeholder=""
                                                        [value]="probeConfigGroupDetailResponse?.probeType | enumMappingDisplayNamePipe :probeTypesList"
                                                        disabled>
                                                </div>
                                            </div>
                                            <!--Probe Type end-->

                                            <!--Created Date & Time start-->
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label><strong class="">Created Date & Time</strong></label>
                                                    <input type="text" class="form-control" id="" placeholder=""
                                                        [value]="probeConfigGroupDetailResponse?.createdDate | date:dateTimeDisplayFormat"
                                                        disabled>
                                                </div>
                                            </div>
                                            <!--Created Date & Time end-->

                                            <!--Last Modified Date & Time start-->
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label><strong class="">Last Modified Date &
                                                            Time</strong></label>
                                                    <input type="text" class="form-control" id="Modified" placeholder=""
                                                        [value]="probeConfigGroupDetailResponse?.modifiedDate | date:dateTimeDisplayFormat"
                                                        disabled>
                                                </div>
                                            </div>
                                            <!--Last Modified Date & Time end-->

                                            <!--Description start-->
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label><strong class="">Description</strong></label>
                                                    <textarea type="text"
                                                        [value]="probeConfigGroupDetailResponse?.description" rows="3"
                                                        class="form-control textAreaResize" name="Description"
                                                        readonly></textarea>
                                                </div>
                                            </div>
                                            <!--Description End-->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-------------------------------------->
                <!---------- second row start ---------->
                <!-------------------------------------->

                <div class="row" *ngIf="probeConfigGroupDetailResponse != null && 
                                        probeConfigGroupDetailResponse?.presets != null && 
                                        probeConfigGroupDetailResponse?.presets.length > 0">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="card shadow">
                                    <div class="card-body">
                                        <div class="container">
                                            <label class="mb-1 h5-tag">Related
                                                Preset(s)</label>
                                            <hr class="hrMargin">
                                            <div class="bottomMargin-5"
                                                *ngIf="probeConfigGroupDetailResponse?.presets != null">
                                                Total
                                                {{probeConfigGroupDetailResponse?.presets.length}}
                                                Related Presets(s)</div>
                                            <table class="table table-sm table-bordered" aria-hidden="true">
                                                <thead>
                                                    <tr class="thead-light">
                                                        <th style="width: 10%;">Sr. No.</th>
                                                        <th style="width: 25%;">Part Number</th>
                                                        <th style="width: 25%;">Name</th>
                                                        <th style="width: 20%;">Validity Period</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr
                                                        *ngFor='let presetGroupObj of probeConfigGroupDetailResponse?.presets;let fgIndex=index'>
                                                        <td>{{fgIndex+1}}</td>
                                                        <td>{{presetGroupObj?.partNumberCode}}
                                                        </td>
                                                        <td>{{presetGroupObj?.displayName}}
                                                        </td>
                                                        <td>{{presetGroupObj?.validity |
                                                            enumMappingDisplayNamePipe:validityEnumList}}
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-------------------------------------->
                <!---------- second row End ---------->
                <!-------------------------------------->


                <!-------------------------------------->
                <!---------- third row start ---------->
                <!-------------------------------------->

                <div class="row" *ngIf="probeConfigGroupDetailResponse != null && 
                                        probeConfigGroupDetailResponse?.features != null && 
                                        probeConfigGroupDetailResponse?.features.length > 0">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="card shadow">
                                    <div class="card-body">
                                        <div class="container">
                                            <label class="mb-1 h5-tag">Related
                                                Feature(s)</label>
                                            <hr class="hrMargin">
                                            <div class="bottomMargin-5"
                                                *ngIf="probeConfigGroupDetailResponse?.features != null">
                                                Total
                                                {{probeConfigGroupDetailResponse?.features.length}}
                                                Related Feature(s)</div>
                                            <table class="table table-sm table-bordered" aria-hidden="true">
                                                <thead>
                                                    <tr class="thead-light">
                                                        <th style="width: 10%;">Sr. No.</th>
                                                        <th style="width: 25%;">Part Number</th>
                                                        <th style="width: 25%;">Name</th>
                                                        <th style="width: 20%;">Validity Period</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr
                                                        *ngFor='let featureGroupObj of probeConfigGroupDetailResponse?.features;let fgIndex=index'>
                                                        <td>{{fgIndex+1}}</td>
                                                        <td>{{featureGroupObj?.partNumberCode}}
                                                        </td>
                                                        <td>{{featureGroupObj?.displayName}}
                                                        </td>
                                                        <td>{{featureGroupObj?.validity |
                                                            enumMappingDisplayNamePipe:validityEnumList}}
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-------------------------------------->
                <!---------- third row End ---------->
                <!-------------------------------------->
            </div>
            <!--Body end-->

        </div>
    </div>
</body>