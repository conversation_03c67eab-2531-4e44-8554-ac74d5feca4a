import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { FeaturesBaseResponse } from 'src/app/model/probe/FeaturesBaseResponse.model';

@Pipe({
    name: 'featuresBaseResponseDisplayPipe'
})
export class FeaturesBaseResponseDisplayPipe implements PipeTransform {

    transform(featuresBaseResponse: FeaturesBaseResponse): string {
        let featureName = featuresBaseResponse.displayName;
        let featuresPartNumbers = featuresBaseResponse.partNumbers

        if (!isNullOrUndefined(featureName) && !isNullOrUndefined(featuresPartNumbers) && featuresPartNumbers.length > 0) {
            if (!isNullOrUndefined(featuresPartNumbers[0].partNumber)) {
                return featureName + " - " + featuresPartNumbers[0].partNumber.split('-')[0];
            }
        }
        return featureName;
    }

}
