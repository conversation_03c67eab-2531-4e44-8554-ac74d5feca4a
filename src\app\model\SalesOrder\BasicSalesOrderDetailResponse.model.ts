export class BasicSalesOrderDetailResponse {
    salesOrderNumber: string;
    customerName: string;
    customerEmail: string;
    countryId: number;
    poNumber: string;
    orderRecordType: string;
    deviceAutoLock: boolean;
    probeAutoLock: boolean;
    salesForceOrder: boolean;
    soLetterDownload: boolean;


    constructor($salesOrderNumber: string, $customerName: string, $customerEmail: string, $countryId: number, $poNumber: string, $deviceAutoLock: boolean, $probeAutoLock: boolean, $orderRecordType: string) { //NOSONAR
        this.salesOrderNumber = $salesOrderNumber;
        this.customerName = $customerName;
        this.customerEmail = $customerEmail;
        this.countryId = $countryId;
        this.poNumber = $poNumber;
        this.deviceAutoLock = $deviceAutoLock;
        this.probeAutoLock = $probeAutoLock;
        this.orderRecordType = $orderRecordType;
    }

}