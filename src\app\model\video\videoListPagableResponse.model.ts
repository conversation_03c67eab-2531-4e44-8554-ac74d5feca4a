import { Pageable } from "../common/pageable.model";
import { PageResponse } from "../common/PageResponse.model";
import { Sort } from "../common/sort.model";
import { VideoListResponse } from "./video-list-response.model";

export class VideoListPagableResponse extends PageResponse {
    content: Array<VideoListResponse>;

    constructor(pageable: Pageable, totalPages: number, last: boolean, totalElements: number, numberOfElements: number, first: boolean, sort: Sort, size: number, number: number, empty: boolean, content: Array<VideoListResponse>) { //NOSONAR  
        super(pageable, totalPages, last, totalElements, numberOfElements, first, sort, size, number, empty);
        this.content = content;
    }

}