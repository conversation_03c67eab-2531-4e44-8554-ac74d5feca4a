import { HttpErrorResponse } from '@angular/common/http';
import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { DateTimeDisplayFormat } from 'src/app/app.constants';
import { ProbeFeatureHistoryListResponse } from 'src/app/model/probe/ProbeFeatureHistoryListResponse.model';
import { ProbeHistoryResponse } from 'src/app/model/probe/ProbeHistoryResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';

@Component({
  selector: 'app-feature-history-detail',
  templateUrl: './feature-history-detail.component.html',
  styleUrls: ['./feature-history-detail.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class FeatureHistoryDetailComponent implements OnInit {

  @Input() header: string;
  @Input() history: ProbeHistoryResponse;
  @Input() probeId: number;

  featureHistoryDetailList: ProbeFeatureHistoryListResponse;

  dateDisplayFormat: string = DateTimeDisplayFormat;

  constructor(private activeModal: NgbActiveModal,
    private exceptionService: ExceptionHandlingService,
    private probeApiService: ProbeApiService,
    private commonOperationsService: CommonOperationsService) { }


  public ngOnInit(): void {
    this.getProbeDetailHistory();
  }

  /**
   * Get Probe History Detail
   * 
   * <AUTHOR>
   */
  private getProbeDetailHistory(): void {
    this.setLoading(true);
    this.probeApiService.getProbeHistoryDetail(this.history?.licenseHistoryId, this.probeId).subscribe({
      next: (response) => {
        this.featureHistoryDetailList = response.body;
        this.setLoading(false);
      }, error: (error: HttpErrorResponse) => {
        this.setLoading(false);
        this.exceptionService.customErrorMessage(error);
        this.dismiss();
      }
    });
  }

  /**
   * set Loading status
   * 
   * <AUTHOR>
   * @param status 
   */
  private setLoading(status: boolean): void {
    this.commonOperationsService.callCommonLoadingSubject(status);
  }

  /**
   * Close Model
   * 
   * <AUTHOR>
   */
  public dismiss(): void {
    this.activeModal.dismiss();
  }

}
