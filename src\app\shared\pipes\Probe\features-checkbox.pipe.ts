import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ConfigBaseMappingRequest } from 'src/app/model/probe/ConfigBaseMappingRequest.model';

@Pipe({
  name: 'featuresCheckBoxPipe'
})
export class FeaturesCheckBoxPipe implements PipeTransform {

  transform(features: Array<ConfigBaseMappingRequest>, featureId: number, reloadPipe: boolean): boolean {
    if (!isNullOrUndefined(features) && reloadPipe) {
      let filterData = features.filter(obj => obj.id == featureId);
      if (filterData.length == 1) {
        return filterData[0].enable;
      }
    }
    return false;
  }

}
