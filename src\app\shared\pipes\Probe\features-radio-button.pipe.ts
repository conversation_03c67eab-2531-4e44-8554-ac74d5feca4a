import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ConfigBaseMappingRequest } from 'src/app/model/probe/ConfigBaseMappingRequest.model';
import { EndDateOptions } from '../../enum/endDateOptions.enum';

@Pipe({
    name: 'featuresRadioButtonPipe'
})
export class FeaturesRadioButtonPipe implements PipeTransform {

    transform(features: Array<ConfigBaseMappingRequest>, featureId: number, endDateUi: EndDateOptions, reloadPipe: boolean): boolean {
        if (!isNullOrUndefined(features) && reloadPipe) {
            let filterData = features.filter(obj => obj.id == featureId);
            if (filterData.length == 1) {
                return filterData[0].endDateUi == endDateUi;
            }
        }
        return false;
    }

}