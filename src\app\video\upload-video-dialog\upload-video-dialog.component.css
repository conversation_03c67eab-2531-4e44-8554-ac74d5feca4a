@import '../../../assets/css/custom_style.css';

.video-upload-title {
    color: #495057;
    font-weight: 500;
}

.upload-video-form input[type="text"] {
    height: 36px;
}

.upload-video-form input[type="file"] {
    width: 36%;
    font-size: 12px;
}

.upload-success {
    vertical-align: middle;
    color: #6bad47;
    font-weight: 400;
}


#uploadVideoDialog table {
    border-collapse: separate;
    border-spacing: 0 0.5rem;
}

.mat-chip-list-wrapper {
    margin: -5px !important;
}

.mat-chip {
    background-color: #E4EAEE !important;
    border: 1px solid #8e9499;
    min-width: 84px;
    height: 20px !important;
    border-radius: 0.25rem !important;
    padding: 10px 6px !important;
    font-size: 14px !important;
    font-weight: 600;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.71;
    letter-spacing: normal;
    color: #0f1519 !important;
    opacity: 1 !important;
    margin-left: 0 !important;
}

.mat-chip .mat-chip-remove {
    color: #f46c26 !important;
}

.validation {
    color: #c70e24;
    font-size: 12px;
}

#uploadVideoDialog ::ng-deep .mat-mdc-standard-chip .mat-mdc-chip-action-label,
#uploadVideoDialog ::ng-deep .mat-mdc-standard-chip .mdc-evolution-chip__action--primary {
    overflow: hidden;
    padding-right: 25px;
}

#uploadVideoDialog ::ng-deep .mdc-evolution-chip__cell--trailing {
    left: -35px;
    position: relative;
}