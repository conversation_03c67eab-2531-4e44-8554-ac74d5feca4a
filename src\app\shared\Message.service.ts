import { common_error_empty_filter,common_error_empty_start_date,
         common_error_empty_end_date,common_error_invalid_date,
         common_error_other } from './config';
import { Inject, Injectable } from '@angular/core';

@Injectable({
    providedIn: 'root'
  })
export class MessageService{
    
   constructor(
   @Inject(common_error_empty_filter) public empty_filter: string,
   @Inject(common_error_empty_start_date) public empty_startdate: string,
   @Inject(common_error_empty_end_date) public empty_enddate: string,
   @Inject(common_error_invalid_date) public invalid_date: string,
   @Inject(common_error_other) public message_other: string){ }
} 