import { HttpClient, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, firstValueFrom, Observable } from 'rxjs';
import { OTSKitManagemantSearchRequestBody } from 'src/app/model/KitManagement/otsWorld/OTSKitManagemantSearchRequestBody.model';
import { OTSKitManagementDetailResponse } from 'src/app/model/KitManagement/otsWorld/OTSKitManagementDetailResponse.model';
import { OTSKitManagementPageResponse } from 'src/app/model/KitManagement/otsWorld/OTSKitManagementPageResponse.model';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { CommonsService } from '../../util/commons.service';
import { createRequestOption } from '../../util/request-util';

@Injectable({
  providedIn: 'root'
})
export class OtsKitManagemantApiCallService {
  private serverApiUrl = this.configInjectService.getServerApiUrl();
  public otsKitBaseUrl = this.serverApiUrl + 'api/kit-management/ots-world';

  constructor(private http: HttpClient,
    private commonsService: CommonsService,
    private configInjectService: ConfigInjectService) { }

  /**
* Get OTS Kit List
* 
* <AUTHOR>
* @param requestBody 
* @param req 
* @returns 
*/
  public getOTSKitList(requestBody: OTSKitManagemantSearchRequestBody, req: any): Observable<HttpResponse<OTSKitManagementPageResponse>> {
    const options = createRequestOption(req);
    return this.http.post<OTSKitManagementPageResponse>(this.otsKitBaseUrl + "/search", requestBody, { params: options, observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }


  /**
  * Get OTS Kit Detail 
  * 
  * @param kitId 
  * @returns 
  */
  public getOTSKitDetail(otsKitId: number): Observable<HttpResponse<OTSKitManagementDetailResponse>> {
    return this.http.get<OTSKitManagementDetailResponse>(this.otsKitBaseUrl + "/" + otsKitId, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
  * Get Rev Version 
  *  
  * @returns 
  */
  public async getOtsKitRevVersion(): Promise<string> {
    const res: HttpResponse<string> = await firstValueFrom(this.http.get<string>(`${this.otsKitBaseUrl}/revVersion`, { observe: 'response' }));
    return res.body;
  }
}
