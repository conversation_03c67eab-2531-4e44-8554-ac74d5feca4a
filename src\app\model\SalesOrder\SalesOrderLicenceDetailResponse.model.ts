import { ProductConfigStatus } from "src/app/shared/enum/SalesOrder/ProductConfigStatus.enum";
import { AssociatedConfigLicence } from "../associated-config-licence.model";
import { SalesOrderProductBaseResponse } from "./SalesOrderProductBaseResponse.model";

export class SalesOrderLicenceDetailResponse extends SalesOrderProductBaseResponse {
    displayName: string;
    associatedFeatures: Array<AssociatedConfigLicence>;
    associatedPresets: Array<AssociatedConfigLicence>;

    constructor(sopmId: number, entitySerialNumber: string, entityPk: number, entityStatus: ProductConfigStatus, displayName: string, associatedFeatures: Array<AssociatedConfigLicence>, associatedPresets: Array<AssociatedConfigLicence>) {
        super(sopmId, entitySerialNumber, entityStatus, entityPk)
        this.displayName = displayName;
        this.associatedFeatures = associatedFeatures;
        this.associatedPresets = associatedPresets;
    }

}