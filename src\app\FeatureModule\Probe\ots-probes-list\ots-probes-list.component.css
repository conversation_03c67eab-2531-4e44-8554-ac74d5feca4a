@import '../../../../../src/assets/css/custom_style.css';

.probe-table {
    overflow-x: auto;
}

.probe-table th,
.probe-table td {
    min-width: 100px;
    max-width: 250px;
}

.probe-table th {
    white-space: nowrap;
}

.probe-table .width-unset {
    min-width: auto;
}

.mr-10 {
    margin-right: 10px;
}

.mr-5 {
    margin-right: 5px !important;
}

.ml-10 {
    margin-left: 10px !important;
}

.card_child {
    padding: 1rem;
}

.switchBtn .textHight {
    line-height: 28px;
}

.probe-table .min_column_width {
    min-width: 55px !important;
    max-width: 78px !important;
}