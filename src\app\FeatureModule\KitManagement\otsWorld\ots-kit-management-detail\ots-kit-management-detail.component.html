<!-- loading start -->
<!-- loading gif start -->
<div class="ringLoading" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!-- loading gif end -->
<!-- loading end -->

<body class="bg-white" *ngIf="otsDetailPageDisplay">

    <!-- main container start -->
    <div class="container-fluid">
        <!-- row start -->
        <div class="row">
            <div class="col-md-12">
                <!-------------------------------->
                <!-- header row start -->
                <!-------------------------------->
                <div class="row headerAlignment">
                    <!--  header start -->
                    <label class="childFlex h5-tag">OTS Kit Management Detail&nbsp;-&nbsp;<span>Rev
                            version {{otsKitManagementDetailResponse?.revVersion}}</span></label>
                    <!--  header end -->

                    <div class="childFlex">
                        <!-- back button start -->
                        <button class="btn btn-sm btn-outline-secondary device-back-btn" (click)="back()"
                            id="otsDetailBack"><i class="fa fa-reply" aria-hidden="true"></i>&nbsp;&nbsp;Back</button>
                        <!-- back button end -->

                        <!-- refresh button start -->
                        <button class="btn btn-sm btn-orange ml-2" (click)="refreshOtskitDetailPage()"
                            id="otsDetailRefresh"><em class="fa fa-refresh"></em></button>
                        <!-- refresh button end -->
                    </div>
                </div>
                <!-------------------------------->
                <!--  detail header row end -->
                <!---######################################################################-->
                <!-------------------------------->
                <!-- otsKitManagementDetailResponse detail fields row start -->
                <!-------------------------------->
                <div class="row">
                    <div class="col-md-12">
                        <!-------------------------------->
                        <!-- main card start -->
                        <!-------------------------------->
                        <div class="card">
                            <div class="card-body">
                                <div class="card shadow">
                                    <div class="card-body">
                                        <div class="row">
                                            <!-------------------------------->
                                            <!-------------------------------->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">Country</strong></label>
                                                    <input type="text" [value]="otsKitManagementDetailResponse?.country"
                                                        class="form-control" name="Country" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">Kit Part Number</strong></label>
                                                    <input type="text"
                                                        [value]="otsKitManagementDetailResponse?.otsKitPartNumberCode"
                                                        class="form-control" name="KitPartNumber" readonly>
                                                </div>
                                            </div>

                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">Last Modified Date & Time</strong></label>
                                                    <input type="text"
                                                        [value]="otsKitManagementDetailResponse?.modifiedDate | date:'MMM d, y, h:mm:ss a'"
                                                        class="form-control" name="LastModifiedTime" readonly>
                                                </div>
                                            </div>

                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <label><strong class="">Description</strong></label>
                                                    <textarea type="text"
                                                        [value]="otsKitManagementDetailResponse?.description" rows="3"
                                                        class="form-control textAreaResize" name="Description"
                                                        readonly></textarea>
                                                </div>
                                            </div>
                                            <!-------------------------------->
                                            <!-------------------------------->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-------------------------------->
                        <!-- main card start -->
                        <!-------------------------------->
                    </div>
                </div>

                <!---######################################################################-->

                <div class="row" id="otsProbeDeatilTable">
                    <div class="col-md-12">
                        <!-------------------------------->
                        <!-- main card start -->
                        <!-------------------------------->
                        <div class="card">
                            <div class="card-body">
                                <div class="card shadow">
                                    <div class="card-body">
                                        <div class="row tablePadding">
                                            <div class="col-md-8">
                                                <!-------------------------------->
                                                <!-------------------------------->
                                                <label class="h6-tag mb-3"><u>Probe Details</u></label>
                                                <table class="table table-sm table-bordered" aria-hidden="true">
                                                    <thead>
                                                        <tr class="thead-light">
                                                            <th>Sr. No.</th>
                                                            <th>Probe Part Number</th>
                                                            <th>Probe Config Group</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <ng-template ngFor let-probeResponseObj
                                                            [ngForOf]="otsKitManagementDetailResponse?.probes"
                                                            let-probeIndex="index">
                                                            <tr>
                                                                <td>{{probeIndex + 1}}</td>
                                                                <td>{{probeResponseObj.probePartNumberCode}}
                                                                </td>
                                                                <td>
                                                                    <ul class="ulList m-0 pl-3">
                                                                        <ng-container
                                                                            *ngIf="probeConfigGroupPermission">
                                                                            <li class="spanunderline"
                                                                                *ngFor="let probe of probeResponseObj.probeConfigGroups"
                                                                                id="otsDetailToPcg"
                                                                                (click)="showFeature(probe.probeConfigGroupId)">
                                                                                {{probe.probeConfigGroupPartNumberCode}}
                                                                            </li>
                                                                        </ng-container>

                                                                        <ng-container
                                                                            *ngIf="!probeConfigGroupPermission">
                                                                            <li *ngFor="let probe of probeResponseObj.probeConfigGroups"
                                                                                id="otsDetailToPcg">
                                                                                {{probe.probeConfigGroupPartNumberCode}}
                                                                            </li>
                                                                        </ng-container>
                                                                    </ul>
                                                                </td>
                                                            </tr>

                                                        </ng-template>
                                                    </tbody>
                                                </table>
                                                <!-------------------------------->
                                                <!-------------------------------->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-------------------------------->
                        <!-- main card start -->
                        <!-------------------------------->
                    </div>
                </div>
                <!---######################################################################-->
            </div>
        </div>
    </div>
</body>

<!--Probe Config Group Detail start-->
<div *ngIf="featureDetailPageDisplay">
    <app-probe-config-group-detail (showListingPage)="showOtsdetail()" [probeConfigGroupId]="
        moduleDetailId"></app-probe-config-group-detail>
</div>
<!--Probe Config Group Detail End-->