import { Pipe, PipeTransform } from '@angular/core';
import { AuditListResponse } from 'src/app/model/Audit/AuditListResponse';
import { AuditModuleEnum } from '../enum/Audit/AuditModuleEnum.enum';
import { PermissionService } from '../permission.service';
import { AuditActionEnum } from '../enum/Audit/AuditActionEnum.enum';
import { PermissionAction } from '../enum/Permission/permissionAction.enum';

@Pipe({
  name: 'moduleDetailPageDisplayPermissionCheckPipe',
})
export class ModuleDetailPageDisplayPermissionCheckPipe implements PipeTransform {

  constructor(private permissionService: PermissionService) { }

  /**
   * Permission check For Module
   * <AUTHOR> @param auditListResponse 
   * @param modulePermissionMapping 
   * @returns 
   */
  transform(auditListResponse: AuditListResponse, modulePermissionMapping: Map<string, boolean>): boolean {

    if (auditListResponse.module == AuditModuleEnum.KIT_MANAGEMENT) {
      let otsKitManagementAction: Array<string> = [AuditActionEnum.OTS_KIT_MANGEMENT_ADD_IMPORT_CSV, AuditActionEnum.OTS_KIT_MANGEMENT_DELETE_IMPORT_CSV];
      let bridgeKitManagementAction: Array<string> = [AuditActionEnum.BRIDGE_KIT_MANGEMENT_ADD_IMPORT_CSV, AuditActionEnum.BRIDGE_MANGEMENT_DELETE_IMPORT_CSV]
      return (otsKitManagementAction.includes(auditListResponse.action) &&
        this.permissionService.getKitManagementPermission(PermissionAction.GET_OTS_KIT_MANAGEMENT_ACTION)) ||
        (bridgeKitManagementAction.includes(auditListResponse.action) &&
          this.permissionService.getKitManagementPermission(PermissionAction.GET_BRIDGE_KIT_MANAGEMENT_ACTION));
    } else {
      return modulePermissionMapping?.get(auditListResponse?.module);
    }
  }

}
