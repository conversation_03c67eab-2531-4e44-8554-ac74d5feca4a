import { Injectable } from '@angular/core';
import { NgbModal, NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SoftwareBuildConfirmComponent } from '../../Software-build-confirm/software-build-confirm.component';

@Injectable({
  providedIn: 'root'
})
export class SoftWareBuildConformationService {

  constructor(private modalService: NgbModal) { }

  public openInventoryOperationModel(
    btnOkText: string,
    btnCancelText: string,
    header: string,
    mainbody: string,
    activeModelReference?: NgbActiveModal,
    dialogSize: 'sm' | 'lg' = 'sm'): Promise<boolean> {
    const modalRef = this.modalService.open(SoftwareBuildConfirmComponent, { windowClass: "modal fade" });
    modalRef.componentInstance.header = header;
    modalRef.componentInstance.mainbody = mainbody;
    modalRef.componentInstance.btnOkText = btnOkText;
    modalRef.componentInstance.btnCancelText = btnCancelText;
    modalRef.componentInstance.activeModelReference = activeModelReference;
    return modalRef.result;

  }

}
