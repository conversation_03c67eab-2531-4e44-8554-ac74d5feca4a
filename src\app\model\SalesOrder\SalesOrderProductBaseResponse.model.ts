import { ProductConfigStatus } from "src/app/shared/enum/SalesOrder/ProductConfigStatus.enum";

export class SalesOrderProductBaseResponse {
    sopmId: number;
    entitySerialNumber: string;
    entityStatus: ProductConfigStatus;
    entityPk: number;

    constructor(sopmId: number, entitySerialNumber: string, entityStatus: ProductConfigStatus, entityPk: number) {
        this.sopmId = sopmId;
        this.entitySerialNumber = entitySerialNumber;
        this.entityStatus = entityStatus;
        this.entityPk = entityPk;
    }
}
