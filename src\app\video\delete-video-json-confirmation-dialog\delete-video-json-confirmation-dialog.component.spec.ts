import { TestBed } from '@angular/core/testing';
import { DeleteVideoJsonConfirmationDialogComponent } from './delete-video-json-confirmation-dialog.component';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

describe('DeleteVideoJsonConfirmationDialogComponent', () => {
  let component: DeleteVideoJsonConfirmationDialogComponent;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DeleteVideoJsonConfirmationDialogComponent],
      providers: [NgbActiveModal]
    }).compileComponents();

    component = TestBed.createComponent(DeleteVideoJsonConfirmationDialogComponent).componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
