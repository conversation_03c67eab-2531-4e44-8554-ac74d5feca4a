/**------------------------------------------------------ */
/**Angular Version Update 15 to 17.2.1 */
/**------------------------------------------------------ */

/**
 * Default Dependencies 
 */

"@angular/animations"
"@angular/common" 
"@angular/compiler"
"@angular/core"
"@angular/forms"
"@angular/platform-browser"
"@angular/platform-browser-dynamic"
"@angular/router"
"rxjs"
"tslib"
"zone.js"

/**
 * Dependencies
 */
 "@angular/localize" --> Used For messages in your code that needs to be translated
 "@angular/material" --> Used For material component like calender
 "@fortawesome/angular-fontawesome" --> Used For stying
 "@fortawesome/fontawesome-svg-core" --> Used For stying and Icon
 "@fortawesome/free-solid-svg-icons"--> Used For Icon
 "@ng-bootstrap/ng-bootstrap" --> Used For stying
 "bootstrap" --> Used For stying
 "bootstrap-toggle" --> Used For stying
 "core-js" --> Used For stying
 "font-awesome-icons" --> Used For icon
 "hammerjs" --> Used For stying
 "is-what" -> Used for Check variable,object is isNull,isNullOrUndefine or isUndefine 
 "jquery" --> Used For animations 
 "jszip" --> multiple file add in zip 
 "ng-multiselect-dropdown" --> Multiselect Dropdown for single selction and multiple selection
 "ngx-cookie" --> Store data in cookies
 "ngx-toastr" --> Snackbar message Display
 "ngx-webstorage" --> Store,Read data in Local Storage

/**
 * Defalut devDependencies 
 */

"@angular-devkit/build-angular"
"@angular/cli"
"@angular/compiler-cli" 
"@types/jasmine" 
"jasmine-core"
"karma" 
"karma-chrome-launcher" 
"karma-coverage" 
"karma-jasmine" 
"karma-jasmine-html-reporter" 
"typescript"



