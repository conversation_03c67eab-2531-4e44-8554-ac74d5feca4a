import { BasicSalesOrderDetailResponse } from "./SalesOrder/BasicSalesOrderDetailResponse.model";

export class CustomerAssociationRequest {
    public button: boolean;
    public isSalesOrderNewAdd: boolean;
    public basicSalesOrderDetailResponse: BasicSalesOrderDetailResponse;

    constructor($button: boolean, $isSalesOrderNewAdd: boolean, $basicSalesOrderDetailResponse: BasicSalesOrderDetailResponse) {
        this.button = $button;
        this.isSalesOrderNewAdd = $isSalesOrderNewAdd;
        this.basicSalesOrderDetailResponse = $basicSalesOrderDetailResponse;
    }
}