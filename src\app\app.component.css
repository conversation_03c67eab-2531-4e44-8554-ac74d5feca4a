@import '../../src/assets/css/custom_style.css';

.emptyAppComponentCssChanges {
  margin: 1px;
}

.version-text {
  font-size: 12px;
  padding-top: 4px;
}

.h5-tag {
  font-size: 1.25rem;
  font-weight: 500;
  line-height: 1.2;
}

.cust-link {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  text-align: left;
  background-color: #fff;
}

/*Loading css*/
.ringLoading_app {
  position: fixed;
  background-color: rgba(255, 255, 255, 0.6);
  width: 100%;
  height: 100%;
  z-index: 100000;
  display: block;
  top: 0px;
  left: 0px;
}

.ringLoading_app .ringLoadingDiv {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.header-app {
  color: #fff;
  height: 30px;
  background-color: #989898;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.15) 0%, rgba(0, 0, 0, 0.15) 100%),
    radial-gradient(at top center, rgba(255, 255, 255, 0.40) 0%, rgba(0, 0, 0, 0.40) 120%);
  background-blend-mode: multiply, multiply;
  vertical-align: center;
}