<div>
  <!------------------------------------->
  <!--------------Title Start------------>
  <!------------------------------------->
  <div class="modal-header">
    <label class="modal-title">{{ createAndUpdateRoleModelRequest?.title }}</label>
  </div>
  <!------------------------------------->
  <!--------------Title End------------>
  <!------------------------------------->

  <!------------------------------------->
  <!-- dialog body - start -->
  <!------------------------------------->
  <div class="modal-body" id="roleCreate">
    <div>
      <form name='createRole' [formGroup]="createRoleForm" *ngIf="isFormReload">
        <table class="w-table" aria-hidden="true">

          <tr>
            <th class="upload-title w-title pb-2" id="Role_Name">
              <span class="pr-2">Role Name</span>
            </th>
            <td class="w-control pb-2">
              <ng-template [ngIf]="createAndUpdateRoleModelRequest.isUpdateData">
                <input tabindex="-1" class="form-control" type="text" formControlName="roleName" name='roleName'
                  autocomplete="off" readonly />
              </ng-template>
              <ng-template [ngIf]="!createAndUpdateRoleModelRequest.isUpdateData">
                <input tabindex="-1" class="form-control" type="text" formControlName="roleName" name='roleName'
                  autocomplete="off" />
              </ng-template>
            </td>
          </tr>
          <!------------------------------------------------>
          <!-----------Error Message For Role Name---------->
          <!--1 required Role Name -->
          <!--2 Role Name maxlength validation error-->
          <!------------------------------------------------>
          <tr>
            <td></td>
            <td>
              <div *ngIf="(createRoleForm.get('roleName').touched || createRoleForm.get('roleName').dirty) && 
              createRoleForm.get('roleName').invalid">
                <div *ngIf="createRoleForm.get('roleName').errors['required']" class="pb-2">
                  <span class="alert-color font-12"> Role Name is required</span>
                </div>
                <div *ngIf="createRoleForm.get('roleName').errors['maxlength']" class="pb-2">
                  <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
                </div>
                <div *ngIf="createRoleForm.get('roleName').errors['pattern']" class="pb-2">
                  <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
                </div>
              </div>
            </td>
          </tr>
          <!----------------------------------->
          <!-----------Permission-------------->
          <!----------------------------------->
          <tr>
            <th class="upload-title w-title pb-2" id="Permission">
              <span class="pr-2">Permission</span>
            </th>
            <td class="w-control pb-2">
              <ng-multiselect-dropdown name="pacsType" [placeholder]="''" [settings]="permissionSetting"
                [data]="permissionList" formControlName="permission">
              </ng-multiselect-dropdown>
            </td>
          </tr>

          <tr>
            <th class="upload-title w-title pb-2" id="Descriptions">
              <span class="pr-2">Descriptions</span>
            </th>
            <td class="w-control pb-2">
              <textarea tabindex="-1" class="form-control textAreaResize" rows="4" autocomplete="off"
                formControlName="description"></textarea>
            </td>
          </tr>
          <!------------------------------------------------>
          <!-----------Error Message For Descriptions---------->
          <!--1 Descriptions maxlength validation error-->
          <!------------------------------------------------>
          <tr>
            <td></td>
            <td>
              <div *ngIf="(createRoleForm.get('description').touched || createRoleForm.get('description').dirty) && 
                    createRoleForm.get('description').invalid">
                <div *ngIf="createRoleForm.get('description').errors['maxlength']" class="pb-2">
                  <span class="alert-color font-12">{{textAreaMaxLengthMessage}}</span>
                </div>
              </div>
            </td>
          </tr>
          <!------------------------------------------------------->
          <!-----------Error Message For Descriptions end---------->
          <!------------------------------------------------------->
        </table>
      </form>
    </div>
  </div>
  <!------------------------------------->
  <!-- dialog buttons - start -->
  <!------------------------------------->
  <div class="modal-footer">
    <button type="button" tabindex="-1" class="btn btn-sm btn-outline-secondary" id="roleCancelButton"
      (click)="decline()">{{
      createAndUpdateRoleModelRequest?.cancelButton }}</button>
    <button type="button" class="btn btn-sm btn-orange" id="uploadBtn" (click)="addOrUpdateRole()"
      [disabled]="createRoleForm.invalid">{{
      createAndUpdateRoleModelRequest?.okButton }}</button>
  </div>

</div>
<!------------------------------------->
<!------------------------------------->