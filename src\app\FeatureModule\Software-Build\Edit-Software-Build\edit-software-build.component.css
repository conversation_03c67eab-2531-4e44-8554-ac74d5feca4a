@import '../../../../assets/css/custom_style.css';

#editInventoryDialog .upload-form-control {
    display: block;
    width: 100%;
    height: calc(1.5em + .75rem + 2px);
    padding: .375rem .75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    /* border: 1px solid #ced4da; */
    border: 1px solid #adadad;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out, box-shadow .15s ease-in-out;
}

#editInventoryDialog .upload-form-control:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 25%);
}

#editInventoryDialog .upload-title {
    color: #495057;
    font-weight: bold;
}

.upload-success {
    vertical-align: middle;
    color: #6bad47;
    font-weight: 400;
}

#editInventoryDialog table {
    border-collapse: separate;
    border-spacing: 0 0.5rem;
}


#editInventoryDialog ::ng-deep .multiselect-dropdown {
    margin-left: 8px;
}

#editInventoryDialog .validation {
    color: #c70e24;
    font-size: 12px;
}

#editInventoryDialog .validationMainDiv {
    padding: 0 .75rem;
}

.editItemInventoryModelSize {
    min-width: 400px;
}

#editInventoryDialog .disabledTextBox {
    background-color: lightgray;
}