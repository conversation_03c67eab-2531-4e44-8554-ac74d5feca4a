import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { PrintListPipe } from './printList.pipe';

@Pipe({
    name: 'modelDisplayNameListToStringConvert'
})
export class ModelDisplayNameListToStringConvert implements PipeTransform {

    constructor(private printListPipe: PrintListPipe) { }

    transform(list: Array<string>): any {
        if (isNullOrUndefined(list)) {
            return null;
        } else {
            return this.printListPipe.transform(list);
        }
    }

}
