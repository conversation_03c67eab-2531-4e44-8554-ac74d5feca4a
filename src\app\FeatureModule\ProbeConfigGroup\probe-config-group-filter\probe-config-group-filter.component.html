<form id="probeConfigGroupFilterform" class="firm" [formGroup]="probeConfigGroupForm">
  <div class="form-group">
    <label name="form-control-label" for="field_countryName"><strong>Part Number</strong></label>
    <input class="form-control" type="text" formControlName="partNumber" />
    <div *ngIf="
        (probeConfigGroupForm.get('partNumber').touched ||
        probeConfigGroupForm.get('partNumber').dirty) &&
        probeConfigGroupForm.get('partNumber').invalid
      ">
      <div *ngIf="probeConfigGroupForm.get('partNumber').errors['maxlength']">
        <span class="alert-color font-12">{{
          small_textBoxMaxLengthMessage
          }}</span>
      </div>
      <div *ngIf="probeConfigGroupForm.get('partNumber').errors['pattern']">
        <span class="alert-color font-12">{{
          specialCharacterErrorMessage
          }}</span>
      </div>
    </div>
  </div>
  <!-- probe type form group start -->
  <div class="form-group">
    <label class="form-control-label" for="field_probeTypes" id="label_probeTypes"><strong>Probe Types</strong></label>
    <ng-multiselect-dropdown id="field_probeTypes" name="probeTypes" [placeholder]="''" formControlName="probeType"
      [settings]="dropdownSettingsForProbeType" [data]="probeTypesList">
    </ng-multiselect-dropdown>
  </div>
  <!-- probe type form group end -->


  <!-- probe Preset form group start -->
  <div class="form-group">
    <label class="form-control-label" for="field_probePresets" id="label_probePresets"><strong>Presets
      </strong></label>
    <ng-multiselect-dropdown id="field_probePresets" name="probePresets" [placeholder]="''" formControlName="presetType"
      [settings]="dropdownSettingsPreset" [data]="presetList">
    </ng-multiselect-dropdown>
  </div>
  <!-- probe Preset form group end -->

  <!-- probe features form group start -->
  <div class="form-group">
    <label class="form-control-label" for="field_probeFeatures" id="label_probeFeatures"><strong>Features
      </strong></label>
    <ng-multiselect-dropdown id="field_probeFeatures" name="probeFeatures" [placeholder]="''"
      formControlName="featureType" [settings]="dropdownSettingsFeature" [data]="featureList">
    </ng-multiselect-dropdown>
  </div>
  <!-- probe features form group end -->

  <hr class="mt-1 mb-2" />
  <!--####################################################-->
  <!---------Action Button Start------->
  <!--####################################################-->
  <div class="">
    <button class="btn btn-sm btn-orange mr-3" [disabled]="probeConfigGroupForm.invalid" (click)="searchData()"
      id="pcgFilterSearch">
      Search
    </button>
    <button class="btn btn-sm btn-orange" (click)="clearFilter(defaultListingPageReloadSubjectParameter)">
      Clear
    </button>
  </div>
  <!--####################################################-->
  <!---------Action Button End------->
  <!--####################################################-->
</form>