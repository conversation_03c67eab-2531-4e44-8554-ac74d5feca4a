import { HttpBackend, HttpClient, HttpErrorResponse } from "@angular/common/http";
import { Injectable } from "@angular/core";
import * as JSZip from "jszip";
import { ToastrService } from "ngx-toastr";
import { firstValueFrom, Subject } from "rxjs";
import { ProbDetailResource } from "src/app/app.constants";

@Injectable({
    providedIn: 'root'
})
export class DownloadService {

    http: HttpClient;

    constructor(public handler: HttpBackend,
        private toste: ToastrService) {
        this.http = new HttpClient(handler);
    }

    private isLoadingSubject = new Subject<boolean>();
    private downloadZipFileForProbSubject = new Subject<boolean>();

    public getisLoadingSubject(): Subject<boolean> {
        return this.isLoadingSubject;
    }

    public setLoading(status: boolean, resource: string): void {
        if (resource == ProbDetailResource) {
            this.isLoadingSubjectForProbDetailPage.next(status);
        } else {
            this.isLoadingSubject.next(status);
        }
    }

    public getdownloadZipFileForProbSubject(): Subject<boolean> {
        return this.downloadZipFileForProbSubject;
    }

    public subscribeDownloadZipFileForProbSubject(status: boolean): void {
        this.downloadZipFileForProbSubject.next(status);
    }

    //probDetailPage
    private isLoadingSubjectForProbDetailPage = new Subject<boolean>();
    private downloadZipFileForProbDetailPageSubject = new Subject<boolean>();

    public getisLoadingSubjectForProbDetailPage(): Subject<boolean> {
        return this.isLoadingSubjectForProbDetailPage;
    }

    public setisLoadingSubjectForProbDetailPage(status: boolean): void {
        this.isLoadingSubjectForProbDetailPage.next(status);
    }

    public getdownloadZipFileForProbDetailPageSubject(): Subject<boolean> {
        return this.downloadZipFileForProbDetailPageSubject;
    }

    public subscribedownloadZipFileForProbDetailPageSubject(status: boolean): void {
        this.downloadZipFileForProbDetailPageSubject.next(status);
    }

    public async dowloadInZipFile(urlList: string[], fileName: string, downloadErrorMessage: string, resource: string) {
        this.setLoading(true, resource);
        let zip = new JSZip();
        let blobErrorList = [];
        if (urlList.length > 1) {
            for (let srt of urlList) {
                const srtfileData: any = await this.getFile(srt);
                const srtFileType: any = new Blob([srtfileData], { type: '' + srtfileData.type + '' });
                if (srtfileData instanceof Blob) {
                    zip.file(this.decodeFileName(srt), srtFileType);
                } else {
                    blobErrorList.push(this.decodeFileName(srt));
                }
            }
        }
        if (blobErrorList.length == urlList.length) {
            this.setLoading(false, resource);
            this.toste.warning(downloadErrorMessage);
        } else if (urlList.length == 1) {
            this.downloadMyFile(urlList[0]);
            this.setLoading(false, resource);
        } else {
            let that = this;
            zip.generateAsync({ type: "blob" }).then(function (content) {
                if (content) {
                    let downloadLink = document.createElement('a');
                    downloadLink.href = window.URL.createObjectURL(new Blob([content], { type: content.type }));
                    downloadLink.setAttribute('download', fileName);
                    document.body.appendChild(downloadLink);
                    downloadLink.click();
                }
                that.setLoading(false, resource);
            });
        }
    }

    /**
   * 
   * @param url 
   * @returns File
   */
    public async getFile(url: string): Promise<Blob | HttpErrorResponse> {
        const httpOptions = {
            responseType: 'blob' as 'json',
            headers: { noAuth: "yes" }
        };
        try {
            const observable = this.http.get<Blob>(url, httpOptions);
            return await firstValueFrom(observable);
        } catch (error) {
            if (error instanceof HttpErrorResponse) {
                return error;
            }
            throw error; // Rethrow any other errors
        }
    }

    /**
     * Create Download link from URI
     * @param attachmentUrl 
     * @param filename 
     */
    public async downloadMyFile(attachmentUrl: string) {
        try {
            const response = await fetch(attachmentUrl);

            // Check if the response status is OK (status code 200–299)
            if (!response.ok) {
                let errorMessage = '';

                // Customize messages based on status code
                switch (response.status) {
                    case 404:
                        errorMessage = 'The requested file was not found.';
                        break;
                    case 500:
                        errorMessage = 'There was a server error while processing your request. Please try again later.';
                        break;
                    case 403:
                        errorMessage = 'You do not have permission to download this file.';
                        break;
                    case 401:
                        errorMessage = 'You are not authorized. Please log in and try again.';
                        break;
                    default:
                        errorMessage = 'An error occurred while trying to download the file. Please try again later.';
                }

                this.toste.error(errorMessage);
                return; // Handle the error appropriately (e.g., show an error message)
            }

            const blobData = await response.blob();
            const filename: string = this.decodeFileName(attachmentUrl);
            const url = URL.createObjectURL(blobData);

            const link = document.createElement('a');
            link.setAttribute('download', filename);
            link.setAttribute('href', url);
            link.setAttribute('target', '_blank');

            document.body.appendChild(link);
            link.click();
            link.remove();
        } catch (error) {
            console.log('An error occurred while downloading the file:', error);
        }
    }


    /**
     * 
     * @param fileName 
     * @param response 
     * download csv file
     */
    public downloadTemplate(fileName: string, response): void {
        let dataType = response.type.toString();
        let binaryData = [];
        binaryData.push(response.body);
        this.downloadLink(fileName, dataType, binaryData);
    }

    public downloadExportCSV(fileName: string, response): void {
        let dataType = response.type;
        let binaryData = [];
        binaryData.push(response);
        this.downloadLink(fileName, dataType, binaryData);
    }

    /**
     * Download Link data
     * <AUTHOR>
     * @param fileName 
     * @param dataType 
     * @param binaryData 
     */
    private downloadLink(fileName: string, dataType: string, binaryData: Array<any>): void {
        let downloadLink = document.createElement('a');
        downloadLink.href = window.URL.createObjectURL(new Blob(binaryData, { type: dataType }));
        downloadLink.setAttribute('download', fileName);
        document.body.appendChild(downloadLink);
        downloadLink.click();
    }

    /**
     * Decode File name
     * @param url 
     * @returns 
     */
    public decodeFileName(url: string): string {
        let blobPath = url.substring(0, url.indexOf('?'));
        let decodeUrl = decodeURIComponent(blobPath);
        return decodeUrl.substring(decodeUrl.lastIndexOf('/') + 1);
    }



}