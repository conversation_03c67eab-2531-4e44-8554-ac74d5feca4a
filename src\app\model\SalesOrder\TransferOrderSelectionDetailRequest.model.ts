import { SalesOrderTransferProductRequest } from "../device/SalesOrderTransferProductRequest.model";

export class TransferOrderSelectionDetailRequest {
    sourceSalesOrderId: number;
    destinationSalesOrderId: number;
    sourceDeviceSelected: SalesOrderTransferProductRequest;
    sourceProbeSelected: SalesOrderTransferProductRequest;

    constructor(
        sourceSalesOrderId: number,
        destinationSalesOrderId: number,
        sourceDeviceSelected: SalesOrderTransferProductRequest,
        sourceProbeSelected: SalesOrderTransferProductRequest
    ) {
        this.sourceSalesOrderId = sourceSalesOrderId;
        this.destinationSalesOrderId = destinationSalesOrderId;
        this.sourceDeviceSelected = sourceDeviceSelected;
        this.sourceProbeSelected = sourceProbeSelected;
    }
}
