<!-- loading start -->
<div class="ringLoading" *ngIf="loading">
    <!-- loading gif start -->
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!-------------------------->
<!-- loading end -->

<body>
    <!-- row start -->
    <div class="row">
        <!--Filter start-->
        <div class="col-md-3 pr-0" *ngIf="(!isFilterHidden)">
            <!-- filter header start -->
            <label class="col-md-12 h5-tag">Filter</label>
            <!-- filter header end -->
            <div class="card mt-3">
                <!-- card body start -->
                <div class="card-body">
                    <!-- filter form start -->
                    <form id="filter-form" [formGroup]="filterForm" role="form" class="form">
                        <!-- itemNumber field start -->
                        <div class="form-group">
                            <label class="form-control-label" for="field_itemNumber"><strong>Version
                                </strong></label>
                            <!-- itemNumber input start -->
                            <input class="form-control" type="text" formControlName="itemNumber" />
                            <!-- itemNumber input end -->
                            <!-- validation for itemNumber start -->
                            <div *ngIf="(filterForm.get('itemNumber').touched || filterForm.get('itemNumber').dirty) &&
                                filterForm.get('itemNumber').invalid ">
                                <div *ngIf="filterForm.get('itemNumber').errors['maxlength']">
                                    <p class="alert-color">{{textBoxMaxCharactersAllowedMessage}}</p>
                                </div>
                                <div *ngIf="filterForm.get('itemNumber').errors['pattern']">
                                    <p class="alert-color">{{specialCharacterErrorMessage}}</p>
                                </div>
                            </div>
                            <!-- validation for itemNumber end -->
                        </div>
                        <!-- itemNumber field end -->
                        <!-- Country filter start -->
                        <div class="form-group">
                            <!-- Country label start -->
                            <label class="form-control-label" for="field_Country"><strong>Country</strong></label>
                            <!-- Country label end -->
                            <!-- Country multiselect dropdown start -->
                            <ng-multiselect-dropdown name="Country" [placeholder]="''" formControlName="country"
                                [settings]="dropdownSettingsCountry" [data]="countryList">
                            </ng-multiselect-dropdown>
                            <!-- Country multiselect dropdown end -->
                        </div>
                        <!-- Country filter end -->
                        <!-- json version filter start -->
                        <div class="form-group">
                            <!-- json version label start -->
                            <label class="form-control-label" for="field_videoJsonVersions"><strong>Json
                                    Version</strong></label>
                            <!-- json version label end -->
                            <!-- json version multiselect dropdown start -->
                            <ng-multiselect-dropdown name="jsonVersions" [placeholder]="''"
                                formControlName="jsonVersions" [settings]="dropdownSettingsJsonVersions"
                                [data]="jsonVersionList">
                            </ng-multiselect-dropdown>
                            <!-- json version multiselect dropdown end -->
                        </div>
                        <!-- json version filter end -->
                        <!-- inventory status filter start -->
                        <div class="form-group">
                            <!-- inventory status label start -->
                            <label class="form-control-label"
                                for="field_inventoryStatus"><strong>Status</strong></label>
                            <!-- inventory status label end -->
                            <!-- inventory multiselect dropdown start -->
                            <ng-multiselect-dropdown class="devicePageDeviceType" name="inventoryStatus"
                                [placeholder]="''" formControlName="inventoryStatus"
                                [settings]="dropdownSettingsInventoryStatus" [data]="inventoryStatus">
                            </ng-multiselect-dropdown>
                            <!-- inventory multiselect dropdown end -->
                        </div>
                        <!-- inventory status filter end -->
                        <!-- device type filter start -->
                        <div class="form-group">
                            <label class="form-control-label" for="field_deviceType"><strong>Device
                                    Type</strong></label>
                            <!-- device type multiselect dropdown start -->
                            <ng-multiselect-dropdown class="devicePageDeviceType" name="deviceType" [placeholder]="''"
                                formControlName="deviceType" [settings]="dropdownSettingsDeviceType"
                                [data]="deviceTypes">
                            </ng-multiselect-dropdown>
                            <!-- device type multiselect dropdown end -->
                        </div>
                        <!-- device type filter end -->
                        <!-- partNumber field start -->
                        <div class="form-group">
                            <label class="form-control-label" for="field_partNumber"><strong>Part Number
                                </strong></label>
                            <!-- partNumber input start -->
                            <input class="form-control" type="text" formControlName="partNumber" />
                            <!-- partNumber input end -->
                            <!-- validation for partNumber start -->
                            <div
                                *ngIf="(filterForm.get('partNumber').touched || filterForm.get('partNumber').dirty) && filterForm.get('partNumber').invalid ">
                                <div *ngIf="filterForm.get('partNumber').errors['maxlength']">
                                    <p class="alert-color">{{textBoxMaxCharactersAllowedMessage}}</p>
                                </div>
                                <div *ngIf="filterForm.get('partNumber').errors['pattern']">
                                    <p class="alert-color">{{specialCharacterErrorMessage}}</p>
                                </div>
                            </div>
                            <!-- validation for partNumber end -->
                        </div>
                        <!-- partNumber field end -->

                        <hr class="mt-1 mb-2">
                        <div class="">
                            <!-- search filter button start -->
                            <button class="btn btn-sm btn-orange mr-3" (click)="searchInventoryFilter()"
                                id="inventoryFilterSearch" [disabled]="filterForm.invalid">Search</button>
                            <!-- search filter button end -->
                            <!-- clear button start -->
                            <button class="btn btn-sm btn-orange" (click)="clearFilter()">Clear</button>
                            <!-- clear button end -->
                        </div>
                    </form>
                    <!-- filter form end -->
                </div>
                <!-- card body end -->
            </div>
        </div>
        <!--Filter End-->

        <!--table Block Start-->
        <div [className]="(isFilterHidden)?'col-md-12 pr-0':'col-md-9 pr-0'">
            <!-- container start -->
            <div class="container-fluid" id="itemInventoryList">
                <!--view Item Inventory Start-->
                <div class="row" class="headerAlignment">
                    <!--------------------------------------->
                    <!--Left Side-->
                    <!--------------------------------------->
                    <div class="childFlex">
                        <!-- hide show filter button div start -->
                        <div class="dropdown" id="itemInventoryHideShowFilter">
                            <!-- hide Show FilterButton start -->
                            <button class="btn btn-sm btn-orange mr-3 ml-0 filter-symbol" (click)="toggleFilter()"
                                [id]="(isFilterHidden)?'Show Filter':'Hide Filter'">
                                <i class="fas fa-filter" aria-hidden="true"
                                    [id]="(isFilterHidden)?'Show Filter':'Hide Filter'"></i>
                                &nbsp;&nbsp;{{ hideShowFilterButtonText }}
                            </button>
                            <!-- hide Show FilterButton end -->
                        </div>
                        <!-- hide show filter button div end -->
                        <div>
                            <label class="mb-0">Show entry</label>
                            <!-- selection of show entry start -->
                            <select id="itemInventoryShowEntry" [(ngModel)]="drpselectsize"
                                class="form-control form-control-sm" (change)="changeDataSize($event)">
                                <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                                    <option [value]="dataSize">{{ dataSize }}</option>
                                </ng-template>
                            </select>
                            <!-- selection of show entry end -->
                        </div>
                    </div>
                    <!--------------------------------------->
                    <!--Right Side-->
                    <!--------------------------------------->
                    <div class="childFlex">
                        <!-- device operation dropdown start -->
                        <div class="mr-3" *ngIf="updateSoftwareBuildPermission">
                            <!-- selection of inventory operation start -->
                            <select id="inventoryOperation" class="form-control form-control-sm"
                                (change)="changeInventoryOperation($event)">
                                <!-- inventory operation options start -->
                                <option [value]="softwareBuildOperations">
                                    {{softwareBuildOperations}}
                                </option>
                                <!-- inventory operation options end -->
                                <!-- map to client device option start -->
                                <option value="Map to Client Devices">
                                    Map to Client Devices
                                </option>
                                <!-- map to client device option end -->
                                <!-- map to demo device option start -->
                                <option value="Map to Demo Devices">
                                    Map to Demo Devices
                                </option>
                                <!-- map to demo device option end -->
                                <!-- map to both device type option start -->
                                <option value="Map to Both type of Devices">
                                    Map to Both type of Devices
                                </option>
                                <!-- map to both device type option end -->
                                <!-- mark as active option start -->
                                <option value="Mark as active">
                                    Mark as active
                                </option>
                                <!-- mark as active option end -->
                                <!-- mark as inactive option start -->
                                <option value="Mark as Inactive">
                                    Mark as Inactive
                                </option>
                                <!-- mark as inactive option end -->
                            </select>
                            <!-- selection of inventory operation end -->
                        </div>
                        <!--device operation dropdown end  -->

                        <!-- upload manuaaly button div start -->
                        <div *ngIf="uploadSoftwareBuildPermission">
                            <!-- upload mannualy button start -->
                            <button class="btn btn-sm btn-orange mr-3" (click)="confirmDownloadDataset()"
                                id="uploadManually">Upload
                                Manually</button>
                            <!-- upload mannualy button end -->
                        </div>
                        <div>
                            <!-- refresh button start -->
                            <button class="btn btn-sm btn-orange" id='refresh_InventoryList'
                                (click)="clickOnRefreshButton()"><em class="fa fa-refresh"></em></button>
                            <!-- refresh button end -->
                        </div>
                        <!-- upload manuaaly button div end -->
                    </div>
                </div>

                <!-- selected item inventories start -->
                <div>Total {{totalItemInventory}} Software Builds
                    <p *ngIf="inventoryIdList != null && inventoryIdList.length > 0">
                        <strong id="fileErrorMessage">{{inventoryIdList.length}} Software Build(s) selected</strong>
                    </p>
                </div>
                <!-- selected item inventories end -->
                <!-- inventory list table start -->
                <div class="table_scroll">
                    <table class="table table-sm table-bordered itemInventoryTable" aria-hidden="true">
                        <!-- table header start -->
                        <thead>
                            <tr class="thead-light">
                                <th hidden>Index</th>
                                <th *ngIf="updateSoftwareBuildPermission" class="checkox-table">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" name="chkselectall"
                                            [id]="selectAllCheckboxId"
                                            (change)="selectAllItem($any($event.target)?.checked)">
                                        <label class="custom-control-label" [for]="selectAllCheckboxId"></label>
                                    </div>
                                </th>
                                <th><span>Version</span></th>
                                <th><span>Title</span></th>
                                <th><span>Attachment</span></th>
                                <th><span>Release Notes</span></th>
                                <th><span>Json Version</span></th>
                                <th><span>Part Number</span></th>
                                <th><span>Created Date & Time</span></th>
                                <th><span>Country</span></th>
                                <th><span>Device Type</span></th>
                                <th><span>Status</span></th>
                                <th *ngIf="(deleteSoftwareBuildPermission || updateSoftwareBuildPermission)"
                                    class="checkox-table centered" colspan="2">
                                    <span>Action</span>
                                </th>
                            </tr>

                        </thead>
                        <!-- table header end -->
                        <!-- table body start -->
                        <tbody>
                            <tr *ngFor="let item of inventory; let i = index">
                                <td hidden>{{i}}</td>
                                <td *ngIf="updateSoftwareBuildPermission">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" [id]="'item_' + item.id "
                                            name="inventory[]" [checked]="inventoryIdList.includes(item.id)"
                                            (change)="onChangeInventory(item.id,item.countries, $event)">
                                        <label class="custom-control-label" [for]=" 'item_' + item.id"></label>
                                    </div>
                                </td>
                                <td>{{item.version}}</td>
                                <td data-toggle="tooltip" data-placement="top" title="{{item.title}}">{{item.title}}
                                </td>
                                <td data-toggle="tooltip" data-placement="top" title="{{item.attachmentName}}"
                                    class="tableDataNoWrap">
                                    <div class="downloadButtonPosition">
                                        <div>
                                            <span>{{item.attachmentName}}</span>
                                        </div>
                                        <div class="ml-2">
                                            <button *ngIf="item.id != null" class="btn btn-sm" id="getAttachmentUrlId"
                                                (click)="getAttachmentUrl(item.id)">
                                                <span class="Pointer video-filter"><em
                                                        class="fa fa-download"></em></span>
                                            </button>
                                        </div>
                                    </div>
                                </td>
                                <td data-toggle="tooltip" data-placement="top" title="{{item.releaseNoteName}}"
                                    class="tableDataNoWrap">
                                    <div class="downloadButtonPosition">
                                        <div>
                                            <span>{{item.releaseNoteName}}</span>
                                        </div>
                                        <div class="ml-2">
                                            <button *ngIf="item.id != null" class="btn btn-sm" id="getReleaseNoteUrlId"
                                                (click)="getReleaseNoteUrl(item.id)">
                                                <span class="Pointer video-filter"><em
                                                        class="fa fa-download"></em></span>
                                            </button>
                                        </div>
                                    </div>
                                </td>
                                <td class="tableDataNoWrap">
                                    <div class="downloadButtonPosition">
                                        <div>
                                            <span>{{item.jsonMaster | jsonDisplayName}}</span>
                                        </div>
                                        <div class="ml-2">
                                            <button *ngIf="item.jsonMaster!=null" class="btn btn-sm"
                                                id="downloadJSONFileId" (click)="downloadJSONFile(item.jsonMaster)">
                                                <span class="Pointer video-filter"><em
                                                        class="fa fa-download"></em></span>
                                            </button>
                                        </div>
                                    </div>
                                </td>
                                <td class="tableDataNoWrap">{{item.partNumber}}</td>
                                <td class="tableDataNoWrap">{{item.createdDate}}</td>
                                <td class="min-col-width">{{item.countries | modelDisplayNameListToStringConvert
                                    }}</td>
                                <td>{{item.deviceTypes | softwareBuildMappedDeviceName}}</td>
                                <td class="tableDataNoWrap">{{item.isActive | softwareBuildStatusDisplayName}}</td>
                                <td *ngIf="updateSoftwareBuildPermission">
                                    <div class="{{ item.countries | hideShowEditIconForSoftwareBuilde }}">
                                        <span class="Pointer video-filter btn btn-sm" id="editInventoryDetailsId"
                                            (click)="editInventoryDetails(item)">
                                            <em class="fa fa-pencil-alt"></em>
                                        </span>
                                    </div>
                                </td>
                                <td *ngIf="deleteSoftwareBuildPermission">
                                    <div class="{{ item.countries | hideShowEditIconForSoftwareBuilde  }}">
                                        <button type="submit" (click)="deleteItem(item.id, item.version)"
                                            [id]="'deleteSoftwearBuildItemId' + item.id" class="btn btn-sm">
                                            <em class="fas fa-trash-alt video-filter"></em>
                                        </button>
                                    </div>
                                </td>
                            </tr>

                        </tbody>
                        <!-- table body end -->

                    </table>
                </div>
                <!-- inventory list table end -->

                <!--paging Start-->
                <div>
                    <div>Showing {{totalItemInventoryDisplay}} out of {{totalItemInventory}} Software Builds</div>
                    <div class="float-right">
                        <!-- ngb pagination start -->
                        <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="itemsPerPage"
                            id="inventory-pagination" [maxSize]="5" [rotate]="true" [boundaryLinks]="true"
                            (pageChange)="loadPage(page)">
                        </ngb-pagination>
                        <!-- ngb pagination end -->

                    </div>
                </div>
                <!--paging End-->
            </div>
            <!-- container end -->
            <!--flud tab 9 row-->
        </div>
        <!--table Block End-->
    </div>
    <!-- row end -->
    <!--Item Inventory detail-->
</body>
<!--***********************************************************-->