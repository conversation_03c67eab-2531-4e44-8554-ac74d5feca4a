<!------------------------------------------------------------->
<!----------- Tranfer Order Selection ------------------------->
<!------------------------------------------------------------->
<ng-template [ngIf]="transferOrderSelectionDisaplay">
    <app-transfer-order-selection (showTranferOrder)="tansferOrderPagesToggle($event[0], $event[1], $event[2])"
        [transferProductDetail]="transferProductDetail" (destinationSalesOrderIdChange)="updateDestinationId($event)"
        (backToDetailPage)="backToDetailPage()">
    </app-transfer-order-selection>
</ng-template>
<!------------------------------------------------------------>
<!--------- Tranfer Order Selection End----------------------->
<!------------------------------------------------------------->

<!------------------------------------------------------------->
<!----------- Tranfer Order Validation Start ------------------>
<!------------------------------------------------------------->
<ng-template [ngIf]="transferOrderValidationDisaplay">
    <app-transfer-order-selection-and-review
        (showSelectionTable)="tansferOrderPagesToggle($event[0], $event[1], $event[2])"
        [destinationSalesOrderId]="destinationSalesOrderId" [transferProductDetail]="transferProductDetail"
        (selectedProbeAndDevice)="selectedProduct($event)"
        (transferOrderReviewRequest)="transferOrderReviewRequest($event)"
        (isSalesOrderManual)="bothSalesOrderIsManual($event)"></app-transfer-order-selection-and-review>
</ng-template>
<!------------------------------------------------------------>
<!----------- Tranfer Order Validation End ------------------->
<!------------------------------------------------------------>

<!------------------------------------------------------------->
<!----------- Tranfer Order Review Start ---------------------->
<!------------------------------------------------------------->
<ng-template [ngIf]="transferOrderReviewDisaplay">
    <app-transfer-order-review (hideAndShowTable)="tansferOrderPagesToggle($event[0], $event[1], $event[2])"
        [selectedProbeAndDevice]="sourceSelectedProbeAndDevice" [isSalesOrderManual]="salesOrderIsManual"
        [reviewOrderRequest]="transferOrderRequest" (backToDetailPage)="backToDetailPage()"></app-transfer-order-review>
</ng-template>
<!------------------------------------------------------------>
<!--------- Tranfer Order Review End ------------------------->
<!------------------------------------------------------------>