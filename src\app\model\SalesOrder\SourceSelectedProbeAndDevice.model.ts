import { SalesOrderTransferProductRequest } from "../device/SalesOrderTransferProductRequest.model";
import { SalesOrderTransferProbeRequest } from "../probe/SalesOrderTransferProbeRequest.model";

export class SourceSelectedProbeAndDevice {
    sourceDeviceSelected: SalesOrderTransferProductRequest;
    sourceProbeSelected: SalesOrderTransferProbeRequest;

    constructor(
        sourceDeviceSelected: SalesOrderTransferProductRequest,
        sourceProbeSelected: SalesOrderTransferProbeRequest
    ) {
        this.sourceDeviceSelected = sourceDeviceSelected;
        this.sourceProbeSelected = sourceProbeSelected;
    }
}