import { HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { DeviceListResource, ListKitManagementResource, OTSKitManagementListResource, ProbDetailResource, ProbListResource } from 'src/app/app.constants';
import { ImportFileForUpdateTemplateDataResponse } from 'src/app/model/importCSVFile/ImportFileForUpdateTemplateDataResponse.model';
import { BASE_URL, commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { ImportCsvFileApiService } from './import-csv-file-api.service';

describe('ImportCsvFileApiService', () => {
  let service: ImportCsvFileApiService;
  let httpMock: HttpTestingController;

  const baseUrl = BASE_URL;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [],
      providers: [commonsProviders(null)]
    });

    service = TestBed.inject(ImportCsvFileApiService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  describe('getBaseUrlForResource', () => {
    it('should return correct base URL for DeviceListResource', () => {
      expect(service.getBaseUrlForResource(DeviceListResource)).toBe(baseUrl + 'api/deviceMasters/');
    });

    it('should return correct base URL for ProbListResource', () => {
      expect(service.getBaseUrlForResource(ProbListResource)).toBe(baseUrl + 'api/probe/');
    });

    it('should return correct base URL for ProbDetailResource', () => {
      expect(service.getBaseUrlForResource(ProbDetailResource)).toBe(baseUrl + 'api/probe/');
    });

    it('should return correct base URL for ListKitManagementResource', () => {
      expect(service.getBaseUrlForResource(ListKitManagementResource)).toBe(baseUrl + 'api/kitManagement/bridge-world/');
    });

    it('should return correct base URL for OTSKitManagementListResource', () => {
      expect(service.getBaseUrlForResource(OTSKitManagementListResource)).toBe(baseUrl + 'api/kit-management/ots-world/');
    });

    it('should return default base URL if resource does not match', () => {
      expect(service.getBaseUrlForResource('unknown')).toBe(baseUrl);
    });
  });

  describe('downloadCsvTemplate', () => {
    it('should make GET request to download CSV template', () => {
      const resource = DeviceListResource;

      service.downloadCsvTemplate(resource).subscribe(response => {
        expect(response).toBeTruthy();
      });

      const req = httpMock.expectOne(baseUrl + 'api/deviceMasters/csv/import/download');
      expect(req.request.method).toBe('GET');
      expect(req.request.responseType).toBe('blob');
      req.flush(new Blob(), { status: 200, statusText: 'OK' });
    });
  });

  describe('importFileForUpdateTemplateData', () => {
    it('should make POST request to import CSV file', () => {
      const formData = new FormData();
      const resource = ListKitManagementResource;
      const dummyResponse: ImportFileForUpdateTemplateDataResponse = null;

      service.importFileForUpdateTemplateData(formData, resource).subscribe(response => {
        expect(response.body).toEqual(dummyResponse);
      });

      const req = httpMock.expectOne(baseUrl + 'api/kitManagement/bridge-world/csv/import');
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toBe(formData);
      req.flush(dummyResponse, { status: 200, statusText: 'OK' });
    });
  });
});
