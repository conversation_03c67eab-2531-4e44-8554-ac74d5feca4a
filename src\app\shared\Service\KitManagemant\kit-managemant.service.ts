import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { DetailKitManagementResource, ListKitManagementResource } from 'src/app/app.constants';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { KitManagemantFilterAction } from 'src/app/model/KitManagement/KitManagemantFilterAction.model';
import { KitManagemantSearchRequestBody } from 'src/app/model/KitManagement/KitManagemantSearchRequestBody.model';
import { LanguageResponse } from 'src/app/model/Languages/LanguageResponse.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';

@Injectable({
  providedIn: 'root'
})
export class KitManagemantService {

  /**
   * Country List
   */
  private countriesList: CountryListResponse[] = [];

  /**
   * Language List
   */
  private languagesList: LanguageResponse[] = [];

  //Refresh Kit List 
  private kitListRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //Refresh Kit Detail page
  private kitDetailRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //OTSKit list filter
  private kitListFilterRequestParameterSubject = new Subject<KitManagemantFilterAction>();

  /**
   * Kit List Page Refresh After some Action Like Serch parameter add
   * Note : Create or Update or Delete Role After Clear All filter and refresh page 
   * <AUTHOR>
   * @returns  
   */
  public getKitListFilterRequestParameterSubject(): Subject<KitManagemantFilterAction> {
    return this.kitListFilterRequestParameterSubject;
  }

  public callKitListFilterRequestParameterSubject(kitManagemantFilterAction: KitManagemantFilterAction): void {
    this.kitListFilterRequestParameterSubject.next(kitManagemantFilterAction);
  }

  /**
   * Kit List Page Refresh After some Action Like 
   * <AUTHOR>
   * @returns ListingPageReloadSubjectParameter
   */
  public getKitListRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.kitListRefreshSubject;
  }

  /**
   * kit Detail Page Refresh After some Action 
   * isReloadData false means delete Kit operation and move list page
   * <AUTHOR>
   * @returns ListingPageReloadSubjectParameter
   */
  public getKitDetailRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.kitDetailRefreshSubject;
  }

  /**
  * This function call the subject for reload the page data
  *  Note : (ListKitManagementResource) -> Filter page subject call -> Listing page subject call
  * clear all filter after page data Reload
  * <AUTHOR>
  * @param isReloadData -> false means move to prev page form Detail to list page.
  * @param resourceName 
  */
  public callRefreshPageSubject(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, resourceName: string, isFilterHidden: boolean): void {
    if (resourceName == ListKitManagementResource) {
      if (isFilterHidden) {
        let kitRequestBody = new KitManagemantSearchRequestBody("", "", [], [], "", "");
        let kitFilterAction = new KitManagemantFilterAction(listingPageReloadSubjectParameter, kitRequestBody);
        this.callKitListFilterRequestParameterSubject(kitFilterAction);
      } else {
        this.kitListRefreshSubject.next(listingPageReloadSubjectParameter);
      }
    } else if (resourceName == DetailKitManagementResource) {
      this.kitDetailRefreshSubject.next(listingPageReloadSubjectParameter);
    }
  }

  /**
 * Set Country
 */
  public setCountryList(countriesList: CountryListResponse[]): void {
    this.countriesList = countriesList;
  }

  /**
   * Get Country
   */
  public getCountryList(): Array<CountryListResponse> {
    return this.countriesList;
  }

  /**
   * Set Language
   */
  public setLanguageList(languagesList: LanguageResponse[]): void {
    this.languagesList = languagesList;
  }

  /**
   * Get Language
   */
  public getLanguageList(): Array<LanguageResponse> {
    return this.languagesList;
  }

}
