import { Pipe, PipeTransform } from "@angular/core";
import { RolePermissionResponse } from "src/app/model/Role/rolePermissionResponse.model";
import { isNullOrUndefined } from 'is-what';
import { PrintListPipe } from "./printList.pipe";
import { PERMISSION_NAME_HIDE } from "src/app/app.constants";


@Pipe({
    name: "GetRolePermissionNamePipe"
})
export class GetRolePermissionName implements PipeTransform {

    constructor(private printListPipe: PrintListPipe) { }

    transform(rolePermissionResponse: RolePermissionResponse[]): string {
        let permissionsList: string[] = [];
        let finalPermisions: string = null;
        if (!isNullOrUndefined(rolePermissionResponse) && rolePermissionResponse.length > 0) {
            for (let permissionsObj of rolePermissionResponse) {
                if (!PERMISSION_NAME_HIDE.includes(permissionsObj.name)) {
                    permissionsList.push(permissionsObj.name);
                }
            }
            finalPermisions = this.printListPipe.transform([...permissionsList].sort((x, y) => x.localeCompare(y)));
        }
        return finalPermisions;
    }
}