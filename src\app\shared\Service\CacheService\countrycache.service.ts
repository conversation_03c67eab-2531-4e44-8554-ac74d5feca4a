import { HttpClient, HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { LocalStorageService } from 'ngx-webstorage';
import { firstValueFrom } from 'rxjs';
import { RdmUserAssignedCountries } from 'src/app/app.constants';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { CommonsService } from '../../util/commons.service';

@Injectable({
  providedIn: 'root'
})
export class CountryCacheService {


  private serverApiUrl = this.configInjectService.getServerApiUrl();
  public countryUrl = this.serverApiUrl + 'api/country';

  constructor(
    private $localStorage: LocalStorageService,
    private commonsService: CommonsService,
    private http: HttpClient,
    private exceptionService: ExceptionHandlingService,
    private configInjectService: ConfigInjectService) { }


  public async getCountryListFromCache(notAssociatedCountryOptionAdd: boolean = false): Promise<CountryListResponse[]> {
    let countryList: any = JSON.parse(JSON.stringify(await this.getCountryList()));
    if (notAssociatedCountryOptionAdd) {
      countryList.push(this.commonsService.notAssociatedObjectForCountry());
    }
    return countryList;
  }

  /**
 * Fetches the country list 
 * 
 * <AUTHOR>
 * @returns A Promise resolving to an array of `Country` objects. Returns an 
 *          empty array on error or if the response body is null.
 */
  private async getCountryList(): Promise<CountryListResponse[]> {
    try {
      // Perform the HTTP GET request and return the response body or an empty array.
      const res: HttpResponse<CountryListResponse[]> = await firstValueFrom(this.http.get<CountryListResponse[]>(this.countryUrl, { observe: 'response' }));
      return res.body || [];
    } catch (error) {
      // Handle HTTP errors using a custom error handler and return an empty array.
      if (error instanceof HttpErrorResponse) {
        this.exceptionService.customErrorMessage(error);
      }
      return [];
    }
  }

  /**
   * Filter User Associated Countries from Country list
   * @returns 
   */
  public filterOutUserAssociatedCountries(countryList: CountryListResponse[]): CountryListResponse[] {
    let rdmUserAssociatedCountries = this.$localStorage.retrieve(RdmUserAssignedCountries);
    if (!isNullOrUndefined(rdmUserAssociatedCountries) && !isNullOrUndefined(countryList)) {
      if (rdmUserAssociatedCountries?.length == 0) {
        return [];
      } else {
        return countryList.filter(o => rdmUserAssociatedCountries.some(({ country }) => o.country === country));
      }
    } else {
      return [];
    }
  }

}
