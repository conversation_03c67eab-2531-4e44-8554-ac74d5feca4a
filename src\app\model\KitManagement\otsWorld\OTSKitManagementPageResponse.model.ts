import { Pageable } from "../../common/pageable.model";
import { PageResponse } from "../../common/PageResponse.model";
import { Sort } from "../../common/sort.model";
import { OTSKitManagementListResponse } from "./OTSKitManagementListResponse.model";

export class OTSKitManagementPageResponse extends PageResponse {
    content: Array<OTSKitManagementListResponse>;


    constructor( //NOSONAR
        pageable: Pageable,
        totalPages: number,
        last: boolean,
        totalElements: number,
        numberOfElements: number,
        first: boolean,
        sort: Sort,
        size: number,
        number: number,
        empty: boolean,
        content: Array<OTSKitManagementListResponse>
    ) {
        super(
            pageable,
            totalPages,
            last,
            totalElements,
            numberOfElements,
            first,
            sort,
            size,
            number,
            empty
        );
        this.content = content;
    }
}
