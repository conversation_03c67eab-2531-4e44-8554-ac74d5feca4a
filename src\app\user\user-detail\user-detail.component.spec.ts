import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { getMockCountryList } from '../../Tesing-Helper/TestCountryInfo';
import { getMockUserDetail } from '../../Tesing-Helper/TestUserInfo';
import { commonsProviders } from '../../Tesing-Helper/test-utils';
import { ConfirmDialogService } from '../../FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { CountryListResponse } from '../../model/Country/CountryListResponse.model';
import { UserResponse } from '../../model/User/UserResponse.model';
import { ExceptionHandlingService } from '../../shared/ExceptionHandling.service';
import { CountryCacheService } from '../../shared/Service/CacheService/countrycache.service';
import { RoleApiCallService } from '../../shared/Service/RoleService/role-api-call.service';
import { AuthJwtService } from '../../shared/auth-jwt.service';
import { PermissionService } from '../../shared/permission.service';
import { HidePermissionNamePipe } from '../../shared/pipes/Role/hidePermissionName.pipe';
import { PrintListPipe } from '../../shared/pipes/printList.pipe';
import { UserApiCallService } from '../../shared/Service/user/user-api-call.service';
import { CommonsService } from '../../shared/util/commons.service';
import { UpdateUserComponent } from '../update-user/update-user.component';
import { UserDetailComponent } from './user-detail.component';

describe('UserDetailComponent', () => {
  let component: UserDetailComponent;
  let fixture: ComponentFixture<UserDetailComponent>;
  let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let authServiceMock: any;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let searchMemberServiceMock: UserApiCallService;
  let exceptionService: ExceptionHandlingService;
  let roleApiCallServiceMock: RoleApiCallService;
  let countryCacheServiceMock: CountryCacheService;

  const roleFilter = ["Cloud Admin", "Device / SV Team"];
  const countryFilter: Array<CountryListResponse> = getMockCountryList();

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    authServiceMock = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getUserPermission']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');  // Adjust this value as per your application logic    
    await TestBed.configureTestingModule({
      declarations: [UserDetailComponent,
        PrintListPipe,
        UpdateUserComponent,
        HidePermissionNamePipe,
      ],
      imports: [FormsModule,
        ReactiveFormsModule,
        NgMultiSelectDropDownModule.forRoot()],
      providers: [
        UserApiCallService,
        RoleApiCallService,
        CommonsService,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        ConfirmDialogService,
        ExceptionHandlingService,
        SessionStorageService,
        HidePermissionNamePipe,
        { provide: AuthJwtService, useValue: authServiceMock },
        { provide: PermissionService, useValue: permissionServiceSpy },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(UserDetailComponent);
    component = fixture.componentInstance;
    authServiceMock.isAuthenticate.and.returnValue(true);
    permissionServiceSpy.getUserPermission?.and.returnValue(true);
    searchMemberServiceMock = TestBed.inject(UserApiCallService);
    exceptionService = TestBed.inject(ExceptionHandlingService);
    roleApiCallServiceMock = TestBed.inject(RoleApiCallService);
    countryCacheServiceMock = TestBed.inject(CountryCacheService);
    fixture.detectChanges();
  });



  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load user detail when authenticated', async () => {
    let mockUserDetailResponse: UserResponse = getMockUserDetail();
    // Mock API
    spyOn(searchMemberServiceMock, 'getSingleMember')?.and.returnValue(of(new HttpResponse<UserResponse>({
      body: mockUserDetailResponse,
      status: 200,
    })));
    spyOn(roleApiCallServiceMock, 'getRoleNameList')?.and.returnValue(Promise.resolve(roleFilter));
    spyOn(countryCacheServiceMock, 'getCountryListFromCache')?.and.returnValue(Promise.resolve(countryFilter));


    // Act: Initialize the component
    component.ngOnInit();
    await fixture.whenStable();
    fixture.detectChanges();
    expect(component.userResponse).toEqual(mockUserDetailResponse);

    //UPDATE USER
    const updateUserBtnEle = fixture.nativeElement.querySelector('#userDetailPageUpdateBtn');
    updateUserBtnEle?.click();
    expect(component.userDetailShow).toBeFalsy();
    expect(component.userUpdateShow).toBeTruthy();
    fixture.detectChanges();
    await fixture.whenStable();
    const updateUserCompElement = fixture.debugElement.query(By.css('app-update-user'));
    const updateUserComponent = updateUserCompElement.componentInstance;
    fixture.detectChanges();
    await fixture.whenStable();
    expect(updateUserComponent).toBeTruthy();

    //back button in update user page
    spyOn(updateUserComponent, 'back').and.callThrough();
    spyOn(component, 'userShow').and.callThrough();
    const updateUserBackBtn = fixture.nativeElement.querySelector('#updateUserBackBtn');
    updateUserBackBtn?.click();
    expect(updateUserComponent.back).toHaveBeenCalled();
    fixture.detectChanges();
    await fixture.whenStable();
    expect(component.userShow).toHaveBeenCalled();
    expect(component.userDetailShow).toBeTruthy();
    expect(component.userUpdateShow).toBeFalsy();

  });

  it('should get user detail by but user detail not found', async () => {
    // Mock API 
    spyOn(searchMemberServiceMock, 'getSingleMember')?.and.returnValue(of(new HttpResponse<UserResponse>({
      body: null,
      status: 204,
    })));
    component.ngOnInit();
    await fixture.whenStable();
    fixture.detectChanges();
    expect(toastrServiceMock.info).toHaveBeenCalled();

  });

  it('should get user detail by id but throw exception', async () => {
    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
    spyOn(searchMemberServiceMock, 'getSingleMember')?.and.returnValue(throwError(() => mockError));
    component.ngOnInit();
    await fixture.whenStable();
    fixture.detectChanges();
    spyOn(exceptionService, 'customErrorMessage')?.and.callThrough();
  });

});
