import { HttpClient, HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { firstValueFrom, Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { TREANSFER_ORDER_EMPTY_LIST } from 'src/app/app.constants';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { KitManagemantProductDetailBaseResponse } from 'src/app/model/KitManagement/KitManagemantProductDetailBaseResponse.model';
import { BasicSalesOrderDetailResponse } from 'src/app/model/SalesOrder/BasicSalesOrderDetailResponse.model';
import { SalesOrderDetailResponse } from 'src/app/model/SalesOrder/SalesOrderDetailResponse.model';
import { SalesOrderFaieldSearchRequestBody } from 'src/app/model/SalesOrder/SalesOrderFaieldSearchRequestBody.model';
import { SalesOrderFailedPageResponse } from 'src/app/model/SalesOrder/SalesOrderFailedPageResponse.model';
import { SalesOrderPageResponse } from 'src/app/model/SalesOrder/SalesOrderPageResponse.model';
import { SalesOrderProductMappingRequest } from 'src/app/model/SalesOrder/SalesOrderProductMappingRequest.model';
import { SalesOrderProductMappingResponse } from 'src/app/model/SalesOrder/SalesOrderProductMappingResponse.model';
import { SalesOrderProductResponse } from 'src/app/model/SalesOrder/SalesOrderProductResponse.model';
import { SalesOrderSchedulerManualSyncTimeResponse } from 'src/app/model/SalesOrder/SalesOrderSchedulerManuleSyncTimeResponse.model';
import { SalesOrderSchedulerSyncTimeResponse } from 'src/app/model/SalesOrder/SalesOrderSchedulerSyncTimeResponse.model';
import { SalesOrderSearchRequestBody } from 'src/app/model/SalesOrder/SalesOrderSearchRequestBody.model';
import { SalesOrderTransferSuccessResponse } from 'src/app/model/SalesOrder/SalesOrderTransferSuccessResponse.model';
import { TranferOrderSelection } from 'src/app/model/SalesOrder/TranferOrderSelection.model';
import { TransferOrderProductReviewResponse } from 'src/app/model/SalesOrder/TransferOrderProductReviewResponse.model';
import { SalesOrderTransferValidateRequest } from 'src/app/model/SalesOrder/SalesOrderTransferValidateRequest.model';
import { TransferOrderSelectionDetailRequest } from 'src/app/model/SalesOrder/TransferOrderSelectionDetailRequest.model';
import { TransferOrderSelectionResponse } from 'src/app/model/SalesOrder/TransferOrderSelectionResponse.model';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { CommonsService } from '../../util/commons.service';
import { createRequestOption } from '../../util/request-util';
import { ManualSalesOrderResponse } from 'src/app/model/SalesOrder/ManualSalesOrderResponse.model';
import { SalesOrderPdfLetterResponse } from 'src/app/model/SalesOrder/SalesOrderPdfLetterResponse';

@Injectable({
  providedIn: 'root'
})
export class SalesOrderApiCallService {

  public salesOrderUrl = this.configInjectService.getServerApiUrl() + 'api/salesOrders';

  constructor(
    private configInjectService: ConfigInjectService,
    private http: HttpClient,
    private exceptionService: ExceptionHandlingService,
    private commonsService: CommonsService,
    private tosterService: ToastrService
  ) { }

  /**
   * Get Sales Order list
   * @param requestBody 
   * @param req 
   * @returns 
   */
  public getSalesOrderList(requestBody: SalesOrderSearchRequestBody, req: any): Observable<HttpResponse<SalesOrderPageResponse>> {
    const options = createRequestOption(req);
    return this.http.post<SalesOrderPageResponse>(this.salesOrderUrl + "/search", requestBody, { params: options, observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Get Sales Order Details
   * 
   * <AUTHOR>
   * @param salesOrderId 
   * @returns 
   */
  public getSalesOrderDetails(salesOrderId: number): Observable<HttpResponse<SalesOrderDetailResponse>> {
    return this.http.get<SalesOrderDetailResponse>(this.salesOrderUrl + "/details/" + salesOrderId, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Get Transfer Order Details
   * 
   * <AUTHOR>
   * @param TransferOrderId 
   * @returns 
   */
  public getTransferOrderSelectionDetails(requestBody: TransferOrderSelectionDetailRequest): Observable<HttpResponse<TransferOrderSelectionResponse>> {
    return this.http.post<TransferOrderSelectionResponse>(this.salesOrderUrl + "/transfer/detail", requestBody, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
  * Get Submit Transfer Order Details
  * 
  * <AUTHOR>
  * @param TransferOrderId 
  * @returns 
  */
  public submitTransferOrder(requestBody: SalesOrderTransferValidateRequest): Observable<HttpResponse<TransferOrderProductReviewResponse>> {
    return this.http.post<TransferOrderProductReviewResponse>(this.salesOrderUrl + "/transfer/validate", requestBody, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }


  /**
   * <AUTHOR>
   * 
   * Get Sales order Number list
   * @returns 
   */
  public async getSalesOrderNumberList(): Promise<string[]> {
    let salesOrderNumberList: Array<string> = [];
    try {
      const res: HttpResponse<string[]> = await firstValueFrom(
        this.http.get<string[]>(`${this.salesOrderUrl}/salesOrderNumber`, { observe: 'response' })
      );
      if (!isNullOrUndefined(res.body)) {
        salesOrderNumberList = res.body;
      }
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        this.exceptionService.customErrorMessage(error);
      }
      salesOrderNumberList = [];
    }
    return salesOrderNumberList;
  }

  /**
  * <AUTHOR>
  * 
  * Get Transer order Number list
  * @returns 
  */
  public async getTranferOrderList(): Promise<TranferOrderSelection[]> {
    let tranferOrderNumberList: Array<TranferOrderSelection> = [];
    try {
      const res: HttpResponse<TranferOrderSelection[]> = await firstValueFrom(
        this.http.get<TranferOrderSelection[]>(`${this.salesOrderUrl}/transfer/nonTransferOrders`, { observe: 'response' })
      );
      if (!isNullOrUndefined(res.body) && res.status === 200) {
        tranferOrderNumberList = res.body;
      }
      if (res.status === 204) {
        this.tosterService?.info(TREANSFER_ORDER_EMPTY_LIST);
      }
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        this.exceptionService.customErrorMessage(error);
      }
      tranferOrderNumberList = [];
    }
    return tranferOrderNumberList;
  }

  /**
  * <AUTHOR>
  * 
  * Get order Record Number list
  * @returns 
  */
  public async getOrderRecordNumberList(param: boolean): Promise<string[]> {
    let OrderRecordTypeList: Array<string> = [];
    try {
      const res: HttpResponse<string[]> = await firstValueFrom(
        this.http.get<string[]>(`${this.salesOrderUrl}/recordType?nonTransferRecordType=${param}`, { observe: 'response' })
      );
      if (!isNullOrUndefined(res.body)) {
        OrderRecordTypeList = res.body;
      }
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        this.exceptionService.customErrorMessage(error);
      }
      OrderRecordTypeList = [];
    }
    return OrderRecordTypeList;
  }
  /**
   * Get Sales order Details
   * 
   * <AUTHOR>
   * 
   * @param salesOrderNumber 
   * @returns 
   */
  public getBasicSalesOrderDetails(salesOrderNumber: string): Observable<HttpResponse<BasicSalesOrderDetailResponse>> {
    return this.http.get<BasicSalesOrderDetailResponse>(this.salesOrderUrl + "/basic/" + salesOrderNumber, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Get Sales Order Failed list
   * 
   * <AUTHOR>
   * @param requestBody 
   * @param req 
   * @returns 
   */
  public getSalesOrderFailedList(requestBody: SalesOrderFaieldSearchRequestBody, req: any): Observable<HttpResponse<SalesOrderFailedPageResponse>> {
    const options = createRequestOption(req);
    return this.http.post<SalesOrderFailedPageResponse>(this.salesOrderUrl + "/failed/search", requestBody, { params: options, observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Reset Sales Order Bridge
   * 
   * <AUTHOR>
   * @param requestBody 
   * @param salesOrderId 
   * @returns 
   */
  public async resetSalesOrderBridge(requestBody: SalesOrderProductMappingRequest, salesOrderId: number): Promise<SalesOrderProductMappingResponse> {
    let salesOrderProductMappingResponse: SalesOrderProductMappingResponse = null;
    try {
      const res: HttpResponse<SalesOrderProductMappingResponse> = await firstValueFrom(
        this.http.put<SalesOrderProductMappingResponse>(`${this.salesOrderUrl}/reset/${salesOrderId}`, requestBody, { observe: 'response' }));
      if (!isNullOrUndefined(res.body)) {
        salesOrderProductMappingResponse = res.body;
      }
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        this.exceptionService.customErrorMessage(error);
      }
    }
    return salesOrderProductMappingResponse;
  }

  /**
   * Update Bridge Info
   * 
   * <AUTHOR>
   * @param bridge 
   * @param resetBridgeResponse 
   * @param isKitBridge 
   * @returns 
   */
  public updateBridgeInfo(bridge: any, resetBridgeResponse: SalesOrderProductMappingResponse, isKitBridge: boolean): any {
    if (isKitBridge) {
      return this.updateKitBridgeInfo(bridge, resetBridgeResponse);
    } else {
      return this.updateExtraBridgeInfo(bridge, resetBridgeResponse);
    }
  }

  /**
   * Kit Bridge Info Update
   * 
   * <AUTHOR>
   * @param bridge 
   * @param resetBridgeResponse 
   * @returns 
   */
  private updateKitBridgeInfo(bridge: KitManagemantProductDetailBaseResponse, resetBridgeResponse: SalesOrderProductMappingResponse) {
    bridge.serialNumber = resetBridgeResponse?.productSerialNumber;
    bridge.productConfigStatus = resetBridgeResponse?.productStatus;
    bridge.productEntityId = resetBridgeResponse?.productEntityId;
    return bridge;
  }

  /**
   * Extra Bridge Info Update
   * 
   * <AUTHOR>
   * @param bridge 
   * @param resetBridgeResponse 
   * @returns 
   */
  private updateExtraBridgeInfo(bridge: SalesOrderProductResponse, resetBridgeResponse: SalesOrderProductMappingResponse) {
    bridge.entitySerialNumber = resetBridgeResponse?.productSerialNumber;
    bridge.entityStatus = resetBridgeResponse?.productStatus;
    bridge.entityPk = resetBridgeResponse?.productEntityId;
    return bridge;
  }

  /**
    * Get SalesOrder Scheduler Sync Time
    * 
    * <AUTHOR>
    * @returns 
    */
  public async getSalesOrderSchedulerSyncTime(): Promise<SalesOrderSchedulerSyncTimeResponse> {
    try {
      const res: HttpResponse<SalesOrderSchedulerSyncTimeResponse> = await firstValueFrom(this.http.get<SalesOrderSchedulerSyncTimeResponse>(this.salesOrderUrl + '/sync/time', { observe: 'response' }));
      return res.body;
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        this.exceptionService.customErrorMessage(error);
      }
      return null;
    }
  }

  /**
   * Delete Sales order
   * <AUTHOR>
   * @param salesOrderId
   * @returns
   */
  public deleteSalesOrder(salesOrderId: number[]): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.delete<SuccessMessageResponse>(this.salesOrderUrl + "/" + salesOrderId, { observe: "response" }).pipe(catchError(this.commonsService.handleError));
  }

  /**
  * Initiates a manual sync for sales orders.
  * @returns An observable of the HTTP response containing the manual sync time response.
  */
  public salesOrderManualSync(): Observable<HttpResponse<SalesOrderSchedulerManualSyncTimeResponse>> {
    return this.http.post<SalesOrderSchedulerManualSyncTimeResponse>(this.salesOrderUrl + "/sync/manual", {}, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
  * Checks the status of the manual sync for a given ID.
  * @param id - The ID of the manual sync to check.
  * @returns An observable of the HTTP response containing a boolean indicating the sync status.
  */
  public manualsync(id: number): Observable<HttpResponse<boolean>> {
    return this.http.get<boolean>(this.salesOrderUrl + "/sync/manual/status/" + id, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Transfer Sales order
   * <AUTHOR>
   * @param salesOrderId
   * @returns
   */
  public TransferSalesOrder(transferOrderRequest: SalesOrderTransferValidateRequest): Observable<HttpResponse<SalesOrderTransferSuccessResponse>> {
    return this.http.post<SalesOrderTransferSuccessResponse>(this.salesOrderUrl + "/transfer", transferOrderRequest, { observe: "response" }).pipe(catchError(this.commonsService.handleError));
  }

  /**
  * Create Manual Sales order
  * <AUTHOR>
  * @param salesOrderId
  * @returns
  */
  public createManualSalesOrder(manualSalesOrderRequest: BasicSalesOrderDetailResponse): Observable<HttpResponse<ManualSalesOrderResponse>> {
    return this.http.post<ManualSalesOrderResponse>(this.salesOrderUrl, manualSalesOrderRequest, { observe: "response" }).pipe(catchError(this.commonsService.handleError));
  }

  /**
  * Download SalesOrderPdfLetter
  * @returns 
  */
  public downloadSalesOrderPdfLetter(salesOrderId: string): Observable<HttpResponse<SalesOrderPdfLetterResponse>> {
    return this.http.get<SalesOrderPdfLetterResponse>(this.salesOrderUrl + "/pdf/" + salesOrderId, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }
}
