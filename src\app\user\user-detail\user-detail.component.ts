import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { DateTimeDisplayFormat, USER_DELETE } from '../../app.constants';
import { UserResponse } from '../../model/User/UserResponse.model';
import { PermissionAction } from '../../shared/enum/Permission/permissionAction.enum';
import { ExceptionHandlingService } from '../../shared/ExceptionHandling.service';
import { PermissionService } from '../../shared/permission.service';
import { UserApiCallService } from '../../shared/Service/user/user-api-call.service';

@Component({
  selector: 'app-user-detail',
  templateUrl: './user-detail.component.html',
  styleUrls: ['./user-detail.component.css']
})
export class UserDetailComponent implements OnInit {

  @Output('singleuser') hidesingleuser = new EventEmitter();
  @Input('userId') userId;

  userResponse: UserResponse = null;

  //loading
  loading = false;

  //single user and update user show hide
  userDetailShow = true;
  userUpdateShow = false;

  //Permission
  updateUserPermission: boolean = false;

  dateDisplayFormat: string = DateTimeDisplayFormat;

  constructor(
    private userApiCallService: UserApiCallService,
    private exceptionService: ExceptionHandlingService,
    private permissionService: PermissionService,
    private toste: ToastrService
  ) { }

  ngOnInit() {
    this.loading = true;
    this.getSingleuser();
    this.updateUserPermission = this.permissionService.getUserPermission(PermissionAction.UPDATE_USER_ACTION);
  }

  private getSingleuser(): void {
    this.loading = true;
    this.userApiCallService.getSingleMember(this.userId).subscribe({
      next: (response: HttpResponse<UserResponse>) => {
        if (response.status == 200) {
          this.userResponse = response?.body;
          this.loading = false;
        } else if (response.status == 204) {
          this.loading = false;
          this.toste.info(USER_DELETE);
          this.back();
        }
      },
      error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });
  }


  public back(): void {
    this.hidesingleuser.emit();
  }


  public updateUserShow(): void {
    this.userDetailShow = false;
    this.userUpdateShow = true;
  }

  public userShow(): void {
    this.loading = true;
    this.getSingleuser();
    this.userDetailShow = true;
    this.userUpdateShow = false;
  }

  /**
  * Refresh User Detail Page Data
  * 
  * <AUTHOR> 
  */
  public refreshUserDetailPage(): void {
    this.getSingleuser();
  }
}
