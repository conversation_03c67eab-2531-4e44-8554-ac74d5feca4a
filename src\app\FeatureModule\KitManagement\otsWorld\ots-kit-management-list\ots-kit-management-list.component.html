<!-- loading start -->
<!-- loading gif start -->
<div class="ringLoading" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!-- loading gif end -->
<!-- loading end -->

<body *ngIf="otsWorldListDisplay">
    <!-- row start -->
    <div class="row" id="otsworldlist">

        <!--############################################################-->
        <!--Filter start-->
        <!--############################################################-->
        <div class="col-md-3 pr-0" *ngIf="(!isFilterHidden)">
            <label class="col-md-12 h5-tag">Filter</label>
            <div class="card mt-3">
                <div class="card-body">
                    <app-ots-kit-management-filter [isFilterComponentInitWithApicall]="isFilterComponentInitWithApicall"
                        [otsKitManagemantSearchRequestBody]="otsKitManagemantSearchRequestBody"
                        [listPageRefreshForbackToDetailPage]="listPageRefreshForbackToDetailPage"></app-ots-kit-management-filter>
                </div>
            </div>
        </div>
        <!--############################################################-->
        <!--Filter End-->
        <!--############################################################-->

        <!--table Block Start-->
        <div [className]="(isFilterHidden)?'col-md-12 pr-0':'col-md-9 pr-0'">
            <div class="container-fluid">
                <!--############################################################-->
                <!--############################################################-->
                <div class="row" class="headerAlignment">
                    <!--############################################################-->
                    <!--Left Side-->
                    <!--############################################################-->
                    <div class="childFlex">
                        <!----------------------------------------------->
                        <!------------Show/hide filter-------------------->
                        <!----------------------------------------------->
                        <div class="dropdown" id="hideShowFilter">
                            <button class="btn btn-sm btn-orange mr-3 ml-0 filter-symbol" (click)="toggleFilter()"
                                id="roleListHideShowButton">
                                <i class="fas fa-filter" aria-hidden="true"></i>
                                &nbsp;&nbsp;{{ hideShowFilterButtonText }}
                            </button>
                        </div>
                        <!----------------------------------------------->
                        <!------------Pagnatation drp-------------------->
                        <!----------------------------------------------->
                        <div>
                            <label class="mb-0">Show entry</label>
                            <select [(ngModel)]="drpselectsize" class="form-control form-control-sm"
                                (change)="changeDataSize($event)" id="otsKitManagemantListShowEntry">
                                <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                                    <option [value]="dataSize">{{ dataSize }}</option>
                                </ng-template>
                            </select>
                        </div>
                        <div class="ml-3 versionDiv"
                            *ngIf="otsKitRevVersionResponse != null && otsKitRevVersionResponse != null">
                            <div class="versionText">Rev version {{otsKitRevVersionResponse}}</div>
                        </div>
                    </div>
                    <!--############################################################-->
                    <!--Right Side-->
                    <!--############################################################-->
                    <div class="childFlex">
                        <div class="btn-group btn-group-sm mr-2" role="group" style="display: inline-block;">
                            <button type="button" class="btn btn-sm btn-cust-border"
                                (click)="showBridgeWorldListDisplay()" *ngIf="bridgeKitDisplayPermissions">Bridge
                                World</button>
                            <button type="button" class="btn btn-sm btn-orange btn-cust-border">OTS
                                World</button>
                        </div>
                        <!------------------------------------------------->
                        <!--------------Operations------------------->
                        <!------------------------------------------------->
                        <ng-template [ngIf]="operationsList.length > 1">
                            <div class="mr-3">
                                <select id="otsKitOperation" class="form-control form-control-sm"
                                    (change)="changeOperation($any($event.target)?.value)">
                                    <ng-template ngFor let-operation [ngForOf]="operationsList">
                                        <option [value]="operation">{{ operation }}</option>
                                    </ng-template>
                                </select>
                            </div>
                        </ng-template>


                        <!------------------------------------------------>
                        <!----------------refresh------------------------->
                        <!------------------------------------------------>
                        <div>
                            <button class="btn btn-sm btn-orange" (click)="clickOnRefreshButton()"
                                id="refresh_otsKitList"><em class="fa fa-refresh"></em></button>
                        </div>
                    </div>
                </div>
                <!--############################################################-->
                <!--############################################################-->
                <!-- selected probes start -->
                <div>Total {{totalRecord}} OTS World Kit(s)
                    <p *ngIf="selectedKitIdList != null && selectedKitIdList.length > 0">
                        <strong>{{selectedKitIdList.length}} OTS World Kit(s) selected</strong>
                    </p>
                </div>
                <!-- selected probes end -->

                <!-------------------------------------------->
                <!-------------------------------------------->
                <!-- kit table start -->
                <!-------------------------------------------->
                <!-------------------------------------------->
                <div class="commonTable">
                    <table class="table table-sm table-bordered" style="overflow-x: scroll;" aria-hidden="true">
                        <!--###########################################-->
                        <!-- table header Start -->
                        <!--###########################################-->
                        <thead>
                            <tr class="thead-light">
                                <th class="checkox-table width-unset" *ngIf="isCheckBoxDiaply">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" name="chkselectall"
                                            [id]="selectAllCheckboxId"
                                            (change)="selectAllItem($any($event.target)?.checked)">
                                        <label class="custom-control-label" [for]="selectAllCheckboxId"></label>
                                    </div>
                                </th>
                                <th><span>Country</span></th>
                                <th><span>Kit Part Number</span></th>
                                <th><span>Probe 1</span></th>
                                <th><span>Probe Config Group 1</span></th>
                                <th><span>Probe 2</span></th>
                                <th><span>Probe Config Group 2</span></th>
                                <th><span>Description</span></th>
                                <th><span>Last Modified Date & Time</span></th>
                            </tr>
                        </thead>
                        <!--###########################################-->
                        <!-- table body start -->
                        <!--###########################################-->
                        <tbody>
                            <tr *ngFor="let kitObj of otsKitManagementListResponse;">
                                <td class="width-unset" *ngIf="isCheckBoxDiaply">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input"
                                            [id]="chkPreFix+kitObj.id+chkPreFix" [name]="checkboxListName"
                                            (change)="selectCheckbox(kitObj,$any($event.target)?.checked)"
                                            [checked]="selectedKitIdList.includes(kitObj.id)">
                                        <label class="custom-control-label"
                                            [for]="chkPreFix+kitObj.id+chkPreFix"></label>
                                    </div>
                                </td>
                                <td (click)="showkitDetail(kitObj.id)" class="spanunderline" id="otsKitListToDeatil">
                                    <span class="text_nowrap">{{kitObj?.country}}</span>
                                </td>
                                <td>
                                    <span>{{kitObj?.otsKitPartNumberCode }}</span>
                                </td>
                                <td><span class="text_nowrap">{{kitObj?.probePartNumberCode1 }}</span></td>
                                <td class="multiprobe">
                                    <span> {{kitObj?.probeConfigGroupPartNumberCode1}}</span>
                                </td>
                                <td><span class="text_nowrap">{{kitObj?.probePartNumberCode2 }}</span></td>
                                <td class="multiprobe">
                                    <span> {{kitObj?.probeConfigGroupPartNumberCode2}}</span>
                                </td>
                                <td class="descriptionText"><span>{{kitObj?.description}}</span></td>
                                <td><span>{{kitObj?.modifiedDate | date:'MMM d, y, h:mm:ss a'}}</span></td>
                            </tr>
                        </tbody>
                        <!--###########################################-->
                        <!-- table body end -->
                        <!--###########################################-->
                    </table>

                </div>
                <!-------------------------------------------->
                <!-------------------------------------------->
                <!-- Kit table start -->
                <!-------------------------------------------->
                <!-------------------------------------------->

                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
                <!--pagination Start-->
                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
                <div>
                    <div>Showing {{totalRecordDisplay}} out of {{totalRecord}} OTS World Kit(s)</div>
                    <div class="float-right">
                        <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="itemsPerPage"
                            id="otsKit-pagination" [maxSize]="5" [rotate]="true" [boundaryLinks]="true"
                            (pageChange)="loadPage(page)">
                        </ngb-pagination>
                    </div>
                </div>
                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
                <!--pagination end-->
                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
            </div>
        </div>
        <!--table Block End-->
    </div>
    <!-- row end -->
</body>

<div *ngIf="otsWorldDetailDisplay">
    <app-ots-kit-management-detail (otskitListPage)="showKitList()" [kitId]="kitId"></app-ots-kit-management-detail>
</div>