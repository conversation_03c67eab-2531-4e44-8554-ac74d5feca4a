import { <PERSON><PERSON>, PipeTransform } from "@angular/core";
import { isNullOrUndefined } from "is-what";
import { REPLACE_COMMA_STRING, SERACH_REG_EXP_FOR_COMMA_PATTERN } from "src/app/app.constants";


@Pipe({
    name: "printListPipe"
})
export class PrintListPipe implements PipeTransform {

    transform(list: string[] | string): string {
        if (!isNullOrUndefined(list)) {
            return list.toString().replace(SERACH_REG_EXP_FOR_COMMA_PATTERN, REPLACE_COMMA_STRING);
        }
        return null;
    }
}