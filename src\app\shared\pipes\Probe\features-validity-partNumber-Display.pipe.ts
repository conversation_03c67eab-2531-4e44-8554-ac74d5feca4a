import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { FeaturesBaseResponse } from 'src/app/model/probe/FeaturesBaseResponse.model';
import { ProbeFeatureResponse } from 'src/app/model/probe/ProbeFeatureResponse.model';
import { ValidityEnum } from '../../enum/ValidityEnum.enum';

@Pipe({
    name: 'featuresValidityPartNumberDisplayPipe'
})
export class FeaturesValidityPartNumberDisplayPipe implements PipeTransform {

    transform(featuresBaseResponse: FeaturesBaseResponse | ProbeFeatureResponse, validityEnum: ValidityEnum): string {
        if (!isNullOrUndefined(featuresBaseResponse) &&
            !isNullOrUndefined(featuresBaseResponse.partNumbers) &&
            featuresBaseResponse.partNumbers.length > 0 &&
            !isNullOrUndefined(validityEnum)) {

            let featuresBaseResponseFilter = featuresBaseResponse.partNumbers.filter(obj => ValidityEnum[obj.validity] == validityEnum);
            if (featuresBaseResponseFilter.length == 1 &&
                !isNullOrUndefined(featuresBaseResponseFilter[0].partNumber)) {
                let partNumberSplit = featuresBaseResponseFilter[0].partNumber.split('-');
                return validityEnum + " - " + partNumberSplit[partNumberSplit.length - 1];
            }
        }
        return validityEnum;
    }

}
