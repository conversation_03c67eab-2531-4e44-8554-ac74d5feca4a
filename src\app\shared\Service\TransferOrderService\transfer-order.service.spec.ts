import { TestBed } from '@angular/core/testing';

import { TransferOrderService } from './transfer-order.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { CommonOperationsService } from '../../util/common-operations.service';
import { RoleApiCallService } from '../RoleService/role-api-call.service';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { AuthJwtService } from '../../auth-jwt.service';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { HidePermissionNamePipe } from '../../pipes/Role/hidePermissionName.pipe';
import { EnumMappingDisplayNamePipe } from '../../pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from '../../pipes/printList.pipe';

describe('TransferOrderService', () => {
  let service: TransferOrderService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        commonsProviders(null),
        CommonOperationsService,
        TransferOrderService,
        RoleApiCallService,
        ExceptionHandlingService,
        AuthJwtService,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        LocalStorageService,
        SessionStorageService,
        ConfirmDialogService,
        HidePermissionNamePipe
      ]
    });
    TestBed.configureTestingModule({});
    service = TestBed.inject(TransferOrderService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
