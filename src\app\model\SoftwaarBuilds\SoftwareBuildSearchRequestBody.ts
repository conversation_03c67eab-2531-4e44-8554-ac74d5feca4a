import { deviceTypesEnum } from "../../shared/enum/deviceTypesEnum.enum";

export class SoftwareBuildSearchRequestBody {
    version: string;
    countryIds: number[]
    deviceTypes: deviceTypesEnum;
    isActive: boolean;
    jsonIds: number[];
    partNumber: string;

    constructor(version: string, countryIds: number[], deviceTypes: deviceTypesEnum, isActive: boolean, jsonIds: number[], partNumber: string) {
        this.version = version;
        this.countryIds = countryIds;
        this.deviceTypes = deviceTypes;
        this.isActive = isActive;
        this.jsonIds = jsonIds;
        this.partNumber = partNumber;
    }
}