import { CommonModule } from '@angular/common';
import { HttpErrorResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { throwError } from 'rxjs';
import { INTERNAL_SERVER_ERROR } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { RoleResponse } from 'src/app/model/Role/roleResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { commonsProviders, rolePermissions, testErrorHandling } from 'src/app/Tesing-Helper/test-utils';
import { CreateAndUpdateRoleComponent } from './create-and-update-role.component';

describe('CreateAndUpdateRoleComponent', () => {
  let component: CreateAndUpdateRoleComponent;
  let fixture: ComponentFixture<CreateAndUpdateRoleComponent>;
  let exceptionHandlingService: ExceptionHandlingService;
  let roleApiCallService: RoleApiCallService;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;



  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    await TestBed.configureTestingModule({
      declarations: [CreateAndUpdateRoleComponent],
      imports: [CommonModule],
      providers: [NgbActiveModal,
        SessionStorageService,
        HidePermissionNamePipe,
        RoleApiCallService,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(CreateAndUpdateRoleComponent);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    roleApiCallService = TestBed.inject(RoleApiCallService);
    // Mock API call to fetch the Role Detail and return a successful response
    spyOn(roleApiCallService, 'getRolePermissionList')?.and.returnValue(Promise.resolve(rolePermissions));
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call toastrService.error with INTERNAL_SERVER_ERROR', async () => {
    component.createAndUpdateRoleModelRequest = { cancelButton: "Cancel", isFilterHidden: null, isUpdateData: true, okButton: "Update", resourseName: "detailRoleResource", roleId: 72, title: "Role Update" }
    testErrorHandling(roleApiCallService.getRoleDetail, () => component.ngOnInit(), exceptionHandlingService, toastrServiceMock, fixture);
  });

  it('should call toastrService.error with 409 Conflict', async () => {
    component.createAndUpdateRoleModelRequest = { cancelButton: "Cancel", isFilterHidden: null, isUpdateData: true, okButton: "Update", resourseName: "detailRoleResource", roleId: 72, title: "Role Update" }
    // **Arrange: Mock dependencies and set up the test environment**
    // Create a mock HttpErrorResponse to simulate a server error
    const mockError = new HttpErrorResponse({ status: 409, statusText: '409 Conflict' });

    // Mock the API call to return an error response
    spyOn(roleApiCallService, 'getRoleDetail')?.and.returnValue(throwError(() => mockError));

    // **Act: Trigger the component's logic**
    // Initialize the component, which invokes `getSalesOrderDetails` internally
    component.ngOnInit();

    // **Act: Trigger Angular lifecycle methods and finalize change detection**
    fixture.detectChanges(); // Run initial change detection
    await fixture.whenStable(); // Wait for asynchronous operations to complete

    // Ensure the toastr service displays an error message for "Role already exist"
    expect(toastrServiceMock.error).toHaveBeenCalledWith("Role already exist");
  });

  it('should call toastrService.error with 412 Precondition Failed', async () => {
    component.createAndUpdateRoleModelRequest = { cancelButton: "Cancel", isFilterHidden: null, isUpdateData: true, okButton: "Update", resourseName: "detailRoleResource", roleId: 72, title: "Role Update" }
    component.roleObject = new RoleResponse(1, '', '', null)
    // **Arrange: Mock dependencies and set up the test environment**
    // Create a mock HttpErrorResponse to simulate a server error
    const mockError = new HttpErrorResponse({ status: 412, statusText: 'Precondition Failed' });
    const mockServerError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });

    // Mock the API call to return an error response
    spyOn(roleApiCallService, 'getRoleDetail')?.and.returnValue(throwError(() => mockError));
    spyOn(roleApiCallService, 'updateRole')?.and.returnValue(throwError(() => mockServerError));

    // Spy on the `CustomerrorMessage` method of `exceptionHandlingService` to verify error handling
    spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

    // **Act: Trigger the component's logic**
    // Initialize the component, which invokes `getSalesOrderDetails` internally
    component.ngOnInit();

    // **Act: Trigger Angular lifecycle methods and finalize change detection**
    fixture.detectChanges(); // Run initial change detection
    await fixture.whenStable(); // Wait for asynchronous operations to complete

    // Ensure the toastr service displays an error message for "Selected role cannot be deleted as it is assigned to an existing user"
    expect(toastrServiceMock.info).toHaveBeenCalledWith("Selected role cannot be deleted as it is assigned to an existing user");

    component.addOrUpdateRole();

    // **Act: Trigger Angular lifecycle methods and finalize change detection**
    fixture.detectChanges(); // Run initial change detection
    await fixture.whenStable(); // Wait for asynchronous operations to complete

    // **Assert: Verify error handling behavior**
    // Ensure the `CustomerrorMessage` method was called to process the error
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  it('should call toastrService.error with INTERNAL_SERVER_ERROR', async () => {
    component.createAndUpdateRoleModelRequest = { cancelButton: "Cancel", isFilterHidden: null, isUpdateData: false, okButton: "Update", resourseName: "detailRoleResource", roleId: 72, title: "Role Update" }
    component.roleObject = new RoleResponse(1, '', '', null)
    // **Arrange: Mock dependencies and set up the test environment**
    // Create a mock HttpErrorResponse to simulate a server error
    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });

    // Mock the API call to return an error response
    spyOn(roleApiCallService, 'createRole')?.and.returnValue(throwError(() => mockError));

    // Spy on the `CustomerrorMessage` method of `exceptionHandlingService` to verify error handling
    spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

    // **Act: Trigger the component's logic**
    // Initialize the component, which invokes `getSalesOrderDetails` internally
    component.addOrUpdateRole();

    // **Act: Trigger Angular lifecycle methods and finalize change detection**
    fixture.detectChanges(); // Run initial change detection
    await fixture.whenStable(); // Wait for asynchronous operations to complete
    // **Assert: Verify error handling behavior**
    // Ensure the `CustomerrorMessage` method was called to process the error
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();

    // Ensure the toastr service displays an error message for `INTERNAL_SERVER_ERROR`
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
  });

});
