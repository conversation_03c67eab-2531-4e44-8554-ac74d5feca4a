import { SalesOrderProductBaseResponse } from "./SalesOrderProductBaseResponse.model";
import { ProductConfigStatus } from "src/app/shared/enum/SalesOrder/ProductConfigStatus.enum";

export class SalesOrderBridgeDetailResponse extends SalesOrderProductBaseResponse {
    bridgeKitPartNumberCode: string;
    productCode: string;
    destinationSalesOrderNumber: string

    constructor(
        sopmId: number,
        bridgeKitPartNumberCode: string,
        productCode: string,
        entitySerialNumber: string,
        entityPk: number,
        entityStatus: ProductConfigStatus,
        destinationSalesOrderNumber: string
    ) {
        super(sopmId, entitySerialNumber, entityStatus, entityPk);
        this.bridgeKitPartNumberCode = bridgeKitPartNumberCode;
        this.productCode = productCode;
        this.destinationSalesOrderNumber = destinationSalesOrderNumber;
    }
}
