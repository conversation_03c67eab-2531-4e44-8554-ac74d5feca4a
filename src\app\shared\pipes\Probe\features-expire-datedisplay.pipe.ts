import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { DatePipe } from '@angular/common';
import { LicensesRequest } from 'src/app/model/probe/multiProbe/LicensesRequest.model';
import { EndDateOptions } from '../../enum/endDateOptions.enum';
import { CommonsService } from '../../util/commons.service';
import { ConfigBaseMappingRequest } from 'src/app/model/probe/ConfigBaseMappingRequest.model';

@Pipe({
    name: 'featuresExpireDateDisplayPipe'
})
export class FeaturesExpireDateDisplayPipe implements PipeTransform {

    constructor(private datePipe: DatePipe,
        private commonsService: CommonsService) { }

    transform(features: Array<LicensesRequest> | Array<ConfigBaseMappingRequest>, featureId: number, reloadPipe: boolean): string {
        if (!isNullOrUndefined(features) && reloadPipe) {
            let filterData = features.filter(obj => obj.id == featureId);
            if (filterData.length == 1 && !isNullOrUndefined(filterData[0].endDate)) {
                if (filterData[0].endDate == -1) {
                    return "Expires on : " + EndDateOptions.UNLIMITED;
                } else {
                    return "Expires on : " + this.datePipe.transform(this.commonsService.getUTCDateForDisplay(filterData[0].endDate), 'MM/dd/yyyy');
                }
            }
        }
        return "";
    }

}
