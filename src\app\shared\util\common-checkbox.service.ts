import { Injectable } from '@angular/core';
import { isNullOrUndefined } from 'is-what';

@Injectable({
  providedIn: 'root'
})
export class CommonCheckboxService {

  /**
   * if user select all checkbox then selectAll Checkbox auto select or 
   * User select all checkbox and move next page and move prev page then selectAll Checkbox auto select 
   * <AUTHOR>
   * @param localIdList 
   * @param selectIdList 
   * @param chkId 
   */
  public defaultSelectAll(localIdList: number[], selectIdList: number[], chkId: string): void {
    let res: boolean = false;
    for (let index in localIdList) {
      let currentId = localIdList[index];
      let listIndex = selectIdList.findIndex(id => id == currentId);
      if (listIndex < 0) {
        res = false;
        break;
      } else {
        res = true;
      }
    }
    let chkElemantId = <HTMLInputElement>document.getElementById(chkId);
    if (chkElemantId != null) {
      chkElemantId.checked = res;
    }
  }

  /**
   * Deselect selectAll checkbox
   * <AUTHOR>
   * @param selectAllChkName 
   */
  public clearSelectAllCheckbox(selectAllChkName: string) {
    let selectAllCheckbox = <HTMLInputElement>document.getElementById(selectAllChkName);
    if (!isNullOrUndefined(selectAllCheckbox)) {
      selectAllCheckbox.checked = false;
    }
  }

  /**
   * select/Deselect all checkbox of current page 
   * <AUTHOR>
   * @param isChecked 
   * @param localListArray 
   * @param selectedId 
   * @param checkboxListName 
   * @returns 
   */
  public selectAllItem(isChecked: boolean, localListArray: number[], selectedId: number[], checkboxListName: string): number[] {
    let selectedIdList = selectedId;
    let checkboxList = (<HTMLInputElement[]><any>document.getElementsByName(checkboxListName));
    let l = checkboxList.length;
    if (isChecked) {
      for (let i = 0; i < l; i++) {
        checkboxList[i].checked = true;
      }
      for (let index in localListArray) {
        let addListId = localListArray[index];
        let listIndex = selectedIdList.findIndex(id => id == addListId);
        if (listIndex < 0) {
          selectedIdList.push(localListArray[index]);
        }
      }
    } else {
      for (let i = 0; i < l; i++) {
        checkboxList[i].checked = false;
      }
      for (let index in localListArray) {
        let spliceListId = localListArray[index];
        let listIndexRemove = selectedIdList.findIndex(id => id == spliceListId);
        selectedIdList.splice(listIndexRemove, 1);
      }

    }
    return selectedIdList;
  }
}
