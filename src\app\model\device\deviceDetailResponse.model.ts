import { ProductStatusEnum } from "src/app/shared/enum/Common/ProductStatus.enum";
import { deviceTypesEnum } from "src/app/shared/enum/deviceTypesEnum.enum";

export class DeviceDetailResponse {
    id: number;
    deviceId: string;
    packageVersion: string;
    androidApplicationVersion: string;
    thorDbVersion: number;
    probeVersion: number;
    handleVersion: number;
    productStatus: ProductStatusEnum;
    lastCheckInTime: number;
    messageGenerationTimestamp: number;
    timezone: number;
    createdDate: number;
    modifiedDate: number;
    locked: boolean;
    pimsDbVersion: number;
    settingsDbVersion: number;
    connectionState: string;
    deviceType: deviceTypesEnum;
    releaseId: number;
    jsonVersion: number;
    deviceSerialNo: string;
    devicePartNo: number;
    salesOrderNumber: string;
    salesOrderId: number;
    customerName: string;
    customerEmail: string;
    country: string;
    orderRecordType: string;
    countryId: number;
    macAddress: string;
    editable: boolean;
    poNumber: string;
    deviceAutoLock: boolean;
    probeAutoLock: boolean;
    soStatus: string;
}