import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { DetailOTSKitManagementResource, OTSKitManagementListResource } from 'src/app/app.constants';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { OTSKitManagemantFilterAction } from 'src/app/model/KitManagement/otsWorld/OTSKitManagemantFilterAction.model';
import { OTSKitManagemantSearchRequestBody } from 'src/app/model/KitManagement/otsWorld/OTSKitManagemantSearchRequestBody.model';

@Injectable({
  providedIn: 'root'
})
export class OtsKitManagemantService {

  //OTSKit list filter
  private otsKitListFilterRequestParameterSubject = new Subject<OTSKitManagemantFilterAction>();

  //OTSRefresh Kit List
  private otsKitListRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //Refresh Kit Detail page
  private otsKitDetailRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  /**
   * Country List Cache
   */
  private countriesList: CountryListResponse[] = [];

  /**
* OTS Kit List Page Refresh After some Action Like Serch parameter add
* Note : Create or Update or Delete Role After Clear All filter and refresh page 
* <AUTHOR>
* @returns  
*/
  public getOTSKitListFilterRequestParameterSubject(): Subject<OTSKitManagemantFilterAction> {
    return this.otsKitListFilterRequestParameterSubject;
  }

  public callOTSKitListFilterRequestParameterSubject(otsKitManagemantFilterAction: OTSKitManagemantFilterAction): void {
    this.otsKitListFilterRequestParameterSubject.next(otsKitManagemantFilterAction);
  }

  /**
 * OTSKit List Page Refresh After some Action Like 
 * <AUTHOR>
 * @returns ListingPageReloadSubjectParameter
 */
  public getOTSKitListRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.otsKitListRefreshSubject;
  }


  /**
  * OTSkit Detail Page Refresh After some Action 
  * isReloadData false means delete Kit operation and move list page
  * <AUTHOR>
  * @returns ListingPageReloadSubjectParameter
  */
  public getOTSKitDetailRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.otsKitDetailRefreshSubject;
  }

  /**
  * This function call the subject for reload the page data
  *  Note : (ListOTSKitManagementResource) -> Filter page subject call -> Listing page subject call
  * clear all filter after page data Reload
  * <AUTHOR>
  * @param isReloadData -> false means move to prev page form Detail to list page.
  * @param resourceName
  */
  public callOTSKitRefreshPageSubject(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, resourceName: string, isFilterHidden: boolean): void {
    if (resourceName == OTSKitManagementListResource) {
      if (isFilterHidden) {
        let otsKitRequestBody = new OTSKitManagemantSearchRequestBody("", [], "",);
        let otsKitFilterAction = new OTSKitManagemantFilterAction(listingPageReloadSubjectParameter, otsKitRequestBody);
        this.callOTSKitListFilterRequestParameterSubject(otsKitFilterAction);
      } else {
        this.otsKitListRefreshSubject.next(listingPageReloadSubjectParameter);
      }
    } else if (resourceName == DetailOTSKitManagementResource) {
      this.otsKitDetailRefreshSubject.next(listingPageReloadSubjectParameter);
    }
  }

  /**
   * Set Country List
   */
  public setCountryList(countriesList: CountryListResponse[]): void {
    this.countriesList = countriesList;
  }

  /**
   * Get Country List
   */
  public getCountryList(): CountryListResponse[] {
    return this.countriesList;
  }

}
