import { HttpErrorResponse } from '@angular/common/http';
import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { ReactiveFormsModule, Validators } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { throwError } from 'rxjs';
import { commonsProviders, countryListResponse } from 'src/app/Tesing-Helper/test-utils';
import { DeviceDetailResource } from 'src/app/app.constants';
import { TranferOrderSelection } from 'src/app/model/SalesOrder/TranferOrderSelection.model';
import { TransferProductDetails } from 'src/app/model/device/TransferProductDetails.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { TransferOrderSelectionComponent } from './transfer-order-selection.component';

describe('TransferOrderSelectionComponent', () => {
  let component: TransferOrderSelectionComponent;
  let fixture: ComponentFixture<TransferOrderSelectionComponent>;
  let salesOrderApiCallServicespy: jasmine.SpyObj<SalesOrderApiCallService>;
  let countryCacheServiceSpy: jasmine.SpyObj<CountryCacheService>;
  let toastrServiceSpy: jasmine.SpyObj<ToastrService>;
  let exceptionServiceSpy: jasmine.SpyObj<ExceptionHandlingService>;
  let commonsServiceSpy: jasmine.SpyObj<CommonsService>;

  beforeEach(async () => {
    salesOrderApiCallServicespy = jasmine.createSpyObj('SalesOrderApiCallService', [
      'getTranferOrderList',
      'getOrderRecordNumberList',
      'createManualSalesOrder'
    ]);

    countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheService', [
      'getCountryListFromCache'
    ]);

    toastrServiceSpy = jasmine.createSpyObj('ToastrService', [
      'success', 'error', 'warning', 'info'
    ]);

    exceptionServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', [
      'customErrorMessage'
    ]);

    commonsServiceSpy = jasmine.createSpyObj('CommonsService', [
      'removeSpacesThrowError',
      'checkNullFieldValue'
    ]);

    commonsServiceSpy.removeSpacesThrowError.and.returnValue(() => null);
    commonsServiceSpy.checkNullFieldValue.and.callFake(val => val);

    await TestBed.configureTestingModule({
      declarations: [TransferOrderSelectionComponent],
      imports: [NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule],
      providers: [
        { provide: ExceptionHandlingService, useValue: exceptionServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        LocalStorageService,
        AuthJwtService,
        SessionStorageService,
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServicespy },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        MultiSelectDropDownSettingService,
        { provide: CommonsService, useValue: commonsServiceSpy },
        commonsProviders(null)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(TransferOrderSelectionComponent);
    component = fixture.componentInstance;

    countryCacheServiceSpy.getCountryListFromCache.and.returnValue(Promise.resolve(countryListResponse));
    salesOrderApiCallServicespy.getOrderRecordNumberList.and.returnValue(Promise.resolve(["Sales Order", "Service Order"]));
    salesOrderApiCallServicespy.getTranferOrderList.and.returnValue(
      Promise.resolve([new TranferOrderSelection(1, "Test Order")])
    );

    // Set up component input
    component.transferProductDetail = new TransferProductDetails(1, 1, "sdec", "SN12345", DeviceDetailResource);
    component.transferProductDetail.serialNumber = "SN12345";

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize component data on ngOnInit', async () => {
    component.ngOnInit();

    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.orderTypeSetting).toBeDefined();
    expect(component.countrySetting).toBeDefined();
    expect(component.orderRecordTypeSetting).toBeDefined();
    expect(component.deviceSerialNumber).toEqual(" - SN12345");
  });

  it('should handle null transferProductDetail during initialization', async () => {
    component.transferProductDetail = null;
    component.ngOnInit();

    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.deviceSerialNumber).toEqual("");
  });

  it('should navigate back if salesOrderList is null', async () => {
    spyOn(component, 'back').and.callThrough();
    spyOn(component.backToDetailPage, 'emit');

    salesOrderApiCallServicespy.getTranferOrderList.and.returnValue(Promise.resolve(null));

    await component.initData();

    expect(component.back).toHaveBeenCalled();
    expect(component.backToDetailPage.emit).toHaveBeenCalled();
  });

  it('should call back() and emit backToDetailPage event', () => {
    spyOn(component.backToDetailPage, 'emit');

    component.back();

    expect(component.backToDetailPage.emit).toHaveBeenCalled();
  });

  it('should mark form field as touched on item click validation', () => {
    const fieldName = 'salesOrderNumber';
    spyOn(component.transferOrderForm.get(fieldName), 'markAsTouched');
    component.transferOrderForm.setErrors({ invalid: true });

    component.onItemClickValidation(fieldName);

    expect(component.transferOrderForm.get(fieldName).markAsTouched).toHaveBeenCalled();
  });

  it('should handle selection and deselection of end-user orders', () => {
    const mockOrder = { id: 123, name: 'Test Order' };

    component.enduserSeletion(mockOrder);
    expect(component.destinationSalesOrderId).toBe(123);

    component.endUserOrderDeSelect(null);
    expect(component.destinationSalesOrderId).toBeNull();
  });

  it('should show selection table when not in sales order disabled mode', () => {
    spyOn(component.showTranferOrder, 'emit');
    spyOn(component.destinationSalesOrderIdChange, 'emit');
    component.destinationSalesOrderId = 123;
    component.isSalesOrderDisabled = false;

    component.showSelectionTable();

    expect(component.destinationSalesOrderIdChange.emit).toHaveBeenCalledWith(123);
    expect(component.showTranferOrder.emit).toHaveBeenCalledWith([false, true, false]);
  });

  it('should call accept() when in sales order disabled mode', () => {
    spyOn(component, 'accept');
    component.isSalesOrderDisabled = true;

    component.showSelectionTable();

    expect(component.accept).toHaveBeenCalled();
  });

  it('should open manual sales order field with appropriate validators', () => {
    component.openManualSalesOrderField();

    expect(component.isSalesOrderDisabled).toBeTrue();
    expect(component.transferOrderForm.get('salesOrderNumber').hasValidator(Validators.required)).toBeFalse();
    expect(component.transferOrderForm.get('manualSalesOrderNumber').hasValidator(Validators.required)).toBeTrue();
    expect(component.transferOrderForm.get('countries').hasValidator(Validators.required)).toBeTrue();
    expect(component.transferOrderForm.get('customerName').hasValidator(Validators.required)).toBeTrue();
    expect(component.transferOrderForm.get('orderRecordType').hasValidator(Validators.required)).toBeTrue();
  });

  it('should close manual sales order field with appropriate validators', () => {
    component.closeManualSalesOrderField();

    expect(component.isSalesOrderDisabled).toBeFalse();
    expect(component.transferOrderForm.get('salesOrderNumber').hasValidator(Validators.required)).toBeTrue();
    expect(component.transferOrderForm.get('manualSalesOrderNumber').hasValidator(Validators.required)).toBeFalse();
    expect(component.transferOrderForm.get('countries').hasValidator(Validators.required)).toBeFalse();
    expect(component.transferOrderForm.get('customerName').hasValidator(Validators.required)).toBeFalse();
    expect(component.transferOrderForm.get('orderRecordType').hasValidator(Validators.required)).toBeFalse();
  });

  it('should handle error when creating manual sales order', fakeAsync(() => {
    // Set up form values
    component.transferOrderForm.setValue({
      salesOrderNumber: null,
      manualSalesOrderNumber: 'ORDER123',
      countries: [{ id: 1, name: 'USA' }],
      customerName: 'Test Customer',
      poNumber: 'PO123',
      customerEmail: '<EMAIL>',
      deviceAutoLock: true,
      probeAutoLock: false,
      orderRecordType: ['Sales Order']
    });

    // Mock the API error response
    const mockError = new HttpErrorResponse({ status: 500 });
    salesOrderApiCallServicespy.createManualSalesOrder.and.returnValue(throwError(() => mockError));

    // Call accept method
    component.accept();
    tick();

    // Check error handling
    expect(exceptionServiceSpy.customErrorMessage).toHaveBeenCalledWith(mockError);
    expect(component.loading).toBeFalse();
  }));

  // Test UI interactions
  it('should have functional Add Sales Order button', async () => {
    component.ngOnInit();

    fixture.detectChanges();
    await fixture.whenStable();

    spyOn(component, 'openManualSalesOrderField');

    const addBtn = fixture.debugElement.query(By.css('#addSalesOrderBtn'));
    expect(addBtn).toBeTruthy();

    addBtn.nativeElement.click();
    expect(component.openManualSalesOrderField).toHaveBeenCalled();
  });

  it('should have functional Close Sales Order button', async () => {
    component.isSalesOrderDisabled = true;
    fixture.detectChanges();

    spyOn(component, 'closeManualSalesOrderField');

    const closeBtn = fixture.debugElement.query(By.css('#closeSalesOrderBtn'));
    expect(closeBtn).toBeTruthy();

    closeBtn.nativeElement.click();
    expect(component.closeManualSalesOrderField).toHaveBeenCalled();
  });

  it('should have functional Next button', async () => {
    // Set a valid salesOrderNumber - fixed to be an array
    component.transferOrderForm.patchValue({
      salesOrderNumber: ['ORDER123']
    });

    component.ngOnInit();

    fixture.detectChanges();
    await fixture.whenStable();

    spyOn(component, 'showSelectionTable');

    const nextBtn = fixture.debugElement.query(By.css('#orderTypeNextBtn'));
    expect(nextBtn).toBeTruthy();

    nextBtn.nativeElement.click();
    expect(component.showSelectionTable).toHaveBeenCalled();
  });

  it('should have functional Back button', async () => {
    component.ngOnInit();

    fixture.detectChanges();
    await fixture.whenStable();

    spyOn(component, 'back');

    const backBtn = fixture.debugElement.query(By.css('#tranferredOrderPageBack'));
    expect(backBtn).toBeTruthy();

    backBtn.nativeElement.click();
    expect(component.back).toHaveBeenCalled();
  });

  // Added to make sure validation is tested
  it('should validate form fields correctly', () => {
    component.transferOrderForm.patchValue({
      salesOrderNumber: null,
      manualSalesOrderNumber: '',
      countries: null,
      customerName: '',
      orderRecordType: null
    });

    // Test required validator on salesOrderNumber
    expect(component.transferOrderForm.get('salesOrderNumber').invalid).toBeTrue();

    // Set a valid salesOrderNumber - fixed to be an array
    component.transferOrderForm.patchValue({
      salesOrderNumber: ['ORDER123']
    });

    expect(component.transferOrderForm.get('salesOrderNumber').valid).toBeTrue();

    // Test open manual sales order which changes validators
    component.openManualSalesOrderField();

    // Now manualSalesOrderNumber should be required
    expect(component.transferOrderForm.get('manualSalesOrderNumber').invalid).toBeTrue();
    expect(component.transferOrderForm.get('countries').invalid).toBeTrue();
    expect(component.transferOrderForm.get('customerName').invalid).toBeTrue();
    expect(component.transferOrderForm.get('orderRecordType').invalid).toBeTrue();

    // Set valid values for manual form
    component.transferOrderForm.patchValue({
      manualSalesOrderNumber: 'ORDER456',
      countries: [{ id: 1, name: 'USA' }],
      customerName: 'Test Customer',
      orderRecordType: ['Sales Order']
    });

    expect(component.transferOrderForm.valid).toBeTrue();
  });
});