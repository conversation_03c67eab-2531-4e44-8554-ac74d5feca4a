import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { UpdateProbeTypeComponent } from 'src/app/FeatureModule/Probe/update-probe-type/update-probe-type.component';

@Injectable({
  providedIn: 'root'
})
export class UpdateProbeTypeService {

  constructor(
    private modalService: NgbModal
  ) { }


  /**
   * Open Probe Type Update Model
   * 
   * @param title 
   * @param btnOkText 
   * @param btnCancelText 
   * @param probeType 
   * @param probeIdList 
   * @param dialogSize 
   * @returns 
   */
  public openUpdateProbeTypePopup(
    title: string,
    btnOkText: string,
    btnCancelText: string,
    probeType: string,
    probeIdList: Array<number>,
    dialogSize: 'sm' | 'lg' = 'sm'): Promise<boolean> {
    const modalRef = this.modalService.open(UpdateProbeTypeComponent, { size: dialogSize });
    modalRef.componentInstance.title = title;
    modalRef.componentInstance.btnOkText = btnOkText;
    modalRef.componentInstance.btnCancelText = btnCancelText;
    modalRef.componentInstance.probeType = probeType;
    modalRef.componentInstance.probeIdList = probeIdList;
    return modalRef.result;
  }


}
