import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, Output, ViewEncapsulation } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { DateTimeDisplayFormat, DeleteProbeConfigGroupConfirmationMessage, PROBE_CONFIG_GROUP_DELETE } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { EnumMapping } from 'src/app/model/common/EnumMapping.model';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { ProbeConfigGroupDeatilResponse } from 'src/app/model/ProbeConfigGroup/ProbeConfigGroupDeatilResponse.model';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { ProbeTypeEnum } from 'src/app/shared/enum/ProbeType.enum';
import { ValidityEnum } from 'src/app/shared/enum/ValidityEnum.enum';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { ProbeConfigGroupApiCallService } from 'src/app/shared/Service/ProbeConfigGroup/probe-config-group-api-call.service';
import { KeyValueMappingServiceService } from 'src/app/shared/util/key-value-mapping-service.service';

@Component({
  selector: 'app-probe-config-group-detail',
  templateUrl: './probe-config-group-detail.component.html',
  styleUrl: './probe-config-group-detail.component.css',
  encapsulation: ViewEncapsulation.None,
})
export class ProbeConfigGroupDetailComponent {
  public loading: boolean = false;
  @Input('probeConfigGroupId') probeConfigGroupId: number;
  @Output('showListingPage') showListingPage = new EventEmitter();


  //subject
  subscriptionForCommonloading: Subscription;

  //permission
  deleteProbeConfigGroupPermission: boolean;

  probeConfigGroupDetailResponse: ProbeConfigGroupDeatilResponse;
  dateTimeDisplayFormat: string = DateTimeDisplayFormat;

  probeTypesList: Array<EnumMapping> = [];
  validityEnumList: Array<EnumMapping> = [];

  constructor(
    private probeConfigGroupApiCallService: ProbeConfigGroupApiCallService,
    private exceptionService: ExceptionHandlingService,
    private keyValueMappingService: KeyValueMappingServiceService,
    private dialogservice: ConfirmDialogService,
    private toste: ToastrService,
    private permissionService: PermissionService,
  ) { }


  /**
   * Component Create
   *
   * <AUTHOR>
   */
  public ngOnInit(): void {
    this.deleteProbeConfigGroupPermission = this.permissionService.getProbeConfigGroupPermission(PermissionAction.DELETE_CONFIG_GROUP_ACTION)
    this.probeTypesList = this.keyValueMappingService.enumOptionToList(ProbeTypeEnum);
    this.validityEnumList = this.keyValueMappingService.enumOptionToList(ValidityEnum);
    this.getProbeConfigGroupDetail();
  }

  /**
   * Get probe Config Group Detail
   *
   * <AUTHOR>
   */
  private getProbeConfigGroupDetail(): void {
    this.setLoadingState(true);
    this.probeConfigGroupApiCallService.getProbeConfigGroupDetails(this.probeConfigGroupId)?.subscribe({
      next: (res: HttpResponse<ProbeConfigGroupDeatilResponse>) => {
        if (res.status == 200) {
          this.probeConfigGroupDetailResponse = res.body;
          this.setLoadingState(false);
        } else {
          this.setLoadingState(false);
          this.toste.info(PROBE_CONFIG_GROUP_DELETE);
          this.back();
        }
      },
      error: (error: HttpErrorResponse) => {
        this.setLoadingState(false);
        this.exceptionService.customErrorMessage(error);
      },
    });
  }

  /**
   * Go To Listing Page
   * 
   * <AUTHOR>
   */
  public back(): void {
    this.showListingPage.emit();
  }

  /**
  * Set Loading State
  *
  * <AUTHOR>
  * @param status
  */
  private setLoadingState(status: boolean): void {
    this.loading = status;
  }

  /**
  * Delete Probe Config Group
  *
  * <AUTHOR>
  */
  public deleteProbeConfigGroup(): void {
    this.dialogservice.confirm('Delete', DeleteProbeConfigGroupConfirmationMessage)
      .then((confirmed) => {
        if (confirmed) {
          this.setLoadingState(true);
          this.probeConfigGroupApiCallService.deleteProbeConfigGroup([this.probeConfigGroupId])?.subscribe({
            next: (res: HttpResponse<SuccessMessageResponse>) => {
              this.toste.success(res.body.message);
              this.back();
            },
            error: (error: HttpErrorResponse) => {
              this.setLoadingState(false);
              this.exceptionService.customErrorMessage(error);
            }
          })
        }
      });
  }

  /**
  * Refresh Probe Config Group Detail Page Data
  * 
  * <AUTHOR> 
  */
  public refreshPCGDetailPage(): void {
    this.getProbeConfigGroupDetail();
  }
}