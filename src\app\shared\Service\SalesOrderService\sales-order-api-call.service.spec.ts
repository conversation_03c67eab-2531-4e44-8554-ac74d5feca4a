import { HttpErrorResponse } from '@angular/common/http';
import { HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { TREANSFER_ORDER_EMPTY_LIST } from 'src/app/app.constants';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { KitManagemantProductDetailBaseResponse } from 'src/app/model/KitManagement/KitManagemantProductDetailBaseResponse.model';
import { BasicSalesOrderDetailResponse } from 'src/app/model/SalesOrder/BasicSalesOrderDetailResponse.model';
import { SalesOrderFaieldSearchRequestBody } from 'src/app/model/SalesOrder/SalesOrderFaieldSearchRequestBody.model';
import { SalesOrderFailedPageResponse } from 'src/app/model/SalesOrder/SalesOrderFailedPageResponse.model';
import { SalesOrderProductMappingRequest } from 'src/app/model/SalesOrder/SalesOrderProductMappingRequest.model';
import { SalesOrderProductMappingResponse } from 'src/app/model/SalesOrder/SalesOrderProductMappingResponse.model';
import { SalesOrderProductResponse } from 'src/app/model/SalesOrder/SalesOrderProductResponse.model';
import { SalesOrderSchedulerSyncTimeResponse } from 'src/app/model/SalesOrder/SalesOrderSchedulerSyncTimeResponse.model';
import { SalesOrderTransferValidateRequest } from 'src/app/model/SalesOrder/SalesOrderTransferValidateRequest.model';
import { TranferOrderSelection } from 'src/app/model/SalesOrder/TranferOrderSelection.model';
import { TransferOrderSelectionDetailRequest } from 'src/app/model/SalesOrder/TransferOrderSelectionDetailRequest.model';
import { BASE_URL, commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { CommonsService } from '../../util/commons.service';
import { SalesOrderApiCallService } from './sales-order-api-call.service';

describe('SalesOrderApiCallService Additional Tests', () => {
  let service: SalesOrderApiCallService;
  let httpMock: HttpTestingController;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let exceptionServiceSpy: jasmine.SpyObj<ExceptionHandlingService>;

  beforeEach(() => {
    const commonsServiceMock = { handleError: () => (err: any) => { throw err; } };
    toastrServiceMock = jasmine.createSpyObj('ToastrService', [
      'success', 'error', 'warning', 'info'
    ]);

    exceptionServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);

    TestBed.configureTestingModule({
      imports: [],
      providers: [
        ConfigInjectService,
        { provide: CommonsService, useValue: commonsServiceMock },
        { provide: ExceptionHandlingService, useValue: exceptionServiceSpy },
        commonsProviders(toastrServiceMock)
      ]
    });

    service = TestBed.inject(SalesOrderApiCallService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should update kit bridge info', () => {
    const bridge = new KitManagemantProductDetailBaseResponse();
    const response = null;
    const updated = service['updateKitBridgeInfo'](bridge, response);

    expect(updated.serialNumber).toBeUndefined();
  });

  it('should update extra bridge info', () => {
    const bridge = new SalesOrderProductResponse(null, null, null, null);
    const response = null;
    const updated = service['updateExtraBridgeInfo'](bridge, response);

    expect(updated.entitySerialNumber).toBeUndefined();
  });

  it('should initiate manual sync', () => {
    const mockResponse = null;

    service.salesOrderManualSync().subscribe((res) => {
      expect(res.body).toBeNull();
    });

    const req = httpMock.expectOne(`${service.salesOrderUrl}/sync/manual`);
    expect(req.request.method).toBe('POST');
    req.flush(mockResponse);
  });

  it('should get manual sync status', () => {
    const id = 7;
    const mockResponse = true;

    service.manualsync(id).subscribe((res) => {
      expect(res.body).toBeTrue();
    });

    const req = httpMock.expectOne(`${service.salesOrderUrl}/sync/manual/status/${id}`);
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);
  });

  it('should transfer sales order', () => {
    const mockRequest = new SalesOrderTransferValidateRequest(null, null, null, null, null);
    const mockResponse = null;

    service.TransferSalesOrder(mockRequest).subscribe((res) => {
      expect(res.body).toBeNull();
    });

    const req = httpMock.expectOne(`${service.salesOrderUrl}/transfer`);
    expect(req.request.method).toBe('POST');
    req.flush(mockResponse);
  });

  it('should create manual sales order', () => {
    const mockRequest = new BasicSalesOrderDetailResponse(null, null, null, null, null, null, null, null);
    const mockResponse = null;

    service.createManualSalesOrder(mockRequest).subscribe((res) => {
      expect(res.body).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${service.salesOrderUrl}`);
    expect(req.request.method).toBe('POST');
    req.flush(mockResponse);
  });

  it('should get order record number list', async () => {
    const mockResponse = ['R1', 'R2'];
    const promise = service.getOrderRecordNumberList(true);

    const req = httpMock.expectOne(`${service.salesOrderUrl}/recordType?nonTransferRecordType=true`);
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse);

    const result = await promise;
    expect(result).toEqual(mockResponse);
  });

  it('should submit transfer order', () => {
    const mockRequest = new SalesOrderTransferValidateRequest(null, null, null, null, null);
    const mockResponse = null;

    service.submitTransferOrder(mockRequest).subscribe((res) => {
      expect(res.body).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${service.salesOrderUrl}/transfer/validate`);
    expect(req.request.method).toBe('POST');
    req.flush(mockResponse);
  });

  it('should get transfer order selection details', () => {
    const mockRequest = new TransferOrderSelectionDetailRequest(null, null, null, null);
    const mockResponse = null;

    service.getTransferOrderSelectionDetails(mockRequest).subscribe((res) => {
      expect(res.body).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${service.salesOrderUrl}/transfer/detail`);
    expect(req.request.method).toBe('POST');
    req.flush(mockResponse);
  });

  it('should call getSalesOrderList and return paginated sales order response', () => {
    const requestBody = null;
    const reqParams = null;
    const mockResponse = null;

    service.getSalesOrderList(requestBody, reqParams).subscribe(response => {
    });

    const req = httpMock.expectOne(r =>
      r.method === 'POST' && r.url.includes('/search')
    );

    expect(req.request.body).toEqual(requestBody);
    req.flush(mockResponse, { status: 200, statusText: 'OK' });
  });

  it('should call getSalesOrderDetails and return sales order detail', () => {
    const salesOrderId = 123;
    const mockDetail = null;

    service.getSalesOrderDetails(salesOrderId).subscribe(response => {
      expect(response.body).toEqual(mockDetail);
    });

    const req = httpMock.expectOne(`${BASE_URL}api/salesOrders/details/${salesOrderId}`);
    expect(req.request.method).toBe('GET');
    req.flush(mockDetail, { status: 200, statusText: 'OK' });
  });

  it('should return a list of sales order numbers', async () => {
    const mockOrderNumbers = ['SO001', 'SO002'];

    const promise = service.getSalesOrderNumberList();

    const req = httpMock.expectOne(`${service['salesOrderUrl']}/salesOrderNumber`);
    expect(req.request.method).toBe('GET');
    req.flush(mockOrderNumbers);

    const result = await promise;
    expect(result).toEqual(mockOrderNumbers);
  });

  it('should handle HttpErrorResponse and return an empty list', async () => {
    const promise = service.getSalesOrderNumberList();

    const req = httpMock.expectOne(`${service['salesOrderUrl']}/salesOrderNumber`);
    expect(req.request.method).toBe('GET');
    req.flush('Error occurred', { status: 500, statusText: 'Internal Server Error' });

    const result = await promise;
    expect(result).toEqual([]);
  });

  it('should return a list of transfer orders', async () => {
    const mockTransferOrders: TranferOrderSelection[] = [
      { id: 2, salesOrderNumber: 'Order 1' },
      { id: 3, salesOrderNumber: 'Order 2' }
    ];

    const promise = service.getTranferOrderList();

    const req = httpMock.expectOne(`${service['salesOrderUrl']}/transfer/nonTransferOrders`);
    expect(req.request.method).toBe('GET');
    req.flush(mockTransferOrders, { status: 200, statusText: 'OK' });

    const result = await promise;
    expect(result).toEqual(mockTransferOrders);
  });

  it('should show info message if response status is 204 (No Content)', async () => {
    const promise = service.getTranferOrderList();

    const req = httpMock.expectOne(`${service['salesOrderUrl']}/transfer/nonTransferOrders`);
    expect(req.request.method).toBe('GET');
    req.flush(null, { status: 204, statusText: 'No Content' });

    await promise;
    expect(toastrServiceMock.info).toHaveBeenCalledWith(TREANSFER_ORDER_EMPTY_LIST);
  });

  it('should handle HttpErrorResponse and return an empty list', async () => {
    const promise = service.getTranferOrderList();

    const req = httpMock.expectOne(`${service['salesOrderUrl']}/transfer/nonTransferOrders`);
    expect(req.request.method).toBe('GET');
    req.flush('Error occurred', { status: 500, statusText: 'Internal Server Error' });

    const result = await promise;
    expect(result).toEqual([]);
    expect(exceptionServiceSpy.customErrorMessage).toHaveBeenCalled();
  });

  it('should return empty list and not call error handler for non-HttpErrorResponse errors', async () => {
    const promise = service.getTranferOrderList();

    const req = httpMock.expectOne(`${service['salesOrderUrl']}/transfer/nonTransferOrders`);
    expect(req.request.method).toBe('GET');
    req.error(new ProgressEvent('error'));

    const result = await promise;
    expect(result).toEqual([]);
  });


  it('should return empty list and not call error handler for non-HttpErrorResponse errors', async () => {
    const promise = service.getOrderRecordNumberList(null);

    const req = httpMock.expectOne(`${service['salesOrderUrl']}/recordType?nonTransferRecordType=null`);
    expect(req.request.method).toBe('GET');
    req.error(new ProgressEvent('error'));

    const result = await promise;
    expect(result).toEqual([]);
  });

  it('should return basic sales order details for a given salesOrderNumber', (done) => {
    const salesOrderNumber = 'SO12345';
    const mockResponse: BasicSalesOrderDetailResponse = {
      // Define the mock response data here, based on your response model.
      salesOrderNumber: 'SO12345',
      customerName: 'John Doe',
      // Add other properties as necessary
    } as BasicSalesOrderDetailResponse;

    service.getBasicSalesOrderDetails(salesOrderNumber).subscribe(response => {
      expect(response.body).toEqual(mockResponse);
      expect(response.status).toBe(200);
      done();  // Ensure Jasmine waits for the async operation to finish
    });

    const req = httpMock.expectOne(`${service['salesOrderUrl']}/basic/${salesOrderNumber}`);
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse, { status: 200, statusText: 'OK' });

    // Ensure no other requests are pending after the flush
    httpMock.verify();
  });

  it('should delete sales order and return success response', () => {
    const salesOrderId = [123];  // Example sales order IDs
    const mockResponse: SuccessMessageResponse = { message: 'Success' };  // Example mock response

    service.deleteSalesOrder(salesOrderId).subscribe((response) => {
      expect(response.body).toEqual(mockResponse);
      expect(response.status).toBe(200); // Check status code
    });

    const req = httpMock.expectOne(`${service.salesOrderUrl}/${salesOrderId}`);
    expect(req.request.method).toBe('DELETE');  // Ensure DELETE method
    req.flush(mockResponse, { status: 200, statusText: 'OK' });
  });

  it('should return sales order scheduler sync time on success', async () => {
    const mockResponse: SalesOrderSchedulerSyncTimeResponse = {
      nextExecutionTime: 1234567890,
      lastExecutionTime: 123456789
    };

    const promise = service.getSalesOrderSchedulerSyncTime();

    const req = httpMock.expectOne(`${service.salesOrderUrl}/sync/time`);
    expect(req.request.method).toBe('GET');

    req.flush(mockResponse, { status: 200, statusText: 'OK' });

    const result = await promise;
    expect(result).toEqual(mockResponse);
  });

  it('should return null and call customErrorMessage on HttpErrorResponse', async () => {
    const mockError = new HttpErrorResponse({
      status: 500,
      statusText: 'Internal Server Error',
      error: { message: 'Server error' }
    });

    const promise = service.getSalesOrderSchedulerSyncTime();

    const req = httpMock.expectOne(`${service.salesOrderUrl}/sync/time`);
    expect(req.request.method).toBe('GET');

    req.flush(mockError.error, { status: 500, statusText: 'Internal Server Error' });

    const result = await promise;
    expect(result).toBeNull();
    expect(exceptionServiceSpy.customErrorMessage).toHaveBeenCalledWith(jasmine.any(HttpErrorResponse));
  });

  it('should call updateKitBridgeInfo when isKitBridge is true', () => {
    const bridge = { some: 'data' };
    const resetBridgeResponse = null;
    const expected = { result: 'kitUpdated' };

    const updateKitBridgeSpy = spyOn<any>(service, 'updateKitBridgeInfo').and.returnValue(expected);

    const result = service.updateBridgeInfo(bridge, resetBridgeResponse, true);

    expect(updateKitBridgeSpy).toHaveBeenCalledWith(bridge, resetBridgeResponse);
    expect(result).toEqual(expected);
  });
  it('should call updateExtraBridgeInfo when isKitBridge is false', () => {
    const bridge = { other: 'info' };
    const resetBridgeResponse = null;
    const expected = { result: 'extraUpdated' };

    const updateExtraBridgeSpy = spyOn<any>(service, 'updateExtraBridgeInfo').and.returnValue(expected);

    const result = service.updateBridgeInfo(bridge, resetBridgeResponse, false);

    expect(updateExtraBridgeSpy).toHaveBeenCalledWith(bridge, resetBridgeResponse);
    expect(result).toEqual(expected);
  });

  it('should return response body on successful resetSalesOrderBridge call', async () => {
    const mockRequest = new SalesOrderProductMappingRequest(null);
    const salesOrderId = 123;
    const mockResponse: SalesOrderProductMappingResponse = { /* mock structure */ } as SalesOrderProductMappingResponse;

    const promise = service.resetSalesOrderBridge(mockRequest, salesOrderId);

    const req = httpMock.expectOne(`${service['salesOrderUrl']}/reset/${salesOrderId}`);
    expect(req.request.method).toBe('PUT');
    req.flush(mockResponse, { status: 200, statusText: 'OK' });

    const result = await promise;
    expect(result).toEqual(mockResponse);
  });

  it('should return null if resetSalesOrderBridge response body is null', async () => {
    const mockRequest = new SalesOrderProductMappingRequest(null);
    const salesOrderId = 456;

    const promise = service.resetSalesOrderBridge(mockRequest, salesOrderId);

    const req = httpMock.expectOne(`${service['salesOrderUrl']}/reset/${salesOrderId}`);
    expect(req.request.method).toBe('PUT');
    req.flush(null, { status: 200, statusText: 'OK' });

    const result = await promise;
    expect(result).toBeNull();
  });

  it('should handle HttpErrorResponse and return null', async () => {
    const mockRequest = new SalesOrderProductMappingRequest(null);
    const salesOrderId = 789;

    const promise = service.resetSalesOrderBridge(mockRequest, salesOrderId);

    const req = httpMock.expectOne(`${service['salesOrderUrl']}/reset/${salesOrderId}`);
    expect(req.request.method).toBe('PUT');
    req.flush('Error occurred', { status: 500, statusText: 'Internal Server Error' });

    const result = await promise;
    expect(result).toBeNull();
    expect(exceptionServiceSpy.customErrorMessage).toHaveBeenCalled();
  });

  it('should call getSalesOrderFailedList and return failed sales order page response', () => {
    const mockRequestBody: SalesOrderFaieldSearchRequestBody = {
      // Fill in required fields for the request body
    } as SalesOrderFaieldSearchRequestBody;

    const mockReqParams = {
      page: 0,
      size: 10,
      sort: ['id,desc']
    };
    const mockResponse: SalesOrderFailedPageResponse = {
      // Fill with mock paginated response structure
    } as SalesOrderFailedPageResponse;

    service.getSalesOrderFailedList(mockRequestBody, mockReqParams).subscribe(response => {
      expect(response.body).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(r =>
      r.method === 'POST' &&
      r.url === `${service['salesOrderUrl']}/failed/search` &&
      r.params.get('page') === '0' &&
      r.params.get('size') === '10' &&
      r.params.getAll('sort')?.includes('id,desc')
    );

    expect(req.request.body).toEqual(mockRequestBody);
    req.flush(mockResponse, { status: 200, statusText: 'OK' });
  });


});
