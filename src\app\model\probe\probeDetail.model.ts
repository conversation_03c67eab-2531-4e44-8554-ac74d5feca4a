import { ProductStatusEnum } from 'src/app/shared/enum/Common/ProductStatus.enum';

export class ProbeDetailResponse {
    id: number;
    serialNumber: string;
    type: string;
    features: string;
    presets: string;
    salesOrderNumber: string;
    customerName: string;
    appVersion: string;
    osType: string;
    osVersion: string;
    lastConnectedTime: number;
    productStatus: ProductStatusEnum;
    country: string;
    locked: boolean;
    editable: boolean;

    constructor(id: number, serialNumber: string, type: string, features: string, presets: string, salesOrderNumber: string, customerName: string, appVersion: string, osType: string, osVersion: string, lastConnectedTime: number, productStatus: ProductStatusEnum, country: string, locked: boolean, editable: boolean) { //NOSONAR
        this.id = id;
        this.serialNumber = serialNumber;
        this.type = type;
        this.features = features;
        this.presets = presets;
        this.salesOrderNumber = salesOrderNumber;
        this.customerName = customerName;
        this.appVersion = appVersion;
        this.osType = osType;
        this.osVersion = osVersion;
        this.lastConnectedTime = lastConnectedTime;
        this.productStatus = productStatus;
        this.country = country;
        this.locked = locked;
        this.editable = editable;
    }
}

