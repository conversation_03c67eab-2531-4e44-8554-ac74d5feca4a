export class ProbeHistoryResponse {
    licenseHistoryId: number;
    modifiedDate: number;
    modifiedBy: string;
    features: Array<string>;
    presets: Array<string>;
    reminder: boolean;

    constructor(
        licenseHistoryId: number,
        modifiedDate: number,
        modifiedBy: string,
        features: Array<string>,
        presets: Array<string>,
        reminder: boolean
    ) {
        this.licenseHistoryId = licenseHistoryId;
        this.modifiedDate = modifiedDate;
        this.modifiedBy = modifiedBy;
        this.features = features;
        this.presets = presets;
        this.reminder = reminder;
    }
}
