import { ProbeRequest } from "./ProbeRequest.model";


export class ProbeFeatureRequest {
    salesOrderNumber: string;
    customerName: string;
    customerEmail: string;
    countryId: number;
    poNumber: string;
    orderRecordType: string;
    probes: Array<ProbeRequest>;
    newProbe: boolean;
    deviceAutoLock: boolean;
    probeAutoLock: boolean;

    constructor($salesOrderNumber: string, $customerName: string, $customerEmail: string, $countryId: number, $poNumber: string, $orderRecordType: string, $probes: Array<ProbeRequest>, $newProbe: boolean, $deviceAutoLock: boolean, $probeAutoLock: boolean) { //NOSONAR
        this.salesOrderNumber = $salesOrderNumber;
        this.customerName = $customerName;
        this.customerEmail = $customerEmail;
        this.countryId = $countryId;
        this.poNumber = $poNumber;
        this.orderRecordType = $orderRecordType;
        this.probes = $probes;
        this.newProbe = $newProbe;
        this.deviceAutoLock = $deviceAutoLock;
        this.probeAutoLock = $probeAutoLock;
    }

}