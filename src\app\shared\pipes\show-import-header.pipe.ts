import { Pipe, PipeTransform } from '@angular/core';
import { DeviceListResource, ListKitManagementResource, OTSKitManagementListResource } from 'src/app/app.constants';

@Pipe({
  name: 'showImportHeader',
})
export class ShowImportHeaderPipe implements PipeTransform {

  private readonly headerMap: Record<string, string> = {
    [DeviceListResource]: 'HW ID / Serial No',
    [ListKitManagementResource]: 'Kit Part Number',
    [OTSKitManagementListResource]: 'Kit Part Number'
  };

  transform(value: string): string {
    return this.headerMap[value] || '';
  }
}
