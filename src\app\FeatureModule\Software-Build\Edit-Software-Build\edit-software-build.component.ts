import { HttpErrorResponse } from "@angular/common/http";
import { Component, Input, OnInit, ViewEncapsulation } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from "ngx-toastr";
import { CLIENT_DEVICE, DEMO_DEVICE, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH, MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, MAXIMUM_TEXTBOX_LENGTH } from "src/app/app.constants";
import { CountryListResponse } from "src/app/model/Country/CountryListResponse.model";
import { MultiSelectDropdownSettings } from "src/app/model/MultiSelectDropdownSettings.model";
import { BasicModelConfig } from "src/app/model/common/BasicModelConfig.model";
import { EditSoftwateBildRequest } from "src/app/model/SoftwaarBuilds/EditSoftwateBildRequest.model";
import { SoftwareBuildListResponse } from "src/app/model/SoftwaarBuilds/SoftwareBuildListResponse.model";
import { ExceptionHandlingService } from "src/app/shared/ExceptionHandling.service";
import { deviceTypesEnum } from "src/app/shared/enum/deviceTypesEnum.enum";
import { SoftwareBuildStatusEnum } from "src/app/shared/enum/SoftwareBuildStatusEnum";
import { CommonsService } from "src/app/shared/util/commons.service";
import { DownloadService } from "src/app/shared/util/download.service";
import { MultiSelectDropDownSettingService } from "src/app/shared/util/multi-select-drop-down-setting.service";
import { ValidationService } from "src/app/shared/util/validation.service";
import { SoftwareBuildApiCallService } from "../software-build-services/software-api-call/software-build-api-call.service";

@Component({
  selector: "app-edit-software-build",
  templateUrl: "./edit-software-build.component.html",
  styleUrls: ["./edit-software-build.component.css"],
  encapsulation: ViewEncapsulation.None
})
export class EditSoftwareBuildComponent implements OnInit {
  @Input() basicModelConfig: BasicModelConfig;
  @Input() inventory: SoftwareBuildListResponse;
  @Input() jsonVersionList: any[];
  @Input() countryList: CountryListResponse[];


  inventoryStatus: string[];
  deviceTypes: string[];

  //Text box max limit set
  textBoxMaxLength: number = MAXIMUM_TEXTBOX_LENGTH;
  textBoxMaxCharactersAllowedMessage: string = MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  small_textBoxMaxCharactersAllowedMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;

  form = new FormGroup({
    Title: new FormControl("", [Validators.required, Validators.maxLength(this.textBoxMaxLength)]),
    deviceTypes: new FormControl([]),
    inventoryStatus: new FormControl([], Validators.required),
    jsonVersion: new FormControl([]),
    country: new FormControl([], Validators.required),
    partNumber: new FormControl(null, [Validators.required, Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), this.validationService.removeSpaces]),
  });

  dropdownSettingsSoftwareStatus: MultiSelectDropdownSettings = null;
  dropdownSettingsJsonVersion: MultiSelectDropdownSettings = null;
  dropdownSettingsDeviceTypes: MultiSelectDropdownSettings = null;
  countryDropdownSettings: MultiSelectDropdownSettings = null;

  textFieldDisabled: boolean = true;

  constructor(
    private activeModal: NgbActiveModal,
    private commonService: CommonsService,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private validationService: ValidationService,
    private commonsService: CommonsService,
    private softwareBuildApiCallService: SoftwareBuildApiCallService,
    private toste: ToastrService,
    private downloadService: DownloadService,
    private exceptionService: ExceptionHandlingService
  ) { }

  ngOnInit() {
    this.inventoryStatus = [];
    this.inventoryStatus.push(SoftwareBuildStatusEnum.ACTIVE);
    this.inventoryStatus.push(SoftwareBuildStatusEnum.INACTIVE);
    this.deviceTypes = [];
    this.deviceTypes.push(CLIENT_DEVICE);
    this.deviceTypes.push(DEMO_DEVICE);
    this.deviceTypes.push(deviceTypesEnum.ABOVE_BOTH);

    this.countryDropdownSettings = this.multiSelectDropDownSettingService.getCountryDrpSetting(false, true);
    this.dropdownSettingsSoftwareStatus = this.multiSelectDropDownSettingService.getSoftwareStatusDropdownSetting();
    this.dropdownSettingsJsonVersion = this.multiSelectDropDownSettingService.getjsonVersionDropdownSetting(true);
    this.dropdownSettingsDeviceTypes = this.multiSelectDropDownSettingService.getDeviceTypeDropdownSetting();

    this.updateCountryList();
    this.setFormValue();
  }

  private setFormValue(): void {
    this.form.get('Title').setValue(this.inventory?.title);
    this.form.get('deviceTypes').setValue(this.commonService.getDeviceTypeDropDownValueSelection(this.inventory?.deviceTypes));
    this.form.get('inventoryStatus').setValue(this.inventory?.isActive
      ? [SoftwareBuildStatusEnum.ACTIVE]
      : [SoftwareBuildStatusEnum.INACTIVE]);
    let selectedJsonMaster = isNullOrUndefined(this.inventory?.jsonMaster) ? [] : [this.inventory?.jsonMaster];
    this.form.get('jsonVersion').setValue(selectedJsonMaster);
    let selectedCountry = this.commonsService.getDropDownValueByName(this.countryList, this.inventory?.countries);
    this.form.get("country").setValue(selectedCountry);
    this.form.get("country").markAsTouched();
    this.form.get('partNumber').setValue(this.inventory?.partNumber);
  }

  private updateCountryList(): void {
    if (!isNullOrUndefined(this.countryList)) {
      for (let index in this.countryList) {
        this.countryList[index].isDisabled = false;
      }
    }
  }

  public decline() {
    this.activeModal.close(false);
  }

  public accept() {
    let formValue = this.form.value;
    this.setLoading(true);
    let jsonVersion = formValue.jsonVersion?.length == 1 ? formValue.jsonVersion[0] : null;
    let deviceTypeFilterValue = this.commonService.getDeviceTypeFilterValueArray(this.form, 'deviceTypes');
    let deviceTypes = isNullOrUndefined(deviceTypeFilterValue) ? [] : deviceTypeFilterValue;
    let requestData = new EditSoftwateBildRequest(deviceTypes,
      formValue.inventoryStatus.includes(SoftwareBuildStatusEnum.ACTIVE),
      jsonVersion,
      this.commonsService.getIdsFromArray(formValue.country),
      formValue?.partNumber);
    this.softwareBuildApiCallService.updateInventory(this.inventory?.id, requestData).subscribe({
      next: (response) => {
        this.toste.success(response.body['message']);
        this.activeModal.close(true);
        this.setLoading(false);
      }, error: (error: HttpErrorResponse) => {
        this.setLoading(false);
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  private setLoading(status: boolean): void {
    this.downloadService.setLoading(status, null);
  }

}
