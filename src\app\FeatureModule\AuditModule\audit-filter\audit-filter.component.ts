import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { isNullOrUndefined, isUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { AUDIT_MODIFIED_END_DATE_FILTER, AUDIT_MODIFIED_START_DATE_FILTER, AUDIT_MODULE_UNIQUE_ID_FILTER, SPECIAL_CHARACTER_PATTERN, SPECIAL_CHARACTER_ERROR_MESSAGE, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, MAXIMUM_TEXTBOX_LENGTH } from 'src/app/app.constants';
import { AuditModuleWithActionResponse } from 'src/app/model/Audit/AuditModuleWithActionResponse.model';
import { AuditFilterAction } from 'src/app/model/Audit/auditFilterAction';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';
import { BaseResponse } from 'src/app/model/common/BaseResponse.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { AuditApiCallService } from 'src/app/shared/Service/Audit/audit-api-call.service';
import { AuditService } from 'src/app/shared/Service/Audit/audit.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { ValidationService } from 'src/app/shared/util/validation.service';
import { AuditSearchRequsetBody } from '../../../model/Audit/auditSearchRequsetBody';

@Component({
  selector: 'app-audit-filter',
  templateUrl: './audit-filter.component.html',
  styleUrls: ['./audit-filter.component.css']
})
export class AuditFilterComponent implements OnInit {

  @Input("isFilterComponentInitWithApicall") isFilterComponentInitWithApicall: boolean;
  @Input("listPageRefreshForbackToDetailPage") listPageRefreshForbackToDetailPage: boolean;
  @Input("auditSearchRequsetBody") auditSearchRequsetBody: AuditSearchRequsetBody;
  @Input("archivedAuditSearch") archivedAuditSearch: boolean;


  //DropDown Setting 
  moduleSetting: MultiSelectDropdownSettings = null;
  actionSetting: MultiSelectDropdownSettings = null;
  //DropDown List
  moduleList: Array<AuditModuleWithActionResponse> = [];
  actionList: Array<BaseResponse> = [];

  //maxDate
  maxdate: Date = new Date();

  //MaxLength Message
  small_textBoxMaxLengthMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  textBoxMaxLengthMessage: string = MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;


  filterAuditForm = new FormGroup({
    modules: new FormControl([], []),
    actions: new FormControl([], []),
    uniqueId: new FormControl('', [Validators.maxLength(MAXIMUM_TEXTBOX_LENGTH), this.validationService.removeSpaces, Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    user: new FormControl('', [Validators.maxLength(MAXIMUM_TEXTBOX_LENGTH), this.validationService.removeSpaces, Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    modifiedStartDate: new FormControl(null, []),
    modifiedEndDate: new FormControl(null, []),
    archivedData: new FormControl(null, []),
  });

  subscriptionForRefeshList: Subscription;

  defaultListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);


  constructor(
    private commonsService: CommonsService,
    private commonOperationsService: CommonOperationsService,
    private auditService: AuditService,
    private auditApiCallService: AuditApiCallService,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private toste: ToastrService,
    private validationService: ValidationService) { }

  public ngOnInit(): void {
    this.moduleSetting = this.multiSelectDropDownSettingService.getAuditModuleDrpSetting();
    this.actionSetting = this.multiSelectDropDownSettingService.getAuditActionDrpSetting();
    this.onInitSubject();
    this.getFilterList();
    if (this.isFilterComponentInitWithApicall) {
      this.clearFilter(this.defaultListingPageReloadSubjectParameter);
    }
  }

  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForRefeshList)) { this.subscriptionForRefeshList.unsubscribe() }
  }

  /**
   * Get Filter List
   * 
   * <AUTHOR>
   */
  public async getFilterList(): Promise<void> {
    if (this.auditService.getAuditModuleListInStore()?.length == 0) {
      this.moduleList = await this.auditApiCallService.getAuditModuleList();
      this.auditService.setAuditModuleListInStore(this.moduleList);
    } else {
      this.moduleList = this.auditService.getAuditModuleListInStore();
    }
    this.setFilterValue();
  }

  /**
   * Set Filter value 
   * 
   * Note : if user hide and show filter then set data in storage
   * 
   * <AUTHOR>
   */
  public setFilterValue() {
    this.filterAuditForm?.get('archivedData').setValue(this.archivedAuditSearch);
    if (this.auditSearchRequsetBody != null) {
      let startModifiedDate = isNullOrUndefined(this.auditSearchRequsetBody.startmodifiedDate) ? null : new Date(this.auditSearchRequsetBody.startmodifiedDate);
      let endModifiedDate = isNullOrUndefined(this.auditSearchRequsetBody.endModifiedDate) ? null : new Date(this.auditSearchRequsetBody.endModifiedDate);
      let auditModule = isNullOrUndefined(this.auditSearchRequsetBody.auditModule) ? [] : this.moduleList?.filter(obj => obj?.name == this.auditSearchRequsetBody.auditModule);
      //Show and hide filter action List Update
      if (auditModule.length > 0) {
        this.changeModule(this.auditSearchRequsetBody.auditModule, true);
      }
      let auditAction = isNullOrUndefined(this.auditSearchRequsetBody.auditAction) ? [] : this.actionList?.filter(obj => JSON.stringify(this.auditSearchRequsetBody.auditAction).includes(`"${obj?.name}"`));
      this.filterAuditForm?.get('modules').setValue(auditModule);
      this.filterAuditForm?.get('actions').setValue(auditAction);
      this.filterAuditForm?.get('uniqueId').setValue(this.auditSearchRequsetBody.uniqueId);
      this.filterAuditForm?.get('user').setValue(this.auditSearchRequsetBody.modifiedBy);
      this.filterAuditForm?.get('modifiedStartDate').setValue(startModifiedDate);
      this.filterAuditForm?.get('modifiedEndDate').setValue(endModifiedDate);
    }
    if (this.listPageRefreshForbackToDetailPage) {
      this.auditListPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }


  public onInitSubject(): void {
    /**
     * Sales Order Detail Page Refresh After some Action Like Create or Update or Delete Sales Order
     * <AUTHOR>
     */
    this.subscriptionForRefeshList = this.auditService.getAuditListRefreshSubject()?.subscribe((listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter) => {
      if (listingPageReloadSubjectParameter.isReloadData) {
        if (listingPageReloadSubjectParameter.isClearFilter) {
          this.clearFilter(listingPageReloadSubjectParameter);
        } else {
          //page change 1,2,3
          this.auditListPageRefresh(listingPageReloadSubjectParameter);
        }
      }
    });
  }


  /**
   * Reload Listing Data
   * <AUTHOR>
   */
  public searchData(): void {
    let allFormValue = this.filterAuditForm.value;
    this.filterAuditForm?.get('uniqueId').setValue(this.commonsService.checkNullFieldValue(allFormValue.uniqueId));
    //Check All Filed
    if (this.commonsService.checkValueIsNullOrEmpty(allFormValue.modules) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.uniqueId) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.user) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.modifiedStartDate) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.modifiedEndDate) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.archivedData)) {
      this.commonOperationsService.showEmptyFilterTosteMessge();
    } else if (this.commonsService.checkValueIsNullOrEmpty(allFormValue.modifiedStartDate) &&
      !this.commonsService.checkValueIsNullOrEmpty(allFormValue.modifiedEndDate)) {
      //Check Start Date Is NullOrEmpty and End Date Is Not NullOrEmpty
      this.toste.info(AUDIT_MODIFIED_START_DATE_FILTER);
    } else if (!this.commonsService.checkValueIsNullOrEmpty(allFormValue.modifiedStartDate) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.modifiedEndDate)) {
      //Check Start Date Is Not NullOrEmpty and End Date Is NullOrEmpty
      this.toste.info(AUDIT_MODIFIED_END_DATE_FILTER);
    } else if (!this.commonsService.checkValueIsNullOrEmpty(allFormValue.uniqueId) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.modules)) {
      //Check uniqueId Is Not NullOrEmpty and modules Is NullOrEmpty
      this.toste.info(AUDIT_MODULE_UNIQUE_ID_FILTER);
    } else {
      this.auditListPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }

  /**
   * Clear All filter and Reload Data
   * <AUTHOR>
   */
  public clearFilter(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    this.filterAuditForm?.reset();
    //Clear Filter Then Action list Dropdown Empty
    this.changeModule(null, false);
    this.auditListPageRefresh(listingPageReloadSubjectParameter);
  }

  /**
   * Get Filter Data and pass to Listing page and Reload Page 
   * <AUTHOR>
   */
  private auditListPageRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    if (this.filterAuditForm.invalid) {
      this.filterAuditForm.reset();
    }
    let allFormValue = this.filterAuditForm.value;
    let modifiedStartDate = this.commonsService.checkValueIsNullOrEmpty(allFormValue.modifiedStartDate) ? null : new Date(allFormValue.modifiedStartDate).getTime();
    let modifiedEndDate = this.commonsService.checkValueIsNullOrEmpty(allFormValue.modifiedEndDate) ? null : this.commonsService.getEndTimeOfDay(new Date(allFormValue.modifiedEndDate)).getTime();
    let modules = this.commonsService.checkValueIsNullOrEmpty(allFormValue.modules) ? null : allFormValue.modules[0]?.name;
    let actions = this.commonsService.checkValueIsNullOrEmpty(allFormValue.actions) ? [] : allFormValue.actions.map(action => action?.name);
    let archivedData = this.commonsService.checkValueIsNullOrEmpty(allFormValue.archivedData) ? false : allFormValue.archivedData;
    let auditSearchRequsetBody = new AuditSearchRequsetBody(
      modules,
      actions,
      this.commonsService.checkNullFieldValue(allFormValue.uniqueId),
      this.commonsService.checkNullFieldValue(allFormValue.user),
      modifiedStartDate, modifiedEndDate);
    let auditFilterAction = new AuditFilterAction(listingPageReloadSubjectParameter, auditSearchRequsetBody, archivedData);
    this.auditService.callAuditListFilterRequestParameterSubject(auditFilterAction);
  }

  /**
   * Select/DeSelect Module 
   * Note : Based On Module Select Action List Update
   * <AUTHOR>
   * @param moduleName 
   * @param isSelect 
   */
  public changeModule(moduleName: any, isSelect: boolean): void {
    let filterModule = this.moduleList?.filter(obj => obj?.name == moduleName);
    if (isSelect && filterModule.length == 1) {
      this.actionList = filterModule[0].actions;
    } else {
      this.actionList = [];
    }
    this.filterAuditForm?.get('actions').setValue([]);
  }

  /**
  * handle the change event for ArchivedData checkbox
  * <AUTHOR>
  * @param actionName 
  */
  public onArchivedDataChange(event: Event): void {
    const checked = (event.target as HTMLInputElement).checked;
    this.filterAuditForm.get('archivedData')?.setValue(checked);
  }

}
