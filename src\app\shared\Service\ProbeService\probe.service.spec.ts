import { DatePipe } from '@angular/common';
import { TestBed } from '@angular/core/testing';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { AuthJwtService } from '../../auth-jwt.service';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { CommonsService } from '../../util/commons.service';
import { ProbeApiService } from './probe-api.service';
import { ProbeService } from './probe.service';

describe('ProbeService', () => {
  let service: ProbeService;

  beforeEach(() => {
    const toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    TestBed.configureTestingModule({
      imports: [],
      providers: [
        ProbeService,
        CommonsService,
        ExceptionHandlingService,
        AuthJwtService,
        DatePipe,
        SessionStorageService,
        ProbeApiService,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        commonsProviders(toastrServiceMock)
      ]
    });
    service = TestBed.inject(ProbeService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
