import { isNullOrUndefined } from 'is-what';

export class UserSearchRequest {
    private login: string;
    private userRole: string[];
    private countryIds?: number[];

    constructor($login: string, $userRole: string[], $countryIds?: number[]) {
        this.login = this.checkUndefine($login) ? $login.trim() : null;
        this.userRole = this.checkUndefinedStringList($userRole) ? $userRole : null;
        this.countryIds = this.checkUndefinedList($countryIds) ? $countryIds : null;
    }

    checkUndefine(fieldValue: string) {
        return !(isNullOrUndefined(fieldValue) || fieldValue == "undefine");
    }

    checkUndefinedList(fieldValues: number[]) {
        return !(isNullOrUndefined(fieldValues) || (typeof fieldValues === "object" && fieldValues.length == 0));
    }

    checkUndefinedStringList(fieldValues: string[]) {
        return !(isNullOrUndefined(fieldValues) || (typeof fieldValues === "object" && fieldValues.length == 0));
    }


}  