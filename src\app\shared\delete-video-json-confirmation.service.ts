import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DeleteVideoJsonConfirmationDialogComponent } from '../video/delete-video-json-confirmation-dialog/delete-video-json-confirmation-dialog.component';

@Injectable({
  providedIn: 'root'
})
export class DeleteVideoJsonConfirmationService {

  constructor(
    private modalService: NgbModal
  ) { }

  public confirm(
    title: string,
    message: string,
    btnOkText: string,
    btnCancelText: string,
    dialogSize: 'sm' | 'lg' = 'sm'): Promise<any> {
    const modalRef = this.modalService.open(DeleteVideoJsonConfirmationDialogComponent, { windowClass: 'uploadVideoModelwidth'});
    modalRef.componentInstance.title = title;
    modalRef.componentInstance.message = message;
    modalRef.componentInstance.btnOkText = btnOkText;
    modalRef.componentInstance.btnCancelText = btnCancelText;

    return modalRef.result;
  }
}
