<!-- loading start -->
<div class="ringLoading_app" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!-- loading end -->
<div *ngIf="isAuthenticated()">
    <!-- Center section: Centered Text -->
    <div class="w-100 text-center header-app" *ngIf="rdmEnvironment !='RDM_ENVIRONMENT_VALUE'">
        <span class="h5">{{rdmEnvironment}}</span>
    </div>
    <!-- form group start -->
    <header class="form-group">
        <nav class="navbar navbar-expand-md navbar-light border-bottom">
            <div style="width:61px">
                <a href="#" class="navbar-brand echonous-logo border-right pr-2 pl-0 pt-0 pb-0"><img
                        src="assets/favicon.png" width="100%" height="auto" alt="echonous"></a>
            </div>
            <!-- RDM header -->
            <label class="pr-2  pt-2 h5-tag">RDM Tool</label>
            <span class="pl-3 version-text"> {{ rdmVersion }}</span>
            <!-- navbar collapse button start -->
            <button type="button" class="navbar-toggler" data-toggle="collapse" data-target="#navbarCollapse">
                <span class="navbar-toggler-icon"></span>
            </button>
            <!-- navbar collapse button end -->
            <div class="collapse navbar-collapse" id="navbarCollapse">
                <!-- navbar div start -->
                <div class="navbar-nav ml-auto">
                    <!-- dropdown start -->
                    <div class="nav-item dropdown">
                        <!-- ccount navigation start -->
                        <a href="#" class="nav-link dropdown-toggle cust-link" data-toggle="dropdown"><em
                                class="fa fa-user pr-2"></em>Account</a>
                        <!-- ccount navigation end -->
                        <div class="dropdown-menu dropdown-menu-right">
                            <!-- sign out setting start -->
                            <a (click)="logout()" id="logout" style="cursor: pointer;"
                                class="dropdown-item cust-link"><em class="fa fa-power-off pr-2"></em>Sign out</a>
                            <!-- sign out setting start -->
                        </div>
                    </div>
                    <!-- dropdown end -->
                </div>
                <!-- navbar div end -->
            </div>
        </nav>
    </header>
    <!-- form group end -->
</div>
<router-outlet></router-outlet>