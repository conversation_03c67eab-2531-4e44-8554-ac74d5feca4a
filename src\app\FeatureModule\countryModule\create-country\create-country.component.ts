import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, Input, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { COUNTRY_ALREADY_EXISTING, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH, SPECIAL_CHARACTER_PATTERN, SPECIAL_CHARACTER_ERROR_MESSAGE, TEXTAREA_MAX_CHARACTERS_ALLOWED_MESSAGE } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { CreateCountryModelRequest } from 'src/app/model/Country/CreateUpdateCountry/CreateAndUpdateCountryModelRequest.model';
import { CreateCountryRequest } from 'src/app/model/Country/CreateUpdateCountry/CreateUpdateCountryRequest.model';
import { LanguageResponse } from 'src/app/model/Languages/LanguageResponse.model';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { CountryAndLanguageService } from 'src/app/shared/Service/CountryAndLanguageService/country-and-language.service';
import { CountryApiCallService } from 'src/app/shared/Service/CountryService/country-api-call.service';
import { CountryService } from 'src/app/shared/Service/CountryService/country.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';

/**
* Component for creating a country.
*
* This component handles the form submission, validation, and API calls required to create country data.
* It also initializes dropdown settings and fetches language data to populate the form fields.
*
* <AUTHOR>
*/
@Component({
  selector: 'app-create-country',
  templateUrl: './create-country.component.html',
  styleUrl: './create-country.component.css',
  encapsulation: ViewEncapsulation.None
})
export class CreateCountryComponent {

  @Input('createCountryModelRequest') createCountryModelRequest: CreateCountryModelRequest;
  loading = false

  languageSetting: MultiSelectDropdownSettings = null;
  languageList: LanguageResponse[] = [];

  isFormReload: boolean = true;

  //MaxLength Message
  textBoxMaxLengthMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  textAreaMaxLengthMessage: string = TEXTAREA_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;

  // Form Group
  createCountryForm = new FormGroup({
    countryName: new FormControl('', [Validators.required, Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), this.commonsService.removeSpacesThrowError(), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    language: new FormControl([], []),
  });

  constructor(
    private activeModal: NgbActiveModal,
    private countryApiCallService: CountryApiCallService,
    private countryAndLanguageService: CountryAndLanguageService,
    private toste: ToastrService,
    private commonsService: CommonsService,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private exceptionService: ExceptionHandlingService,
    private countryService: CountryService) { }

  /**
  * Initializes the component by setting up dropdown settings and fetching the language list.
  * 
  * <AUTHOR>
  */
  public ngOnInit(): void {
    this.languageSetting = this.multiSelectDropDownSettingService.getLanguageDrpSetting();
    this.getInitData();
  }

  /**
  * Fetches initial data, such as the list of available languages.
  * 
  * <AUTHOR>
  * @returns {Promise<void>} A promise indicating when the data fetching is complete.
  */
  private async getInitData(): Promise<void> {
    this.loading = true;
    this.languageList = await this.countryAndLanguageService.getLanguageList();
    this.loading = false;
  }

  /**
  * Closes the popup and discards changes.
  * 
  * <AUTHOR>
  */
  public decline(): void {
    this.activeModal.close(false);
  }

  /**
  * Prepares the form data for API submission by constructing a `CreateCountryRequest` object.
  * 
  * <AUTHOR>
  * @returns {CreateCountryRequest} The formatted request object.
  */
  private getCountryResponseObject(): CreateCountryRequest {
    let formValue = this.createCountryForm.value;
    let permissionId = this.commonsService.getIdsFromArray(formValue.language);
    return new CreateCountryRequest(this.commonsService.checkNullFieldValue(formValue.countryName), permissionId);
  }

  /**
  * Closes the popup and confirms changes.
  * 
  * <AUTHOR>
  */
  private accept(): void {
    this.activeModal.close(true);
  }

  /**
  * Initiates the process to add a new country by calling the create API.
  * 
  * <AUTHOR>
  */
  public addCountry() {
    this.createCountry(this.getCountryResponseObject());
  }

  /**
  * Sends a request to create or update a country and handles the response.
  * 
  * <AUTHOR>
  * @param countryResponse The request object containing country data.
  * @returns {void}
  */
  private createCountry(countryResponse: CreateCountryRequest): void {
    this.loading = true;
    this.countryApiCallService.createCountry(countryResponse).subscribe({
      next: (res: HttpResponse<SuccessMessageResponse>) => {
        this.toste.success(res.body.message);
        let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, true, false);
        this.countryService.callRefreshPageSubject(listingPageReloadSubjectParameter, this.createCountryModelRequest.resourseName, this.createCountryModelRequest.isFilterHidden, null);
        this.accept();
      }, error: (error: HttpErrorResponse) => {
        if (error.status === 409) {
          this.toste.error(COUNTRY_ALREADY_EXISTING);
        } else {
          this.exceptionService.customErrorMessage(error);
        }
        this.loading = false;
      }
    });
  }

}
