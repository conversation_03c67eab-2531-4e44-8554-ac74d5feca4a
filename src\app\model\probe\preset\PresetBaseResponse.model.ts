
import { BaseResponse } from "../../common/BaseResponse.model";
import { PresetPartNumberBaseResponse } from "./presetPartNumberBaseResponse.model";

export class PresetsBaseResponse extends BaseResponse {
    featurePartNumbers: Array<PresetPartNumberBaseResponse>;

    constructor($id: number, $name: string, $displayName: string, $featurePartNumbers: Array<PresetPartNumberBaseResponse>) {
        super($id, $name, $displayName);
        this.featurePartNumbers = $featurePartNumbers;
    }

}