import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { ListAuditResource } from 'src/app/app.constants';
import { AuditFilterAction } from 'src/app/model/Audit/auditFilterAction';
import { AuditSearchRequsetBody } from 'src/app/model/Audit/auditSearchRequsetBody';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AuditDetailModelComponent } from 'src/app/FeatureModule/AuditModule/audit-detail-model/audit-detail-model.component';
import { AuditListResponse } from 'src/app/model/Audit/AuditListResponse';
import { EnumMapping } from 'src/app/model/common/EnumMapping.model';
import { BooleanKeyValueMapping } from 'src/app/model/common/BooleanKeyValueMapping.model';
import { KeyValueMappingServiceService } from '../../util/key-value-mapping-service.service';
import { AuditModuleEnum } from '../../enum/Audit/AuditModuleEnum.enum';
import { PermissionService } from '../../permission.service';
import { PermissionAction } from '../../enum/Permission/permissionAction.enum';
import { AuditModuleWithActionResponse } from 'src/app/model/Audit/AuditModuleWithActionResponse.model';
import { isNullOrUndefined } from 'is-what';
import { AuditActionEnum } from '../../enum/Audit/AuditActionEnum.enum';
import { DeviceConnectionState } from '../../enum/Device/DeviceConnectionState.model';
import { ProductConfigStatus } from '../../enum/SalesOrder/ProductConfigStatus.enum';
import { PartNumberType } from '../../enum/SalesOrder/PartNumberType.enum';
import { ProductStatusEnum } from '../../enum/Common/ProductStatus.enum';


@Injectable({
  providedIn: 'root'
})
export class AuditService {

  auditModuleList: Array<AuditModuleWithActionResponse> = [];



  constructor(
    private modalService: NgbModal,
    private keyValueMappingServiceService: KeyValueMappingServiceService,
    private permissionService: PermissionService
  ) {
  }

  auditUrlDataMap = new Map([
    ["DEVICES", "device"],
    ["PROBES", "probe"]
  ]);

  auditReversePemission = new Map([
    ["DEVICES", PermissionAction.AUDIT_DEVICE_REVERSE_ACTION],
    ["PROBES", PermissionAction.AUDIT_PROBE_REVERSE_ACTION]
  ]);

  //Audit list filter
  private auditListFilterRequestParameterSubject = new Subject<AuditFilterAction>();

  //Refresh Audit List 
  private auditListRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  public getAuditModuleListInStore() {
    return this.auditModuleList;
  }

  public setAuditModuleListInStore(auditModuleList: Array<AuditModuleWithActionResponse>) {
    this.auditModuleList = auditModuleList;
  }


  /**
   * audit List Page Refresh After some Action Like Serch parameter add
   * Note : Clear All filter and refresh page 
   * <AUTHOR>
   * @returns audit List 
   */
  public getAuditListFilterRequestParameterSubject(): Subject<AuditFilterAction> {
    return this.auditListFilterRequestParameterSubject;
  }

  public callAuditListFilterRequestParameterSubject(auditFilterAction: AuditFilterAction): void {
    this.auditListFilterRequestParameterSubject.next(auditFilterAction);
  }

  /**
   * Sales Order List Page Refresh After some Action
   * <AUTHOR>
   * @returns ListingPageReloadSubjectParameter
   */
  public getAuditListRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.auditListRefreshSubject;
  }

  /**
   * This function call the subject for reload the page data
   *  Note : (ListAuditResource) -> Filter page subject call -> Listing page subject call
   * clear all filter after page data Reload
   * <AUTHOR>
   * @param isReloadData -> false means move to prev page 
   * @param resourceName 
   */
  public callRefreshPageSubject(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, resourceName: string, isFilterHidden: boolean, auditSearchRequsetBodyApply: AuditSearchRequsetBody, archivedAuditSearch: boolean): void {
    if (resourceName == ListAuditResource) {
      if (isFilterHidden) {
        let auditSearchRequsetBody = new AuditSearchRequsetBody(null, [], null, null, null, null);
        if (!isNullOrUndefined(auditSearchRequsetBodyApply) && !listingPageReloadSubjectParameter.isClearFilter) {
          auditSearchRequsetBody = auditSearchRequsetBodyApply;
        }
        let auditFilterAction = new AuditFilterAction(listingPageReloadSubjectParameter, auditSearchRequsetBody, archivedAuditSearch);
        this.callAuditListFilterRequestParameterSubject(auditFilterAction);
      } else {
        this.auditListRefreshSubject.next(listingPageReloadSubjectParameter);
      }
    }
  }



  /**
   * Open Probe Type Update Model
   * 
   * @param title 
   * @param btnOkText 
   * @param btnCancelText 
   * @param probeType 
   * @param probeIdList 
   * @param dialogSize 
   * @returns 
   */
  public openAuditDetailPopup(
    auditResponse: AuditListResponse,
    dialogSize = 'lg'): Promise<boolean> {
    const modalRef = this.modalService.open(AuditDetailModelComponent, { size: dialogSize });
    modalRef.componentInstance.auditResponse = auditResponse;
    return modalRef.result;
  }

  /**
   * Boolean Key Value Mapping
   * 
   * <AUTHOR>
   * @returns 
   */
  public getBooleanKeyValueMap(): Map<string, Array<BooleanKeyValueMapping>> {
    let booleanKeyValueMap = new Map<string, Array<BooleanKeyValueMapping>>();
    let firmwareVideoOptionsMap = this.keyValueMappingServiceService.firmwareVideoOptionList();
    booleanKeyValueMap.set('locked', this.keyValueMappingServiceService.lockedUnlockOptionList());
    booleanKeyValueMap.set('isEditable', this.keyValueMappingServiceService.editEnableDisableOptionList());
    booleanKeyValueMap.set('isActive', this.keyValueMappingServiceService.activeInActiveOptionList());
    booleanKeyValueMap.set('isVideoUpdateAvailable', firmwareVideoOptionsMap);
    booleanKeyValueMap.set('isFirmwareUpdateAvailable', firmwareVideoOptionsMap);
    return booleanKeyValueMap;
  }

  /**
   * Enum Key Value Mapping
   * 
   * <AUTHOR>
   * @returns 
   */
  public getEnumKeyValueMap(): Map<string, Array<EnumMapping>> {
    let deviceTypeEnum: Array<EnumMapping> = this.keyValueMappingServiceService.deviceTypeEnumOptionList();
    let productConfigStatusEnum: Array<EnumMapping> = this.keyValueMappingServiceService.enumOptionToList(ProductConfigStatus);
    let enumKeyValueMap = new Map<string, Array<EnumMapping>>();
    enumKeyValueMap.set('deviceType', deviceTypeEnum);
    enumKeyValueMap.set('deviceTypes', deviceTypeEnum);
    enumKeyValueMap.set('productStatus', this.keyValueMappingServiceService.enumOptionToList(ProductStatusEnum));
    enumKeyValueMap.set('partNumberTypeEnum', this.keyValueMappingServiceService.enumOptionToList(PartNumberType));
    enumKeyValueMap.set('soConfigStatus', productConfigStatusEnum);
    enumKeyValueMap.set('productConfigStatus', productConfigStatusEnum);
    enumKeyValueMap.set('connectionState', this.keyValueMappingServiceService.enumOptionToList(DeviceConnectionState));
    return enumKeyValueMap;
  }

  /**
   * Get Module and Unique Key Display Name Mapping
   * <AUTHOR>
   * @returns 
   */
  public getModuleUniqueKeyDisplayMapping(): Map<string, string> {
    let moduleUniqueKeyMapping = new Map<string, string>();
    moduleUniqueKeyMapping.set(AuditModuleEnum.DEVICE, "HW ID");
    moduleUniqueKeyMapping.set(AuditModuleEnum.PROBE, "Probe Serial Number");
    moduleUniqueKeyMapping.set(AuditModuleEnum.SOFTWARE_BUILD, "Version");
    moduleUniqueKeyMapping.set(AuditModuleEnum.USER, "Login Name");
    moduleUniqueKeyMapping.set(AuditModuleEnum.VIDEO, "Video Id / JSON Version");
    moduleUniqueKeyMapping.set(AuditModuleEnum.ROLE, "Name");
    moduleUniqueKeyMapping.set(AuditModuleEnum.SALES_ORDER, "Order Number");
    moduleUniqueKeyMapping.set(AuditModuleEnum.KIT_MANAGEMENT, null);
    moduleUniqueKeyMapping.set(AuditModuleEnum.PROBE_CONFIG_GROUP, "Part Number");
    moduleUniqueKeyMapping.set(AuditModuleEnum.COUNTRY, "Country Name");
    return moduleUniqueKeyMapping;
  }

  public getChildModuleUniqueKeyDisplay(module: string, actionName: any): string {
    let otsKitManagementAction = [AuditActionEnum.OTS_KIT_MANGEMENT_ADD_IMPORT_CSV, AuditActionEnum.OTS_KIT_MANGEMENT_DELETE_IMPORT_CSV];
    let bridgeKitManagementAction = [AuditActionEnum.BRIDGE_KIT_MANGEMENT_ADD_IMPORT_CSV, AuditActionEnum.BRIDGE_MANGEMENT_DELETE_IMPORT_CSV];
    if (module == AuditModuleEnum.KIT_MANAGEMENT) {
      if (otsKitManagementAction.includes(actionName)) {
        return " OTS Kit Part Number";
      } else if (bridgeKitManagementAction.includes(actionName)) {
        return "Kit Part Number / Country";
      }
    }
    return null;
  }

  /**
   * Module Permission Mapping
   * Note :Based On Permission Ditail page display or not
   * 
   * <AUTHOR>
   * @returns 
   */
  public getModulePermissionMapping(): Map<string, boolean> {
    let modulePermissionMapping = new Map<string, boolean>();
    modulePermissionMapping.set(AuditModuleEnum.DEVICE, this.permissionService.getDevicePermission(PermissionAction.GET_DEVICE_ACTION));
    modulePermissionMapping.set(AuditModuleEnum.PROBE, this.permissionService.getProbPermission(PermissionAction.GET_PROB_ACTION));
    modulePermissionMapping.set(AuditModuleEnum.USER, this.permissionService.getUserPermission(PermissionAction.GET_USER_ACTION));
    modulePermissionMapping.set(AuditModuleEnum.ROLE, this.permissionService.getRolePermission(PermissionAction.GET_ROLE_ACTION));
    modulePermissionMapping.set(AuditModuleEnum.SALES_ORDER, this.permissionService.getSalesOrderPermission(PermissionAction.GET_SALES_ORDER_ACTION));
    modulePermissionMapping.set(AuditModuleEnum.KIT_MANAGEMENT, this.permissionService.getKitManagementPermission(PermissionAction.KIT_MANAGEMANT_TAB_ACTION));
    modulePermissionMapping.set(AuditModuleEnum.PROBE_CONFIG_GROUP, this.permissionService.getProbeConfigGroupPermission(PermissionAction.GET_CONFIG_GROUP_ACTION));
    return modulePermissionMapping
  }

  /**
  * Get audit URL for the given module
  * 
  * <AUTHOR>
  * @returns 
  */
  public setUrlAccordingModule(module: string): string {
    return this.auditUrlDataMap.get(module) || "";
  }

  /**
  * Check if user has permission to revert for the given module
  * 
  * <AUTHOR>
  * @returns 
  */
  public checkPermissionForRevert(module: string): boolean {
    return this.permissionService.getAuditPermission(this.auditReversePemission.get(module));
  }

}
