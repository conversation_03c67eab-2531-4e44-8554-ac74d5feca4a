
export class UserSearchResponse {
	id: number;
	login: string;
	createdDate: number;
	lastLoggedIn: number;
	userRoles: Array<string>;
	countries: Array<string>;
	modules: Array<string>;

	constructor($id: number, $login: string, $createdDate: number, $lastLoggedIn: number, $userRoles: Array<string>, $countries: Array<string>, $modules: Array<string>) {
		this.id = $id;
		this.login = $login;
		this.createdDate = $createdDate;
		this.lastLoggedIn = $lastLoggedIn;
		this.userRoles = $userRoles;
		this.countries = $countries;
		this.modules = $modules;
	}

}