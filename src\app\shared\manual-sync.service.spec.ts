import { TestBed } from '@angular/core/testing';
import { ManualSyncService } from './manual-sync.service';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { ManualSyncComponent } from '../FeatureModule/SalesOrderModule/manual-sync/manual-sync.component';
import { SalesOrderSchedulerManualSyncTimeResponse } from '../model/SalesOrder/SalesOrderSchedulerManuleSyncTimeResponse.model';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';

describe('ManualSyncService', () => {
  let service: ManualSyncService;
  let modalServiceMock: jasmine.SpyObj<NgbModal>;
  let modalRefMock: jasmine.SpyObj<NgbModalRef>;


  beforeEach(() => {
    modalRefMock = jasmine.createSpyObj<NgbModalRef>(
      'NgbModalRef',
      [], // No methods to spy on
      ['componentInstance', 'result'] // Properties to mock
    );
    Object.defineProperty(modalRefMock, 'componentInstance', {
      value: {} as any,
      writable: true
    });
    modalRefMock.result = Promise.resolve(true);

    modalServiceMock = jasmine.createSpyObj('NgbModal', ['open']);
    modalServiceMock.open.and.returnValue(modalRefMock);

    TestBed.configureTestingModule({
      providers: [
        ManualSyncService,
        { provide: NgbModal, useValue: modalServiceMock },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting()
      ]
    });

    service = TestBed.inject(ManualSyncService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should open modal with correct config and pass data to component', async () => {
    const mockTitle = 'Sync Now';
    const mockBtnText = 'Start Sync';
    const mockResponse: SalesOrderSchedulerManualSyncTimeResponse = {
      id: 1234567
    };

    await service.confirm(mockTitle, mockResponse, mockBtnText);

    expect(modalServiceMock.open).toHaveBeenCalledWith(ManualSyncComponent, {
      windowClass: 'modal fade',
      backdrop: 'static',
      keyboard: false
    });

    const modalInstance = modalRefMock.componentInstance;
    expect(modalInstance.title).toBe(mockTitle);
    expect(modalInstance.btnOkText).toBe(mockBtnText);
    expect(modalInstance.salesOrderSchedulerManualSyncTimeResponse).toEqual(mockResponse);

  });

  it('should use default button text when not provided', async () => {
    const mockTitle = 'Sync Now';
    const mockResponse: SalesOrderSchedulerManualSyncTimeResponse = {
      id: 1234567
    };

    await service.confirm(mockTitle, mockResponse); // no btnOkText provided

    const modalInstance = modalRefMock.componentInstance;
    expect(modalInstance.btnOkText).toBe('Upload');
  });

});
