import { BaseResponse } from "../common/BaseResponse.model";
import { FeaturePartNumberResponse } from "./FeaturePartNumberResponse.model";

export class ProbeFeatureResponse extends BaseResponse {
    featureId: number;
    partNumbers: Array<FeaturePartNumberResponse>;

    constructor(id: number, name: string, displayName: string, featureId: number, partNumbers: Array<FeaturePartNumberResponse>) {
        super(id, name, displayName);
        this.featureId = featureId;
        this.partNumbers = partNumbers;
    }
}