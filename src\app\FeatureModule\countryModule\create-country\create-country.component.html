<!-- loading start -->
<!-- loading gif start -->
<div class="ringLoading" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!-- loading gif end -->
<!-- loading end -->

<div>
    <!------------------------------------->
    <!--------------Title Start------------>
    <!------------------------------------->
    <div class="modal-header">
        <label class="modal-title">{{ createCountryModelRequest?.title }}</label>
    </div>
    <!------------------------------------->
    <!--------------Title End------------>
    <!------------------------------------->

    <!------------------------------------->
    <!-- dialog body - start -->
    <!------------------------------------->
    <div class="modal-body" id="CountryCreate">
        <div>
            <form name='createCountry' [formGroup]="createCountryForm" *ngIf="isFormReload">
                <table class="w-table" aria-hidden="true" style="width: 100%;">

                    <tr>
                        <th class="upload-title w-title pb-2" id="Country_Name">
                            <span class="pr-2">Country Name</span>
                        </th>
                        <td class="w-control pb-2">
                            <input tabindex="-1" class="form-control" type="text" formControlName="countryName"
                                name='countryName' autocomplete="off" />
                        </td>
                    </tr>
                    <!------------------------------------------------>
                    <!-----------Error Message For Country Name---------->
                    <!--1 required Country Name -->
                    <!--2 Country Name maxlength validation error-->
                    <!------------------------------------------------>
                    <tr>
                        <td></td>
                        <td>
                            <div *ngIf="(createCountryForm.get('countryName').touched || createCountryForm.get('countryName').dirty) && 
                createCountryForm.get('countryName').invalid">
                                <div *ngIf="createCountryForm.get('countryName').errors['required']" class="pb-2">
                                    <span class="alert-color font-12"> Country Name is required</span>
                                </div>
                                <div *ngIf="createCountryForm.get('countryName').errors['maxlength']" class="pb-2">
                                    <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
                                </div>
                                <div *ngIf="createCountryForm.get('countryName').errors['pattern']" class="pb-2">
                                    <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <!----------------------------------->
                    <!-----------Language---------------->
                    <!----------------------------------->
                    <tr>
                        <th class="upload-title w-title pb-2" id="language">
                            <span class="pr-2">Language</span>
                        </th>
                        <td class="w-control pb-2">
                            <ng-multiselect-dropdown name="pacsType" [placeholder]="''" [settings]="languageSetting"
                                [data]="languageList" formControlName="language">
                            </ng-multiselect-dropdown>
                        </td>
                    </tr>
                </table>
            </form>
        </div>
    </div>
    <!------------------------------------->
    <!-- dialog buttons - start -->
    <!------------------------------------->
    <div class="modal-footer">
        <button type="button" tabindex="-1" class="btn btn-sm btn-outline-secondary" id="countryCancelButton"
            (click)="decline()">{{
            createCountryModelRequest?.cancelButton }}</button>
        <button type="button" class="btn btn-sm btn-orange" id="uploadBtn" (click)="addCountry()"
            [disabled]="createCountryForm.invalid">{{
            createCountryModelRequest?.okButton }}</button>
    </div>

</div>
<!------------------------------------->
<!------------------------------------->