<!-- loading start -->
<div class="ringLoading" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!-- loading end -->

<body>
    <div class="row" *ngIf="displayJob">

        <!--Filter start-->
        <div class="col-md-3 pr-0" *ngIf="(!isFilterHidden)" id="jobFilterBtn">
            <!-- filter header start -->
            <label class="col-md-12 h5-tag">Filter</label>
            <!-- filter header end -->
            <div class="card mt-3">
                <div class="card-body">
                    <!-- filter form start -->
                    <form id="filter-form" [formGroup]="filterForm" role="form" class="form">
                        <!-- hardware ID text field -->
                        <div class="form-group">
                            <label class="form-control-label" for="field_jobDeviceId"><strong>HW ID</strong></label>
                            <input class="form-control" type="text" formControlName="jobDeviceId" />
                            <div
                                *ngIf="(filterForm.get('jobDeviceId').touched || filterForm.get('jobDeviceId').dirty) && filterForm.get('jobDeviceId').invalid ">
                                <div *ngIf="filterForm.get('jobDeviceId').errors['maxlength']">
                                    <p class="alert-color">{{textBoxMaxCharactersAllowedMessage}}</p>
                                </div>
                                <div *ngIf="filterForm.get('jobDeviceId').errors['pattern']">
                                    <p class="alert-color">{{specialCharacterErrorMessage}}</p>
                                </div>
                            </div>
                        </div>
                        <!-- system software version start -->
                        <div class="form-group">
                            <label class="form-control-label" for="field_packageVersion"
                                id="label_packageVersion"><strong>System SW Version</strong></label>
                            <!-- system sw version multiselect dropdown start -->
                            <ng-multiselect-dropdown id="field_packageVersion" name="packageVersions" [placeholder]="''"
                                formControlName="packageVersions" [disabled]="disabled"
                                [settings]="systemSoftwearVersionDropdownSetting" [data]="pkg_v">
                            </ng-multiselect-dropdown>
                            <!-- system sw version multiselect dropdown end -->
                        </div>

                        <!-- device status filter start -->
                        <div class="form-group">
                            <label class="form-control-label" for="field_connectionState"
                                id="label_connectionState"><strong>Device
                                    Status</strong></label>
                            <!-- connection state multiselect dropdown start -->
                            <ng-multiselect-dropdown id="field_connectionState" name="connectionState"
                                class="devicePageDeviceType" [placeholder]="''" formControlName="connectionState"
                                [disabled]="disabled" [settings]="deviceConnectionStateDropdownSetting"
                                [data]="deviceConnectionState">
                            </ng-multiselect-dropdown>
                            <!-- connection state multiselect dropdown end -->
                        </div>
                        <!-- job type filter start -->
                        <div class="form-group">
                            <label class="form-control-label" for="field_drpJobTypes" id="label_drpJobTypes"><strong>Job
                                    Type</strong></label>
                            <!-- device type multiselect dropdown start -->
                            <ng-multiselect-dropdown class="devicePageDeviceType" id="field_drpJobTypes"
                                name="drpJobTypes" [placeholder]="''" formControlName="drpJobTypes"
                                [disabled]="disabled" [settings]="JobTypeDropdownSetting" [data]="jobTypes">
                            </ng-multiselect-dropdown>
                            <!-- device type multiselect dropdown end -->
                        </div>
                        <!-- job status filter start -->
                        <div class="form-group">
                            <label class="form-control-label" for="field_JobStatus" id="label_JobStatus"><strong>Job
                                    Status</strong></label>
                            <!-- job status multiselect dropdown start -->
                            <ng-multiselect-dropdown id="field_JobStatus" name="JobStatus" [placeholder]="''"
                                formControlName="jobStatus" [disabled]="disabled" [settings]="jobStatusDropdownSetting"
                                [data]="jobStatusList">
                            </ng-multiselect-dropdown>
                            <!-- job status multiselect dropdown end -->
                        </div>

                        <hr class="mt-1 mb-2">
                        <div class="">
                            <!-- search filter button -->
                            <button class="btn btn-sm btn-orange mr-3" (click)="searchJobFiltet()"
                                [disabled]="filterForm.invalid" id="searchJob">Search</button>
                            <!-- clear filter button -->
                            <button class="btn btn-sm btn-orange" (click)="clearFilter()">Clear</button>
                        </div>
                    </form>
                    <!-- filter form end -->
                </div>
            </div>
        </div>
        <!--Filter End-->
        <!--table Block Start-->
        <div [className]="(isFilterHidden)?'col-md-12 pr-0':'col-md-9 pr-0'">
            <!-- container fluid start -->
            <div class="container-fluid">
                <!--view job Start-->
                <div class="row" class="headerAlignment">
                    <!--------------------------------------->
                    <!--Left Side-->
                    <!--------------------------------------->
                    <div class="childFlex">
                        <!-- hide show filter button div start -->
                        <div class="dropdown">
                            <!-- hide Show FilterButton start -->
                            <button class="btn btn-sm btn-orange mr-3 ml-0 filter-symbol" (click)="toggleFilter()"
                                [id]="(isFilterHidden)?'Show Filter':'Hide Filter'">
                                <i class="fas fa-filter" aria-hidden="true"
                                    [id]="(isFilterHidden)?'Show Filter':'Hide Filter'"></i>
                                &nbsp;&nbsp;{{ hideShowFilterButtonText }}
                            </button>
                            <!-- hide Show FilterButton end -->
                        </div>
                        <!-- hide show filter button div end -->
                        <!-- change show entry for page start -->
                        <div class="text-left mb-3">
                            <label class="mb-0">Show entry</label>
                            <!-- dropdown for select page entry start -->
                            <select id="" [(ngModel)]="drpselectsize" class="form-control form-control-sm"
                                (change)="changeDataSize($event)" id="jobListShowEntry">
                                <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                                    <option [value]="dataSize">{{ dataSize }}</option>
                                </ng-template>
                            </select>
                            <!-- dropdown for select page entry end -->
                        </div>
                        <!-- change show entry for page end -->
                    </div>
                    <div class="childFlex">
                        <div class="mb-3">
                            <!-- refresh page start -->
                            <div class="dropdown">
                                <!-- refresh button start -->
                                <button class="btn btn-sm btn-orange" (click)="clickOnRefreshButton()"><em
                                        class="fa fa-refresh"></em></button>
                                <!-- refresh button end -->
                            </div>
                            <!-- refresh page end -->
                        </div>
                    </div>
                </div>




                <!--view jobs Start-->
                <div>Total {{totalJob}} Jobs</div>
                <!-- jobs list table start -->
                <table class="table table-sm table-bordered" aria-hidden="true">
                    <!-- jobs table head start -->
                    <thead>
                        <tr class="thead-light">
                            <th><span>Id</span></th>
                            <th><span>HW ID</span></th>
                            <th><span>Job Type</span></th>
                            <th hidden><span>JobSchedule Id</span></th>
                            <th><span>Start Date & Time</span></th>
                            <th><span>End Date & Time</span></th>
                            <th><span>Job Status</span></th>
                        </tr>

                    </thead>
                    <!-- jobs table head end -->
                    <!-- jobs table body start -->
                    <tbody>
                        <tr *ngFor="let job of jobs ;trackBy: trackId;">
                            <td>{{job.id}}</td>
                            <td *ngIf="deviceReaderPermission" class="spanunderline" data-toggle="tooltip"
                                id="jobDeviceId" data-placement="top" title="{{job.deviceMasterId}}"
                                (click)="deviceDetailModel(job.deviceMasterIdPk)">
                                {{job.deviceMasterId}}</td>
                            <td *ngIf="!deviceReaderPermission">{{job.deviceMasterId}}</td>
                            <td>{{job.type}}</td>
                            <td style="text-align: center" hidden>{{job.jobScheduleStatusId}}</td>
                            <td>{{job.startDate}}</td>
                            <td>{{job.endDate}}</td>
                            <td class="spanunderline" id="jobStatusId" *ngIf="jobReaderPermission"
                                (click)="openJobDetail(job)">
                                {{job.status}}</td>
                            <td class="spanunderline" *ngIf="!jobReaderPermission">{{job.status}}</td>
                        </tr>

                    </tbody>
                    <!-- jobs table body end -->

                </table>
                <!-- jobs list table end -->

                <!--paging Start-->
                <div>
                    <div>Showing {{totalJobDisplay}} out of {{totalJob}} Jobs</div>
                    <div class="float-right">
                        <!-- pagination start -->
                        <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="itemsPerPage"
                            id="jobPagination" [maxSize]="5" [rotate]="true" [boundaryLinks]="true"
                            (pageChange)="loadPage(page)">
                        </ngb-pagination>
                        <!-- pagination end -->
                    </div>
                </div>
                <!--paging End-->
            </div>
            <!-- container fluid end -->
            <!--flud tab 9 row-->
        </div>
        <!--table Block End-->
    </div>
    <!--job detail-->
</body>
<div *ngIf="displayDeviceDetail">
    <app-device-detail [deviceIdInput]="deviceIdInput" [resource]="deviceActivityListResource"
        (showDevice)="showJob()"></app-device-detail>
</div>

<div *ngIf="displayJobDetail">
    <app-job-detail [jobScheduleStatusId]="jobScheduleStatusId" (showJob)="showJob()"></app-job-detail>
</div>