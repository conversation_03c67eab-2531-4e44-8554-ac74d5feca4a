import { ContentInput } from "./ImportFileContentInput.model";

export class ImportFilePopupInput {
    title: string;
    subTitle: string;
    isDownloadTemplate: boolean;
    downloadTemplateFileName: string;
    content: ContentInput;
    btnCancelText: string;
    resourse: string;


    constructor($title: string, $subTitle: string, $isDownloadTemplate: boolean, $downloadTemplateFileName: string, $content: ContentInput, $btnCancelText: string, $resourse: string) {
        this.title = $title;
        this.subTitle = $subTitle;
        this.content = $content;
        this.isDownloadTemplate = $isDownloadTemplate;
        this.downloadTemplateFileName = $downloadTemplateFileName;
        this.btnCancelText = $btnCancelText;
        this.resourse = $resourse;
    }

}