<!-- loading start -->
<div class="ringLoading" *ngIf="loading">
    <!-- loading gif start -->
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
    <!-- loading gif end -->
</div>
<!-- loading end -->

<body class="bg-white">
    <!-- container start -->
    <div class="container-fluid">
        <!-- row start -->
        <div class="row">
            <!-- column start -->
            <div class="col-md-12">
                <!-- job detail row start -->
                <div class="row">
                    <label class="col-md-6 h5-tag">Job Detail</label>
                    <div class="ml-auto col-md-6 text-right mb-3">
                        <!-- back button div start -->
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary mr-3" id="backtoJobList" (click)="back()"><i
                                    class="fa fa-reply" aria-hidden="true"></i>&nbsp;&nbsp;Back</button>
                            <!-- back button end -->

                            <!-- refresh button start -->
                            <button class="btn btn-sm btn-orange ml-2" (click)="refreshJobDetailPage()"
                                id="jobDeatilsRefresh"><em class="fa fa-refresh"></em></button>
                            <!-- refresh button start -->

                        </div>
                        <!-- back button div end -->
                    </div>
                </div>
                <!-- job detail row end -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <!-- job detail card shadow start -->
                                <div class="card shadow">
                                    <!-- job detail fields card body start -->
                                    <div class="card-body">

                                        <!-- jobs detail fields start -->
                                        <div class="row">
                                            <!-- job id field start -->
                                            <div class="col-md-3">
                                                <!-- job id form group start -->
                                                <div class="form-group">
                                                    <label><strong class="">Job Id </strong></label>
                                                    <input type="text" class="form-control" name="connectionState"
                                                        [value]="jobHistoryResponse?.jobId" readonly>

                                                </div>
                                                <!-- job id form group end -->
                                            </div>
                                            <!-- job id field end -->

                                            <!-- job type form group start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <!-- job type input start -->
                                                    <label><strong class="">Job Type</strong></label>
                                                    <input type="text" class="form-control" name="jobType"
                                                        [value]="jobHistoryResponse?.jobType" readonly>
                                                    <!-- job type input end -->

                                                </div>
                                            </div>
                                            <!-- job type form group end -->

                                            <!-- hardware id field start -->
                                            <div class="col-md-3">
                                                <!-- hardware id form group start -->
                                                <div class="form-group">
                                                    <label><strong class="">HW ID</strong></label>
                                                    <!-- hardware id input start -->
                                                    <input type="text" class="form-control" name="deviceId"
                                                        [value]="jobHistoryResponse?.deviceId" readonly>
                                                    <!-- hardware id input end -->


                                                </div>
                                                <!-- hardware id form group end -->
                                            </div>
                                            <!-- hardware id field end -->



                                            <div class="col-md-3" *ngIf="jobHistoryResponse?.jobType=='FIRMWARE'">
                                                <div class="form-group">
                                                    <label><strong class="">Firmware version </strong></label>
                                                    <input type="text" class="form-control" name="modifiedDate"
                                                        [value]="jobHistoryResponse?.firmwareResponse?.version"
                                                        readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-3" *ngIf="jobHistoryResponse?.jobType=='VIDEO'">
                                                <div class="form-group">
                                                    <label><strong class="">Json version </strong></label>
                                                    <input type="text" class="form-control" name="jsonVersion"
                                                        [value]="jobHistoryResponse?.jsonVersion" readonly>
                                                </div>
                                            </div>


                                        </div>
                                        <!-- jobs detail fields end -->

                                        <!--form end-->
                                    </div>
                                    <!-- job detail fields card body end -->
                                </div>
                                <!-- job detail card shadow end -->



                            </div>
                        </div>
                    </div>


                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="card shadow">
                                    <div class="card-body">
                                        <div class="container">
                                            <table class="table table-sm table-bordered" aria-hidden="true">
                                                <thead>
                                                    <tr class="thead-light">
                                                        <th>Time Stamp</th>
                                                        <th>Status</th>
                                                    </tr>

                                                </thead>
                                                <tbody>
                                                    <tr *ngFor='let data of jobHistoryResponse?.statusHistory'>
                                                        <td>{{data.timestamp | date:"MMM d, y, h:mm:ss a"}}</td>
                                                        <td>{{data.status}}</td>
                                                    </tr>

                                                </tbody>

                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--Row End-->
            </div>
            <!-- column end -->
        </div>
        <!-- row end -->
    </div>
    <!-- container end -->
</body>