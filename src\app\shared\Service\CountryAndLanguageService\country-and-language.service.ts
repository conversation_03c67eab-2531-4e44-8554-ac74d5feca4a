import { Injectable } from '@angular/core';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { HttpClient, HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { PermissionService } from '../../permission.service';
import { PermissionAction } from '../../enum/Permission/permissionAction.enum';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { LanguageResponse } from 'src/app/model/Languages/LanguageResponse.model';
import { firstValueFrom } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CountryAndLanguageService {


  private serverApiUrl = this.configInjectService.getServerApiUrl();
  public countryUrl = this.serverApiUrl + 'api/country';
  public languageUrl = this.serverApiUrl + 'api/language';

  constructor(private configInjectService: ConfigInjectService,
    private http: HttpClient,
    private permissionService: PermissionService,
    private exceptionService: ExceptionHandlingService) { }


  /**
   * Get Language List
   * 
   * <AUTHOR>
   * @returns 
   */
  public async getLanguageList(): Promise<Array<LanguageResponse>> {
    if (!this.permissionService.getLanguagePermission(PermissionAction.GET_LANGUAGE_ACTION)) {
      return [];
    }
    try {
      const res: HttpResponse<LanguageResponse[]> = await firstValueFrom(this.http.get<LanguageResponse[]>(this.languageUrl, { observe: 'response' }));
      return res.body || [];
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        this.exceptionService.customErrorMessage(error);
      }
      return [];
    }
  }



}
