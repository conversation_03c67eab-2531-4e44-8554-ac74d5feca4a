import { HttpClient, HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, catchError, firstValueFrom } from 'rxjs';
import { AuditDetailResponse } from 'src/app/model/Audit/auditDetailResponse.model';
import { AuditListPageResponse } from 'src/app/model/Audit/auditListPageResponse';
import { AuditModuleWithActionResponse } from 'src/app/model/Audit/AuditModuleWithActionResponse.model';
import { AuditSearchRequsetBody } from 'src/app/model/Audit/auditSearchRequsetBody';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { CommonsService } from '../../util/commons.service';
import { createRequestOption } from '../../util/request-util';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { AuditService } from './audit.service';

@Injectable({
  providedIn: 'root'
})
export class AuditApiCallService {

  private serverApiUrl = this.configInjectService.getServerApiUrl();
  public auditUrl = this.serverApiUrl + 'api/audit';

  constructor(private configInjectService: ConfigInjectService,
    private http: HttpClient,
    private exceptionService: ExceptionHandlingService,
    private commonsService: CommonsService,
    private auditService: AuditService) { }

  /**
   * Get Module With Action List
   * 
   * <AUTHOR>
   * @returns 
   */
  public async getAuditModuleList(): Promise<Array<AuditModuleWithActionResponse>> {
    try {
      const res: HttpResponse<Array<AuditModuleWithActionResponse>> = await firstValueFrom(this.http.get<Array<AuditModuleWithActionResponse>>(this.auditUrl + '/module/actions', { observe: 'response' }));
      return res.body || [];
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        this.exceptionService.customErrorMessage(error);
      }
      return [];
    }
  }

  /**
  * Get Audit list
  * 
  * <AUTHOR>
  * 
  * @param requestBody 
  * @param req 
  * @returns 
  */
  public getAuditList(auditSearchRequsetBody: AuditSearchRequsetBody, req: any, archivedAuditSearch: boolean): Observable<HttpResponse<AuditListPageResponse>> {
    const options = createRequestOption(req);
    return this.http.post<AuditListPageResponse>(this.auditUrl + "/search?isArchivedData=" + archivedAuditSearch, auditSearchRequsetBody, { params: options, observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }



  /**
  * Get Audit Detail
  * 
  * <AUTHOR>
  * 
  * @param requestBody 
  * @param req 
  * @returns 
  */
  public getAuditDetail(auditId: number, archivedData: boolean): Observable<HttpResponse<Array<AuditDetailResponse>>> {
    return this.http.get<Array<AuditDetailResponse>>(this.auditUrl + "/details/" + auditId + "?isArchivedData=" + archivedData, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
  * Get Audit Detail
  * 
  * <AUTHOR>
  * 
  * @param requestBody 
  * @param req 
  * @returns 
  */
  public reverseTransferProduct(productId: number, module: string): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.put<SuccessMessageResponse>(this.auditUrl + "/reverse/" + this.auditService.setUrlAccordingModule(module) + "/" + productId, {}, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }
}
