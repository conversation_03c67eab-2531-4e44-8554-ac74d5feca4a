import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { Subject } from 'rxjs';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { DeviceHistoricalData } from 'src/app/shared/enum/Probe/DeviceHistoricalData.enum';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { KeyValueMappingServiceService } from 'src/app/shared/util/key-value-mapping-service.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { ProbeOperationService } from '../ProbeService/Probe-Operation/probe-operation.service';
import { OtsProbesFilterComponent } from './ots-probes-filter.component';

describe('OtsProbesFilterComponent', () => {
  let component: OtsProbesFilterComponent;
  let fixture: ComponentFixture<OtsProbesFilterComponent>;
  let mockProbeService: jasmine.SpyObj<ProbeOperationService>;

  beforeEach(async () => {
    mockProbeService = jasmine.createSpyObj('ProbeOperationService', [
      'getProbeListRefreshSubject',
      'getSalesOrderNumberListFromCache',
      'getCountryListFromCache',
      'getProbeTypesListFromCache',
      'getFeaturesListFromCache',
      'getPresetListFromCache',
      'clearAllFiltersAndRefresh',
      'processFilterSearch',
      'buildProbeListFilterRequestBody',
      'callProbeListFilterRequestParameterSubject',
      'getProbeSearchRequestBodyForListingApi',
      'getListPageRefreshForbackToOtherPage',
      'setListPageRefreshForbackToOtherPage'
    ]);

    await TestBed.configureTestingModule({
      imports: [NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, FormsModule],
      declarations: [OtsProbesFilterComponent],
      providers: [
        FormBuilder,
        { provide: ProbeOperationService, useValue: mockProbeService },
        MultiSelectDropDownSettingService,
        KeyValueMappingServiceService,
        PrintListPipe,
        CommonsService,
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(OtsProbesFilterComponent);
    component = fixture.componentInstance;

    // Default mock returns
    mockProbeService.getProbeListRefreshSubject.and.returnValue(new Subject());
    mockProbeService.getSalesOrderNumberListFromCache.and.returnValue([]);
    mockProbeService.getCountryListFromCache.and.returnValue([]);
    mockProbeService.getProbeTypesListFromCache.and.returnValue([]);
    mockProbeService.getFeaturesListFromCache.and.returnValue([]);
    mockProbeService.getPresetListFromCache.and.returnValue([]);
    fixture.detectChanges(); // ngOnInit runs here
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    expect(component.filterForm).toBeTruthy();
    expect(component.filterForm.get('serialNumber')).toBeTruthy();
  });

  it('should call clearFilter and reset form', () => {
    spyOn(component, 'clearAllFilter');
    component.clearFilter();
    expect(component.clearAllFilter).toHaveBeenCalled();
    expect(mockProbeService.clearAllFiltersAndRefresh).toHaveBeenCalled();
  });

  it('should clear all filters', () => {
    component.filterForm.patchValue({ serialNumber: '1234' });
    component.clearAllFilter();
    expect(component.filterForm.get('serialNumber')?.value).toBeNull();
  });

  it('should process search if form is valid', () => {
    mockProbeService.processFilterSearch.and.returnValue(true);
    component.searchData();
    expect(mockProbeService.processFilterSearch).toHaveBeenCalled();
  });

  it('should not proceed with search if form is invalid', () => {
    mockProbeService.processFilterSearch.and.returnValue(false);
    component.searchData();
    expect(mockProbeService.processFilterSearch).toHaveBeenCalled();
  });

  it('should trigger searchFilteredProbes()', () => {
    spyOn(component, 'searchData');
    component.searchFilteredProbes();
    expect(component.searchData).toHaveBeenCalled();
  });

  it('should set deviceHistoricalData correctly', () => {
    const mockEvent = { target: { checked: true } };
    component.setDeviceHistoricalData(mockEvent);
    expect(component.filterForm.get('deviceHistoricalData')?.value).toBe(DeviceHistoricalData.HISTOTY);
  });

  it('should handle onCountrySelect with -1 ID', () => {
    const mockItem = { id: -1, name: 'Other' };
    component.onCountrySelect(mockItem);
    const selected = component.filterForm.get('countries')?.value;
    expect(selected[0]).toEqual(mockItem);
  });

  it('should handle onCountryDeSelect', () => {
    component.onCountryDeSelect();
  });

  it('should unsubscribe on ngOnDestroy', () => {
    const subject = new Subject<ListingPageReloadSubjectParameter>();
    mockProbeService.getProbeListRefreshSubject.and.returnValue(subject);
    component.onInitSubject();
    spyOn(component['subscriptionForRefreshList'], 'unsubscribe');
    component.ngOnDestroy();
    expect(component['subscriptionForRefreshList'].unsubscribe).toHaveBeenCalled();
  });

  it('should handle refresh list subject with clear filter', () => {
    const param = new ListingPageReloadSubjectParameter(true, true, false, false);
    const subject = new Subject<ListingPageReloadSubjectParameter>();
    mockProbeService.getProbeListRefreshSubject.and.returnValue(subject);
    component.onInitSubject();
    subject.next(param);
  });

  it('should handle refresh list subject with data reload only', () => {
    const param = new ListingPageReloadSubjectParameter(true, false, false, false);
    const subject = new Subject<ListingPageReloadSubjectParameter>();
    mockProbeService.getProbeListRefreshSubject.and.returnValue(subject);
    component.onInitSubject();
    subject.next(param);
  });
});
