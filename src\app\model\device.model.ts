import { ProductStatusEnum } from '../shared/enum/Common/ProductStatus.enum';
import { deviceTypesEnum } from '../shared/enum/deviceTypesEnum.enum';

export class IDevice {
  id: number;
  deviceId: string;
  packageVersion: string;
  productStatus: ProductStatusEnum;
  lastCheckInTime: number;
  locked: boolean;
  deviceType: deviceTypesEnum;
  editable: boolean;
  deviceSerialNo: string;
  salesOrderNumber: string;
  customerName: string;
  country: string

  constructor(id: number, //NOSONAR
    deviceId: string,
    packageVersion: string,
    productStatus: ProductStatusEnum,
    lastCheckInTime: number,
    locked: boolean,
    deviceType: deviceTypesEnum,
    editable: boolean,
    deviceSerialNo: string,
    salesOrderNumber: string,
    customerName: string,
    country: string) {
    this.id = id;
    this.deviceId = deviceId;
    this.packageVersion = packageVersion;
    this.productStatus = productStatus;
    this.lastCheckInTime = lastCheckInTime;
    this.locked = locked;
    this.deviceType = deviceType;
    this.editable = editable;
    this.deviceSerialNo = deviceSerialNo;
    this.salesOrderNumber = salesOrderNumber;
    this.customerName = customerName;
    this.country = country;
  }
}

