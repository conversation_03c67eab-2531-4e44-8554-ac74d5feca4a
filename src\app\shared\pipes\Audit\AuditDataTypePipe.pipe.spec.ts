import { AuditDataTypePipe } from './AuditDataTypePipe.pipe';
import { KeyValueMappingServiceService } from '../../util/key-value-mapping-service.service';
import { PrintListPipe } from '../printList.pipe';
import { DatePipe } from '@angular/common';
import { AuditDataTypeEnum } from '../../enum/Audit/AuditDataTypeEnum.enum';
import { AuditDetailResponse } from 'src/app/model/Audit/auditDetailResponse.model';
import { CommonsService } from '../../util/commons.service';

describe('AuditDataTypePipe', () => {
    let pipe: AuditDataTypePipe;
    let keyValueMappingService: KeyValueMappingServiceService;
    let printListPipe: PrintListPipe;
    let datePipe: DatePipe;

    beforeEach(() => {
        printListPipe = new PrintListPipe();
        datePipe = new DatePipe('en-US');
        keyValueMappingService = new KeyValueMappingServiceService(printListPipe, new CommonsService());
        pipe = new AuditDataTypePipe(keyValueMappingService, printListPipe, datePipe);
    });

    it('should transform TYPE_BOOLEAN using keyValueMappingService', () => {
        const booleanMap = new Map();
        booleanMap.set('active', [
            { key: 'true', value: 'Yes' },
            { key: 'false', value: 'No' }
        ]);

        const result = pipe.transform(
            'true',
            { type: AuditDataTypeEnum.TYPE_BOOLEAN, field: 'active' } as AuditDetailResponse,
            booleanMap,
            new Map()
        );

        expect(result).toBeNull();
    });

    it('should transform TYPE_ENUM using keyValueMappingService', () => {
        const enumMap = new Map();
        enumMap.set('role', [
            { key: 'ADMIN', value: 'Admin' },
            { key: 'USER', value: 'User' }
        ]);

        const result = pipe.transform(
            'ADMIN',
            { type: AuditDataTypeEnum.TYPE_ENUM, field: 'role' } as AuditDetailResponse,
            new Map(),
            enumMap
        );

        expect(result).toBe('Admin');
    });

    it('should transform TYPE_STRING by returning raw value', () => {
        const result = pipe.transform(
            'Hello World',
            { type: AuditDataTypeEnum.TYPE_STRING, field: 'message' } as AuditDetailResponse,
            new Map(),
            new Map()
        );

        expect(result).toBe('Hello World');
    });

    it('should transform TYPE_DATE using DatePipe', () => {
        const dateValue = '2023-01-01T12:00:00Z';

        const result = pipe.transform(
            dateValue,
            { type: AuditDataTypeEnum.TYPE_DATE, field: 'createdAt' } as AuditDetailResponse,
            new Map(),
            new Map()
        );
        expect(result).not.toBeNull();
    });


});
