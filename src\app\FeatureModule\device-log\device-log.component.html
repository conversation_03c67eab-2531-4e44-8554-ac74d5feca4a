<!-- name : device log component -->
<!-- description : use for display device log -->
<!-- loading - start -->
<div class="ringLoading" *ngIf="loading">
  <!-- loading gif start -->
  <div class="ringLoadingDiv">
    <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
  </div>
  <!-- loading gif end -->
</div>
<!-- loading - end -->
<!--log detail start-->
<div class="row" *ngIf="displaylog">

  <!--Filter start-->
  <div class="col-md-3 pr-0" *ngIf="(!isFilterHidden)">
    <!-- filter header start -->
    <label class="col-md-12 h5-tag">Filter</label>
    <!-- filter header end -->
    <div class="card mt-3">
      <div class="card-body">
        <!-- filter form - start -->
        <form id="filter-form" [formGroup]="filterForm" role="form" class="form">
          <!--------------------------------------->
          <!-- Serial Number - formfield - start -->
          <!--------------------------------------->
          <div class="form-group">
            <label class="form-control-label" for="field_logSerialNumber"><strong>Serial Number</strong></label>
            <input class="form-control" type="text" formControlName="logSerialNumber" />
            <div
              *ngIf="(filterForm.get('logSerialNumber').touched || filterForm.get('logSerialNumber').dirty) && filterForm.get('logSerialNumber').invalid ">
              <div *ngIf="filterForm.get('logSerialNumber').errors['maxlength']">
                <p class="alert-color">{{textBoxMaxCharactersAllowedMessage}}</p>
              </div>
              <div *ngIf="filterForm.get('logSerialNumber').errors['pattern']">
                <p class="alert-color">{{specialCharacterErrorMessage}}</p>
              </div>
            </div>
          </div>
          <!--------------------------------------->
          <!-- Serial Number - fromfield - end -->
          <!--------------------------------------->

          <!-- HW id - formfield - start -->
          <div class="form-group">
            <!-- hardware ID label start -->
            <label class="form-control-label" for="field_logDeviceId"><strong>HW ID</strong></label>
            <!-- hardware ID label end -->
            <!-- hardware ID input start -->
            <input class="form-control" type="text" formControlName="logDeviceId" />
            <!-- hardware ID input end -->
            <!-- validation for logDeviceId start -->
            <div
              *ngIf="(filterForm.get('logDeviceId').touched || filterForm.get('logDeviceId').dirty) && filterForm.get('logDeviceId').invalid ">
              <div *ngIf="filterForm.get('logDeviceId').errors['maxlength']">
                <p class="alert-color">{{textBoxMaxCharactersAllowedMessage}}</p>
              </div>
              <div *ngIf="filterForm.get('logDeviceId').errors['pattern']">
                <p class="alert-color">{{specialCharacterErrorMessage}}</p>
              </div>
            </div>
            <!-- validation for logDeviceId end -->
          </div>
          <!-- HW id - fromfield - end -->
          <!-- Log type - fromfield - start -->
          <div class="form-group">
            <!-- log type label start -->
            <label class="form-control-label" for="field_logType" id="label_logType"><strong>Log Type</strong></label>
            <!-- log type label end -->
            <!-- log type multiselect dropdown start -->
            <ng-multiselect-dropdown class="devicePageDeviceType" id="field_logType" name="logType" [placeholder]="''"
              formControlName="logType" [settings]="dropdownSettings" [data]="logTypeList">
            </ng-multiselect-dropdown>
            <!-- log type multiselect dropdown end -->
          </div>
          <!-- Log type - fromfield - end -->
          <!-- Exam id - formfield - start -->
          <div class="form-group">
            <!-- Exam ID label start -->
            <label class="form-control-label" for="field_logExamId"><strong>Exam Id</strong></label>
            <!-- Exam ID label end -->
            <!-- Exam ID input start -->
            <input class="form-control" type="text" formControlName="logExamId" />
            <!-- Exam ID input end -->
            <!-- validation for logExamId start -->
            <div
              *ngIf="(filterForm.get('logExamId').touched || filterForm.get('logExamId').dirty) && filterForm.get('logExamId').invalid ">
              <div *ngIf="filterForm.get('logExamId').errors['maxlength']">
                <p class="alert-color">{{textBoxMaxCharactersAllowedMessage}}</p>
              </div>
              <div *ngIf="filterForm.get('logExamId').errors['pattern']">
                <p class="alert-color">{{specialCharacterErrorMessage}}</p>
              </div>
            </div>
            <!-- validation for logExamId end -->
          </div>
          <!-- Exam id - fromfield - end -->

          <!-- Log Start Date - fromfield - start -->
          <div class="form-group">
            <!-- log startdate label start -->
            <label class="form-control-label" for="field_logRange"><strong>Log Start Date</strong></label>
            <!-- log startdate label end -->
            <mat-form-field>
              <!-- log start date input start -->
              <input class="form-control" style="width:90%;display: inherit;" matInput [matDatepicker]="start"
                placeholder="Choose a Start date" formControlName="logFrom" [max]="maxdate">
              <mat-datepicker-toggle matSuffix [for]="start"></mat-datepicker-toggle>
              <mat-datepicker #start></mat-datepicker>
              <!-- log start date input end -->
            </mat-form-field>
          </div>
          <!-- Log Start Date - fromfield - end -->
          <!-- Log End Date - fromfield - start -->
          <div class="form-group">
            <!-- log end date label start -->
            <label class="form-control-label" for="field_logType" id="label_logType"><strong>Log End
                Date</strong></label>
            <!-- log end date label end -->
            <!-- mat form field for log end date start -->
            <mat-form-field>
              <input class="form-control" style="width:90%;display: inherit;" matInput [matDatepicker]="end"
                placeholder="Choose a End date" formControlName="logTo" [max]="maxdate">
              <mat-datepicker-toggle matSuffix [for]="end"></mat-datepicker-toggle>
              <mat-datepicker #end></mat-datepicker>
            </mat-form-field>
            <!-- mat form field for log end date end -->
          </div>
          <!-- Log End Date - fromfield - end -->

          <hr class="mt-1 mb-2">
          <div class="">
            <!-- search button -->
            <button class="btn btn-sm btn-orange mr-3" (click)="searchLogFilter()"
              [disabled]="filterForm.invalid">Search</button>
            <!-- clear button -->
            <button class="btn btn-sm btn-orange" (click)="clearFilter()">Clear</button>
          </div>
        </form>
        <!-- filter form - end -->
      </div>
    </div>
  </div>
  <!--Filter End-->
  <!--table Block Start-->
  <div [className]="(isFilterHidden)?'col-md-12 pr-0':'col-md-9 pr-0'">
    <!-- main container start -->
    <div class="container-fluid">
      <!--view Devive Start-->
      <div class="row" class="headerAlignment">
        <!--------------------------------------->
        <!--Left Side-->
        <!--------------------------------------->
        <div class="childFlex">
          <div class="dropdown" id="deviceLogHideShowFilter">
            <button class="btn btn-sm btn-orange mr-3 ml-0 filter-symbol" (click)="toggleFilter()"
              [id]="(isFilterHidden)?'Show Filter':'Hide Filter'">
              <i class="fas fa-filter" aria-hidden="true" [id]="(isFilterHidden)?'Show Filter':'Hide Filter'"></i>
              &nbsp;&nbsp;{{ hideShowFilterButtonText }}
            </button>
          </div>
          <div>
            <label class="mb-0">Show entry</label>
            <select id="deviceLogShowEntry" [(ngModel)]="drpselectsize" class="form-control form-control-sm"
              (change)="changeDataSize($event)">
              <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                <option [value]="dataSize">{{ dataSize }}</option>
              </ng-template>
            </select>
          </div>
        </div>
        <!--------------------------------------->
        <!--Right Side-->
        <!--------------------------------------->
        <div class="childFlex">
          <div *ngIf="downloadDeviceLogPermission">
            <button class="btn btn-sm btn-outline-secondary " (click)="downloadLogs()"><i class="fa fa-download"
                aria-hidden="true"></i>&nbsp;&nbsp;Download</button>&nbsp;&nbsp;
          </div>
          <div>
            <button class="btn btn-sm btn-orange" (click)="refreshPage()"><em class="fa fa-refresh"></em></button>
          </div>
        </div>
      </div>




      <!--view log Start-->
      <div>Total {{totalLog}} Logs <p *ngIf="downloadLogFilename.length>0"><strong>{{downloadLogFilename.length}}
            Log(s) selected</strong> </p>
      </div>
      <!-- Device Log Table - start -->
      <div id="device-log">
        <table class="table table-sm table-bordered table-scroll" aria-hidden="true">
          <thead>
            <tr class="thead-light">
              <th class="checkox-table" *ngIf="downloadDeviceLogPermission">
                <div class="custom-control custom-checkbox">
                  <input type="checkbox" class="custom-control-input" name="chkseleclallLog" id="selectLog"
                    (change)="selectAllLog($event)">
                  <label class="custom-control-label" for="selectLog"></label>
                </div>
              </th>
              <th><span class="text_nowrap">Serial Number</span></th>
              <th><span class="text_nowrap">HW ID</span></th>
              <th><span class="text_nowrap">Device Status</span></th>
              <th><span class="text_nowrap">Log Type</span></th>
              <th><span class="text_nowrap">Exam Id</span></th>
              <th><span class="text_nowrap">Log Date & Time</span></th>
              <th><span class="text_nowrap">Upload Date & Time</span></th>
              <th><span class="text_nowrap">File Size</span></th>
            </tr>

          </thead>
          <tbody>
            <tr *ngFor="let log of logsDetail; let i = index">
              <td *ngIf="downloadDeviceLogPermission">
                <div class="custom-control custom-checkbox">
                  <input type="checkbox" class="custom-control-input" [id]="log.deviceId+i+i+i+i+i" name="deviceLog[]"
                    (change)="setDownloadLogFileName($event,log.fileName)" [checked]="defaultSelectLog(log.fileName)">
                  <label class="custom-control-label" [for]="log.deviceId+i+i+i+i+i"></label>
                </div>
              </td>
              <td class="text_nowrap">{{log?.serialNumber}}</td>
              <td class="allDetail spanunderline" *ngIf="deviceReaderPermission && log.deviceId != null"
                style="cursor: pointer;" (click)="deviceDetailModel(log.deviceIdPk)" class="text_nowrap spanunderline"
                id="deviceLogToDevieDetail">
                {{log.deviceId}}</td>
              <td *ngIf="(!deviceReaderPermission) || log.deviceId == null" class="text_nowrap">
                <span>{{log.deviceId}}</span>
              </td>
              <td class="text_nowrap">{{log.deviceStatus}}</td>
              <td class="text_nowrap">{{log.logType}}</td>
              <td class="text_nowrap">{{log.examId}}</td>
              <td class="text_nowrap">{{log.createdDate | date:'MMM d, y, h:mm:ss a'}}</td>
              <td class="text_nowrap">{{log.uploadedDate | date:'MMM d, y, h:mm:ss a'}}</td>
              <td class="text_nowrap">{{log.fileSize | fileSizeConvert}}</td>
            </tr>
          </tbody>

        </table>
      </div>
      <!-- Device Log Table - end -->
      <!--paging Start-->
      <div>
        <div>Showing {{ totalLogDisplay }} out of {{totalLog}} Logs</div>
        <div class="float-right">

          <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="itemsPerPage" [maxSize]="5"
            id="deviceLogPagination" [rotate]="true" [boundaryLinks]="true"
            (pageChange)="loadPage(page)"></ngb-pagination>

        </div>
      </div>
      <!--paging End-->
    </div>
    <!-- main container end -->
    <!--flud tab 9 row-->
  </div>
  <!--table Block End-->
</div>
<!--log detail-->

<div *ngIf="displayDeviceDetail">
  <app-device-detail [deviceIdInput]="deviceIdInput" [resource]="deviceLogListResource"
    (showDevice)="showLog()"></app-device-detail>
</div>