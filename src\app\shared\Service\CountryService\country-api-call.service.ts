import { HttpClient, HttpResponse } from "@angular/common/http";
import { CountryRequestBody } from "src/app/model/Country/CountryRequestBody.model";
import { Observable, catchError } from 'rxjs';
import { createRequestOption } from "../../util/request-util";
import { ConfigInjectService } from "../../InjectService/config-inject.service";
import { CountryPageResponse } from "src/app/model/Country/CountryPageResponse.model";
import { Injectable } from "@angular/core";
import { CommonsService } from "../../util/commons.service";
import { CreateCountryRequest } from "src/app/model/Country/CreateUpdateCountry/CreateUpdateCountryRequest.model";
import { SuccessMessageResponse } from "src/app/model/common/SuccessMessageResponse.model";

@Injectable({
    providedIn: 'root'
})
export class CountryApiCallService {

    private serverApiUrl = this.configInjectService.getServerApiUrl();

    private countryBaseUrl: string = this.serverApiUrl + "api/";
    private countryPostfix = "country";

    constructor(private http: HttpClient,
        private configInjectService: ConfigInjectService,
        private commonsService: CommonsService) { }

    public getCountryList(requestBody: CountryRequestBody, req: any): Observable<HttpResponse<CountryPageResponse>> {
        const options = createRequestOption(req);
        return this.http.post<CountryPageResponse>(this.countryBaseUrl + this.countryPostfix + "/search", requestBody, { params: options, observe: 'response' }).pipe(catchError(this.commonsService.handleError));
    }

    /**
    * Create Country
    * <AUTHOR>
    * @param countryResponse 
    * @returns  Observable<HttpResponse<SuccessMessageResponse>>
    */
    public createCountry(countryResponse: CreateCountryRequest): Observable<HttpResponse<SuccessMessageResponse>> {
        return this.http.post<SuccessMessageResponse>(this.countryBaseUrl + this.countryPostfix, countryResponse, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
    }

    /**
    * Deletes one or more countries by their IDs.
    *
    * Sends a DELETE request with the country IDs as a path parameter.
    *
    * @param countryId - Array of country IDs to be deleted.
    * @returns Observable<HttpResponse<SuccessMessageResponse>> - HTTP response with a success message.
    */
    public deleteCountry(countryId: Array<number>): Observable<HttpResponse<SuccessMessageResponse>> {
        return this.http.delete<SuccessMessageResponse>(`${this.countryBaseUrl}${this.countryPostfix}/${countryId}`, { observe: "response" }).pipe(catchError(this.commonsService.handleError));
    }

}