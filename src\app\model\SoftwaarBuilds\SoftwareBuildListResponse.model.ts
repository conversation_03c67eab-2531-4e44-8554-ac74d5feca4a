import { deviceTypesEnum } from "../../shared/enum/deviceTypesEnum.enum";
import { Jsonlist } from "../video/jsonlist.model";

export class SoftwareBuildListResponse {
    id: number;
    version: string;
    title: string;
    lifecycle: string;
    revision: string;
    effectiveDate: number;
    attachmentName: string;
    attachmentUrl: string;
    releaseNoteName: string;
    releaseNoteUrl: string;
    createdDate: number;
    status: string;
    description: string;
    action: string;
    countries: Array<string>;
    jsonMaster: Jsonlist;
    deviceTypes: Array<deviceTypesEnum>;
    partNumber: string;
    isActive: boolean;
}
