import { HttpClient, HttpResponse } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { createRequestOption } from '../../util/request-util';
import { JobHistoryResponse } from '../../../model/job/jobHistoryResponse.model';
import { IJobchange } from '../../../model/jobchange.model';
import { API_BASE_URL } from '../../config';

@Injectable({
  providedIn: 'root'
})
export class JobService {

  public resourceUrl = this.SERVER_API_URL + 'api/jobs/';

  constructor(protected http: HttpClient, @Inject(API_BASE_URL) public SERVER_API_URL: string) { }

  public getJobType() {
    return this.http.get<string[]>(this.resourceUrl + 'types', { observe: 'response' });
  }

  public getJobStatus() {
    return this.http.get<string[]>(this.resourceUrl + 'status', { observe: 'response' });
  }

  public getJobList(filterData: any, req?: any): Observable<HttpResponse<IJobchange>> {
    const data = {
      packageVersions: filterData.packageVersions,
      probeVersions: filterData.probeVersions,
      handleVersions: filterData.handleVersions,
      status: filterData.connectionState,
      deviceId: filterData.jobDeviceId,
      jobType: filterData.drpJobTypes,
      jobScheduleStatus: filterData.jobStatus
    };
    const options = createRequestOption(req);
    return this.http.post<IJobchange>(this.resourceUrl + 'search', data, { params: options, observe: 'response' });
  }

  public getJobHistory(jobScheduleStatusId: number): Observable<HttpResponse<JobHistoryResponse>> {
    return this.http.get<JobHistoryResponse>(this.resourceUrl + 'jobScheduleStatus/' + jobScheduleStatusId, { observe: 'response' });
  }

}