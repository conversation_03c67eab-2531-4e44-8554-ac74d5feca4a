import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ChangeDetectorRef, Component, EventEmitter, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { isUndefined } from 'is-what';
import { Subscription } from 'rxjs';
import { ITEMS_PER_PAGE, OTSKitManagementListResource } from 'src/app/app.constants';
import { OTSKitManagemantFilterAction } from 'src/app/model/KitManagement/otsWorld/OTSKitManagemantFilterAction.model';
import { OTSKitManagemantSearchRequestBody } from 'src/app/model/KitManagement/otsWorld/OTSKitManagemantSearchRequestBody.model';
import { OTSKitManagementListResponse } from 'src/app/model/KitManagement/otsWorld/OTSKitManagementListResponse.model';
import { OTSKitManagementPageResponse } from 'src/app/model/KitManagement/otsWorld/OTSKitManagementPageResponse.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { OtsKitManagemantApiCallService } from 'src/app/shared/Service/KitManagemant/ots-kit-managemant-api-call.service';
import { OtsKitManagemantService } from 'src/app/shared/Service/KitManagemant/ots-kit-managemant.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { collapseFilterTextEnum } from 'src/app/shared/enum/collapseFilterButtonText.enum';
import { PermissionService } from 'src/app/shared/permission.service';
import { CommonCheckboxService } from 'src/app/shared/util/common-checkbox.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';

@Component({
  selector: 'app-ots-kit-management-list',
  templateUrl: './ots-kit-management-list.component.html',
  styleUrl: './ots-kit-management-list.component.css',
  encapsulation: ViewEncapsulation.None
})
export class OtsKitManagementListComponent implements OnInit {

  @Output("showBridgeWorldList") showBridgeWorldList = new EventEmitter();

  loading: boolean = false;

  //Permission
  bridgeKitDisplayPermissions: boolean = false;

  //Page
  itemsPerPage: number = 0;
  page: number = 0;
  previousPage: number = 1;
  totalItems: number = 0;

  //Totel Count Display
  totalRecordDisplay: number = 0;
  totalRecord: number = 0;

  //Page Size DropDown
  dataSizes: string[] = [];
  drpselectsize: number = ITEMS_PER_PAGE;

  //Operation List
  operationsList: string[] = [];

  //Filter
  isFilterComponentInitWithApicall: boolean = true;
  isFilterHidden: boolean = true;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;

  //selected Id Collect
  selectedKitIdList: number[] = [];
  localKitIdListArray: number[] = [];

  //unique CheckBox Name
  chkPreFix = "otsKit";
  selectAllCheckboxId = "selectAllOtsKit";
  checkboxListName = "otsKitItem[]";
  //checkboxDisplay
  isCheckBoxDiaply: boolean = false;

  //Hide Show List and Detail Page
  otsWorldDetailDisplay: boolean = false;
  otsWorldListDisplay: boolean = false;
  listPageRefreshForbackToDetailPage: boolean = false;
  kitId: number = null;

  otsKitManagementListResponse: Array<OTSKitManagementListResponse> = [];

  //subscriptionotsKitFilterComponent
  subscriptionForLoading: Subscription;
  subscriptionForKitListFilterRequestParameter: Subscription;

  // ots kit Rev Version 
  otsKitRevVersionResponse: string;

  //ots kit serach request body store
  otsKitManagemantSearchRequestBody: OTSKitManagemantSearchRequestBody = null;

  constructor(private authservice: AuthJwtService,
    private commonsService: CommonsService,
    private commonOperationsService: CommonOperationsService,
    private otsKitManagemantService: OtsKitManagemantService,
    private otsKitManagemantApiCallService: OtsKitManagemantApiCallService,
    private exceptionService: ExceptionHandlingService,
    private commonCheckboxService: CommonCheckboxService,
    private permissionService: PermissionService,
    private cdr: ChangeDetectorRef,
    private countryCacheService: CountryCacheService) {
  }
  /**
  * Init methoad
  * 
  * <AUTHOR>
  */
  public ngOnInit(): void {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.page = 0;
      this.dataSizes = this.commonsService.accessDataSizes();
      this.operationsList = this.commonOperationsService.accessOTSKitOperations(true);
      this.bridgeKitDisplayPermissions = this.permissionService.getKitManagementPermission(PermissionAction.GET_BRIDGE_KIT_MANAGEMENT_ACTION);
      this.isFilterComponentInitWithApicall = true;
      this.isFilterHidden = false;
      this.otsWorldListDisplay = true;
      this.otsWorldDetailDisplay = false;
      this.listPageRefreshForbackToDetailPage = false;
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.drpselectsize = ITEMS_PER_PAGE;
      this.selectedKitIdList = [];
      this.refreshFilter();
      this.getOtsKitRevVersion();
    }
    this.subjectInit();
  }

  private subjectInit(): void {
    /**
     * Loading Hide/Display
     * <AUTHOR>
     */
    this.subscriptionForLoading = this.commonOperationsService.getCommonLoadingSubject()?.subscribe((res: boolean) => {
      this.setLoadingStatus(res);
    });

    /**
     * This Subject call from Filter component
     * Load all the Data
     * 
     * <AUTHOR>
     */
    this.subscriptionForKitListFilterRequestParameter = this.otsKitManagemantService.getOTSKitListFilterRequestParameterSubject()?.subscribe((otsKitManagemantFilterAction: OTSKitManagemantFilterAction) => {
      if (otsKitManagemantFilterAction.listingPageReloadSubjectParameter.isReloadData) {
        if (otsKitManagemantFilterAction.listingPageReloadSubjectParameter.isDefaultPageNumber) {
          this.selectedKitIdList = [];
          this.resetPage()
        }
        if (otsKitManagemantFilterAction.listingPageReloadSubjectParameter.isOtherAction) {
          this.getOtsKitRevVersion();
        }
        this.loadAll(otsKitManagemantFilterAction.otsKitManagemantSearchRequestBody);
      }
    });
  }

  /**
   * Destroy subscription
   * 
   * <AUTHOR>
   */
  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForLoading)) { this.subscriptionForLoading.unsubscribe() }
    if (!isUndefined(this.subscriptionForKitListFilterRequestParameter)) { this.subscriptionForKitListFilterRequestParameter.unsubscribe() }
    this.otsKitManagemantService.setCountryList([]);
  }

  /**
  * Get Rev Version 
  *  
  * @returns 
  */
  public async getOtsKitRevVersion(): Promise<void> {
    this.otsKitRevVersionResponse = await this.otsKitManagemantApiCallService.getOtsKitRevVersion();
  }

  /**
   * Kit Operation like import csv
   * 
   * <AUTHOR>
   * 
   * @param operationName 
   */
  public changeOperation(operationName: string): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, true, false, true)
    this.commonOperationsService.commonOperationForOTSKitManagement(operationName, listingPageReloadSubjectParameter, OTSKitManagementListResource, this.isFilterHidden);
  }

  /**
  * Refresh button click
  *
  * <AUTHOR>
  */
  public async clickOnRefreshButton(): Promise<void> {
    this.loading = true;
    this.resetPage();
    // Filter is hidden, directly update the service cache
    const countriesList = await this.countryCacheService.getCountryListFromCache(false);
    this.otsKitManagemantService.setCountryList(countriesList);

    this.filterPageSubjectCallForReloadPage(true, false);
  }

  /**
   * Clear all filter ,Reset Page and Reload the page
   * 
   * <AUTHOR>
   */
  public refreshFilter(): void {
    this.resetPage();
    this.filterPageSubjectCallForReloadPage(true, true);
  }

  /**
   * Item par page Value Changes like (10,50,100)
   * 
   * <AUTHOR>
   * @param datasize 
   */
  public changeDataSize(datasize): void {
    this.setLoadingStatus(true);
    this.selectedKitIdList = [];
    this.itemsPerPage = datasize.target.value;
    this.filterPageSubjectCallForReloadPage(true, false);
  }



  /**
  * Reset Page
  * 
  * <AUTHOR>
  */
  private resetPage(): void {
    this.page = 0;
    this.previousPage = 1;
  }

  /**
    * Change The Page
    * callKitListRefreshSubject ->Call the filter component
    * filter not clear and send with filter requrest and load data 
    * 
    * <AUTHOR>
    * 
    * @param page 
    */
  public loadPage(page): void {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.commonCheckboxService.clearSelectAllCheckbox(this.selectAllCheckboxId);
      this.filterPageSubjectCallForReloadPage(false, false);
    }
  }

  /**
   * Call Filter component subject and reload page
   * 
   * <AUTHOR>
   * 
   * @param isDefaultPageNumber 
   * @param isClearFilter 
   */
  public filterPageSubjectCallForReloadPage(isDefaultPageNumber: boolean, isClearFilter: boolean): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false)
    this.otsKitManagemantService.callOTSKitRefreshPageSubject(listingPageReloadSubjectParameter, OTSKitManagementListResource, this.isFilterHidden);
  }

  /**
  * Toggle Filter
  * 
  * <AUTHOR>
  * 
  * @param id 
  */
  public toggleFilter(): void {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = false;
    this.isFilterHidden = !this.isFilterHidden;
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }

  /**
   * Get Kit List
   * 
   * <AUTHOR>
   * 
   * @param otsKitManagemantSearchRequestBody 
   */
  public loadAll(otsKitManagemantSearchRequestBody: OTSKitManagemantSearchRequestBody): void {
    this.otsKitManagemantSearchRequestBody = otsKitManagemantSearchRequestBody;
    let pageObj = {
      page: this.page - 1,
      size: this.itemsPerPage
    }
    this.setLoadingStatus(true);
    this.otsKitManagemantApiCallService.getOTSKitList(otsKitManagemantSearchRequestBody, pageObj)?.subscribe(
      {
        next: (rolePageResponse: HttpResponse<OTSKitManagementPageResponse>) => {
          if (rolePageResponse.status == 200) {
            this.paginateDataset(rolePageResponse.body);
          } else {
            this.otsKitManagementListResponse = [];
            this.totalRecordDisplay = 0;
            this.totalRecord = 0;
            this.loading = false;
          }
          this.setLoadingStatus(false);
        }, error: (error: HttpErrorResponse) => {
          this.setLoadingStatus(false);
          this.exceptionService.customErrorMessage(error);
        }
      });
  }

  /**
   * List API Response set
   * 
   * @param kitManagementPageResponse 
   */
  private paginateDataset(otsKitManagementPageResponse: OTSKitManagementPageResponse): void {
    this.totalItems = otsKitManagementPageResponse.totalElements;
    this.otsKitManagementListResponse = otsKitManagementPageResponse.content;
    this.page = otsKitManagementPageResponse.number + 1;
    this.totalRecord = otsKitManagementPageResponse.totalElements;
    this.totalRecordDisplay = otsKitManagementPageResponse.numberOfElements;
    this.setLocalId(this.otsKitManagementListResponse);
    this.setLoadingStatus(false);
  }

  /**
   * Local kit Id list create for Select all Checkbox
   * 
   * <AUTHOR>
   * 
   * @param roleIdList 
   */
  public setLocalId(kitList: Array<OTSKitManagementListResponse>): void {
    this.localKitIdListArray = [];
    for (let kitObj of kitList) {
      this.localKitIdListArray.push(kitObj.id);
    }
    this.defaultSelectAll();
  }

  /**
   * select All checkbox select or deSelect
   * 
   * <AUTHOR>
   */
  private defaultSelectAll(): void {
    this.commonCheckboxService.defaultSelectAll(this.localKitIdListArray, this.selectedKitIdList, this.selectAllCheckboxId);
  }

  /**
   * single Checkbox Select
   * 
   * <AUTHOR>
   * 
   * @param roleObj 
   * @param isChecked 
   */
  public selectCheckbox(kitManagementResponseObj: OTSKitManagementListResponse, isChecked: boolean): void {
    if (isChecked) {
      this.selectedKitIdList.push(kitManagementResponseObj.id);
    } else {
      let index = this.selectedKitIdList.findIndex(obj => obj == kitManagementResponseObj.id);
      this.selectedKitIdList.splice(index, 1);
    }
    this.defaultSelectAll();
  }

  /**
   * Select All CheckBox
   * 
   * <AUTHOR>
   * 
   * @param isChecked 
   */
  public selectAllItem(isChecked: boolean): void {
    this.selectedKitIdList = this.commonCheckboxService.selectAllItem(isChecked, this.localKitIdListArray, this.selectedKitIdList, this.checkboxListName);
  }

  /**
   * Set Loading status
   * 
   * <AUTHOR>
   * 
   * @param status 
   */
  private setLoadingStatus(status: boolean): void {
    this.loading = status;
    this.cdr.detectChanges();
  }

  /**
   * Show kit Detail
   * 
   * @param id 
   * 
   * <AUTHOR>
   */
  public showkitDetail(id: number): void {
    this.kitId = id;
    this.otsWorldListDisplay = false;
    this.otsWorldDetailDisplay = true;
  }

  /**
   * Show Kit List 
   * 
   * <AUTHOR>
   */
  public showKitList(): void {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = true;
    this.kitId = null;
    this.otsWorldListDisplay = true;
    this.otsWorldDetailDisplay = false;
    this.selectedKitIdList = [];
    this.filterPageSubjectCallForReloadPage(true, false);
  }

  public showBridgeWorldListDisplay(): void {
    this.showBridgeWorldList.emit();
  }

}
