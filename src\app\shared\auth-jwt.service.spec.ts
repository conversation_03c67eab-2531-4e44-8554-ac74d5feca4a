import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { RdmAction, RdmAuthenticationToken, RdmEmailToken, RdmUserAssignedCountries, RdmUserId, RdmUserRoles } from '../app.constants';
import { ConfirmDialogService } from '../FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { AuthJwtService } from './auth-jwt.service';
import { API_BASE_URL } from './config';
import { PermissionService } from './permission.service';
import { CommonsService } from './util/commons.service';

describe('AuthJwtService', () => {
  let service: AuthJwtService;
  let httpMock: HttpTestingController;
  let localStorageService: jasmine.SpyObj<LocalStorageService>;
  let sessionStorageService: jasmine.SpyObj<SessionStorageService>;
  let router: jasmine.SpyObj<Router>;
  let permissionService: jasmine.SpyObj<PermissionService>;

  const API_URL = 'http://example.com/api/';

  beforeEach(() => {
    const localStorageSpy = jasmine.createSpyObj('LocalStorageService', ['retrieve', 'store', 'clear']);
    const sessionStorageSpy = jasmine.createSpyObj('SessionStorageService', ['retrieve', 'store', 'clear']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);
    const permissionSpy = jasmine.createSpyObj('PermissionService', ['setPermission']);
    const commonsSpy = jasmine.createSpyObj('CommonsService', ['handleError']);

    TestBed.configureTestingModule({
      imports: [],
      providers: [
        AuthJwtService,
        { provide: LocalStorageService, useValue: localStorageSpy },
        { provide: SessionStorageService, useValue: sessionStorageSpy },
        { provide: Router, useValue: routerSpy },
        { provide: PermissionService, useValue: permissionSpy },
        { provide: CommonsService, useValue: commonsSpy },
        { provide: API_BASE_URL, useValue: API_URL },
        ConfirmDialogService,
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting()
      ]
    });

    service = TestBed.inject(AuthJwtService);
    httpMock = TestBed.inject(HttpTestingController);
    localStorageService = TestBed.inject(LocalStorageService) as jasmine.SpyObj<LocalStorageService>;
    sessionStorageService = TestBed.inject(SessionStorageService) as jasmine.SpyObj<SessionStorageService>;
    router = TestBed.inject(Router) as jasmine.SpyObj<Router>;
    permissionService = TestBed.inject(PermissionService) as jasmine.SpyObj<PermissionService>;
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should return token from localStorage or sessionStorage', () => {
    localStorageService.retrieve.withArgs(RdmAuthenticationToken).and.returnValue(null);
    sessionStorageService.retrieve.withArgs(RdmAuthenticationToken).and.returnValue('token');
    expect(service.getToken()).toBe('token');
  });

  it('should call syncUserInfo and return response', () => {
    service.syncUserInfo().subscribe(res => {
      expect(res.status).toBe(200);
    });

    const req = httpMock.expectOne(API_URL + 'api/sync');
    expect(req.request.method).toBe('GET');
    req.flush({}, { status: 200, statusText: 'OK' });
  });

  it('should navigate to login page', () => {
    service.loginNavigate();
    expect(router.navigate).toHaveBeenCalledWith(['']);
  });

  it('should return true if user ID is present', () => {
    localStorageService.retrieve.withArgs(RdmUserId).and.returnValue(123);
    expect(service.isAuthenticate()).toBeTrue();
  });

  it('should return false if user ID is null', () => {
    localStorageService.retrieve.withArgs(RdmUserId).and.returnValue(null);
    expect(service.isAuthenticate()).toBeFalse();
  });

  it('should store country masters', () => {
    service.setCountryMasters(['IN', 'US']);
    expect(localStorageService.store).toHaveBeenCalledWith(RdmUserAssignedCountries, ['IN', 'US']);
  });

  it('should set permissions', () => {
    service.setPermission(['READ']);
    expect(permissionService.setPermission).toHaveBeenCalledWith(['READ']);
  });

  it('should set user id', () => {
    service.setUserId(123);
    expect(localStorageService.store).toHaveBeenCalledWith(RdmUserId, 123);
  });

  it('should store authentication token in both storages', () => {
    service.storeAuthenticationToken('jwtToken', true);
    expect(localStorageService.store).toHaveBeenCalledWith(RdmAuthenticationToken, 'jwtToken');
    expect(sessionStorageService.store).toHaveBeenCalledWith(RdmAuthenticationToken, 'jwtToken');
  });

  it('should clear all tokens and complete logout observable', (done) => {
    service.logout().subscribe({
      complete: () => {
        expect(localStorageService.clear).toHaveBeenCalledWith(RdmAuthenticationToken);
        expect(localStorageService.clear).toHaveBeenCalledWith(RdmUserId);
        expect(sessionStorageService.clear).toHaveBeenCalledWith(RdmEmailToken);
        done();
      }
    });
  });

  it('should clear all related data on clear()', () => {
    service.clear();
    expect(localStorageService.clear).toHaveBeenCalledWith(RdmAuthenticationToken);
    expect(localStorageService.clear).toHaveBeenCalledWith(RdmUserId);
    expect(localStorageService.clear).toHaveBeenCalledWith(RdmEmailToken);
    expect(localStorageService.clear).toHaveBeenCalledWith(RdmAction);
    expect(localStorageService.clear).toHaveBeenCalledWith(RdmUserAssignedCountries);
    expect(localStorageService.clear).toHaveBeenCalledWith(RdmUserRoles);
  });

  it('should return true when email token is null', () => {
    localStorageService.retrieve.withArgs(RdmEmailToken).and.returnValue(null);
    expect(service.getEmailToket()).toBeTrue();
  });

  it('should store user roles', () => {
    service.setUserRoles(['ADMIN']);
    expect(localStorageService.store).toHaveBeenCalledWith(RdmUserRoles, ['ADMIN']);
  });

  it('should return user roles if present', () => {
    localStorageService.retrieve.withArgs(RdmUserRoles).and.returnValue(['USER']);
    expect(service.getUserRoles()).toEqual(['USER']);
  });

  it('should return empty array if roles are null', () => {
    localStorageService.retrieve.withArgs(RdmUserRoles).and.returnValue(null);
    expect(service.getUserRoles()).toEqual([]);
  });
});
