<div class="ringLoading" *ngIf="loading">
  <!-- loading gif start -->
  <div class="ringLoadingDiv">
    <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
  </div>
  <!-- loading gif end -->
</div>

<!-- ---------------------------------------------------------------------------- -->
<!-- name : update feature dialog component -->
<!-- description : update Features dialog for assign feature subscription -->
<!-- ---------------------------------------------------------------------------- -->
<div id="deleteExam" tabindex="-1" role="dialog" aria-labelledby="" aria-hidden="true">
  <div class="modal-body" id="updateFeatureModel">

    <!-------------------------------------------------------------------------------->
    <!--title start-->
    <!--close button start-->
    <!-------------------------------------------------------------------------------->
    <div class="row">
      <div class="col-md-3">
        <em class="" id="deleteModelSymbol"></em>
        <label id="exampleModalDelete" class="modal-title h5-tag pb-2 ">{{ basicModelConfig?.title }}</label>
      </div>
    </div>

    <!-- -------------------------------------------------------------- -->
    <!-- -------------------------------------------------------------- -->
    <!-- updateFeatureForm - start -->
    <!-- -------------------------------------------------------------- -->
    <!-- -------------------------------------------------------------- -->
    <div id="mainSection">
      <p class="text-lg align-self-start ml-2 mb-0">
        <strong>Configure Preset(s):</strong>
      </p>

      <div class="row m-0 border border-2  border-secondary">
        <ng-template [ngIf]="currentPresets != null && currentPresets.length > 0">
          <ng-template ngFor let-presetsObject let-i="index" [ngForOf]="currentPresets">
            <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 p-1 border-license" [ngStyle]="{
  'border-left': i % 3 === 0 ? 'none' : 'inherit',
  'border-bottom': i >= currentPresets.length - (currentPresets.length % 3 === 0 ? 3 : currentPresets.length % 3) ? '0' : 'inherit'
}">
              <div class="custom-control custom-checkbox mr-2">
                <input type="checkbox" class="custom-control-input"
                  [id]="presetsObject.displayName+'_'+presetsObject.presetId.toString()" name="presets[]"
                  [checked]="probeObject?.presets | assignConfigCheckBoxPipe:presetsObject.presetId:reloadPipe"
                  (change)="onChangePresetsForUpdate(presetsObject.presetId,$any($event.target)?.checked,defaultStartDate,presetsObject)"
                  [disabled]="this.currentPresets | assignConfigDisablePipe:presetsObject.presetId:presetProbeConfigType">
                <label class="custom-control-label checboxLineHeight"
                  [for]="presetsObject.displayName+'_'+presetsObject.presetId.toString()">{{presetsObject |
                  featuresBaseResponseDisplayPipe }}</label>
              </div>
              <div style="display: flex; justify-content: space-around;">
                <ng-template [ngIf]="presetsObject | featureValidityOptionHideShow : validityPerpetual">
                  <div class="custom-control custom-radio">
                    <input type="radio" class="custom-control-input"
                      [id]="'Perpetual_'+presetsObject.displayName+'_'+presetsObject.presetId.toString()"
                      [name]="presetsObject.displayName+'_'+presetsObject.presetId.toString()"
                      [checked]="probeObject?.presets | featuresRadioButtonPipe:presetsObject.presetId:unlimitedEndDateOptions:reloadPipe"
                      (change)="onChangePresetsEndDateForUpdate(presetsObject.presetId,defaultStartDate,unlimitedEndDateOptions,null)"
                      [disabled]="this.currentPresets | assignConfigDisablePipe:presetsObject.presetId:presetProbeConfigType">
                    <label class="custom-control-label radioButtonLineHeight"
                      [for]="'Perpetual_'+presetsObject.displayName+'_'+presetsObject.presetId.toString()">
                      {{presetsObject | featuresValidityPartNumberDisplayPipe:validityPerpetual}}</label>
                  </div>
                </ng-template>
                <ng-template [ngIf]="presetsObject | featureValidityOptionHideShow : validityOneYear">
                  <div class="custom-control custom-radio">
                    <input type="radio" class="custom-control-input"
                      [id]="'month_'+presetsObject.displayName+'_'+presetsObject.presetId.toString()"
                      [name]="presetsObject.displayName+'_'+presetsObject.presetId.toString()"
                      [checked]="probeObject?.presets | featuresRadioButtonPipe:presetsObject.presetId:oneYearEndDateOptions:reloadPipe"
                      (change)="onChangePresetsEndDateForUpdate(presetsObject.presetId,defaultStartDate,oneYearEndDateOptions,null)"
                      [disabled]="this.currentPresets | assignConfigDisablePipe:presetsObject.presetId:presetProbeConfigType">
                    <label class="custom-control-label radioButtonLineHeight"
                      [for]="'month_'+presetsObject.displayName+'_'+presetsObject.presetId.toString()">
                      {{presetsObject | featuresValidityPartNumberDisplayPipe:validityOneYear}}</label>
                  </div>
                </ng-template>
                <div class="custom-control custom-radio">
                  <div class="custom-control custom-radio">
                    <input type="radio" class="custom-control-input"
                      [id]="'customDate_'+presetsObject.displayName+'_'+presetsObject.presetId.toString()"
                      [name]="presetsObject.displayName+'_'+presetsObject.presetId.toString()"
                      [checked]="probeObject.presets | featuresRadioButtonPipe:presetsObject.presetId:customEndDateOptions:reloadPipe"
                      (change)="onChangePresetsEndDateForUpdate(presetsObject.presetId,defaultStartDate,customEndDateOptions,null)"
                      [disabled]="this.currentPresets | assignConfigDisablePipe:presetsObject.presetId:presetProbeConfigType">
                    <label class="custom-control-label radioButtonLineHeight"
                      [for]="'customDate_'+presetsObject.displayName+'_'+presetsObject.presetId.toString()">{{customEndDateOptions}}</label>
                  </div>
                </div>
              </div>
              <div class="d-flex mt-2" id="datepickerId"
                *ngIf="probeObject.presets | featuresCustomEndDateDisplayPipe:presetsObject.presetId:reloadPipe">
                <label class="customDate-lbl pl-3" for="field_logRange">Date</label>
                <mat-form-field>
                  <input class="form-control datePikerTextBox" matInput [matDatepicker]="customDate"
                    placeholder="Choose a Start date" readonly
                    [value]="probeObject.presets | featuresSelectCustomDatePipe:presetsObject.presetId:reloadPipe"
                    (dateChange)="onChangePresetsEndDateForUpdate(presetsObject.presetId,defaultStartDate,customEndDateOptions,$any($event.target).value)">
                  <mat-datepicker-toggle matSuffix [for]="customDate"></mat-datepicker-toggle>
                  <mat-datepicker #customDate></mat-datepicker>
                </mat-form-field>
              </div>
              <div class="pl-3 pt-2">
                <span style="color: #919199;">{{probeObject?.presets |
                  featuresExpireDateDisplayPipe:presetsObject.presetId:reloadPipe}}</span>
              </div>
            </div>
          </ng-template>
        </ng-template>
        <ng-template [ngIf]="currentPresets==null || currentPresets.length == 0 && allApiCall">
          <div class="row">
            <div class="col-md-12">
              <span>No presets available to configure</span>
            </div>
          </div>
        </ng-template>
      </div>


      <p class="text-lg align-self-start mt-2 mb-0 ml-2">
        <strong>Configure Feature(s):</strong>
      </p>

      <div class="row m-0 border border-2 border-secondary">
        <ng-template [ngIf]="currentFeatures != null && currentFeatures.length > 0">
          <ng-template ngFor let-featuresObject let-i="index" [ngForOf]="currentFeatures">
            <div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 p-1 border-license" [ngStyle]="{
  'border-left': i % 3 === 0 ? 'none' : 'inherit',
  'border-bottom': i >= currentFeatures.length - (currentFeatures.length % 3 === 0 ? 3 : currentFeatures.length % 3) ? '0' : 'inherit'
}">
              <div class="custom-control custom-checkbox mr-2">
                <input type="checkbox" class="custom-control-input"
                  [id]="'feature_'+featuresObject.featureId.toString()" name="feature[]"
                  [checked]="probeObject?.features | assignConfigCheckBoxPipe:featuresObject.featureId:reloadPipe"
                  (change)="onChangeFeaturesForUpdate(featuresObject.featureId,$any($event.target)?.checked,defaultStartDate,featuresObject)"
                  [disabled]="this.currentFeatures | assignConfigDisablePipe:featuresObject.featureId:featureProbeConfigType">
                <label class="custom-control-label checboxLineHeight"
                  [for]="'feature_'+featuresObject.featureId.toString()">{{featuresObject |
                  featuresBaseResponseDisplayPipe }}</label>
              </div>
              <div style="display: flex; justify-content: space-around;">
                <ng-template [ngIf]="featuresObject | featureValidityOptionHideShow : validityPerpetual">
                  <div class="custom-control custom-radio">
                    <input type="radio" class="custom-control-input"
                      [id]="'Perpetual_'+featuresObject.featureId.toString()"
                      [name]="featuresObject.displayName+'_'+featuresObject.featureId.toString()"
                      [checked]="probeObject?.features | featuresRadioButtonPipe:featuresObject.featureId:unlimitedEndDateOptions:reloadPipe"
                      (change)="onChangeFeaturesEndDateForUpdate(featuresObject.featureId,defaultStartDate,unlimitedEndDateOptions,null)"
                      [disabled]="this.currentFeatures | assignConfigDisablePipe:featuresObject.featureId:featureProbeConfigType">
                    <label class="custom-control-label radioButtonLineHeight"
                      [for]="'Perpetual_'+featuresObject.featureId.toString()">
                      {{featuresObject | featuresValidityPartNumberDisplayPipe:validityPerpetual}}</label>
                  </div>
                </ng-template>
                <ng-template [ngIf]="featuresObject | featureValidityOptionHideShow : validityOneYear">
                  <div class="custom-control custom-radio">
                    <input type="radio" class="custom-control-input"
                      [id]="'month_'+featuresObject.displayName+'_'+featuresObject.featureId.toString()"
                      [name]="featuresObject.displayName+'_'+featuresObject.featureId.toString()"
                      [checked]="probeObject?.features | featuresRadioButtonPipe:featuresObject.featureId:oneYearEndDateOptions:reloadPipe"
                      (change)="onChangeFeaturesEndDateForUpdate(featuresObject.featureId,defaultStartDate,oneYearEndDateOptions,null)"
                      [disabled]="this.currentFeatures | assignConfigDisablePipe:featuresObject.featureId:featureProbeConfigType">
                    <label class="custom-control-label radioButtonLineHeight"
                      [for]="'month_'+featuresObject.displayName+'_'+featuresObject.featureId.toString()">
                      {{featuresObject | featuresValidityPartNumberDisplayPipe:validityOneYear}}</label>
                  </div>
                </ng-template>
                <div class="custom-control custom-radio p-0">
                  <div class="custom-control custom-radio">
                    <input type="radio" class="custom-control-input"
                      [id]="'customDate_'+featuresObject.displayName+'_'+featuresObject.featureId.toString()"
                      [name]="featuresObject.displayName+'_'+featuresObject.featureId.toString()"
                      [checked]="probeObject.features | featuresRadioButtonPipe:featuresObject.featureId:customEndDateOptions:reloadPipe"
                      (change)="onChangeFeaturesEndDateForUpdate(featuresObject.featureId,defaultStartDate,customEndDateOptions,null)"
                      [disabled]="this.currentFeatures | assignConfigDisablePipe:featuresObject.featureId:featureProbeConfigType">
                    <label class="custom-control-label radioButtonLineHeight"
                      [for]="'customDate_'+featuresObject.displayName+'_'+featuresObject.featureId.toString()">{{customEndDateOptions}}</label>
                  </div>
                </div>
              </div>
              <div class="d-flex mt-2" id="datepickerId"
                *ngIf="probeObject.features | featuresCustomEndDateDisplayPipe:featuresObject.featureId:reloadPipe">
                <label class="customDate-lbl pl-3" for="field_logRange">Date</label>
                <mat-form-field>
                  <input class="form-control datePikerTextBox" matInput [matDatepicker]="customDate"
                    placeholder="Choose a Start date" readonly
                    [value]="probeObject.features | featuresSelectCustomDatePipe:featuresObject.featureId:reloadPipe"
                    (dateChange)="onChangeFeaturesEndDateForUpdate(featuresObject.featureId,defaultStartDate,customEndDateOptions,$any($event.target).value)">
                  <mat-datepicker-toggle matSuffix [for]="customDate"></mat-datepicker-toggle>
                  <mat-datepicker #customDate></mat-datepicker>
                </mat-form-field>
              </div>
              <div class="pl-3 pt-2">
                <span style="color: #919199;">{{probeObject?.features |
                  featuresExpireDateDisplayPipe:featuresObject.featureId:reloadPipe}}</span>
              </div>
            </div>
          </ng-template>
        </ng-template>
        <ng-template [ngIf]="currentFeatures==null || currentFeatures.length == 0 && allApiCall">
          <div class="row">
            <div class="col-md-12">
              <span>No feature available to configure</span>
            </div>
          </div>
        </ng-template>
      </div>
    </div>


    <!-- -------------------------------------------------------------- -->
    <!-- -------------------------------------------------------------- -->
    <!-- dialog buttons - start -->
    <!--Set Reminder Option-->
    <!--Download license-->
    <!-- close button -->
    <!-- accept button -->
    <!-- -------------------------------------------------------------- -->
    <!-- -------------------------------------------------------------- -->
    <div class="btn-group modal-footer-btn update-feature-footer">
      <div>
        <ng-template [ngIf]="setReminderOptionDisplayPermission">
          <div class="custom-control custom-checkbox mb-2">
            <input type="checkbox" class="custom-control-input" id="isReminder" name="isReminder"
              [checked]="probeObject?.reminder" [disabled]="isReminderDisabled"
              (change)="setIsReminder($any($event.target)?.checked)">
            <label class="custom-control-label" for="isReminder">Set Reminder Option</label>
          </div>
        </ng-template>
        <ng-template [ngIf]="isDownloadLicenceDisplay">
          <div class="custom-control custom-checkbox">
            <input type="checkbox" class="custom-control-input" id="isDownload" name="isDownload"
              (change)="onChangeDownloadLicense($any($event.target)?.checked)" [checked]="isDownload">
            <label class="custom-control-label" for="isDownload">Download license</label>
          </div>
        </ng-template>
      </div>
      <div>
        <button type="button" class="btn btn-sm btn-outline-secondary mr-3" id="deleteModelCancelButton"
          data-dismiss="modal" (click)="close()">{{basicModelConfig?.btnCancelText}}</button>

        <button type="button" class="btn btn-sm btn-orange" id="assignLicenseOkBtn"
          (click)="submitAssignFeature()">{{basicModelConfig?.btnOkText}}</button>
      </div>
    </div>
    <!-- -------------------------------------------------------------- -->
    <!--Set Reminder Option-->
    <!--Download license-->
    <!-- close button -->
    <!-- accept button -->
    <!-- dialog buttons - end -->
    <!-- -------------------------------------------------------------- -->

    <!-- -------------------------------------------------------------- -->
    <!-- updateFeatureForm - end -->
    <!-- -------------------------------------------------------------- -->

  </div>
</div>