import { ComponentFixture, TestBed, fakeAsync, tick, flush } from '@angular/core/testing';
import { ManualSyncComponent } from './manual-sync.component';
import { HttpResponse } from '@angular/common/http';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { ConfigInjectService } from 'src/app/shared/InjectService/config-inject.service';
import { of, throwError } from 'rxjs';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { SalesOrderSchedulerManualSyncTimeResponse } from 'src/app/model/SalesOrder/SalesOrderSchedulerManuleSyncTimeResponse.model';

describe('ManualSyncComponent', () => {
  let component: ManualSyncComponent;
  let fixture: ComponentFixture<ManualSyncComponent>;
  let salesOrderApiCallServiceMock: jasmine.SpyObj<SalesOrderApiCallService>;
  let exceptionServiceMock: jasmine.SpyObj<ExceptionHandlingService>;
  let activeModalMock: jasmine.SpyObj<NgbActiveModal>;
  let configInjectServiceMock: jasmine.SpyObj<ConfigInjectService>;

  beforeEach(async () => {
    salesOrderApiCallServiceMock = jasmine.createSpyObj('SalesOrderApiCallService', ['manualsync']);
    exceptionServiceMock = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    activeModalMock = jasmine.createSpyObj('NgbActiveModal', ['close']);
    configInjectServiceMock = jasmine.createSpyObj('ConfigInjectService', ['']);

    await TestBed.configureTestingModule({
      declarations: [ManualSyncComponent],
      providers: [
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServiceMock },
        { provide: ExceptionHandlingService, useValue: exceptionServiceMock },
        { provide: NgbActiveModal, useValue: activeModalMock },
        { provide: ConfigInjectService, useValue: configInjectServiceMock },
        commonsProviders(null)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ManualSyncComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    fixture.detectChanges();
    expect(component).toBeTruthy();
  });

  it('should close modal when accept is called', () => {
    component.accept();
    expect(activeModalMock.close).toHaveBeenCalledWith(true);
  });

  it('should not start sync if salesOrderSchedulerManualSyncTimeResponse is not provided', () => {
    spyOn<any>(component, 'startExponentialBackoffSync');
    component.salesOrderSchedulerManualSyncTimeResponse = undefined;

    fixture.detectChanges();

    expect(component['startExponentialBackoffSync']).not.toHaveBeenCalled();
  });

  it('should start sync if salesOrderSchedulerManualSyncTimeResponse is provided', () => {
    spyOn<any>(component, 'startExponentialBackoffSync');
    component.salesOrderSchedulerManualSyncTimeResponse = { id: 123 } as SalesOrderSchedulerManualSyncTimeResponse;

    fixture.detectChanges();

    expect(component['startExponentialBackoffSync']).toHaveBeenCalledWith(123);
  });

  it('should perform exponential backoff when syncing', fakeAsync(() => {
    // Setup mock response that always returns inProgress = false after the last call
    // to stop additional timers from being scheduled
    let callCount = 0;
    salesOrderApiCallServiceMock.manualsync.and.callFake(() => {
      callCount++;
      // After 6 calls, return false to stop scheduling
      const inProgress = callCount < 6;
      return of(new HttpResponse({ body: inProgress }));
    });

    // Initialize with response data
    component.salesOrderSchedulerManualSyncTimeResponse = { id: 123 } as SalesOrderSchedulerManualSyncTimeResponse;
    component.ngOnInit();

    // First call (1s)
    tick(1000);
    expect(salesOrderApiCallServiceMock.manualsync).toHaveBeenCalledWith(123);
    expect(salesOrderApiCallServiceMock.manualsync).toHaveBeenCalledTimes(1);

    // Second call (2s after first)
    tick(2000);
    expect(salesOrderApiCallServiceMock.manualsync).toHaveBeenCalledTimes(2);

    // Third call (4s after second)
    tick(4000);
    expect(salesOrderApiCallServiceMock.manualsync).toHaveBeenCalledTimes(3);

    // Fourth call (8s after third)
    tick(8000);
    expect(salesOrderApiCallServiceMock.manualsync).toHaveBeenCalledTimes(4);

    // Fifth call (16s after fourth)
    tick(16000);
    expect(salesOrderApiCallServiceMock.manualsync).toHaveBeenCalledTimes(5);

    // The 6th call will return inProgress = false, so no more scheduling should happen
    tick(32000);
    expect(salesOrderApiCallServiceMock.manualsync).toHaveBeenCalledTimes(6);
    expect(component.inProgress).toBeFalse();

    // Make sure all timers are flushed
    flush();
  }));

  it('should stop scheduling when inProgress becomes false', fakeAsync(() => {
    // First call returns true (in progress)
    const firstResponse = new HttpResponse({ body: true });
    // Second call returns false (completed)
    const secondResponse = new HttpResponse({ body: false });

    salesOrderApiCallServiceMock.manualsync.and.returnValues(
      of(firstResponse),
      of(secondResponse)
    );

    // Initialize with response data
    component.salesOrderSchedulerManualSyncTimeResponse = { id: 123 } as SalesOrderSchedulerManualSyncTimeResponse;
    component.ngOnInit();

    // First call (1s)
    tick(1000);
    expect(salesOrderApiCallServiceMock.manualsync).toHaveBeenCalledTimes(1);

    // Second call (2s after first)
    tick(2000);
    expect(salesOrderApiCallServiceMock.manualsync).toHaveBeenCalledTimes(2);

    // No more calls after that
    tick(10000);
    expect(salesOrderApiCallServiceMock.manualsync).toHaveBeenCalledTimes(2);

    // Verify inProgress is false
    expect(component.inProgress).toBeFalse();

    // Make sure all timers are flushed
    flush();
  }));

  it('should cap interval at 32 seconds for future calls', fakeAsync(() => {
    // Setup response that sets inProgress to false after the 7th call
    let callCount = 0;
    salesOrderApiCallServiceMock.manualsync.and.callFake(() => {
      callCount++;
      // After 7 calls, return false to stop scheduling
      const inProgress = callCount < 7;
      return of(new HttpResponse({ body: inProgress }));
    });

    // Initialize with response data
    component.salesOrderSchedulerManualSyncTimeResponse = { id: 123 } as SalesOrderSchedulerManualSyncTimeResponse;
    component.ngOnInit();

    // First call (1s)
    tick(1000);
    expect(salesOrderApiCallServiceMock.manualsync).toHaveBeenCalledTimes(1);

    // Second call (2s later)
    tick(2000);
    expect(salesOrderApiCallServiceMock.manualsync).toHaveBeenCalledTimes(2);

    // Third call (4s later)
    tick(4000);
    expect(salesOrderApiCallServiceMock.manualsync).toHaveBeenCalledTimes(3);

    // Fourth call (8s later)
    tick(8000);
    expect(salesOrderApiCallServiceMock.manualsync).toHaveBeenCalledTimes(4);

    // Fifth call (16s later)
    tick(16000);
    expect(salesOrderApiCallServiceMock.manualsync).toHaveBeenCalledTimes(5);

    // Sixth call (32s later)
    tick(32000);
    expect(salesOrderApiCallServiceMock.manualsync).toHaveBeenCalledTimes(6);

    // Seventh call (also 32s later, capped)
    tick(32000);
    expect(salesOrderApiCallServiceMock.manualsync).toHaveBeenCalledTimes(7);
    expect(component.inProgress).toBeFalse();

    // Make sure all timers are flushed
    flush();
  }));

  it('should handle API errors correctly', fakeAsync(() => {
    const error = new Error('API error');
    salesOrderApiCallServiceMock.manualsync.and.returnValue(throwError(() => error));

    spyOn<any>(component, 'clearAllTimeouts').and.callThrough();

    // Initialize with response data
    component.salesOrderSchedulerManualSyncTimeResponse = { id: 123 } as SalesOrderSchedulerManualSyncTimeResponse;
    component.ngOnInit();

    // First call (1s)
    tick(1000);

    // Should clear all timeouts
    expect(component['clearAllTimeouts']).toHaveBeenCalled();

    // Make sure all timers are flushed
    flush();
  }));

  it('should clear timeouts on component destruction', () => {
    spyOn<any>(component, 'clearAllTimeouts');
    component.ngOnDestroy();
    expect(component['clearAllTimeouts']).toHaveBeenCalled();
  });
});