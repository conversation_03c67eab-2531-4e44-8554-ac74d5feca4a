import { HttpClient } from '@angular/common/http';
import { Injectable, InjectionToken } from '@angular/core';


export const API_BASE_URL = new InjectionToken<string>('API_BASE_URL');
export const API_SSO_BASE_URL = new InjectionToken<string>('API_SSO_BASE_URL');
export const SSO_REGISTRATION_ID = new InjectionToken<string>('SSO_REGISTRATION_ID');
export const RDM_VERSION = new InjectionToken<string>('RDM_VERSION');
export const RDM_ENVIRONMENT = new InjectionToken<string>('RDM_ENVIRONMENT');
export const common_error_empty_filter = new InjectionToken<string>('common_error_empty_filter');
export const common_error_empty_start_date = new InjectionToken<string>('common_error_empty_start_date');
export const common_error_empty_end_date = new InjectionToken<string>('common_error_empty_end_date');
export const common_error_invalid_date = new InjectionToken<string>('common_error_invalid_date');
export const common_error_other = new InjectionToken<string>('common_error_other');

export function ConfigFactory(configService: ConfigService, file: string, property: string) {
    return configService.loadJSON(file)[property];
}

@Injectable()
export class ConfigService {

    public config: any;
    constructor(private http: HttpClient) {
    }

    loadJSON(filePath) {
        const json = this.loadTextFileAjaxSync(filePath, "application/json");
        return JSON.parse(json);
    }

    loadTextFileAjaxSync(filePath, mimeType) {
        const xmlhttp = new XMLHttpRequest();
        xmlhttp.open("GET", filePath, false);
        if (mimeType != null) {
            if (xmlhttp.overrideMimeType) {
                xmlhttp.overrideMimeType(mimeType);
            }
        }
        xmlhttp.send();
        if (xmlhttp.status == 200) {
            return xmlhttp.responseText;
        }
        else {
            return null;
        }
    }
}
