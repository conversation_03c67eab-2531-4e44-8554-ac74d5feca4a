import { SalesOrderProduct } from "./SalesOrderProduct.model";

export class TransferOrderSelectionProductResponse {
    id: number;
    salesOrderNumber: string;
    product: SalesOrderProduct;
    countryDisplayName: string;

    constructor(
        id: number,
        salesOrderNumber: string,
        product: SalesOrderProduct,
        countryDisplayName: string
    ) {
        this.id = id;
        this.salesOrderNumber = salesOrderNumber;
        this.product = product;
        this.countryDisplayName = countryDisplayName;
    }
}