@import '../../../src/assets/css/custom_style.css';

#loginscreen .bgimg {
  width: 100%;
  height: 100%;
  background-color: #7F7F84;
}

#loginscreen {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

#formContent {
  background: #fff;
  padding: 10px 70px 20px 70px;
  border-radius: 20px;
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.2);
}

#formContent p {
  font-size: 1.5em;
  margin-bottom: 15px;
  font-weight: bold;
  text-align: center;
}

#loginscreen .logoButton {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid #ccc;
  padding: 10px;
  width: 315px;
  cursor: pointer;
}

#loginscreen .btn {
  cursor: pointer;
  background: none;
  border: none;
  color: #333;
  font-size: 16px;
  font-weight: bold;
}


#loginscreen .cust-label {
  font-size: 1.5em;
  color: #777;
  margin-top: 10px;
  text-align: center;
}

#loginscreen .logo-echonous {
  height: 100px;
  width: 140px;
  display: block;
  margin: 0 auto;
  scale: 2;
  background: url('../../assets/echonous-logo-lg.png') no-repeat center center;
  background-size: contain;
  position: relative;
}

/* ANIMATIONS */

/* Simple CSS3 Fade-in-down Animation */

#loginscreen .fadeInDown {
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown;
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

@-webkit-keyframes fadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }

  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }

  100% {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}

/* Simple CSS3 Fade-in Animation */
@-webkit-keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@-moz-keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

#loginscreen .fadeIn {
  opacity: 0;
  -webkit-animation: fadeIn ease-in 1;
  -moz-animation: fadeIn ease-in 1;
  animation: fadeIn ease-in 1;

  -webkit-animation-fill-mode: forwards;
  -moz-animation-fill-mode: forwards;
  animation-fill-mode: forwards;

  -webkit-animation-duration: 1s;
  -moz-animation-duration: 1s;
  animation-duration: 1s;
}

#loginscreen .fadeIn.first {
  -webkit-animation-delay: 0.4s;
  -moz-animation-delay: 0.4s;
  animation-delay: 0.4s;
}

#loginscreen .fadeIn.second {
  -webkit-animation-delay: 0.6s;
  -moz-animation-delay: 0.6s;
  animation-delay: 0.6s;
}

#loginscreen .fadeIn.third {
  -webkit-animation-delay: 0.8s;
  -moz-animation-delay: 0.8s;
  animation-delay: 0.8s;
}

#loginscreen .fadeIn.fourth {
  -webkit-animation-delay: 1s;
  -moz-animation-delay: 1s;
  animation-delay: 1s;
}

/* Simple CSS3 Fade-in Animation */
#loginscreen .underlineHover:after {
  display: block;
  left: 0;
  bottom: -10px;
  width: 0;
  height: 2px;
  background-color: #56baed;
  content: "";
  transition: width 0.2s;
}

#loginscreen .underlineHover:hover {
  color: #0d0d0d;
}

#loginscreen .underlineHover:hover:after {
  width: 100%;
}