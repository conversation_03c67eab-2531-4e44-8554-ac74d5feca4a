import { TestBed } from '@angular/core/testing';

import { SalesOrderService } from './sales-order.service';
import { Subject } from 'rxjs';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { SalesOrderFilterAction } from 'src/app/model/SalesOrder/SalesFilterAction.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { SalesOrderSearchRequestBody } from 'src/app/model/SalesOrder/SalesOrderSearchRequestBody.model';
import { SalesOrderFaieldSearchRequestBody } from 'src/app/model/SalesOrder/SalesOrderFaieldSearchRequestBody.model';
import { SalesOrderFailedFilterAction } from 'src/app/model/SalesOrder/SalesOrderFailedFilterAction.model';
import { DetailSalesOrderResource, ListSalesOrderResource } from 'src/app/app.constants';

describe('SalesOrderService', () => {
  let service: SalesOrderService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(SalesOrderService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
describe('SalesOrderService', () => {
  let service: SalesOrderService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(SalesOrderService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should set and get country list', () => {
    const countries: CountryListResponse[] = [{ id: 1, country: 'Country1', languages: ['English'] }];
    service.setCountryList(countries);
    expect(service.getCountryList()).toEqual(countries);
  });

  it('should get sales order list refresh subject', () => {
    expect(service.getSalesOrderListRefreshSubject()).toBeInstanceOf(Subject);
  });

  it('should get sales order detail refresh subject', () => {
    expect(service.getSalesOrderDetailRefreshSubject()).toBeInstanceOf(Subject);
  });

  it('should get sales order list filter request parameter subject', () => {
    expect(service.getSalesOrderListFilterRequestParameterSubject()).toBeInstanceOf(Subject);
  });

  it('should call sales order list filter request parameter subject', () => {
    const salesOrderFilterAction = new SalesOrderFilterAction(new ListingPageReloadSubjectParameter(false, false, false, false, false), new SalesOrderSearchRequestBody(null, null, null, null, null, null, null, null));
    spyOn(service.getSalesOrderListFilterRequestParameterSubject(), 'next');
    service.callSalesOrderListFilterRequestParameterSubject(salesOrderFilterAction);
    expect(service.getSalesOrderListFilterRequestParameterSubject().next).toHaveBeenCalledWith(salesOrderFilterAction);
  });

  it('should call refresh page subject for list sales order resource', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(false, false, false, false, false);
    const salesOrderSearchRequestBodyApply = new SalesOrderSearchRequestBody(null, null, null, null, null, null, null, null);
    spyOn(service.getSalesOrderListFilterRequestParameterSubject(), 'next');
    spyOn(service.getSalesOrderListRefreshSubject(), 'next');
    service.callRefreshPageSubject(listingPageReloadSubjectParameter, ListSalesOrderResource, true, salesOrderSearchRequestBodyApply);
    expect(service.getSalesOrderListFilterRequestParameterSubject().next).toHaveBeenCalled();
  });

  it('should call refresh page subject for detail sales order resource', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(false, false, false, false, false);
    spyOn(service.getSalesOrderDetailRefreshSubject(), 'next');
    service.callRefreshPageSubject(listingPageReloadSubjectParameter, DetailSalesOrderResource, false, new SalesOrderSearchRequestBody(null, null, null, null, null, null, null, null));
    expect(service.getSalesOrderDetailRefreshSubject().next).toHaveBeenCalledWith(listingPageReloadSubjectParameter);
  });

  it('should get sales order failed list refresh subject', () => {
    expect(service.getSalesOrderFailedListRefreshSubject()).toBeInstanceOf(Subject);
  });

  it('should get sales order failed list filter request parameter subject', () => {
    expect(service.getSalesOrderFailedListFilterRequestParameterSubject()).toBeInstanceOf(Subject);
  });

  it('should call sales order failed list filter request parameter subject', () => {
    const salesOrderFailedFilterAction = new SalesOrderFailedFilterAction(new ListingPageReloadSubjectParameter(false, false, false, false, false), new SalesOrderFaieldSearchRequestBody(null));
    spyOn(service.getSalesOrderFailedListFilterRequestParameterSubject(), 'next');
    service.callSalesOrderFailedListFilterRequestParameterSubject(salesOrderFailedFilterAction);
    expect(service.getSalesOrderFailedListFilterRequestParameterSubject().next).toHaveBeenCalledWith(salesOrderFailedFilterAction);
  });

  it('should call refresh page subject for failed order', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(false, false, false, false, false);
    spyOn(service.getSalesOrderFailedListFilterRequestParameterSubject(), 'next');
    spyOn(service.getSalesOrderFailedListRefreshSubject(), 'next');
    service.callRefreshPageSubjectForFaildOrder(listingPageReloadSubjectParameter, true);
    expect(service.getSalesOrderFailedListFilterRequestParameterSubject().next).toHaveBeenCalled();
  });
});
