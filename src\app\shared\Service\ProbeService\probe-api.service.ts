import { DatePipe } from '@angular/common';
import { HttpClient, HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { Observable, firstValueFrom, lastValueFrom } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { FeatureLicenseEmptySasUrl, ProbeTypeMessage } from 'src/app/app.constants';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { ConfigMappingRequest } from 'src/app/model/probe/ConfigMappingRequest.model';
import { DeviceListByProbeIdPagableResponse } from 'src/app/model/probe/DeviceListByProbeIdPagableResponse.model';
import { AddUpdateMultiProbeResponse } from 'src/app/model/probe/multiProbe/AddUpdateMultiProbeResponse.model';
import { ProbeFeatureRequest } from 'src/app/model/probe/multiProbe/ProbeFeatureRequest.model';
import { ProbeConnectionHistoryRequest } from 'src/app/model/probe/ProbeConnectionHistoryRequest.model';
import { ProbeDetailPageResponse } from 'src/app/model/probe/ProbeDetailPageResponse.model';
import { ProbeDetailWithConfig } from 'src/app/model/probe/ProbeDetailWithConfig.model';
import { ProbeDownloadCSVParameterRequest } from 'src/app/model/probe/ProbeDownloadCSVParameterRequest.model';
import { ProbeDownloadCSVRequest } from 'src/app/model/probe/ProbeDownloadCSVRequest.model';
import { ProbeFeatureHistoryListResponse } from 'src/app/model/probe/ProbeFeatureHistoryListResponse.model';
import { ProbeFeatureResponse } from 'src/app/model/probe/ProbeFeatureResponse.model';
import { ProbeHistoryPagableResponse } from 'src/app/model/probe/ProbeHistoryPagableResponse.model';
import { ProbeListFilterRequestBody } from 'src/app/model/probe/ProbeListFilterRequestBody.model';
import { ProbePresetResponse } from 'src/app/model/probe/ProbePresetResponse.model';
import { ProbeSearchParameterRequest } from 'src/app/model/probe/ProbeSearchParameterRequest.model';
import { ProbeTypeResponse } from 'src/app/model/probe/probeTypeResponse.model';
import { ProbeTypeUpdateRequest } from 'src/app/model/probe/ProbeTypeUpdateRequest';
import { BasicSalesOrderDetailResponse } from 'src/app/model/SalesOrder/BasicSalesOrderDetailResponse.model';
import { SalesOrderPdfLetterResponse } from 'src/app/model/SalesOrder/SalesOrderPdfLetterResponse';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { CommonsService } from '../../util/commons.service';
import { DownloadService } from '../../util/download.service';
import { createRequestOption } from '../../util/request-util';

@Injectable({
  providedIn: 'root'
})
export class ProbeApiService {

  public probesUrl = this.configInjectService.getServerApiUrl() + 'api/probes';

  constructor(private configInjectService: ConfigInjectService,
    private http: HttpClient,
    private exceptionService: ExceptionHandlingService,
    private commonsService: CommonsService,
    private toste: ToastrService,
    private downloadService: DownloadService,
    private datePipe: DatePipe) { }


  public async getprobeTypeResponseList(isAddDefaultOptions: boolean): Promise<Array<ProbeTypeResponse>> {
    let probeTypeResponse: Array<ProbeTypeResponse> = [];
    try {
      const res: HttpResponse<Array<ProbeTypeResponse>> = await firstValueFrom(this.http.get<Array<ProbeTypeResponse>>(`${this.probesUrl}/types/config`, { observe: 'response' }));
      probeTypeResponse = res.body;
      if (isAddDefaultOptions) {
        probeTypeResponse.splice(0, 0, new ProbeTypeResponse(-1, ProbeTypeMessage, ProbeTypeMessage, null, null, null));
      }
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        this.exceptionService.customErrorMessage(error);
      }
      probeTypeResponse = [];
    }
    return probeTypeResponse;
  }


  public async getFeaturesList(): Promise<Array<ProbeFeatureResponse>> {
    let featuresResponseList: Array<ProbeFeatureResponse> = [];
    try {
      const res: HttpResponse<Array<ProbeFeatureResponse>> = await firstValueFrom(this.http.get<Array<ProbeFeatureResponse>>(`${this.probesUrl}/features`, { observe: 'response' }));
      featuresResponseList = res.body;
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        this.exceptionService.customErrorMessage(error);
      }
      featuresResponseList = [];
    }
    return featuresResponseList;
  }

  /**
    * Get Probe Preset
    * 
    * <AUTHOR>
    * @returns 
    */
  public async getPresetsList(): Promise<Array<ProbePresetResponse>> {
    let presetsResponseList: Array<ProbePresetResponse> = [];
    try {
      const res: HttpResponse<Array<ProbePresetResponse>> = await firstValueFrom(this.http.get<Array<ProbePresetResponse>>(`${this.probesUrl}/presets`, { observe: 'response' }));
      presetsResponseList = res.body;
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        this.exceptionService.customErrorMessage(error);
      }
      presetsResponseList = [];
    }
    return presetsResponseList;
  }

  /**
   * Get Probe Detail
   * 
   * <AUTHOR>
   * @param probeId 
   * @returns 
   */
  public async getAsyncProbeDetailInfo(probeId: number): Promise<ProbeDetailWithConfig> {
    try {
      const res: HttpResponse<ProbeDetailWithConfig> = await firstValueFrom(this.http.get<ProbeDetailWithConfig>(`${this.probesUrl}/${probeId}`, { observe: 'response' }));
      return res.body;
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        this.exceptionService.customErrorMessage(error);
      }
      return null;
    }
  }

  /**
   * Save Multiple Probe
   * 
   * <AUTHOR>
   * @param probeFeatureRequest 
   * @returns 
   */
  public saveMutiprobe(probeFeatureRequest: ProbeFeatureRequest): Observable<HttpResponse<AddUpdateMultiProbeResponse>> {
    return this.http.post<AddUpdateMultiProbeResponse>(this.probesUrl, probeFeatureRequest, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Get Probe Detail Information
   * 
   * <AUTHOR>
   * @param probeId 
   * @returns 
   */
  public getProbeDetailInfo(probeId: number): Observable<HttpResponse<ProbeDetailWithConfig>> {
    return this.http.get<ProbeDetailWithConfig>(this.probesUrl + '/' + probeId, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Assign features to prob
   * 
   * <AUTHOR>
   * @param probeIds 
   * @param ConfigMappingRequest 
   * @returns 
   */
  public updateProbeFeatures(probeIds: number, configMappingRequest: ConfigMappingRequest): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.put<SuccessMessageResponse>(this.probesUrl + '/license/' + probeIds, configMappingRequest, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }


  /**
   * Get History Details of probe history
   * 
   * <AUTHOR>
   * @param probeId 
   * @param historyId 
   * @returns 
   */
  public getProbeHistoryDetail(licenseHistoryId: number, probeId: number): Observable<HttpResponse<ProbeFeatureHistoryListResponse>> {
    return this.http.get<ProbeFeatureHistoryListResponse>(this.probesUrl + '/' + probeId + '/config-history/' + licenseHistoryId, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }


  /**
   * Get Probe History
   * 
   * <AUTHOR>
   * @param probeId 
   * @param req 
   * @returns 
   */
  public getProbeHistory(probeId: number, req?): Observable<HttpResponse<ProbeHistoryPagableResponse>> {
    const options = createRequestOption(req);
    return this.http.get<ProbeHistoryPagableResponse>(this.probesUrl + '/' + probeId + '/config-history', { params: options, observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Get Probe List 
   * 
   * <AUTHOR>
   * @param filterData 
   * @param req 
   * @returns 
   */
  public getAllProbes(filterData: ProbeListFilterRequestBody, req: ProbeSearchParameterRequest): Observable<HttpResponse<ProbeDetailPageResponse>> {
    const options = createRequestOption(req);
    return this.http.post<ProbeDetailPageResponse>(this.probesUrl + '/search', filterData, { params: options, observe: 'response' });
  }

  /**
   * Prepare Filter data
   * 
   * <AUTHOR>
   * @param currentValue 
   * @param isMultiValue 
   * @returns 
   */
  public getFilterValue(currentValue: any, isMultiValue: boolean): any {
    let emptyValue = isMultiValue ? [] : null;
    return this.commonsService.checkValueIsNullOrEmpty(currentValue) ? emptyValue : currentValue;
  }

  /**
   * Association Probe With SalesOrder
   * 
   * <AUTHOR>
   * @param probeIds 
   * @param basicSalesOrderDetailResponse 
   * @returns 
   */
  public associationProbeWithSalesOrder(probeIds: number[], basicSalesOrderDetailResponse: BasicSalesOrderDetailResponse): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.put<SuccessMessageResponse>(this.probesUrl + '/soDetails/' + probeIds, basicSalesOrderDetailResponse, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Probe Type Update
   * 
   * @param probeIds 
   * @param probeType 
   * @returns 
   */
  public probeTypeUpdate(probeIds: number[], probeTypeUpdateRequest: ProbeTypeUpdateRequest): Observable<HttpResponse<SuccessMessageResponse>> {
    const options = createRequestOption(probeTypeUpdateRequest);
    return this.http.put<SuccessMessageResponse>(this.probesUrl + '/type/' + probeIds, probeTypeUpdateRequest, { observe: 'response', params: options }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Disable Product Status For Probe
   * @param probeIds 
   * @returns 
   */
  public disableProductStatusForProbe(probeIds: Array<number>): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.put<SuccessMessageResponse>(this.probesUrl + '/status/disable/' + probeIds, null, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * RMA Product Status For Probe
   * @param probeIds 
   * @returns 
   */
  public rmaProductStatusForProbe(probeIds: Array<number>): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.put<SuccessMessageResponse>(this.probesUrl + '/status/rma/' + probeIds, null, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * Generate CSV File For Probe
   * 
   * <AUTHOR>
   * @param probeDownloadCSVRequest 
   * @returns 
   */
  public generateCSVFileForProbe(probeDownloadCSVRequest: ProbeDownloadCSVRequest, req: ProbeDownloadCSVParameterRequest): Observable<HttpResponse<any>> {
    const options = createRequestOption(req);
    return this.http.post<any>(this.probesUrl.concat('/generateCSV'), probeDownloadCSVRequest, { params: options, observe: 'response' });
  }

  /**
   * Generate CSV File For Probe Historical Connection
   * 
   * <AUTHOR>
   * @param ProbeConnectionHistoryRequest 
   * @returns 
   */
  public generateCSVFileForProbeHistoricalConnection(probeConnectionHistoryRequest: ProbeConnectionHistoryRequest): Observable<HttpResponse<any>> {
    return this.http.post<any>(this.probesUrl + '/connectionHistory/generateCSV', probeConnectionHistoryRequest, { observe: 'response' });
  }

  /**
   * Download CSV File For Probe
   * 
   * <AUTHOR>
   * @param fileName 
   * @returns 
   */
  public downloadCSVFileForProbe(fileName: string): Observable<HttpResponse<any>> {
    return this.http.get<any>(this.probesUrl.concat('/downloadProbesData/').concat(fileName), { responseType: 'blob' as 'json' });
  }

  /**
   * Probe Lock/Unlock API
   * 
   * <AUTHOR>
   * @param listOfId 
   * @param lockState 
   */
  public async updateLockState(listOfId: Array<number>, lockState: boolean): Promise<boolean> {
    let lockUnlockApi = this.http.put(this.probesUrl + '/state/' + listOfId + '?locked=' + lockState, {}, { observe: 'response' });
    try {
      let response: HttpResponse<any> = await lastValueFrom(lockUnlockApi);
      this.toste.success(response?.body?.message);
      return true;
    } catch (error: any) {
      this.exceptionService.customErrorMessage(error);
      return false;
    }
  }

  /**
 * Probe Enable/Disable API
 * 
 * <AUTHOR>
 * @param listOfId 
 * @param status 
 */
  public editEnableDisableProbe(listOfId: Array<number>, status: boolean): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.put<SuccessMessageResponse>(this.probesUrl + '/edit/' + listOfId + '?enable=' + status, null, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
 * Delete Probes API
 * 
 * <AUTHOR>
 * @param probeIds 
 */
  public deleteProbes(probeIds: number[]): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.delete<SuccessMessageResponse>(this.probesUrl + '/' + probeIds, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
 * get Probetype Display Name 
 * 
 * <AUTHOR>
 * @return 
 */
  public getProbeTypesList(): Observable<HttpResponse<any>> {
    return this.http.get<any>(this.probesUrl + '/types', { observe: 'response' });
  }

  /**
* Probe Connection History
* 
* <AUTHOR>
* @return 
*/
  public getDeviceListByProbeId(probeId: number, req?: any): Observable<HttpResponse<DeviceListByProbeIdPagableResponse>> {
    const options = createRequestOption(req);
    return this.http.post<DeviceListByProbeIdPagableResponse>(this.probesUrl + '/' + probeId + '/devices', {}, { params: options, observe: 'response' });
  }

  /**
   * Get SasUri of probe feature license
   * @param probeId 
   * @returns 
   */
  public getSasUriofFeatureLicense(probeId: number[]) {
    return this.http.get(this.probesUrl + '/' + probeId + "/license/download", { observe: 'response' })
  }

  /**
   * 
   * @param probeId 
   */
  public async dowloadSasUriofFeatureLicenseAsync(probeId: number[], resource: string): Promise<void> {
    this.downloadService.setLoading(true, resource);
    try {
      const res: HttpResponse<any> = await firstValueFrom(
        this.http.get<HttpResponse<any>>(`${this.probesUrl}/license/download/${probeId}`, { observe: 'response' })
      );

      if (res.status === 200 && res.body.length > 0) {
        const urlList = res.body
          .filter(url => !isNullOrUndefined(url['sasUri']) && url['sasUri'].trim() !== "")
          .map(url => url['sasUri']);

        const fileName = this.datePipe.transform(new Date(), 'MMM d, y, h:mm:ss a');
        if (urlList.length > 0) {
          await this.downloadService.dowloadInZipFile(urlList, fileName, FeatureLicenseEmptySasUrl, resource);
        } else {
          this.downloadService.setLoading(false, resource);
        }
      } else {
        this.toste.warning(FeatureLicenseEmptySasUrl);
        this.downloadService.setLoading(false, resource);
      }
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        this.exceptionService.customErrorMessage(error);
      }
      this.downloadService.setLoading(false, resource);
    }
  }

  /**
   * Download SalesOrderPdfLetter
   * @returns 
   */
  public downloadSalesOrderPdfLetter(salesOrderId: string): Observable<HttpResponse<SalesOrderPdfLetterResponse>> {
    return this.http.get<SalesOrderPdfLetterResponse>(this.probesUrl + "/pdf/" + salesOrderId, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }
}
