<!------------------------------------------->
<!--loading start-->
<!------------------------------------------->
<div class="ringLoading" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!------------------------------------------->
<!--loading end-->
<!------------------------------------------->
<!------------------------------------------->
<!--Body start-->
<!------------------------------------------->

<body class="bg-white">
    <!--container start-->
    <div class="container-fluid">
        <!--Row start-->
        <div class="row">
            <div class="col-md-12">
                <!--header  Start-->
                <div class="row">
                    <label class="col-md-6 h5-tag">User Detail</label>
                    <div class="ml-auto col-md-6 text-right mb-3">
                        <!--Back button start-->
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary mr-3" (click)="back()"
                                id="updateUserBackBtn"><i class="fa fa-reply"
                                    aria-hidden="true"></i>&nbsp;&nbsp;Back</button>

                        </div>
                        <!--Back button end-->
                    </div>
                </div>
                <!--header  end-->
                <!--Body start-->
                <div class="row">
                    <div class="col-md-12">
                        <!--card start-->
                        <div class="card">
                            <!--Card body start-->
                            <div class="card-body">
                                <div class="card shadow">
                                    <div class="card-body">
                                        <!--last logged in time start-->
                                        <div class="row">
                                            <label class="col-md-6 h6-tag">Last Logged in {{userResponse?.lastLoggedIn |
                                                date:'MM/dd/yyyy, h:mm:ss a'}}</label>
                                        </div>
                                        <!--last logged in time end-->
                                        <hr>
                                        <!--Form start-->
                                        <form [formGroup]="form" *ngIf="isRefreshForm">
                                            <div class="row">
                                                <!--First name start-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label><strong class="">First Name</strong></label>
                                                        <input type="text" class="form-control" name="firstName"
                                                            formControlName="firstName" required>

                                                        <div
                                                            *ngIf="(form.get('firstName').touched || form.get('firstName').dirty) && form.get('firstName').invalid ">
                                                            <div *ngIf="form.get('firstName').errors['required']">
                                                                <p class="alert-color">Firstname required(*)</p>
                                                            </div>
                                                            <div
                                                                *ngIf="form.get('firstName').errors['pattern'] && !(form.get('firstName').errors['maxlength'])">
                                                                <p class="alert-color">Enter Only Character</p>
                                                            </div>
                                                            <!---Charater limits-->
                                                            <div *ngIf="form.get('firstName').errors['maxlength']">
                                                                <p class="alert-color">
                                                                    {{textBoxMaxCharactersAllowedMessage}}</p>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                                <!--First name end-->
                                                <!--last name start-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label><strong class="">Last Name</strong></label>
                                                        <input type="text" class="form-control" name="lastName"
                                                            formControlName="lastName" required>

                                                        <div
                                                            *ngIf="(form.get('lastName').touched || form.get('lastName').dirty) && form.get('lastName').invalid ">
                                                            <div *ngIf="form.get('lastName').errors['required']">
                                                                <p class="alert-color">Lastname required(*)</p>
                                                            </div>
                                                            <div
                                                                *ngIf="form.get('lastName').errors['pattern'] && !(form.get('lastName').errors['maxlength'])">
                                                                <p class="alert-color">Enter Only Character</p>
                                                            </div>
                                                            <!---Charater limits-->
                                                            <div *ngIf="form.get('lastName').errors['maxlength']">
                                                                <p class="alert-color">
                                                                    {{textBoxMaxCharactersAllowedMessage}}</p>
                                                            </div>
                                                        </div>

                                                    </div>
                                                </div>
                                                <!--First name end-->
                                                <!--email start-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label><strong class="">E-mail</strong></label>
                                                        <input type="text" class="form-control" name="email"
                                                            id="userEmail" formControlName="email" readonly>
                                                    </div>
                                                </div>
                                                <!--email end-->
                                                <!--Login Name start-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label><strong class="">Login Name</strong></label>
                                                        <input type="text" class="form-control" name="login"
                                                            [(value)]="login" readonly>
                                                    </div>
                                                </div>
                                                <!--Login Name end-->
                                                <!--Role start-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label for="update_user_role"><strong
                                                                class="">Role</strong></label>
                                                        <ng-multiselect-dropdown name="userRoles" [placeholder]="''"
                                                            (click)="onItemSelectValidation('userRoles')"
                                                            id="update_user_role" formControlName="userRoles"
                                                            [settings]="roleSetting" [data]="userRolesSelection">
                                                        </ng-multiselect-dropdown>
                                                        <!-- validation error start -->
                                                        <div
                                                            *ngIf="(form.get('userRoles').touched || form.get('userRoles').dirty) && form.get('userRoles').invalid">
                                                            <!-- required selection error strat -->
                                                            <div *ngIf="form.get('userRoles').errors['required']">
                                                                <p class="alert-color">Please select role</p>
                                                            </div>
                                                            <!-- required selection error end -->
                                                        </div>
                                                        <!-- validation error end -->
                                                    </div>
                                                </div>
                                                <!--Role end-->
                                                <!--Role start-->
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label><strong class="">Country</strong></label>
                                                        <ng-multiselect-dropdown name="country" [placeholder]="''"
                                                            (click)="onItemSelectValidation('country')"
                                                            formControlName="country" [settings]="countrySetting"
                                                            [data]="countryList">
                                                        </ng-multiselect-dropdown>
                                                        <div
                                                            *ngIf="(form.get('country').touched || form.get('country').dirty)  && form.get('country').invalid">
                                                            <p class="alert-color">Atleast one country must be selected
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--Role end-->
                                            </div>
                                        </form>
                                        <!--form end-->
                                    </div>
                                </div>
                                <!--Hr line start-->
                                <hr class="mt-4">
                                <!--Hr line end-->
                                <!--Update user start-->
                                <div class="d-flex justify-content-between align-content-center">
                                    <div class="ml-auto text-right">
                                        <button class="btn btn-orange btn-sm" (click)="updateAccountConfirmation()"
                                            id="updateUserBtn" [disabled]="!form.valid">Update</button>
                                    </div>
                                </div>
                                <!--Update user end-->


                            </div>
                            <!--Card body end-->
                        </div>
                        <!--card end-->
                    </div>


                </div>
                <!--Body end-->
            </div>
        </div>
        <!--Row end-->
    </div>
    <!--container end-->
</body>
<!------------------------------------------->
<!--Body end-->
<!------------------------------------------->