import { DatePipe } from '@angular/common';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { INTERNAL_SERVER_ERROR, PROBE_ALREADY_EXIEST, SALES_ORDER_DELETED_OR_USER_DEFINED, VIOLATION_UNIQUE_KEY_CONSTRAINT } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { SalesOrderDetailResponse } from 'src/app/model/SalesOrder/SalesOrderDetailResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { BooleanKeyValueMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/BooleanKeyValueMappingDisplayNamePipe.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { KeyValueMappingServiceService } from 'src/app/shared/util/key-value-mapping-service.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { SalesOrderDetailComponent } from './sales-order-detail.component';
import { TransferOrderSelectionComponent } from '../../TransferOrder/transfer-order-selection/transfer-order-selection.component';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { TransferOrderModuleComponent } from '../../TransferOrder/transfer-order-module/transfer-order-module.component';
import { ReactiveFormsModule } from '@angular/forms';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';

describe('SalesOrderDetailComponent', () => {
  let component: SalesOrderDetailComponent;
  let fixture: ComponentFixture<SalesOrderDetailComponent>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let exceptionHandlingService: ExceptionHandlingService;
  let salesOrderApiCallServicespy: jasmine.SpyObj<SalesOrderApiCallService>;
  let probeApiCallServiceSpy: jasmine.SpyObj<ProbeApiService>;



  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    salesOrderApiCallServicespy = jasmine.createSpyObj('SalesOrderApiCallService', ['deleteSalesOrder', 'getSalesOrderDetails']);
    probeApiCallServiceSpy = jasmine.createSpyObj('ProbeApiService', ['getprobeTypeResponseList', 'saveMutiprobe']);


    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    await TestBed.configureTestingModule({
      declarations: [SalesOrderDetailComponent, BooleanKeyValueMappingDisplayNamePipe, TransferOrderModuleComponent, TransferOrderSelectionComponent],
      imports: [NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule],
      providers: [
        SessionStorageService,
        HidePermissionNamePipe,
        PrintListPipe,
        KeyValueMappingServiceService,
        EnumMappingDisplayNamePipe,
        DatePipe,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: ProbeApiService, useValue: probeApiCallServiceSpy },
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServicespy },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(SalesOrderDetailComponent);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call toastrService.error with INTERNAL_SERVER_ERROR', () => {
    // **Arrange: Mock dependencies and set up the test environment**
    // Create a mock HttpErrorResponse to simulate a server error
    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });

    // Mock the API call to return an error response
    salesOrderApiCallServicespy.getSalesOrderDetails?.and.returnValue(throwError(() => mockError));

    // Spy on the `CustomerrorMessage` method of `exceptionHandlingService` to verify error handling
    spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

    // **Act: Trigger the component's logic**
    // Initialize the component, which invokes `getSalesOrderDetails` internally
    component.ngOnInit();

    // **Assert: Verify error handling behavior**
    // Ensure the `CustomerrorMessage` method was called to process the error
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();

    // Ensure the toastr service displays an error message for `INTERNAL_SERVER_ERROR`
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
  });


  it('should call toastrService.info with getProbeConfigGroupDetails for empty response', () => {
    // **Arrange: Mock dependencies and set up the test environment**
    // Mock the API call to return an empty response with HTTP status 205
    salesOrderApiCallServicespy.getSalesOrderDetails?.and.returnValue(
      of(new HttpResponse<SalesOrderDetailResponse>({
        body: null,// Simulate an empty response body
        status: 205, // Mock HTTP status indicating a specific response
        statusText: 'OK',
      }))
    );

    // **Act: Trigger the component's logic**
    // Initialize the component, which invokes `getSalesOrderDetails` internally
    component.ngOnInit();

    // **Assert: Verify the behavior for empty responses**
    // Ensure the toastr service displays an informational message for empty responses
    expect(toastrServiceMock.info).toHaveBeenCalledWith(SALES_ORDER_DELETED_OR_USER_DEFINED);
  });

  describe('callMultiprobeSaveAPI - Error Handling Scenarios', () => {

    it('should handle error 409 with unique key constraint violation', () => {
      // Arrange: Simulate a 409 Conflict error with a unique key constraint violation message
      probeApiCallServiceSpy.saveMutiprobe?.and.returnValue(
        throwError(() =>
          new HttpErrorResponse({
            status: 409,
            statusText: 'Conflict',
            error: { errorMessage: VIOLATION_UNIQUE_KEY_CONSTRAINT },
          })
        )
      );

      // Act: Call the method under test
      component.callMultiprobeSaveAPI(null);

      // Assert: Verify that the correct error message is shown to the user
      expect(toastrServiceMock.error).toHaveBeenCalledWith(PROBE_ALREADY_EXIEST);
    });

    it('should handle error 409 with a generic error message', () => {
      // Arrange: Simulate a 409 Conflict error with a different error message
      probeApiCallServiceSpy.saveMutiprobe?.and.returnValue(
        throwError(() =>
          new HttpErrorResponse({
            status: 409,
            statusText: 'Conflict',
            error: { errorMessage: 'Some other error message' },
          })
        )
      );

      // Act: Call the method under test
      component.callMultiprobeSaveAPI(null);

      // Assert: Verify that the generic error message is displayed as info
      expect(toastrServiceMock.info).toHaveBeenCalledWith('Some other error message');
    });

    it('should handle error 412 (Precondition Failed)', () => {
      // Arrange: Simulate a 412 Precondition Failed error
      probeApiCallServiceSpy.saveMutiprobe?.and.returnValue(
        throwError(() =>
          new HttpErrorResponse({
            status: 412,
            statusText: 'Precondition Failed',
            error: { errorMessage: 'The selected country is null. Either its not valid or no country is associated with the requested order in the RDM.' }
          })
        )
      );

      // Act: Call the method under test
      component.callMultiprobeSaveAPI(null);

      // Assert: Verify that the precondition failed message is displayed
      expect(toastrServiceMock.info).toHaveBeenCalledWith("The selected country is null. Either its not valid or no country is associated with the requested order in the RDM.");
    });

    it('should handle server-side errors (500)', () => {
      // Arrange: Simulate a 500 Internal Server Error
      probeApiCallServiceSpy.saveMutiprobe?.and.returnValue(
        throwError(() =>
          new HttpErrorResponse({
            status: 500,
            statusText: 'Internal Server Error',
          })
        )
      );

      // Spy on the `customErrorMessage` method to ensure error logging/handling is invoked
      spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

      // Act: Call the method under test
      component.callMultiprobeSaveAPI(null);

      // Assert: Verify that the error is processed and appropriate feedback is provided
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled(); // Ensure error is logged/processed
      expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR); // Show generic error message
    });

    // Test redirect to Transfer Order Selection page
    it("Redirect to Transfer Order Selection Page", async () => {
      // Toggle to show transfer order page and hide sales order page
      component.transferOrderSelectionToggle(false, true);

      // Update the view
      fixture.detectChanges();

      // Wait for async tasks
      await fixture.whenStable();

      // Check sales order page is hidden
      expect(component.salesOrderDetailPageDiplay).toBeFalsy();

      // Check transfer order page is shown
      expect(component.transferOrderSelectionDisaplay).toBeTruthy();
    });
  });
});
