import { Pageable } from "./common/pageable.model";
import { PageResponse } from "./common/PageResponse.model";
import { Sort } from "./common/sort.model";
import { IDevice } from "./device.model";

export class DeviceListResponse extends PageResponse {
    content: Array<IDevice>;


    constructor( //NOSONAR
        pageable: Pageable,
        totalPages: number,
        last: boolean,
        totalElements: number,
        numberOfElements: number,
        first: boolean,
        sort: Sort,
        size: number,
        number: number,
        empty: boolean,
        content: Array<IDevice>
    ) {
        super(
            pageable,
            totalPages,
            last,
            totalElements,
            numberOfElements,
            first,
            sort,
            size,
            number,
            empty
        );
        this.content = content;
    }
}
