import { Pipe, PipeTransform } from '@angular/core';
import { AuditDataTypeEnum } from '../../enum/Audit/AuditDataTypeEnum.enum';
import { BooleanKeyValueMapping } from 'src/app/model/common/BooleanKeyValueMapping.model';
import { EnumMapping } from 'src/app/model/common/EnumMapping.model';
import { KeyValueMappingServiceService } from '../../util/key-value-mapping-service.service';
import { AuditDetailResponse } from 'src/app/model/Audit/auditDetailResponse.model';
import { isNullOrUndefined } from 'is-what';
import { PrintListPipe } from '../printList.pipe';
import { DateTimeDisplayFormat } from 'src/app/app.constants';
import { DatePipe } from '@angular/common';

@Pipe({
    name: 'auditDataTypePipe'
})
export class AuditDataTypePipe implements PipeTransform {



    constructor(private keyValueMappingServiceService: KeyValueMappingServiceService,
        private printListPipe: PrintListPipe, private datePipe: DatePipe) { }

    transform(value: string, auditDetail: AuditDetailResponse, booleanKeyValueMap: Map<string, Array<BooleanKeyValueMapping>>,
        enumKeyValueMap: Map<string, Array<EnumMapping>>): any {
        switch (auditDetail.type) {

            case AuditDataTypeEnum.TYPE_STRING:
                if (auditDetail.multiple) {
                    return isNullOrUndefined(value) ? null : this.printListPipe.transform(value);
                }
                return value;

            case AuditDataTypeEnum.TYPE_BOOLEAN:
                return this.keyValueMappingServiceService.getBooleanValue(value, auditDetail.field, booleanKeyValueMap);

            case AuditDataTypeEnum.TYPE_ENUM:
                return this.keyValueMappingServiceService.getEnumKeyValue(value, auditDetail.field, enumKeyValueMap);

            case AuditDataTypeEnum.TYPE_DATE:
                return isNullOrUndefined(value) ? null : this.datePipe.transform(value, DateTimeDisplayFormat);

            default:
                return value

        }


    }



}
