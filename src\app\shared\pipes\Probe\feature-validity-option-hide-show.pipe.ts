import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { FeaturesBaseResponse } from 'src/app/model/probe/FeaturesBaseResponse.model';
import { ProbeFeatureResponse } from 'src/app/model/probe/ProbeFeatureResponse.model';
import { ValidityEnum } from '../../enum/ValidityEnum.enum';

@Pipe({
    name: 'featureValidityOptionHideShow'
})
export class FeatureValidityOptionHideShowPipe implements PipeTransform {

    transform(featuresBaseResponse: FeaturesBaseResponse | ProbeFeatureResponse, validityEnum: ValidityEnum): boolean {
        if (!isNullOrUndefined(featuresBaseResponse) &&
            !isNullOrUndefined(featuresBaseResponse.partNumbers) &&
            featuresBaseResponse.partNumbers.length > 0 &&
            !isNullOrUndefined(validityEnum)) {
            let featuresBaseResponseFilter = featuresBaseResponse.partNumbers.filter(obj => ValidityEnum[obj.validity] == validityEnum);
            return featuresBaseResponseFilter.length == 1;
        }
        return false;
    }

}
