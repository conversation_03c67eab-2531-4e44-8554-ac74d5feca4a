import { CommonModule } from '@angular/common';
import { HttpBackend, HttpClient, HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { commonsProviders, testDropdownInteraction, testErrorHandling, testToggleFilter } from '../Tesing-Helper/test-utils';
import { DownloadVideoFileError, INTERNAL_SERVER_ERROR, ITEMS_PER_PAGE, UpdateJsonSuccessMessage, UpdateVideoSuccessMessage, UploadVideoSuccessMessage } from '../app.constants';
import { ConfirmDialogService } from '../FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { SuccessMessageResponse } from '../model/common/SuccessMessageResponse.model';
import { DownloadJsonResponse, SubTitleInformation, VideoInformation } from '../model/video/download-json-response.model';
import { DownloadVideoResponse } from '../model/video/download-video-response.model';
import { UploadVideoRequest } from '../model/video/upload-video-request.model';
import { SubTitles, UploadVideoResponse } from '../model/video/upload-video-response.model';
import { VideoListPagableResponse } from '../model/video/videoListPagableResponse.model';
import { ExceptionHandlingService } from '../shared/ExceptionHandling.service';
import { MessageService } from '../shared/Message.service';
import { SSOLoginService } from '../shared/Service/SSO/ssologin.service';
import { AuthJwtService } from '../shared/auth-jwt.service';
import { common_error_empty_end_date, common_error_empty_filter, common_error_empty_start_date, common_error_invalid_date, common_error_other } from '../shared/config';
import { PermissionService } from '../shared/permission.service';
import { CommonsService } from '../shared/util/commons.service';
import { VideoService } from '../shared/videoservice/video.service';
import { UploadJsonDialogComponent } from './upload-json-dialog/upload-json-dialog.component';
import { VideoComponent } from './video.component';
import { SoftwareBuildApiCallService } from '../FeatureModule/Software-Build/software-build-services/software-api-call/software-build-api-call.service';

// Helper function for dialog interactions
function handleDeleteDialog(action: 'accept' | 'dismiss'): void {
  const dialogElement = document.querySelector('app-delete-video-json-confirmation-dialog');
  const dialogComponent = window['ng'].getComponent(dialogElement);

  const buttonId = action === 'accept' ? '#deleteModelOkButton' : '#deleteModelDismissButton';
  const actionBtn = document.querySelector<HTMLElement>(buttonId);

  spyOn(dialogComponent, action).and.callThrough();
  actionBtn.click();

  expect(dialogComponent[action]).toHaveBeenCalled();

  // Cleanup
  document.body.innerHTML = '';
}

describe('VideoComponent', () => {
  let component: VideoComponent;
  let fixture: ComponentFixture<VideoComponent>;
  let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;
  let authServiceSpy: jasmine.SpyObj<AuthJwtService>;
  let exceptionHandlingService: ExceptionHandlingService;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let videoServiceMock: jasmine.SpyObj<VideoService>;
  let softwareBuildApiCallServiceMock: jasmine.SpyObj<SoftwareBuildApiCallService>;
  let httpClientSpy: jasmine.SpyObj<any>;


  const videoListResponse: VideoListPagableResponse = {
    "content": [{
      "parentId": 1,
      "videoId": 1,
      "jsonId": null,
      "title": "Getting started v2.2",
      "name": "Video1_v2-3",
      "date": 1696419678943,
      "attachment": "Video1_v2-3.zip",
      "notes": "test121",
      "type": "VIDEO",
      "editable": false
    }, {
      "parentId": 2,
      "videoId": 2,
      "jsonId": 3,
      "title": "Test2",
      "name": "Video2_v2-3",
      "date": 1696422531298,
      "attachment": "Video2_v2-3.zip",
      "notes": "12345678",
      "type": "VIDEO",
      "editable": false
    }, {
      "parentId": 3,
      "videoId": 3,
      "jsonId": null,
      "title": "Test3",
      "name": "Video3_v2-3",
      "date": 1696423269382,
      "attachment": "Video3_v2-3.zip",
      "notes": "Raj",
      "type": "VIDEO",
      "editable": false
    }, {
      "parentId": 4,
      "videoId": 4,
      "jsonId": null,
      "title": "Test4",
      "name": "Video4_v2-3",
      "date": 1696422890027,
      "attachment": "Video4_v2-3.zip",
      "notes": "",
      "type": "VIDEO",
      "editable": false
    }, {
      "parentId": 5,
      "videoId": 5,
      "jsonId": null,
      "title": "test5",
      "name": "Video5_v2-3",
      "date": 1696422936649,
      "attachment": "Video5_v2-3.zip",
      "notes": "",
      "type": "VIDEO",
      "editable": false
    }, {
      "parentId": 6,
      "videoId": 6,
      "jsonId": null,
      "title": "test6",
      "name": "Video6_v2-3",
      "date": 1696423165462,
      "attachment": "Video6_v2-3.zip",
      "notes": "",
      "type": "VIDEO",
      "editable": false
    }, {
      "parentId": 7,
      "videoId": 7,
      "jsonId": null,
      "title": "Test7",
      "name": "Video7_v2-3",
      "date": 1696423304609,
      "attachment": "Video7_v2-3.zip",
      "notes": "",
      "type": "VIDEO",
      "editable": false
    }, {
      "parentId": 8,
      "videoId": 8,
      "jsonId": null,
      "title": "test8",
      "name": "Video8_v2-3",
      "date": 1696423364941,
      "attachment": "Video8_v2-3.zip",
      "notes": "",
      "type": "VIDEO",
      "editable": false
    }, {
      "parentId": 9,
      "videoId": 9,
      "jsonId": null,
      "title": "test9",
      "name": "Video9_v2-3",
      "date": 1696424589750,
      "attachment": "Video9_v2-3.zip",
      "notes": "Raj",
      "type": "VIDEO",
      "editable": false
    }, {
      "parentId": 10,
      "videoId": 10,
      "jsonId": null,
      "title": "test10",
      "name": "Video10_v2-3",
      "date": 1696424659782,
      "attachment": "Video10_v2-3.zip",
      "notes": "Test test test",
      "type": "VIDEO",
      "editable": false
    }],
    "pageable": {
      "pageNumber": 0,
      "pageSize": 10,
      "sort": {
        "empty": true,
        "unsorted": true,
        "sorted": false
      },
      "offset": 0,
      "unpaged": false,
      "paged": true
    },
    "last": false,
    "totalElements": 25,
    "totalPages": 3,
    "size": 10,
    "number": 0,
    "sort": {
      "empty": true,
      "unsorted": true,
      "sorted": false
    },
    "first": true,
    "numberOfElements": 10,
    "empty": false
  }

  const editVideoResponse: UploadVideoRequest = {
    "title": "Getting started v2.2",
    "duration": "13:00",
    "videoFile": "Video1_v2-3.mp4",
    "videoChanged": false,
    "thumbnailFile": "Video1_v2-3.jpg",
    "thumbnailChanged": false,
    "videoFileSize": 36917361,
    "thumbnailFileSize": 214980,
    "notes": "test121",
    "subtitles": [{
      "title": "Video1_v2-3_sv",
      "subtitleFile": "Video1_v2-3_sv.srt",
      "changed": false,
      "subTitleFileSize": 3243
    }, {
      "title": "Video1_v2-3_nl",
      "subtitleFile": "Video1_v2-3_nl.srt",
      "changed": false,
      "subTitleFileSize": 3605
    }, {
      "title": "Video1_v2-3_no",
      "subtitleFile": "Video1_v2-3_no.srt",
      "changed": false,
      "subTitleFileSize": 3299
    }, {
      "title": "Video1_v2-3_da",
      "subtitleFile": "Video1_v2-3_da.srt",
      "changed": false,
      "subTitleFileSize": 3341
    }, {
      "title": "Video1_v2-3_pt",
      "subtitleFile": "Video1_v2-3_pt.srt",
      "changed": false,
      "subTitleFileSize": 3463
    }, {
      "title": "Video1_v2-3_de",
      "subtitleFile": "Video1_v2-3_de.srt",
      "changed": false,
      "subTitleFileSize": 3662
    }, {
      "title": "Video1_v2-3_en",
      "subtitleFile": "Video1_v2-3_en.srt",
      "changed": false,
      "subTitleFileSize": 3223
    }, {
      "title": "Video1_v2-3_fr",
      "subtitleFile": "Video1_v2-3_fr.srt",
      "changed": false,
      "subTitleFileSize": 3710
    }, {
      "title": "Video1_v2-3_it",
      "subtitleFile": "Video1_v2-3_it.srt",
      "changed": false,
      "subTitleFileSize": 3813
    }, {
      "title": "Video1_v2-3_ru",
      "subtitleFile": "Video1_v2-3_ru.srt",
      "changed": false,
      "subTitleFileSize": 4612
    }, {
      "title": "Video1_v2-3_es",
      "subtitleFile": "Video1_v2-3_es.srt",
      "changed": false,
      "subTitleFileSize": 3474
    }],
    "id": 1
  }

  const mockVideoResponse = {
    videoUrl: 'http://test.com/video.mp4',
    thumbnailUrl: 'http://test.com/thumb.jpg',
    subtitles: ['http://test.com/sub.srt']
  };

  beforeEach(async () => {
    // Create spies for services
    httpClientSpy = jasmine.createSpyObj('HttpClient', ['get']);
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getVideoPermission']);
    videoServiceMock = jasmine.createSpyObj('VideoService', [
      'getVideoList', 'deleteVideo', 'deleteJson', 'createJson', 'uploadVideo', 'getVideo', 'updateVideo',
      'updateJson', 'downloadVideoZipFile', 'downloadJSONFile'
    ]);
    softwareBuildApiCallServiceMock = jasmine.createSpyObj('InventoryService', ['uploadFileToStorage']);


    // Mocking the getFile method to return a mocked Blob (successful response)
    httpClientSpy.get.and.returnValue(of(new Blob(['Mocked file content'], { type: 'application/octet-stream' })));

    // TestBed configuration
    await TestBed.configureTestingModule({
      declarations: [VideoComponent, UploadJsonDialogComponent],
      imports: [
        NgbPaginationModule, ReactiveFormsModule, FormsModule, CommonModule
      ],
      providers: [
        MessageService,
        PermissionService,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        SessionStorageService,
        ExceptionHandlingService,
        AuthJwtService,
        CommonsService,
        SSOLoginService,
        ConfirmDialogService,
        { provide: common_error_empty_filter, useValue: 'Some default value' },
        { provide: common_error_empty_start_date, useValue: 'Start date error' },
        { provide: AuthJwtService, useValue: authServiceSpy },
        { provide: common_error_empty_end_date, useValue: 'End date error' },
        { provide: common_error_invalid_date, useValue: 'Invalid date error' },
        { provide: VideoService, useValue: videoServiceMock },
        { provide: common_error_other, useValue: 'Other error' },
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: SoftwareBuildApiCallService, useValue: softwareBuildApiCallServiceMock },
        { provide: HttpBackend, useValue: {} },
        commonsProviders(toastrServiceMock),
        { provide: HttpClient, useValue: httpClientSpy } // Inject mocked HttpClient
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(VideoComponent);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    component = fixture.componentInstance;
    // @ts-ignore - Override the httpClient for testing
    component.httpClient = httpClientSpy;
    fixture.detectChanges();
  });


  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {

    it('should initialize the component when the user is authenticated', async () => {

      // Simulate user authentication
      authServiceSpy.isAuthenticate?.and.returnValue(true);

      // Simulate user permissions for accessing Role functionality
      permissionServiceSpy.getVideoPermission?.and.returnValue(true);

      component.ngOnInit();
      // **Act: Trigger Angular lifecycle methods and finalize change detection**
      fixture.detectChanges(); // Run initial change detection
      await fixture.whenStable(); // Wait for asynchronous operations to complete

      spyOn(component, 'clearFilter')?.and.callThrough();
      const button = fixture.nativeElement.querySelector('#refresh_VideoList');
      fixture.detectChanges(); // Run initial change detection
      await fixture.whenStable(); // Wait for asynchronous operations to complete
      expect(button).toBeTruthy();
      button?.click();
      expect(component.clearFilter).toHaveBeenCalled();

      // Test dropdown interaction
      testDropdownInteraction(fixture, component, '#videoDetailShowEntry');

      const paginationElement = fixture.debugElement.query(By.css('#video-pagination'));
      paginationElement.triggerEventHandler('pageChange', 2);
      fixture.detectChanges();
      expect(component.page).toBe(2);
    });

    // Test: Toggles filter visibility and updates the button text
    it('should toggle filter visibility and update the button text', () => {
      // Use the generic utility to test the toggle behavior
      testToggleFilter(component);
    });

    // Test: Displays an error message when an internal server error occurs
    it('should display an error message when an internal server error occurs', async () => {
      // Arrange: Simulate a 500 Internal Server Error
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
      videoServiceMock.getVideoList?.and.returnValue(throwError(() => mockError));
      spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

      // Act: Trigger the data loading method
      component.ngOnInit();

      fixture.detectChanges(); // Run initial change detection
      await fixture.whenStable(); // Wait for asynchronous operations to complete

      // Assert: Verify the error handling logic is triggered
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
    });

  });

  it('should initialize form controls on ngOnInit video List', async () => {
    // Simulate user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);

    // Simulate user permissions for accessing getVideoPermission functionality
    permissionServiceSpy.getVideoPermission?.and.returnValue(true);

    videoServiceMock.getVideoList?.and.returnValue(of(new HttpResponse<VideoListPagableResponse>({
      body: videoListResponse,
      status: 200,
      statusText: 'OK',
    })));

    // Initialize the component (triggers ngOnInit and sets up component state)
    component.ngOnInit();
    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution

    // Verify permissions and pagination-related properties
    expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE); // Items per page should match the configured constant
    expect(component.drpselectsize).toBe(ITEMS_PER_PAGE); // Dropdown size should match the configured constant
    expect(component.dataSizes).toEqual(['10', '25', '50', '100']); // Data sizes for pagination dropdown

    // Assert that the component state reflects the API response data
    expect(component.totalItems).toEqual(videoListResponse.totalElements); // Total items should match the API response
    expect(component.videosList).toEqual(videoListResponse.content); // List should populate with the response content
    expect(component.totalMember).toEqual(videoListResponse.totalElements); // Total records should match the response
    expect(component.totalMemberDisplay).toEqual(videoListResponse.numberOfElements); // Displayed records should match the response
  })


  it('should initialize form controls on ngOnInit video List and Delete Video', async () => {
    // Simulate user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);

    // Simulate user permissions for accessing getVideoPermission functionality
    permissionServiceSpy.getVideoPermission?.and.returnValue(true);

    videoServiceMock.getVideoList?.and.returnValue(of(new HttpResponse<VideoListPagableResponse>({
      body: videoListResponse,
      status: 200,
      statusText: 'OK',
    })));

    videoServiceMock.deleteVideo?.and.returnValue(of(new HttpResponse<SuccessMessageResponse>({
      body: { "message": "Video Deleted Successfully." },
      status: 200, // Set a valid HTTP status
      statusText: 'OK', // Optionally set a status text
    })))

    // Initialize the component (triggers ngOnInit and sets up component state)
    component.ngOnInit();
    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution

    // Get the delete DebugElement
    const deleteButtonDebugEl = fixture.debugElement.query(By.css('#deleteVideoAndJson1'));
    // Get the native element
    const deleteButtonNativeEl = deleteButtonDebugEl.nativeElement as HTMLInputElement;
    // Simulate the click
    deleteButtonNativeEl.click();

    // First Call: Initial Operation
    let deleteOperationsElement = document.querySelector('app-delete-video-json-confirmation-dialog');
    let deleteOperationsComponent = window['ng'].getComponent(deleteOperationsElement);

    spyOn(deleteOperationsComponent, 'close').and.callThrough();
    const closeBtn = document.querySelector<HTMLElement>('#deleteModelCloseButton');

    // Simulate the button click
    closeBtn.click();

    // Verify the method was called
    expect(deleteOperationsComponent.close).toHaveBeenCalled();

    // Clear DOM and Component References
    deleteOperationsElement = null;
    deleteOperationsComponent = null;

    // Reset DOM
    document.body.innerHTML = '';

    // Detect Changes for Cleanup
    fixture.detectChanges();
    await fixture.whenStable();

    // Simulate the click
    deleteButtonNativeEl.click();

    // First Call: Initial Operation
    let deleteOperationsElement1 = document.querySelector('app-delete-video-json-confirmation-dialog');
    let deleteOperationsComponent1 = window['ng'].getComponent(deleteOperationsElement1);

    spyOn(deleteOperationsComponent1, 'dismiss').and.callThrough();
    const dismissBtn = document.querySelector<HTMLElement>('#deleteModelDismissButton');

    // Simulate the button click
    dismissBtn.click();

    // Verify the method was called
    expect(deleteOperationsComponent1.dismiss).toHaveBeenCalled();

    // Clear DOM and Component References
    deleteOperationsElement1 = null;
    deleteOperationsComponent1 = null;

    // Reset DOM
    document.body.innerHTML = '';

    // Detect Changes for Cleanup
    fixture.detectChanges();
    await fixture.whenStable();

    // Simulate the click
    deleteButtonNativeEl.click();

    // First Call: Initial Operation For Delete Video Json Confirmation Dialog
    let deleteVideoJson = document.querySelector('app-delete-video-json-confirmation-dialog');
    let deleteVideoJsonComponent = window['ng'].getComponent(deleteVideoJson);

    spyOn(deleteVideoJsonComponent, 'accept').and.callThrough();
    const okBtn = document.querySelector<HTMLElement>('#deleteModelOkButton');

    // Simulate the button click
    okBtn.click();

    // Verify the method was called
    expect(deleteVideoJsonComponent.accept).toHaveBeenCalled();

    // Clear DOM and Component References
    deleteVideoJson = null;
    deleteVideoJsonComponent = null;

    // Reset DOM
    document.body.innerHTML = '';

    // Detect Changes for Cleanup
    fixture.detectChanges();
    await fixture.whenStable();

    expect(toastrServiceMock.success).toHaveBeenCalledWith("Video Deleted Successfully.");
  });

  it('should initialize form controls on ngOnInit video List and Delete Video Error', async () => {
    // Simulate user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);
    permissionServiceSpy.getVideoPermission?.and.returnValue(true);

    videoServiceMock.getVideoList?.and.returnValue(of(new HttpResponse<VideoListPagableResponse>({
      body: videoListResponse,
      status: 200,
      statusText: 'OK',
    })));

    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
    videoServiceMock.deleteVideo?.and.returnValue(throwError(() => mockError));
    spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

    // Initialize the component
    component.ngOnInit();
    fixture.detectChanges();

    // Trigger delete action
    const deleteButton = fixture.debugElement.query(By.css('#deleteVideoAndJson1')).nativeElement;
    deleteButton.click();

    // Handle dialog interaction
    handleDeleteDialog('accept');

    // Verify error handling
    fixture.detectChanges();
    await fixture.whenStable();

    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
  });

  it('should initialize form controls on ngOnInit video List and Delete json', async () => {
    // Simulate user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);
    permissionServiceSpy.getVideoPermission?.and.returnValue(true);

    videoServiceMock.getVideoList?.and.returnValue(of(new HttpResponse<VideoListPagableResponse>({
      body: videoListResponse,
      status: 200,
      statusText: 'OK',
    })));

    videoServiceMock.deleteJson?.and.returnValue(of(new HttpResponse<SuccessMessageResponse>({
      body: { "message": "Json Deleted Successfully." },
      status: 200,
      statusText: 'OK',
    })));

    // Initialize the component
    component.ngOnInit();
    fixture.detectChanges();

    // Trigger delete action
    const deleteJsonButton = fixture.debugElement.query(By.css('#deleteVideoAndJson2')).nativeElement;
    deleteJsonButton.click();

    // Test dismiss action
    handleDeleteDialog('dismiss');

    fixture.detectChanges();
    await fixture.whenStable();

    // Test accept action
    deleteJsonButton.click();
    handleDeleteDialog('accept');

    fixture.detectChanges();
    await fixture.whenStable();

    expect(toastrServiceMock.success).toHaveBeenCalledWith("Json Deleted Successfully.");
  });

  it('should initialize form controls on ngOnInit filter', async () => {
    // Simulate user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);

    // Simulate user permissions for accessing getVideoPermission functionality
    permissionServiceSpy.getVideoPermission?.and.returnValue(true);

    videoServiceMock.getVideoList?.and.returnValue(of(new HttpResponse<VideoListPagableResponse>({
      body: videoListResponse,
      status: 200,
      statusText: 'OK',
    })));
    spyOn(component, "getAllVideosDetail").and.callThrough();

    component.ngOnInit();
    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution

    // Get the delete DebugElement
    const searchButtonDebugEl = fixture.debugElement.query(By.css('#serachFilterButton'));
    // Get the native element
    const saearchButtonNativeEl = searchButtonDebugEl.nativeElement as HTMLInputElement;
    // Simulate the click
    saearchButtonNativeEl.click();

    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    expect(toastrServiceMock.info).toHaveBeenCalled();

    component.videoFilterForm.get("videoTitleJsonTitle").setValue("Test");

    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    // Simulate the click
    saearchButtonNativeEl.click();

    expect(component.getAllVideosDetail).toHaveBeenCalled();
  });

  it('should initialize form controls on ngOnInit For Create Json', async () => {
    // Simulate user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);

    // Simulate user permissions for accessing getVideoPermission functionality
    permissionServiceSpy.getVideoPermission?.and.returnValue(true);

    videoServiceMock.getVideoList?.and.returnValue(of(new HttpResponse<VideoListPagableResponse>({
      body: videoListResponse,
      status: 200,
      statusText: 'OK',
    })));

    videoServiceMock.createJson?.and.returnValue(of(new HttpResponse<SuccessMessageResponse>({
      body: {
        "message": "JSON created and videos associated with JSON successfully."
      },
      status: 200,
      statusText: 'OK',
    })));
    spyOn(component, "getAllVideosDetail").and.callThrough();

    component.ngOnInit();
    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution

    // Get the Create JSON DebugElement
    const createButtonDebugEl = fixture.debugElement.query(By.css("#Create\\20 JSON\\20 File"));
    // Get the native element
    const createButtonNativeEl = createButtonDebugEl.nativeElement as HTMLInputElement;
    // Simulate the click
    createButtonNativeEl.click();

    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    // Get the CheckBox DebugElement
    const selectButtonDebugEl = fixture.debugElement.query(By.css("#parentid1"));
    // Get the native element
    const selectButtonNativeEl = selectButtonDebugEl.nativeElement as HTMLInputElement;
    // Simulate the click
    selectButtonNativeEl.click();

    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    selectButtonNativeEl.click();

    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    selectButtonNativeEl.click();

    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    // Get the Add Json DebugElement
    const addJsonButtonDebugEl = fixture.debugElement.query(By.css("#AddJsonVideo"));
    // Get the native element
    const addJsonButtonNativeEl = addJsonButtonDebugEl.nativeElement as HTMLInputElement;
    // Simulate the click
    addJsonButtonNativeEl.click();

    // First Call: Initial Operation
    let uploadJsonElement = document.querySelector('app-upload-json-dialog');
    let uploadJsonComponent = window['ng'].getComponent(uploadJsonElement);

    spyOn(uploadJsonComponent, 'dismiss').and.callThrough();
    const declineBtn = document.querySelector<HTMLElement>('#dismissVideoJson');

    // Simulate the button click
    declineBtn.click();

    // Verify the method was called
    expect(uploadJsonComponent.dismiss).toHaveBeenCalled();

    // Clear DOM and Component References
    uploadJsonElement = null;
    uploadJsonComponent = null;

    // Reset DOM
    document.body.innerHTML = '';

    // Simulate the click
    addJsonButtonNativeEl.click();

    // First Call: Initial Operation
    let uploadJsonAgainElement = document.querySelector('app-upload-json-dialog');
    let uploadJsonAgainComponent = window['ng'].getComponent(uploadJsonAgainElement);

    const okBtn = document.querySelector<HTMLElement>('#AddEditJson');

    uploadJsonAgainComponent.createJSONForm.get('Title').setValue("Demo");
    uploadJsonAgainComponent.createJSONForm.get('version').setValue("1.2.3.4");

    // Simulate the button click
    okBtn.click();

    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    // Verify the method was called
    expect(toastrServiceMock.success).toHaveBeenCalledWith("JSON created and videos associated with JSON successfully.");

    // Clear DOM and Component References
    uploadJsonAgainElement = null;
    uploadJsonAgainComponent = null;

    // Reset DOM
    document.body.innerHTML = '';


    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    // Get the Create JSON DebugElement
    const create1ButtonDebugEl = fixture.debugElement.query(By.css("#Create\\20 JSON\\20 File"));
    // Get the native element
    const create1ButtonNativeEl = create1ButtonDebugEl.nativeElement as HTMLInputElement;
    // Simulate the click
    create1ButtonNativeEl.click();

    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    // Get the CheckBox DebugElement
    const select1ButtonDebugEl = fixture.debugElement.query(By.css("#parentid1"));
    // Get the native element
    const select1ButtonNativeEl = select1ButtonDebugEl.nativeElement as HTMLInputElement;
    // Simulate the click
    select1ButtonNativeEl.click();

    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    // Get the Create JSON DebugElement
    const cancelButtonDebugEl = fixture.debugElement.query(By.css("#Cancel\\20 JSON\\20 Creation"));
    // Get the native element
    const cancelButtonNativeEl = cancelButtonDebugEl.nativeElement as HTMLInputElement;
    // Simulate the click
    cancelButtonNativeEl.click();
  });

  it('should initialize form controls on ngOnInit Add And Update Video', async () => {
    // Simulate user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);

    // Simulate user permissions for accessing getVideoPermission functionality
    permissionServiceSpy.getVideoPermission?.and.returnValue(true);

    videoServiceMock.getVideoList?.and.returnValue(of(new HttpResponse<VideoListPagableResponse>({
      body: videoListResponse,
      status: 200,
      statusText: 'OK',
    })));

    videoServiceMock.uploadVideo.and.returnValue(of(new HttpResponse<any>({
      body: {
        "videoUrl": "https://.core.windows.net/rdm-container/video%2FVideo1_v2-3%2Fvideo%2FVideo1_v2-3.mp4?sv=2024-11-04&se=2025-02-03T11%3A51%3A29Z&sr=b&sp=cw&sig=EHuLmOCe8ZzZovmFDi0VwwWqDnDgcB8xJEix4gtJU1U%3D",
        "videoChanged": true,
        "thumbnailUrl": "https://.core.windows.net/rdm-container/video%2FVideo1_v2-3%2Fthumbnail%2FVideo1_v2-3.jpg?sv=2024-11-04&se=2025-02-03T11%3A51%3A29Z&sr=b&sp=cw&sig=OdqlXrZXvrCgYkZzM8vDL3YhU%2B%2BPLM8vrqXIY14kua8%3D",
        "thumbnailChanged": true,
        "subTitles": [{
          "name": "video/Video1_v2-3/subtitle/Video1_v2-3_de.srt",
          "url": "https://.core.windows.net/rdm-container/video%2FVideo1_v2-3%2Fsubtitle%2FVideo1_v2-3_de.srt?sv=2024-11-04&se=2025-02-03T11%3A51%3A29Z&sr=b&sp=cw&sig=gs0C7lCDZAmvEfTdyI3%2B6vOvIgcv8rh08HSYPcs8r3k%3D",
          "subTitleChanged": true
        }, {
          "name": "video/Video1_v2-3/subtitle/Video1_v2-3_no.srt",
          "url": "https://.core.windows.net/rdm-container/video%2FVideo1_v2-3%2Fsubtitle%2FVideo1_v2-3_no.srt?sv=2024-11-04&se=2025-02-03T12%3A49%3A42Z&sr=b&sp=cw&sig=yX%2Box%2BoIiYRvFYwxmw4yivbQqdKJTxDWSZSSReDTQt0%3D",
          "subTitleChanged": true
        }],
        "upload": false
      },
      status: 200,
      statusText: 'OK',
    })));

    softwareBuildApiCallServiceMock.uploadFileToStorage?.and.returnValue(of(new HttpResponse<any>({
      body: null,
      status: 200,
      statusText: 'OK',
    })));

    videoServiceMock.getVideo?.and.returnValue(of(new HttpResponse<UploadVideoRequest>({
      body: editVideoResponse,
      status: 200,
      statusText: 'OK',
    })));

    videoServiceMock.updateVideo?.and.returnValue(of(new HttpResponse<UploadVideoResponse>({
      body: {
        "videoUrl": null,
        "videoChanged": false,
        "thumbnailUrl": null,
        "thumbnailChanged": false,
        "subTitles": [new SubTitles(null, null, false)],
        "upload": false
      },
      status: 200,
      statusText: 'OK',
    })));

    component.ngOnInit();
    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution

    // Get the Upload Video DebugElement
    const uploadButtonDebugEl = fixture.debugElement.query(By.css('#uploadVideo'));
    // Get the native element
    const uploadButtonNativeEl = uploadButtonDebugEl.nativeElement as HTMLInputElement;
    // Simulate the click
    uploadButtonNativeEl.click();

    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    // First Call: Initial Operation
    let uploadVideoElement = document.querySelector('app-upload-video-dialog');
    let uploadVideoComponent = window['ng'].getComponent(uploadVideoElement);

    spyOn(uploadVideoComponent, 'decline').and.callThrough();
    const declineBtn = document.querySelector<HTMLElement>('#declineVideo');

    // Simulate the button click
    declineBtn.click();

    // Verify the method was called
    expect(uploadVideoComponent.decline).toHaveBeenCalled();

    // Clear DOM and Component References
    uploadVideoElement = null;
    uploadVideoComponent = null;

    // Reset DOM
    document.body.innerHTML = '';

    // Simulate the click
    uploadButtonNativeEl.click();

    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    // First Call: Initial Operation
    let uploadVideoAgainElement = document.querySelector('app-upload-video-dialog');
    let uploadVideoAgainComponent = window['ng'].getComponent(uploadVideoAgainElement);

    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    // Use document.querySelector to get the file input element
    const fileInput: HTMLInputElement = document.querySelector<HTMLElement>('#videofile') as HTMLInputElement;

    // Create a mock file (e.g., a sample MP3 file)
    const mockFile = new File(['video content'], 'sample.mp3', { type: 'video/mp3' });

    // Create a DataTransfer object and add the mock file to it
    const dataTransfer = new DataTransfer();
    dataTransfer.items.add(mockFile);

    // Simulate the file input change event
    fileInput.files = dataTransfer.files;
    fileInput.dispatchEvent(new Event('change'));

    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    // Verify the method was called
    expect(uploadVideoAgainComponent.validVideoFileType).toBe("select mp4 file only");

    // Create a mock file (e.g., a sample MP3 file)
    const mockMp4File = new File(['video content'], 'sample.mp4', { type: 'video/mp4' });

    // Create a DataTransfer object and add the mock file to it
    const dataTransferMp4 = new DataTransfer();
    dataTransferMp4.items.add(mockMp4File);

    // Simulate the file input change event
    fileInput.files = dataTransferMp4.files;
    fileInput.dispatchEvent(new Event('change'));

    // Trigger change detection after the event
    fixture.detectChanges();

    // Use document.querySelector to get the thumbnail file input element
    const thumbnailFileInput = document.querySelector<HTMLInputElement>('#thumbnailfile');

    // Create a mock thumbnail file (e.g., a sample JPG file)
    const mockThumbnailFile = new File(['thumbnail content'], 'sample.webp', { type: 'image/webp' });

    // Create a dataImageTransfer object and add the mock file to it
    const dataImageTransfer = new DataTransfer();
    dataImageTransfer.items.add(mockThumbnailFile);

    // Simulate the file input change event
    thumbnailFileInput.files = dataImageTransfer.files;
    thumbnailFileInput.dispatchEvent(new Event('change'));

    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    // Verify the method was called
    expect(uploadVideoAgainComponent.validThumbnailFileType).toBe("select jpg/jpeg file only");

    // Create a mock thumbnail file (e.g., a sample JPG file)
    const mockThumbnai1lFile = new File(['thumbnail content'], 'sample.jpeg', { type: 'image/jpeg' });

    // Create a dataImageTransfer object and add the mock file to it
    const dataImage1Transfer = new DataTransfer();
    dataImage1Transfer.items.add(mockThumbnai1lFile);

    // Simulate the file input change event
    thumbnailFileInput.files = dataImage1Transfer.files;
    thumbnailFileInput.dispatchEvent(new Event('change'));

    // Trigger change detection after the event
    fixture.detectChanges();

    // Use document.querySelector to get the subtitle file input element
    const srtFileInput = document.querySelector<HTMLInputElement>('#srtfile');

    // Create a mock subtitle file (e.g., a sample SRT file)
    const mockSrtFile = new File(['subtitle content'], 'sample.srtt', { type: 'application/x-subript' });
    const mockSrt2File = new File(['subtitle content'], 'sample2.srtt', { type: 'application/x-subript' });

    // Create a dataSrcTransfer object and add the mock file to it
    const dataSrcTransfer = new DataTransfer();
    dataSrcTransfer.items.add(mockSrtFile);
    dataSrcTransfer.items.add(mockSrt2File);

    // Simulate the file input change event
    srtFileInput.files = dataSrcTransfer.files;
    srtFileInput.dispatchEvent(new Event('change'));

    // Trigger change detection after the event
    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    // Create a mock subtitle file (e.g., a sample SRT file)
    const mockSrt1File = new File(['subtitle content'], 'video/Video1_v2-3/subtitle/Video1_v2-3_de.srt', { type: 'application/x-subrip' });

    // Create a DataTransfer object and add the mock file to it
    const datasrc1Transfer = new DataTransfer();
    datasrc1Transfer.items.add(mockSrt1File);

    // Simulate the file input change event
    srtFileInput.files = datasrc1Transfer.files;
    srtFileInput.dispatchEvent(new Event('change'));

    // Trigger change detection after the event
    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    uploadVideoAgainComponent.removeSrt(uploadVideoAgainComponent?.uploadVideoForm, null, 'thumbnailFile', null);
    uploadVideoAgainComponent.removeSrt(uploadVideoAgainComponent?.uploadVideoForm, null, 'videoFile', null);
    uploadVideoAgainComponent.removeSrt(uploadVideoAgainComponent?.uploadVideoForm, null, 'srtFilesList', null);

    srtFileInput.dispatchEvent(new Event('change'));
    thumbnailFileInput.dispatchEvent(new Event('change'));
    fileInput.dispatchEvent(new Event('change'));

    // Trigger change detection after the event
    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    uploadVideoAgainComponent.accept();

    // Trigger change detection after the event
    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution
    await fixture.whenStable();

    expect(toastrServiceMock.success).toHaveBeenCalledWith(UploadVideoSuccessMessage);
    // Clear DOM and Component References
    uploadVideoAgainElement = null;
    uploadVideoAgainComponent = null;

    // Reset DOM
    document.body.innerHTML = '';
  });

  it('should initialize form controls on ngOnInit update Video', async () => {

    // Simulate user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);

    // Simulate user permissions for accessing getVideoPermission functionality
    permissionServiceSpy.getVideoPermission?.and.returnValue(true);

    videoServiceMock.getVideoList?.and.returnValue(of(new HttpResponse<VideoListPagableResponse>({
      body: videoListResponse,
      status: 200,
      statusText: 'OK',
    })));

    videoServiceMock.getVideo?.and.returnValue(of(new HttpResponse<UploadVideoRequest>({
      body: editVideoResponse,
      status: 200,
      statusText: 'OK',
    })));

    videoServiceMock.updateVideo?.and.returnValue(of(new HttpResponse<UploadVideoResponse>({
      body: {
        "videoUrl": null,
        "videoChanged": false,
        "thumbnailUrl": null,
        "thumbnailChanged": false,
        "subTitles": [],
        "upload": false
      },
      status: 200,
      statusText: 'OK',
    })));
    component.ngOnInit();
    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution

    // Get the Upload Video DebugElement
    const editButtonDebugEl = fixture.debugElement.query(By.css('#editVideoJson'));
    // Get the native element
    const editButtonNativeEl = editButtonDebugEl.nativeElement as HTMLInputElement;
    // Simulate the click
    editButtonNativeEl.click();


    // Use document.querySelector to get the subtitle file input element
    const uploadButton = document.querySelector<HTMLInputElement>('#uploadVideoBtn');

    uploadButton.click();
    // Detect Changes for Cleanup
    fixture.detectChanges();
    await fixture.whenStable();

    expect(toastrServiceMock.success).toHaveBeenCalledWith(UpdateVideoSuccessMessage);
  })

  it('should initialize form controls on ngOnInit update Json', async () => {

    // Simulate user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);

    // Simulate user permissions for accessing getVideoPermission functionality
    permissionServiceSpy.getVideoPermission?.and.returnValue(true);

    videoServiceMock.getVideoList?.and.returnValue(of(new HttpResponse<VideoListPagableResponse>({
      body: videoListResponse,
      status: 200,
      statusText: 'OK',
    })));

    videoServiceMock.updateJson?.and.returnValue(of(new HttpResponse<SuccessMessageResponse>({
      body: {
        "message": "Notes updated for JSON."
      },
      status: 200,
      statusText: 'OK',
    })));
    component.ngOnInit();
    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution

    // Get the Upload Video Button element using querySelector
    const editButtonNativeEl = fixture.nativeElement.querySelectorAll('#editVideoJson');
    // Simulate the click
    editButtonNativeEl[1].click();


    // Use document.querySelector to get the subtitle file input element
    const editJsonButton = document.querySelector<HTMLInputElement>('#AddEditJson');

    editJsonButton.click();
    // Detect Changes for Cleanup
    fixture.detectChanges();
    await fixture.whenStable();

    expect(toastrServiceMock.success).toHaveBeenCalledWith(UpdateJsonSuccessMessage);
  });

  // Common setup function for download tests
  const setupDownloadTest = () => {
    // Common authentication and permission setup
    authServiceSpy.isAuthenticate?.and.returnValue(true);
    permissionServiceSpy.getVideoPermission?.and.returnValue(true);

    // Common mock blob and http setup
    const mockBlob = new Blob(['test'], { type: 'text/plain' });
    httpClientSpy.get.and.returnValue(of(mockBlob));

    // Common video list setup
    videoServiceMock.getVideoList?.and.returnValue(of(new HttpResponse<VideoListPagableResponse>({
      body: videoListResponse,
      status: 200,
      statusText: 'OK',
    })));
  };

  it('should initialize form controls on ngOnInit download Video Attachment', async () => {
    setupDownloadTest();
    spyOn(component, 'createZipFromFiles').and.callThrough();

    videoServiceMock.downloadVideoZipFile?.and.returnValue(of(new HttpResponse<DownloadVideoResponse>({
      body: {
        "videoUrl": "https://.net/rdm-container/video%2FVideo1_v2-3%2Fvideo%2FVideo1_v2-3.mp4?sv=2024-11-04&se=2025-02-06T14%3A18%3A41Z&sr=b&sp=r&sig=k7NVg5JBpZoXTg5MC3BpGA74zYbnLg0dUgpZM2YX0iw%3D",
        "thumbnailUrl": "https://.net/rdm-container/video%2FVideo1_v2-3%2Fthumbnail%2FVideo1_v2-3.jpg?sv=2024-11-04&se=2025-02-06T14%3A18%3A41Z&sr=b&sp=r&sig=dTfe93ckb69TMN5OTt7i18R7sfPImwe72xnuM96iBpA%3D",
        "subtitles": [
          "https://.net/rdm-container/video%2FVideo1_v2-3%2Fsubtitle%2FVideo1_v2-3_en.srt?sv=2024-11-04&se=2025-02-06T14%3A18%3A41Z&sr=b&sp=r&sig=O0739Ugcl1SmNCbuL%2B7ScaS1vvieiTvG8HvvYiY4ENo%3D",
        ]
      },
      status: 200,
      statusText: 'OK',
    })));

    component.ngOnInit();
    fixture.detectChanges();

    const downloadZipFile = fixture.debugElement.query(By.css('#downloadZipFile')).nativeElement;
    downloadZipFile.click();

    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.createZipFromFiles).toHaveBeenCalled();
  });

  it('should initialize form controls on ngOnInit download JSON Attachment', async () => {
    setupDownloadTest();
    spyOn(component, 'downloadJSONFile').and.callThrough();

    videoServiceMock.downloadJSONFile?.and.returnValue(of(new HttpResponse<DownloadJsonResponse>({
      body: new DownloadJsonResponse(
        "0.0",
        [new VideoInformation(
          "0",
          "00:00",
          437836,
          [new SubTitleInformation("en", "test_en")]
        )]
      ),
      status: 200,
      statusText: 'OK',
    })));

    component.ngOnInit();
    fixture.detectChanges();

    const downloadZipFile = fixture.nativeElement.querySelectorAll('#downloadZipFile')[1];
    downloadZipFile.click();

    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.downloadJSONFile).toHaveBeenCalled();
  });

  it('should handle network error', async () => {
    // Arrange
    const jsonId = 123;
    const jsonFileName = 'test.json';
    const mockError = new Error('Network Error');

    videoServiceMock.downloadJSONFile.and.returnValue(throwError(() => mockError));
    spyOn(exceptionHandlingService, 'customErrorMessage').and.callThrough();
    // Act
    component.downloadJSONFile(jsonId, jsonFileName);
    // Detect Changes for Cleanup
    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.loading).toBeFalse();
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  it('should handle empty response body', async () => {
    // Arrange
    const jsonId = 123;
    const jsonFileName = 'test.json';

    testErrorHandling(videoServiceMock.downloadJSONFile, () => component.downloadJSONFile(jsonId, jsonFileName), exceptionHandlingService, toastrServiceMock, fixture);
  });

  it('should handle network error while downloading Attachment file', async () => {
    // Arrange
    const jsonId = 123;
    const jsonFileName = 'test.json';

    videoServiceMock.downloadVideoZipFile.and.returnValue(
      of(new HttpResponse<DownloadVideoResponse>({ body: null }))
    );
    spyOn(exceptionHandlingService, 'customErrorMessage').and.callThrough();
    // Act
    component.downloadZipFile(jsonId, jsonFileName);
    // Detect Changes for Cleanup
    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.loading).toBeFalse();
  });

  it('should handle empty response body while downloading Attachment file', async () => {
    // Arrange
    const jsonId = 123;
    const jsonFileName = 'test.json';

    testErrorHandling(videoServiceMock.downloadVideoZipFile, () => component.downloadZipFile(jsonId, jsonFileName), exceptionHandlingService, toastrServiceMock, fixture);
  });

  it('should handle subtitle download errors', fakeAsync(() => {

    // Arrange
    const responseWithMultipleSubtitles: DownloadVideoResponse = {
      ...mockVideoResponse,
      subtitles: ['http://test.com/sub1.srt', 'http://test.com/sub2.srt']
    };
    const httpResponse = new HttpResponse({
      body: responseWithMultipleSubtitles
    });
    videoServiceMock.downloadVideoZipFile.and.returnValue(of(httpResponse));

    // Mock successful video and thumbnail download but failed subtitle download
    let callCount = 0;
    httpClientSpy.get.and.callFake(() => {
      callCount++;
      if (callCount <= 2) {
        return of(new Blob()); // Success for video and thumbnail
      }
      return throwError(() => new HttpErrorResponse({ // Fail for subtitles
        error: 'Subtitle download failed',
        status: 404
      }));
    });

    // Act
    component.downloadZipFile(1, 'test.zip');
    tick();

    // Assert
    expect(component.loading).toBeFalse();
    expect(toastrServiceMock.error).toHaveBeenCalledWith(DownloadVideoFileError);
  }));

  it('should handle zip generation error', async () => {
    // Method 1: Set required field to null/empty
    component.videoFilterForm.get('videoTitleJsonTitle').setErrors({ 'required': true });

    // OR Method 2: Set multiple validation errors
    component.videoFilterForm.setErrors({ 'customError': true });
    // Arrange
    const httpResponse = new HttpResponse({
      body: mockVideoResponse
    });
    videoServiceMock.downloadVideoZipFile.and.returnValue(of(httpResponse));
    const nonHttpError = new Error('Network error');
    httpClientSpy.get.and.returnValue(throwError(() => nonHttpError));
    spyOn(component, 'getFile').and.callThrough();
    // Act
    component.getAllVideosDetail();
    component.downloadZipFile(1, 'test.zip');

    // Assert
    fixture.detectChanges();
    await fixture.whenStable();

    expect(component.getFile).toHaveBeenCalled();
  });
});

