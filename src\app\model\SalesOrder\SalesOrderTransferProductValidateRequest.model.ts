import { SalesOrderTransferProbeDetailValidateRequest } from "./SalesOrderTransferProbeDetailValidateRequest.model";
import { SalesOrderTransferProductDetailValidateRequest } from "./SalesOrderTransferProductDetailValidateRequest.model";

export class SalesOrderTransferProductValidateRequest {
    bridges: SalesOrderTransferProductDetailValidateRequest[];
    probes: SalesOrderTransferProbeDetailValidateRequest[];

    constructor(bridges: Array<SalesOrderTransferProductDetailValidateRequest>, probes: Array<SalesOrderTransferProbeDetailValidateRequest>) {
        this.bridges = bridges;
        this.probes = probes;
    }
}
