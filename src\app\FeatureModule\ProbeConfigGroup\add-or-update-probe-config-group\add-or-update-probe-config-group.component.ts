import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ChangeDetectorRef, Component, EventEmitter, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { SPECIAL_CHARACTER_PATTERN, SPECIAL_CHARACTER_ERROR_MESSAGE, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH, TEXTAREA_MAX_CHARACTERS_ALLOWED_MESSAGE, TEXTAREA_MAX_LENGTH, MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, PartNumberRequire } from 'src/app/app.constants';
import { ProbeConfigGroupAddOrUpdateRequest } from 'src/app/model/ProbeConfigGroup/ProbeConfigGroupAddOrUpdateRequest.model';
import { ProbeConfigGroupFeatureRequest } from 'src/app/model/ProbeConfigGroup/ProbeConfigGroupFeatureRequest.model';
import { ProbeConfigGroupPresetRequest } from 'src/app/model/ProbeConfigGroup/ProbeConfigGroupPresetRequest.model';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { FeaturePartNumberResponse } from 'src/app/model/probe/FeaturePartNumberResponse.model';
import { PresetPartNumberResponse } from 'src/app/model/probe/PresetPartNumberResponse.model';
import { ProbeFeatureResponse } from 'src/app/model/probe/ProbeFeatureResponse.model';
import { ProbePresetResponse } from 'src/app/model/probe/ProbePresetResponse.model';
import { ProbeTypeResponse } from 'src/app/model/probe/probeTypeResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ProbeConfigGroupApiCallService } from 'src/app/shared/Service/ProbeConfigGroup/probe-config-group-api-call.service';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { ProbeConfigType } from 'src/app/shared/enum/ProbeConfigType.enum';
import { ProbeTypeEnum } from 'src/app/shared/enum/ProbeType.enum';
import { ValidityEnum } from 'src/app/shared/enum/ValidityEnum.enum';
import { CommonsService } from 'src/app/shared/util/commons.service';

@Component({
  selector: 'app-add-or-update-probe-config-group',
  templateUrl: './add-or-update-probe-config-group.component.html',
  styleUrl: './add-or-update-probe-config-group.component.css',
  encapsulation: ViewEncapsulation.None
})
export class AddOrUpdateProbeConfigGroupComponent implements OnInit {

  @Output("showListingPage") showListingPage = new EventEmitter;

  loading: boolean = false;
  probeTypeGroupResponse: ProbeTypeResponse[] = [];
  currentFeatureGroup: Array<ProbeFeatureResponse> = [];
  currentPresetGroup: Array<ProbePresetResponse> = [];
  currentProbeType: ProbeTypeResponse[]

  validityPerpetual = ValidityEnum.PERPETUAL;
  validityOneYear = ValidityEnum.ONE_YEAR;

  featureProbeConfigType = ProbeConfigType.FEATURE;
  presetProbeConfigType = ProbeConfigType.PRESET;

  selectedPresets: Array<ProbeConfigGroupPresetRequest> = [];
  selectedFeatures: Array<ProbeConfigGroupFeatureRequest> = [];

  reloadPipeFeature: boolean = true;
  reloadPipePreset: boolean = true;

  //MaxLength Message
  small_textBoxMaxLengthMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  textBoxMaxLengthMessage: string = MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  textAreaMaxCharactersAllowedMessage: string = TEXTAREA_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;

  partNumberRequire = PartNumberRequire;


  probeConfigGroupForm = new FormGroup({
    partNumber: new FormControl('', [Validators.required,
    Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)
    ]),
    probeType: new FormControl([], [Validators.required]),
    description: new FormControl('', [Validators.maxLength(TEXTAREA_MAX_LENGTH)]),
  });

  constructor(private probeApiService: ProbeApiService,
    private commonsService: CommonsService,
    private probeConfigGroupApiCallService: ProbeConfigGroupApiCallService,
    private exceptionService: ExceptionHandlingService,
    private toste: ToastrService,
    private cdr: ChangeDetectorRef) {

  }

  public ngOnInit(): void {
    this.getInitData();
  }

  private async getInitData(): Promise<void> {
    this.loading = true;
    this.probeTypeGroupResponse = await this.probeApiService.getprobeTypeResponseList(false);
    this.loading = false;
  }


  public back(): void {
    this.showListingPage.emit();
  }

  /**
  * update feature and preset according presetType
  *
  * <AUTHOR>
  * @param probeTypeId
  */
  public updateCurrentProbeConfigGroup(probeTypeId: number): void {
    this.selectedFeatures = [];
    this.selectedPresets = [];
    let updateFeatureGroup: ProbeTypeResponse[] = this.probeTypeGroupResponse.filter(obj => obj.probeTypeId == probeTypeId);
    if (updateFeatureGroup.length == 1) {
      this.currentFeatureGroup = updateFeatureGroup[0].features;
      this.currentPresetGroup = updateFeatureGroup[0].presets;
      this.selectBydefaultPresetAndFeature(updateFeatureGroup);
    } else {
      this.currentFeatureGroup = null;
    }
  }

  private selectBydefaultPresetAndFeature(updateFeatureGroup: Array<ProbeTypeResponse>): void {
    updateFeatureGroup.forEach(obj => {
      obj.presets.forEach(pre => {
        const defaultPartPreset = pre.partNumbers.find(pn => pn.default);
        if (!isNullOrUndefined(defaultPartPreset)) {
          this.selectedPresets.push(new ProbeConfigGroupPresetRequest(pre.presetId, defaultPartPreset.probeTypeMasterPresetMasterMappingId, defaultPartPreset.validity));
        }
      });
      obj.features.forEach(fea => {
        const defaultPartFeature = fea.partNumbers.find(pn => pn.default);
        if (!isNullOrUndefined(defaultPartFeature)) {
          this.selectedFeatures.push(new ProbeConfigGroupFeatureRequest(fea.featureId, defaultPartFeature.probeTypeMasterFeatureMasterMappingId, defaultPartFeature.validity));
        }
      });
    });
  }

  /**
   * create a new probe config group
   * <AUTHOR>
   */
  public saveData(): void {
    this.loading = true;
    const formData = this.probeConfigGroupForm.value;
    let probeType: ProbeTypeResponse[] = this.probeTypeGroupResponse.filter(obj => obj.probeTypeId == formData.probeType[0]);
    let probeTypeEnum = this.commonsService.getEnumKey(ProbeTypeEnum, probeType[0].displayName);
    let probeConfigGroupAddOrUpdateRequest: ProbeConfigGroupAddOrUpdateRequest = new ProbeConfigGroupAddOrUpdateRequest(probeTypeEnum, formData.partNumber.trim(), formData.description, this.selectedFeatures, this.selectedPresets)

    this.probeConfigGroupApiCallService.addOrUpdateProbeConfigGroup(probeConfigGroupAddOrUpdateRequest).subscribe({
      next: (response: HttpResponse<SuccessMessageResponse>) => {
        if (response.status === 200) {
          this.toste.success(response.body.message);
        }
        this.back();
      },
      error: (error: HttpErrorResponse) => {
        this.loading = false;
        if (error.status == 409) {
          this.toste.error(error.error.errorMessage);
        } else {
          this.exceptionService.customErrorMessage(error);
        }
      }
    });

  }

  /**
   * selected presets
   * <AUTHOR>
   * @param presetId 
   * @param partNumbers 
   * @param validity 
   * @param event 
   */
  public onPresetChange(presetId: number, partNumbers: Array<PresetPartNumberResponse>, validity: string): void {
    let validityEnum = this.commonsService.getEnumKey(ValidityEnum, validity);
    const index = this.selectedPresets.findIndex(f => f.presetId === presetId);
    if (index == -1) {
      let partNumber = partNumbers.filter(obj => obj.validity == validityEnum);
      if (partNumber.length > 0) {
        this.selectedPresets.push(new ProbeConfigGroupPresetRequest(presetId, partNumber[0].probeTypeMasterPresetMasterMappingId, validityEnum));
      }
    } else {
      const prestpre = this.selectedPresets.findIndex(f => f.presetId === presetId && f.validity == validityEnum);
      if (prestpre > -1) {
        this.selectedPresets.splice(index, 1);
      } else {
        this.selectedPresets.splice(index, 1);
        let partNumber = partNumbers.filter(obj => obj.validity == validityEnum);
        if (partNumber.length > 0) {
          this.selectedPresets.push(new ProbeConfigGroupPresetRequest(presetId, partNumber[0].probeTypeMasterPresetMasterMappingId, validityEnum));
        }
      }
    }
    this.reloadUiWithPipePreset();
  }

  /**
   * Select Feature
   * 
   * <AUTHOR>
   * @param featureId
   * @param displayName  
   * @param partNumbers 
   * @param validity 
   * @param event 
   */
  public onFeatureChange(featureId: number, partNumbers: Array<FeaturePartNumberResponse>, validity: string): void {
    let validityEnum = this.commonsService.getEnumKey(ValidityEnum, validity);
    const index = this.selectedFeatures.findIndex(f => f.featureId === featureId);
    if (index == -1) {
      let partNumber = partNumbers.filter(obj => obj.validity == validityEnum);
      if (partNumber.length > 0) {
        this.selectedFeatures.push(new ProbeConfigGroupFeatureRequest(featureId, partNumber[0].probeTypeMasterFeatureMasterMappingId, validityEnum));
      }
    } else {
      const featurepre = this.selectedFeatures.findIndex(f => f.featureId === featureId && f.validity == validityEnum);
      if (featurepre > -1) {
        this.selectedFeatures.splice(index, 1);
      } else {
        this.selectedFeatures.splice(index, 1);
        let partNumber = partNumbers.filter(obj => obj.validity == validityEnum);
        if (partNumber.length > 0) {
          this.selectedFeatures.push(new ProbeConfigGroupFeatureRequest(featureId, partNumber[0].probeTypeMasterFeatureMasterMappingId, validityEnum));
        }
      }
    }
    this.reloadUiWithPipeFearture();
  }
  /**
  * Reload the pipe for checkbox and radio button checked/unchecked
  * <AUTHOR>
  */
  public reloadUiWithPipeFearture(): void {
    this.reloadPipeFeature = false;
    this.cdr.detectChanges();
    this.reloadPipeFeature = true;
    this.cdr.detectChanges();
  }

  /**
 * Reload the pipe for checkbox and radio button checked/unchecked
 * <AUTHOR>
 */
  public reloadUiWithPipePreset(): void {
    this.reloadPipePreset = false;
    this.cdr.detectChanges();
    this.reloadPipePreset = true;
    this.cdr.detectChanges();
  }
}