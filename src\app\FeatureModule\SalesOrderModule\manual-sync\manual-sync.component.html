<h3>Manual Order Sync </h3>

<!-- Container for the progress steps -->
<div class="progress-container" id="progressContainer">
    <!-- Step indicating sync has started -->
    <div class="progress-step active">
        <div class="step-icon">
            <!-- SVG icon for sync started -->
            <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" width="512"
                height="512" x="0" y="0" viewBox="0 0 512 512" style="enable-background:new 0 0 512 512"
                xml:space="preserve" class="svg1">
                <g>
                    <path
                        d="M512 86.401V177.6c0 17.673-14.327 32-32 32h-91.199c-17.673 0-32-14.327-32-32s14.327-32 32-32h24.45C377.916 94.955 320.007 64 256 64c-79.639 0-149.848 47.913-178.866 122.063-6.442 16.458-25.006 24.577-41.462 18.137-16.458-6.441-24.578-25.004-18.137-41.461 18.559-47.423 50.547-87.906 92.506-117.073C153.018 15.791 203.489 0 256 0s102.982 15.791 145.959 45.665c17.076 11.87 32.501 25.616 46.041 40.94v-.203c0-17.673 14.327-32 32-32s32 14.326 32 31.999zm-35.672 221.4c-16.457-6.442-35.02 1.68-41.462 18.137C405.848 400.088 335.639 448 256 448c-62.647 0-119.454-29.654-154.97-78.4h22.169c17.673 0 32-14.327 32-32s-14.327-32-32-32H32c-17.673 0-32 14.327-32 32v91.2c0 17.673 14.327 32 32 32s32-14.327 32-32v-3.404c13.54 15.324 28.965 29.069 46.041 40.939C153.018 496.209 203.489 512 256 512s102.982-15.791 145.959-45.665c41.959-29.167 73.947-69.65 92.506-117.073 6.441-16.457-1.679-35.02-18.137-41.461z"
                        fill="#ffffff" opacity="1" data-original="#000000" class=""></path>
                </g>
            </svg>
        </div>
        <div class="step-label">Sync Started</div>
    </div>

    <!-- Line indicating progress -->
    <div class="progress-line">
        <div class="loading-line" [class.completed]="!inProgress"></div>
    </div>

    <!-- Step indicating sync has completed -->
    <div class="progress-step" [class.completed]="!inProgress">
        <div class="step-icon" [class.inactive]="inProgress" [class.active]="!inProgress">
            <!-- SVG icon for sync completed -->
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" fill="none" stroke="currentColor">
                <path d="M23 33.5L29.5 40L41 24" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"
                    class="checkmark" />
            </svg>
        </div>
        <div class="step-label" [class.inactive]="inProgress">Sync Completed</div>
    </div>
</div>

<!-- Modal footer with Ok button -->
<div class="modal-footer">
    <!-- Ok button to close the modal -->
    <button type="button" class="btn btn-sm btn-orange ok-button" id="manualSyncBtn" (click)="accept()"
        [disabled]="inProgress">Ok</button>
</div>