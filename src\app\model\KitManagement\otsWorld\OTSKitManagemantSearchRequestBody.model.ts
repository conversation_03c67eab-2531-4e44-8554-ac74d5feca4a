export class OTSKitManagemantSearchRequestBody {
    otsKitPartNumberCode: string;
    countryIds: number[];
    probePartNumberCode: string;

    constructor($otsKitPartNumberCode: string, $countryIds: number[], $probePartNumberCode: string) {
        this.otsKitPartNumberCode = $otsKitPartNumberCode;
        this.countryIds = $countryIds;
        this.probePartNumberCode = $probePartNumberCode;
    }
}