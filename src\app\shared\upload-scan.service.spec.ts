import { TestBed } from '@angular/core/testing';

import { UploadScanService } from './upload-scan.service';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { API_BASE_URL } from './config';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';

describe('UploadScanService', () => {
  let service: UploadScanService;

  beforeEach(() => {
    TestBed.configureTestingModule({
    imports: [],
    providers: [UploadScanService,
        { provide: API_BASE_URL, useValue: 'http://example.com/api' }, provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting()]
});
    service = TestBed.inject(UploadScanService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
