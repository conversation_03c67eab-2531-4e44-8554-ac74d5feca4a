import { <PERSON>pe, PipeTransform } from '@angular/core';
import { CLIENT_DEVICE, DEMO_DEVICE, TEST_DEVICE } from 'src/app/app.constants';
import { isNullOrUndefined } from 'is-what';
import { deviceTypesEnum } from '../enum/deviceTypesEnum.enum';

@Pipe({
  name: 'deviceTypeName'
})
export class DeviceTypeNamePipe implements PipeTransform {

  transform(deviceType: deviceTypesEnum): any {
    if (isNullOrUndefined(deviceType)) {
      return null;
    } else {
      if (deviceType == deviceTypesEnum.DEMO_DEVICE) {
        return DEMO_DEVICE;
      } else if (deviceType == deviceTypesEnum.CLIENT_DEVICE) {
        return CLIENT_DEVICE;
      } else if (deviceType == deviceTypesEnum.TEST_DEVICE) {
        return TEST_DEVICE;
      } else {
        return null;
      }
    }
  }

}
