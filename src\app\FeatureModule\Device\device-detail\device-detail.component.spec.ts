import { DatePipe } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { Subject } from 'rxjs';
import { TransferOrderModuleComponent } from '../../TransferOrder/transfer-order-module/transfer-order-module.component';
import { commonsProviders } from '../../../Tesing-Helper/test-utils';
import { ConfirmDialogService } from '../../CommonComponent/confirmationdialog/confirmation.service';
import { TransferProductDetails } from '../../../model/device/TransferProductDetails.model';
import { ExceptionHandlingService } from '../../../shared/ExceptionHandling.service';
import { CountryCacheService } from '../../../shared/Service/CacheService/countrycache.service';
import { DeviceOperationService } from '../DeviceService/Device-Operation/device-operation.service';
import { RoleApiCallService } from '../../../shared/Service/RoleService/role-api-call.service';
import { SSOLoginService } from '../../../shared/Service/SSO/ssologin.service';
import { AuthJwtService } from '../../../shared/auth-jwt.service';
import { DeviceService } from '../../../shared/device.service';
import { CustomerAssociationService } from '../../../shared/modalservice/customer-association.service';
import { PermissionService } from '../../../shared/permission.service';
import { BooleanKeyValueMappingDisplayNamePipe } from '../../../shared/pipes/Common/BooleanKeyValueMappingDisplayNamePipe.pipe';
import { EnumMappingDisplayNamePipe } from '../../../shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { HidePermissionNamePipe } from '../../../shared/pipes/Role/hidePermissionName.pipe';
import { DeviceTypeNamePipe } from '../../../shared/pipes/device-type-name.pipe';
import { PrintListPipe } from '../../../shared/pipes/printList.pipe';
import { CommonOperationsService } from '../../../shared/util/common-operations.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { DownloadService } from '../../../shared/util/download.service';
import { KeyValueMappingServiceService } from '../../../shared/util/key-value-mapping-service.service';
import { ValidationService } from '../../../shared/util/validation.service';
import { DeviceDetailComponent } from './device-detail.component';
import { ProductStatusEnum } from '../../../shared/enum/Common/ProductStatus.enum';
import { deviceTypesEnum } from '../../../shared/enum/deviceTypesEnum.enum';
import { PermissionAction } from '../../../shared/enum/Permission/permissionAction.enum';
import { DeviceDetailResource } from '../../../app.constants';


describe('DeviceDetailComponent', () => {
  let component: DeviceDetailComponent;
  let fixture: ComponentFixture<DeviceDetailComponent>;
  let deviceOperationServiceSpy: jasmine.SpyObj<DeviceOperationService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let countryCacheServiceSpy: jasmine.SpyObj<CountryCacheService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let customerAssociationServiceSpy: jasmine.SpyObj<CustomerAssociationService>;
  let validationServiceSpy: jasmine.SpyObj<ValidationService>;
  let confirmDialogServiceSpy: jasmine.SpyObj<ConfirmDialogService>;
  let commonOperationsServiceSpy: jasmine.SpyObj<CommonOperationsService>;
  let keyValueMappingServiceSpy: jasmine.SpyObj<KeyValueMappingServiceService>;
  let enumMappingDisplayNamePipeSpy: jasmine.SpyObj<EnumMappingDisplayNamePipe>;

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    deviceOperationServiceSpy = jasmine.createSpyObj('DeviceOperationService', [
      'loadDeviceDetail', 'getReleaseVersions', 'assignReleaseVersion', 'shouldDisableAssignButton',
      'validateSingleDevicePermissions', 'getDeviceDetailLoadingSubject', 'getDeviceDetailRefreshSubject',
      'getTransferDeviceUISubject', 'changeOperationForDevice'
    ]);

    countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheService', ['getCountryListFromCache']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getDevicePermission']);
    customerAssociationServiceSpy = jasmine.createSpyObj('CustomerAssociationService', ['openCustomerAssociationPopup']);
    validationServiceSpy = jasmine.createSpyObj('ValidationService', ['validateProductStatusForRMAAction']);
    confirmDialogServiceSpy = jasmine.createSpyObj('ConfirmDialogService', [
      'confirm', 'getBasicModelConfigForDisableAction', 'getBasicModelConfigForRMAAction', 'getErrorMessageDisableToRma'
    ]);
    commonOperationsServiceSpy = jasmine.createSpyObj('CommonOperationsService', [
      'accessDeviceListOperations', 'getCommonLoadingSubject', 'changeOperationForDevice'
    ]);
    keyValueMappingServiceSpy = jasmine.createSpyObj('KeyValueMappingServiceService', [
      'enumOptionToList', 'lockedUnlockOptionList', 'editEnableDisableOptionList'
    ]);
    enumMappingDisplayNamePipeSpy = jasmine.createSpyObj('EnumMappingDisplayNamePipe', ['transform']);

    // Setup mock return values
    countryCacheServiceSpy.getCountryListFromCache.and.returnValue(Promise.resolve([]));
    permissionServiceSpy.getDevicePermission.and.returnValue(true);
    keyValueMappingServiceSpy.enumOptionToList.and.returnValue([]);
    keyValueMappingServiceSpy.lockedUnlockOptionList.and.returnValue([]);
    keyValueMappingServiceSpy.editEnableDisableOptionList.and.returnValue([]);
    commonOperationsServiceSpy.getCommonLoadingSubject.and.returnValue(new Subject<boolean>());
    commonOperationsServiceSpy.accessDeviceListOperations.and.returnValue([]);

    await TestBed.configureTestingModule({
      declarations: [DeviceDetailComponent, TransferOrderModuleComponent, EnumMappingDisplayNamePipe, DeviceTypeNamePipe, BooleanKeyValueMappingDisplayNamePipe],
      imports: [],
      providers: [
        DeviceService,
        DownloadService,
        CommonsService,
        LocalStorageService,
        DatePipe,
        ExceptionHandlingService,
        AuthJwtService,
        SessionStorageService,
        SSOLoginService,
        RoleApiCallService,
        HidePermissionNamePipe,
        PrintListPipe,
        { provide: DeviceOperationService, useValue: deviceOperationServiceSpy },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: CustomerAssociationService, useValue: customerAssociationServiceSpy },
        { provide: ValidationService, useValue: validationServiceSpy },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceSpy },
        { provide: CommonOperationsService, useValue: commonOperationsServiceSpy },
        { provide: KeyValueMappingServiceService, useValue: keyValueMappingServiceSpy },
        { provide: EnumMappingDisplayNamePipe, useValue: enumMappingDisplayNamePipeSpy },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(DeviceDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {
    it('should initialize component with default values and call required services', async () => {
      // Arrange
      component.deviceIdInput = 123;
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());
      spyOn(component as any, 'subjectInit').and.stub();

      // Act
      await component.ngOnInit();

      // Assert
      expect(component.loading).toBe(false);
      expect(countryCacheServiceSpy.getCountryListFromCache).toHaveBeenCalledWith(false);
      expect(permissionServiceSpy.getDevicePermission).toHaveBeenCalledWith(PermissionAction.UPDATE_DEVICE_TYPE_ACTION);
      expect(keyValueMappingServiceSpy.enumOptionToList).toHaveBeenCalledWith(ProductStatusEnum);
      expect(keyValueMappingServiceSpy.lockedUnlockOptionList).toHaveBeenCalled();
      expect(keyValueMappingServiceSpy.editEnableDisableOptionList).toHaveBeenCalled();
      expect(component.deviceDetailDisplay).toBe(true);
      expect(component.deviceDetailModel).toHaveBeenCalledWith(123);
      expect((component as any).subjectInit).toHaveBeenCalled();
    });


  });

  describe('ngOnDestroy', () => {
    it('should unsubscribe from loading subscription if it exists', () => {
      // Arrange
      const mockSubscription = jasmine.createSpyObj('Subscription', ['unsubscribe']);
      component.subscriptionForLoading = mockSubscription;

      // Act
      component.ngOnDestroy();

      // Assert
      expect(mockSubscription.unsubscribe).toHaveBeenCalled();
    });

    it('should unsubscribe from device detail refresh subscription if it exists', () => {
      // Arrange
      const mockSubscription = jasmine.createSpyObj('Subscription', ['unsubscribe']);
      component.subscriptionForDeviceDetailRefresh = mockSubscription;

      // Act
      component.ngOnDestroy();

      // Assert
      expect(mockSubscription.unsubscribe).toHaveBeenCalled();
    });

    it('should unsubscribe from transfer device UI subscription if it exists', () => {
      // Arrange
      const mockSubscription = jasmine.createSpyObj('Subscription', ['unsubscribe']);
      component.subscriptionForTransferDeviceUI = mockSubscription;

      // Act
      component.ngOnDestroy();

      // Assert
      expect(mockSubscription.unsubscribe).toHaveBeenCalled();
    });

    it('should handle undefined subscriptions gracefully', () => {
      component.subscriptionForLoading = undefined;
      component.subscriptionForDeviceDetailRefresh = undefined;
      component.subscriptionForTransferDeviceUI = undefined;
      expect(() => component.ngOnDestroy()).not.toThrow();
    });
  });

  describe('subjectInit', () => {
    it('should subscribe to device detail loading subject', () => {
      // Arrange
      const mockLoadingSubject = new Subject<boolean>();
      const mockRefreshSubject = new Subject<any>();
      const mockTransferSubject = new Subject<boolean>();
      deviceOperationServiceSpy.getDeviceDetailLoadingSubject.and.returnValue(mockLoadingSubject);
      deviceOperationServiceSpy.getDeviceDetailRefreshSubject.and.returnValue(mockRefreshSubject);
      deviceOperationServiceSpy.getTransferDeviceUISubject.and.returnValue(mockTransferSubject);

      // Act
      (component as any).subjectInit();

      // Assert
      expect(deviceOperationServiceSpy.getDeviceDetailLoadingSubject).toHaveBeenCalled();
      expect(deviceOperationServiceSpy.getDeviceDetailRefreshSubject).toHaveBeenCalled();
      expect(deviceOperationServiceSpy.getTransferDeviceUISubject).toHaveBeenCalled();
      expect(component.subscriptionForLoading).toBeDefined();
      expect(component.subscriptionForDeviceDetailRefresh).toBeDefined();
      expect(component.subscriptionForTransferDeviceUI).toBeDefined();
    });

    it('should handle loading status changes', () => {
      // Arrange
      const mockLoadingSubject = new Subject<boolean>();
      deviceOperationServiceSpy.getDeviceDetailLoadingSubject.and.returnValue(mockLoadingSubject);
      deviceOperationServiceSpy.getDeviceDetailRefreshSubject.and.returnValue(new Subject<any>());
      deviceOperationServiceSpy.getTransferDeviceUISubject.and.returnValue(new Subject<boolean>());

      // Act
      (component as any).subjectInit();

      // Test loading state changes
      mockLoadingSubject.next(true);
      expect(component.loading).toBe(true);

      mockLoadingSubject.next(false);
      expect(component.loading).toBe(false);
    });

    it('should handle refresh subject changes', () => {
      // Arrange
      const mockRefreshSubject = new Subject<any>();
      deviceOperationServiceSpy.getDeviceDetailLoadingSubject.and.returnValue(new Subject<boolean>());
      deviceOperationServiceSpy.getDeviceDetailRefreshSubject.and.returnValue(mockRefreshSubject);
      deviceOperationServiceSpy.getTransferDeviceUISubject.and.returnValue(new Subject<boolean>());
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      // Act
      (component as any).subjectInit();

      // Test refresh with reload data
      mockRefreshSubject.next({ isReloadData: true });
      expect(component.deviceDetailModel).toHaveBeenCalledWith(component.deviceIdInput);
    });

    it('should handle transfer device UI subject changes', () => {
      // Arrange
      const mockTransferSubject = new Subject<boolean>();
      deviceOperationServiceSpy.getDeviceDetailLoadingSubject.and.returnValue(new Subject<boolean>());
      deviceOperationServiceSpy.getDeviceDetailRefreshSubject.and.returnValue(new Subject<any>());
      deviceOperationServiceSpy.getTransferDeviceUISubject.and.returnValue(mockTransferSubject);
      spyOn(component, 'transferOrderSelectionToggle');

      // Act
      (component as any).subjectInit();

      // Test transfer UI show
      mockTransferSubject.next(true);
      expect(component.transferOrderSelectionToggle).toHaveBeenCalledWith(false, true);
    });
  });

  describe('deviceDetailModel', () => {
    it('should call DeviceOperationService.loadDeviceDetail and handle success', async () => {
      // Arrange
      const mockDeviceId = 123;
      const mockDeviceDetail = {
        id: 123,
        deviceId: 'DEV001',
        deviceType: deviceTypesEnum.TEST_DEVICE,
        countryId: 1,
        packageVersion: '1.0.0',
        orderRecordType: 'SALES_ORDER'
      } as any;
      const mockTransferDetails = new TransferProductDetails(123, 456, 'SN001', 'TEST_DEVICE', 'Customer1');
      const mockResult = {
        success: true,
        deviceDetail: mockDeviceDetail,
        releaseVersionId: 789,
        transferProductDetails: mockTransferDetails
      };
      deviceOperationServiceSpy.loadDeviceDetail.and.returnValue(Promise.resolve(mockResult));
      spyOn(component as any, 'getReleaseVersions').and.returnValue(Promise.resolve());

      // Act
      await component.deviceDetailModel(mockDeviceId);

      // Assert
      expect(deviceOperationServiceSpy.loadDeviceDetail).toHaveBeenCalledWith(mockDeviceId);
      expect(component.deviceDetailResponse).toEqual(mockDeviceDetail);
      expect(component.releaseVersionId).toBe(789);
      expect(component.deviceDetailsTrasferProduct).toEqual(mockTransferDetails);
      expect((component as any).getReleaseVersions).toHaveBeenCalledWith(
        mockDeviceDetail.deviceType,
        mockDeviceDetail.countryId,
        mockDeviceDetail.packageVersion
      );
      expect(commonOperationsServiceSpy.accessDeviceListOperations).toHaveBeenCalled();
      expect(component.loading).toBe(false);
    });



    it('should handle DeviceOperationService.loadDeviceDetail failure and show warning', async () => {
      // Arrange
      const mockDeviceId = 123;
      const mockResult = {
        success: false,
        deviceDetail: null,
        releaseVersionId: -1,
        transferProductDetails: null
      };
      deviceOperationServiceSpy.loadDeviceDetail.and.returnValue(Promise.resolve(mockResult));
      spyOn(component, 'back').and.stub();

      // Act
      await component.deviceDetailModel(mockDeviceId);

      // Assert
      expect(toastrServiceMock.warning).toHaveBeenCalled();
      expect(component.back).toHaveBeenCalled();
      expect(component.loading).toBe(false);
    });
  });

  describe('assignReleaseVersion', () => {
    it('should call DeviceOperationService.assignReleaseVersion', async () => {
      // Arrange
      component.deviceDetailResponse = { id: 123 } as any;
      component.selectedReleaseVersion = 456;
      component.deviceIdInput = 123;
      deviceOperationServiceSpy.validateSingleDevicePermissions.and.returnValue(true);
      deviceOperationServiceSpy.assignReleaseVersion.and.returnValue(Promise.resolve());
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      // Act
      await component.assignReleaseVersion();

      // Assert
      expect(deviceOperationServiceSpy.validateSingleDevicePermissions).toHaveBeenCalled();
      expect(deviceOperationServiceSpy.assignReleaseVersion).toHaveBeenCalledWith(123, 456);
      expect(component.deviceDetailModel).toHaveBeenCalledWith(123);
    });


  });

  describe('changeReleaseVersion', () => {
    beforeEach(() => {
      component.releaseVersions = [
        { id: 456, itemNumber: 'ITEM456' },
        { id: 789, itemNumber: 'ITEM789' }
      ];
      component.releaseVersionId = 123;
    });

    it('should update selectedReleaseVersion when valid item found', () => {
      // Arrange
      const mockEvent = { target: { value: '456' } };
      deviceOperationServiceSpy.shouldDisableAssignButton.and.returnValue(false);

      // Act
      component.changeReleaseVersion(mockEvent);

      // Assert
      expect(component.selectedReleaseVersion).toBe(456);
      expect(deviceOperationServiceSpy.shouldDisableAssignButton).toHaveBeenCalledWith(456, 123);
      expect(component.btnReleaseVersionDisable).toBe(false);
    });


  });



  describe('back', () => {
    it('should emit showDevice event', () => {
      // Arrange
      spyOn(component.showDevice, 'emit');

      // Act
      component.back();

      // Assert
      expect(component.showDevice.emit).toHaveBeenCalled();
    });
  });

  describe('getReleaseVersions', () => {
    it('should get release versions successfully', async () => {
      // Arrange
      const mockResult = {
        success: true,
        releaseVersions: [{ id: 1, itemNumber: 'ITEM1' }],
        selectedReleaseVersion: 1,
        btnReleaseVersionDisable: false
      };
      deviceOperationServiceSpy.getReleaseVersions.and.returnValue(Promise.resolve(mockResult));
      component.releaseVersionId = 123;

      // Act
      await (component as any).getReleaseVersions(deviceTypesEnum.TEST_DEVICE, 1, '1.0.0');

      // Assert
      expect(deviceOperationServiceSpy.getReleaseVersions).toHaveBeenCalledWith(
        deviceTypesEnum.TEST_DEVICE, 1, '1.0.0', component.updateDeviceTypePermission
      );
      expect(component.releaseVersions).toEqual([{ id: 1, itemNumber: 'ITEM1' }]);
      expect(component.selectedReleaseVersion).toBe(123);
      expect(deviceOperationServiceSpy.shouldDisableAssignButton).toHaveBeenCalledWith(123, 123);
    });


  });

  describe('transferOrderSelectionToggle', () => {
    it('should toggle display states and call deviceDetailModel when deviceDetailDisplay is true', () => {
      // Arrange
      component.deviceIdInput = 123;
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      // Act
      component.transferOrderSelectionToggle(true, false);

      // Assert
      expect(component.deviceDetailDisplay).toBe(true);
      expect(component.transferOrderSelectionDisaplay).toBe(false);
      expect(component.deviceDetailModel).toHaveBeenCalledWith(123);
    });

    it('should toggle display states without calling deviceDetailModel when deviceDetailDisplay is false', () => {
      // Arrange
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      // Act
      component.transferOrderSelectionToggle(false, true);

      // Assert
      expect(component.deviceDetailDisplay).toBe(false);
      expect(component.transferOrderSelectionDisaplay).toBe(true);
      expect(component.deviceDetailModel).not.toHaveBeenCalled();
    });
  });

  describe('refreshDeviceDetailPage', () => {
    it('should call deviceDetailModel with deviceIdInput', () => {
      // Arrange
      component.deviceIdInput = 123;
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      // Act
      component.refreshDeviceDetailPage();

      // Assert
      expect(component.deviceDetailModel).toHaveBeenCalledWith(123);
    });
  });

  describe('changeDeviceOperation', () => {
    it('should call commonOperationsService.changeOperationForDevice', () => {
      const mockEvent = { target: { value: 'TEST_OPERATION' } };
      component.deviceDetailResponse = { id: 123 } as any;

      component.changeDeviceOperation(mockEvent);

      expect(deviceOperationServiceSpy.changeOperationForDevice).toHaveBeenCalledWith(
        'TEST_OPERATION',
        DeviceDetailResource,
        [123],
        [component.deviceDetailResponse]
      );
    });
  });

  // ==================== COMPREHENSIVE TESTS FOR 100% COVERAGE ====================

  describe('getReleaseVersions - comprehensive', () => {
    it('should handle getReleaseVersions failure', async () => {
      deviceOperationServiceSpy.getReleaseVersions.and.returnValue(Promise.resolve({
        success: false,
        releaseVersions: [],
        selectedReleaseVersion: -1,
        btnReleaseVersionDisable: true
      }));
      spyOn(component as any, 'setEmptyReleaseVersions');

      await (component as any).getReleaseVersions(deviceTypesEnum.TEST_DEVICE, 1, '1.0.0');

      expect((component as any).setEmptyReleaseVersions).toHaveBeenCalled();
    });

    it('should handle getReleaseVersions error', async () => {
      deviceOperationServiceSpy.getReleaseVersions.and.returnValue(Promise.reject(new Error('API Error')));
      spyOn(component as any, 'setEmptyReleaseVersions');

      await (component as any).getReleaseVersions(deviceTypesEnum.TEST_DEVICE, 1, '1.0.0');

      expect((component as any).setEmptyReleaseVersions).toHaveBeenCalled();
    });
  });

  describe('setEmptyReleaseVersions', () => {
    it('should set empty release versions and toggle assign button', () => {
      spyOn(component as any, 'toggleAssignButton');

      (component as any).setEmptyReleaseVersions();

      expect(component.releaseVersions).toEqual([]);
      expect(component.selectedReleaseVersion).toBe(-1);
      expect((component as any).toggleAssignButton).toHaveBeenCalled();
    });
  });

  describe('toggleAssignButton', () => {
    it('should call shouldDisableAssignButton', () => {
      component.selectedReleaseVersion = 123;
      component.releaseVersionId = 456;
      deviceOperationServiceSpy.shouldDisableAssignButton.and.returnValue(true);

      (component as any).toggleAssignButton();

      expect(deviceOperationServiceSpy.shouldDisableAssignButton).toHaveBeenCalledWith(123, 456);
      expect(component.btnReleaseVersionDisable).toBe(true);
    });
  });

  describe('changeReleaseVersion - edge cases', () => {
    it('should handle no matching item found', () => {
      component.releaseVersions = [
        { id: 456, itemNumber: 'ITEM456' },
        { id: 789, itemNumber: 'ITEM789' }
      ];
      component.releaseVersionId = 123;
      const mockEvent = { target: { value: '999' } }; // Non-existent ID
      deviceOperationServiceSpy.shouldDisableAssignButton.and.returnValue(true);

      component.changeReleaseVersion(mockEvent);

      expect(component.selectedReleaseVersion).toBe(-1);
      expect(component.btnReleaseVersionDisable).toBe(true);
    });

    it('should handle multiple matching items', () => {
      component.releaseVersions = [
        { id: 456, itemNumber: 'ITEM456' },
        { id: 456, itemNumber: 'ITEM456_DUPLICATE' }
      ];
      component.releaseVersionId = 123;
      const mockEvent = { target: { value: '456' } };
      deviceOperationServiceSpy.shouldDisableAssignButton.and.returnValue(false);

      component.changeReleaseVersion(mockEvent);

      expect(component.selectedReleaseVersion).toBe(-1); // Should be -1 when multiple items found
      expect(component.btnReleaseVersionDisable).toBe(false);
    });
  });

  describe('assignReleaseVersion - edge cases', () => {
    it('should return early when validation fails', async () => {
      component.deviceDetailResponse = { id: 123 } as any;
      deviceOperationServiceSpy.validateSingleDevicePermissions.and.returnValue(false);

      await component.assignReleaseVersion();

      expect(deviceOperationServiceSpy.assignReleaseVersion).not.toHaveBeenCalled();
    });

    it('should handle assignReleaseVersion error', async () => {
      component.deviceDetailResponse = { id: 123 } as any;
      component.selectedReleaseVersion = 456;
      component.deviceIdInput = 123;
      deviceOperationServiceSpy.validateSingleDevicePermissions.and.returnValue(true);
      deviceOperationServiceSpy.assignReleaseVersion.and.returnValue(Promise.reject(new Error('Assignment failed')));
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      try {
        await component.assignReleaseVersion();
      } catch (error) {
        expect(error.message).toBe('Assignment failed');
      }

      expect(component.loading).toBe(false);
    });
  });

  describe('deviceDetailModel - comprehensive', () => {
    it('should handle deviceDetailModel error', async () => {
      const mockDeviceId = 123;
      deviceOperationServiceSpy.loadDeviceDetail.and.returnValue(Promise.reject(new Error('Load failed')));

      try {
        await component.deviceDetailModel(mockDeviceId);
      } catch (error) {
        expect(error.message).toBe('Load failed');
      }

      expect(component.loading).toBe(false);
    });

    it('should handle transfer order device type', async () => {
      const mockDeviceId = 123;
      const mockDeviceDetail = {
        id: 123,
        deviceId: 'DEV001',
        deviceType: deviceTypesEnum.TEST_DEVICE,
        countryId: 1,
        packageVersion: '1.0.0',
        orderRecordType: 'TRANSFER_ORDER'
      } as any;
      const mockResult = {
        success: true,
        deviceDetail: mockDeviceDetail,
        releaseVersionId: 789,
        transferProductDetails: null
      };
      deviceOperationServiceSpy.loadDeviceDetail.and.returnValue(Promise.resolve(mockResult));
      spyOn(component as any, 'getReleaseVersions').and.returnValue(Promise.resolve());

      await component.deviceDetailModel(mockDeviceId);

      expect(commonOperationsServiceSpy.accessDeviceListOperations).toHaveBeenCalledWith(false, false, component.resource);
    });
  });






  describe('ngOnInit - comprehensive', () => {
    it('should handle countryCacheService error', async () => {
      countryCacheServiceSpy.getCountryListFromCache.and.returnValue(Promise.reject(new Error('Cache error')));
      component.deviceIdInput = 123;
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      try {
        await component.ngOnInit();
      } catch (error: any) {
        expect(error.message).toBe('Cache error');
      }

      // When error occurs in ngOnInit, loading remains false since the method doesn't complete
      expect(component.loading).toBe(false);
    });

    it('should initialize with all required properties', async () => {
      component.deviceIdInput = 123;
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());
      spyOn(component as any, 'subjectInit').and.stub();

      await component.ngOnInit();

      expect(component.updateDeviceTypePermission).toBe(true);
      expect(component.productStatusList).toEqual([]);
      expect(component.lockUnlockStatus).toEqual([]);
      expect(component.editEnableDisableStatus).toEqual([]);
      expect(component.deviceDetailDisplay).toBe(true);
    });

    it('should initialize with permission false', async () => {
      permissionServiceSpy.getDevicePermission.and.returnValue(false);
      component.deviceIdInput = 123;
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());
      spyOn(component as any, 'subjectInit').and.stub();

      await component.ngOnInit();

      expect(component.updateDeviceTypePermission).toBe(false);
    });
  });

  describe('subjectInit - comprehensive', () => {
    it('should handle undefined subjects gracefully', () => {
      deviceOperationServiceSpy.getDeviceDetailLoadingSubject.and.returnValue(undefined);
      deviceOperationServiceSpy.getDeviceDetailRefreshSubject.and.returnValue(undefined);
      deviceOperationServiceSpy.getTransferDeviceUISubject.and.returnValue(undefined);

      expect(() => (component as any).subjectInit()).not.toThrow();
    });

    it('should handle refresh subject with no reload data', () => {
      const mockRefreshSubject = new Subject<any>();
      deviceOperationServiceSpy.getDeviceDetailLoadingSubject.and.returnValue(new Subject<boolean>());
      deviceOperationServiceSpy.getDeviceDetailRefreshSubject.and.returnValue(mockRefreshSubject);
      deviceOperationServiceSpy.getTransferDeviceUISubject.and.returnValue(new Subject<boolean>());
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      (component as any).subjectInit();

      // Test refresh without reload data
      mockRefreshSubject.next({ isReloadData: false });
      expect(component.deviceDetailModel).not.toHaveBeenCalled();
    });

    it('should handle transfer device UI subject with false value', () => {
      const mockTransferSubject = new Subject<boolean>();
      deviceOperationServiceSpy.getDeviceDetailLoadingSubject.and.returnValue(new Subject<boolean>());
      deviceOperationServiceSpy.getDeviceDetailRefreshSubject.and.returnValue(new Subject<any>());
      deviceOperationServiceSpy.getTransferDeviceUISubject.and.returnValue(mockTransferSubject);
      spyOn(component, 'transferOrderSelectionToggle');

      (component as any).subjectInit();

      // Test transfer UI hide
      mockTransferSubject.next(false);
      expect(component.transferOrderSelectionToggle).not.toHaveBeenCalled();
    });
  });

  // ==================== COMPREHENSIVE TESTS FOR 100% COVERAGE ====================

  describe('toggleAssignButton - comprehensive', () => {
    it('should call shouldDisableAssignButton with correct parameters', () => {
      component.selectedReleaseVersion = 123;
      component.releaseVersionId = 456;
      deviceOperationServiceSpy.shouldDisableAssignButton.and.returnValue(false);

      (component as any).toggleAssignButton();

      expect(deviceOperationServiceSpy.shouldDisableAssignButton).toHaveBeenCalledWith(123, 456);
      expect(component.btnReleaseVersionDisable).toBe(false);
    });

    it('should set button to disabled when shouldDisableAssignButton returns true', () => {
      component.selectedReleaseVersion = 123;
      component.releaseVersionId = 456;
      deviceOperationServiceSpy.shouldDisableAssignButton.and.returnValue(true);

      (component as any).toggleAssignButton();

      expect(component.btnReleaseVersionDisable).toBe(true);
    });
  });

  describe('changeDeviceOperation - comprehensive', () => {
    it('should handle missing deviceDetailResponse', () => {
      const mockEvent = { target: { value: 'TEST_OPERATION' } };
      component.deviceDetailResponse = null;

      expect(() => component.changeDeviceOperation(mockEvent)).toThrow();
    });

    it('should call with correct parameters for different operations', () => {
      const mockEvent = { target: { value: 'LOCK_DEVICE' } };
      component.deviceDetailResponse = { id: 456 } as any;

      component.changeDeviceOperation(mockEvent);

      expect(deviceOperationServiceSpy.changeOperationForDevice).toHaveBeenCalledWith(
        'LOCK_DEVICE',
        DeviceDetailResource,
        [456],
        [component.deviceDetailResponse]
      );
    });
  });

  describe('transferOrderSelectionToggle - comprehensive', () => {
    it('should not call deviceDetailModel when deviceDetailDisplay is false', () => {
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      component.transferOrderSelectionToggle(false, true);

      expect(component.deviceDetailDisplay).toBe(false);
      expect(component.transferOrderSelectionDisaplay).toBe(true);
      expect(component.deviceDetailModel).not.toHaveBeenCalled();
    });

    it('should handle both parameters as false', () => {
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      component.transferOrderSelectionToggle(false, false);

      expect(component.deviceDetailDisplay).toBe(false);
      expect(component.transferOrderSelectionDisaplay).toBe(false);
      expect(component.deviceDetailModel).not.toHaveBeenCalled();
    });

    it('should handle both parameters as true', () => {
      component.deviceIdInput = 789;
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      component.transferOrderSelectionToggle(true, true);

      expect(component.deviceDetailDisplay).toBe(true);
      expect(component.transferOrderSelectionDisaplay).toBe(true);
      expect(component.deviceDetailModel).toHaveBeenCalledWith(789);
    });
  });

  describe('refreshDeviceDetailPage - comprehensive', () => {
    it('should call deviceDetailModel with different deviceIdInput values', () => {
      component.deviceIdInput = 999;
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      component.refreshDeviceDetailPage();

      expect(component.deviceDetailModel).toHaveBeenCalledWith(999);
    });

    it('should handle undefined deviceIdInput', () => {
      component.deviceIdInput = undefined;
      spyOn(component, 'deviceDetailModel').and.returnValue(Promise.resolve());

      component.refreshDeviceDetailPage();

      expect(component.deviceDetailModel).toHaveBeenCalledWith(undefined);
    });
  });

  describe('constructor and property initialization', () => {
    it('should initialize all string properties correctly', () => {
      expect(component.backBtnText).toBeDefined();
      expect(component.serialNo).toBeDefined();
      expect(component.hwId).toBeDefined();
      expect(component.salesOrderNumber).toBeDefined();
      expect(component.deviceType).toBeDefined();
      expect(component.country).toBeDefined();
      expect(component.systemSwVerstion).toBeDefined();
      expect(component.connectionState).toBeDefined();
      expect(component.customerName).toBeDefined();
      expect(component.locked).toBeDefined();
      expect(component.editable).toBeDefined();
      expect(component.status).toBeDefined();
      expect(component.lastCheckinDataAndType).toBeDefined();
      expect(component.macAddress).toBeDefined();
      expect(component.createDateAndTime).toBeDefined();
      expect(component.modifyDateAndTime).toBeDefined();
      expect(component.timeZone).toBeDefined();
      expect(component.probeVersion).toBeDefined();
      expect(component.handleVersion).toBeDefined();
      expect(component.pimsVersion).toBeDefined();
      expect(component.settingVersion).toBeDefined();
      expect(component.upsVersion).toBeDefined();
      expect(component.appVersion).toBeDefined();
      expect(component.jsonVersion).toBeDefined();
      expect(component.orderRecordType).toBeDefined();
      expect(component.partNo).toBeDefined();
      expect(component.po_no).toBeDefined();
      expect(component.customerEmail).toBeDefined();
      expect(component.releaseVersion).toBeDefined();
      expect(component.assignReleaseVersionBtnText).toBeDefined();
    });

    it('should initialize default values correctly', () => {
      // Create a fresh component without ngOnInit being called
      const freshFixture = TestBed.createComponent(DeviceDetailComponent);
      const freshComponent = freshFixture.componentInstance;

      expect(freshComponent.deviceDetailResponse).toBeNull();
      expect(freshComponent.loading).toBe(false);
      expect(freshComponent.releaseVersions).toEqual([]);
      expect(freshComponent.selectedReleaseVersion).toBe(-1);
      expect(freshComponent.btnReleaseVersionDisable).toBe(true);
      expect(freshComponent.deviceDetailDisplay).toBe(false);
      expect(freshComponent.transferOrderSelectionDisaplay).toBe(false);
      expect(freshComponent.page).toBe(0);
      expect(freshComponent.deviceOperations).toEqual([]);
      expect(freshComponent.updateDeviceTypePermission).toBe(false);
      expect(freshComponent.productStatusList).toEqual([]);
      expect(freshComponent.lockUnlockStatus).toEqual([]);
      expect(freshComponent.editEnableDisableStatus).toEqual([]);
      expect(freshComponent.deviceDetailsTrasferProduct).toBeNull();
    });

    it('should initialize subscription properties as undefined', () => {
      // Create a fresh component without ngOnInit being called
      const freshFixture = TestBed.createComponent(DeviceDetailComponent);
      const freshComponent = freshFixture.componentInstance;

      expect(freshComponent.subscriptionForLoading).toBeUndefined();
      expect(freshComponent.subscriptionForDeviceDetailRefresh).toBeUndefined();
      expect(freshComponent.subscriptionForTransferDeviceUI).toBeUndefined();
    });

    it('should initialize array properties correctly', () => {
      expect(Array.isArray(component.countryList)).toBe(true);
      expect(Array.isArray(component.releaseVersions)).toBe(true);
      expect(Array.isArray(component.deviceOperations)).toBe(true);
      expect(Array.isArray(component.productStatusList)).toBe(true);
      expect(Array.isArray(component.lockUnlockStatus)).toBe(true);
      expect(Array.isArray(component.editEnableDisableStatus)).toBe(true);
    });
  });
});
