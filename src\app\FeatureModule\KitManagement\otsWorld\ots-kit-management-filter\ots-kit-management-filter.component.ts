import { Component, Input, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { isUndefined } from 'is-what';
import { Subscription } from 'rxjs';
import { SPECIAL_CHARACTER_PATTERN, SPECIAL_CHARACTER_ERROR_MESSAGE, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH } from 'src/app/app.constants';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { OTSKitManagemantFilterAction } from 'src/app/model/KitManagement/otsWorld/OTSKitManagemantFilterAction.model';
import { OTSKitManagemantSearchRequestBody } from 'src/app/model/KitManagement/otsWorld/OTSKitManagemantSearchRequestBody.model';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { KitManagemantService } from 'src/app/shared/Service/KitManagemant/kit-managemant.service';
import { OtsKitManagemantService } from 'src/app/shared/Service/KitManagemant/ots-kit-managemant.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { ValidationService } from 'src/app/shared/util/validation.service';

@Component({
  selector: 'app-ots-kit-management-filter',
  templateUrl: './ots-kit-management-filter.component.html',
  styleUrl: './ots-kit-management-filter.component.css',
  encapsulation: ViewEncapsulation.None
})
export class OtsKitManagementFilterComponent {

  @Input("isFilterComponentInitWithApicall") isFilterComponentInitWithApicall: boolean;
  @Input("listPageRefreshForbackToDetailPage") listPageRefreshForbackToDetailPage: boolean;
  @Input("otsKitManagemantSearchRequestBody") otsKitManagemantSearchRequestBody: OTSKitManagemantSearchRequestBody;

  //MaxLength Message
  textBoxMaxLengthMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;


  filterOtsKitForm = new FormGroup({
    kitPartNumber: new FormControl(null, [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), this.validationService.removeSpaces, Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    probePartNumber: new FormControl(null, [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), this.validationService.removeSpaces, Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    countries: new FormControl([], []),
  });

  subscriptionForRefeshList: Subscription;

  defaultListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

  countrySetting: MultiSelectDropdownSettings = null;
  countriesList: CountryListResponse[] = [];


  constructor(private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private kitManagemantService: KitManagemantService,
    private otsKitManagemantService: OtsKitManagemantService,
    private commonsService: CommonsService,
    private commonOperationsService: CommonOperationsService,
    private countryCacheService: CountryCacheService,
    private validationService: ValidationService) { }


  /**
    * On Init 
    * 
    * <AUTHOR>
    */
  public ngOnInit(): void {
    this.countrySetting = this.multiSelectDropDownSettingService.getCountryDrpSetting(false, false);
    this.onInitSubject();
    this.getFilterList();
    if (this.isFilterComponentInitWithApicall) {
      this.clearFilter(this.defaultListingPageReloadSubjectParameter);
    }
  }

  /**
   * Destroy 
   */
  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForRefeshList)) { this.subscriptionForRefeshList.unsubscribe() }
  }

  /**
   * Subject 
   * 
   * <AUTHOR>
   */
  public onInitSubject(): void {
    /**
     * Kit Refresh After some Action 
     * <AUTHOR>
     */
    this.subscriptionForRefeshList = this.otsKitManagemantService.getOTSKitListRefreshSubject().subscribe((listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter) => {
      if (listingPageReloadSubjectParameter.isReloadData) {
        if (listingPageReloadSubjectParameter.isClearFilter) {
          this.clearFilter(listingPageReloadSubjectParameter);
        } else {
          //page change 1,2,3
          this.otsKitListPageRefresh(listingPageReloadSubjectParameter);
        }
      }
    });
  }


  /**
   * Get Filter Value List
   *
   * <AUTHOR>
   */
  public async getFilterList(): Promise<void> {
    // Only call API if service cache is empty (first time mount)
    if (this.otsKitManagemantService.getCountryList().length === 0) {
      this.countriesList = await this.countryCacheService.getCountryListFromCache(false);
      this.otsKitManagemantService.setCountryList(this.countriesList);
    } else {
      // Use cached data from service
      this.countriesList = this.otsKitManagemantService.getCountryList();
    }
    this.setFilterValue();
  }

  /**
   * set Filter value
   */
  private setFilterValue(): void {
    if (this.otsKitManagemantSearchRequestBody != null) {
      this.filterOtsKitForm.get('kitPartNumber').setValue(this.otsKitManagemantSearchRequestBody.otsKitPartNumberCode);
      this.filterOtsKitForm.get('probePartNumber').setValue(this.otsKitManagemantSearchRequestBody.probePartNumberCode);
      this.filterOtsKitForm.get('countries').setValue(this.commonsService.getDropDownValue(this.countriesList, this.otsKitManagemantSearchRequestBody.countryIds));
    }
    if (this.listPageRefreshForbackToDetailPage) {
      this.otsKitListPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }


  /**
   * Search Data
   * 
   * <AUTHOR>
   */
  public searchData(): void {
    let allFormValue = this.filterOtsKitForm.value;
    this.filterOtsKitForm.get('kitPartNumber').setValue(this.commonsService.checkNullFieldValue(allFormValue.kitPartNumber));
    this.filterOtsKitForm.get('probePartNumber').setValue(this.commonsService.checkNullFieldValue(allFormValue.probePartNumber));
    if (this.filterOtsKitForm.invalid || (this.commonsService.checkValueIsNullOrEmpty(allFormValue.kitPartNumber) && this.commonsService.checkValueIsNullOrEmpty(allFormValue.probePartNumber) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.countries))) {
      this.commonOperationsService.showEmptyFilterTosteMessge();
    } else {
      this.otsKitListPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }

  /**
   * Clear All filter and serach api call
   * 
   * <AUTHOR>
   * 
   * @param listingPageReloadSubjectParameter 
   */
  public clearFilter(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    this.filterOtsKitForm.get('kitPartNumber').setValue(null);
    this.filterOtsKitForm.get('countries').setValue([]);
    this.filterOtsKitForm.get('probePartNumber').setValue(null);
    this.otsKitListPageRefresh(listingPageReloadSubjectParameter);
  }

  /**
   * Kit List Page Serch api call
   * 
   * <AUTHOR>
   * 
   * @param listingPageReloadSubjectParameter 
   */
  private otsKitListPageRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    if (this.filterOtsKitForm.invalid) {
      this.filterOtsKitForm.reset();
    }
    let allFormValue = this.filterOtsKitForm.value;
    let countryIdList = this.commonsService.getIdsFromArray(allFormValue.countries);
    let otsKitManagemantSearchRequestBody = new OTSKitManagemantSearchRequestBody(
      this.commonsService.checkNullFieldValue(allFormValue.kitPartNumber), countryIdList, this.commonsService.checkNullFieldValue(allFormValue.probePartNumber));
    let otsKitManagemantFilterAction = new OTSKitManagemantFilterAction(listingPageReloadSubjectParameter, otsKitManagemantSearchRequestBody);
    this.otsKitManagemantService.callOTSKitListFilterRequestParameterSubject(otsKitManagemantFilterAction);
  }
}
