import { ProbeTypeEnum } from "src/app/shared/enum/ProbeType.enum";
import { ProbeConfigGroupFeatureRequest } from "./ProbeConfigGroupFeatureRequest.model";
import { ProbeConfigGroupPresetRequest } from "./ProbeConfigGroupPresetRequest.model";

export class ProbeConfigGroupAddOrUpdateRequest {
    probeType: ProbeTypeEnum;
    partNumberCode: string;
    description: string;
    features: Array<ProbeConfigGroupFeatureRequest>;
    presets: Array<ProbeConfigGroupPresetRequest>;

    constructor($probeType: ProbeTypeEnum, $partNumberCode: string, $description: string, $features: Array<ProbeConfigGroupFeatureRequest>, $presets: Array<ProbeConfigGroupPresetRequest>) {
        this.probeType = $probeType;
        this.partNumberCode = $partNumberCode;
        this.description = $description;
        this.features = $features;
        this.presets = $presets;
    }
}
