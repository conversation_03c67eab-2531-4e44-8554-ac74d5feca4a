import { ValidityEnum } from "../shared/enum/ValidityEnum.enum";
import { FeatureResponse } from "../model/ProbeConfigGroup/FeatureResponse.model";
import { PresetResponse } from "../model/ProbeConfigGroup/PresetResponse.model";

export const AiFast = new FeatureResponse(0, '', '', "AI Fast", ValidityEnum.PERPETUAL);

export const Lungs_Torso = new PresetResponse(0, '', '', "Lungs Torso", ValidityEnum.PERPETUAL);

export const Heart = new PresetResponse(0, '', '', "Heart", ValidityEnum.PERPETUAL);

export const Abdomen = new PresetResponse(0, '', '', "Abdomen", ValidityEnum.PERPETUAL);

export const Bladder = new PresetResponse(0, '', '', "Bladder", ValidityEnum.PERPETUAL);

export const Msk = new PresetResponse(0, '', '', "Msk", ValidityEnum.PERPETUAL);

export const PwDoppler = new FeatureResponse(0, '', '', "PW Doppler", ValidityEnum.PERPETUAL);

export const CwDoppler = new FeatureResponse(0, '', '', "CW Doppler", ValidityEnum.PERPETUAL);

export const AutoEf = new FeatureResponse(0, '', '', "Auto EF", ValidityEnum.PERPETUAL);

export const AutoDoppler = new FeatureResponse(0, '', '', "Auto Doppler", ValidityEnum.PERPETUAL);

export const Trio20 = new FeatureResponse(0, '', '', "Trio 2.0", ValidityEnum.PERPETUAL);

export const AutoPreset = new PresetResponse(0, '', '', "Auto Preset", ValidityEnum.PERPETUAL);

export const TDI = new FeatureResponse(0, '', '', "TDI", ValidityEnum.PERPETUAL);

export const Nerve = new PresetResponse(0, "P008425-001", '', "Nerve", ValidityEnum.PERPETUAL);