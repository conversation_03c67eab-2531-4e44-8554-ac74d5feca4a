import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';

@Pipe({
    name: 'transferOrderMessageDisplay'
})
export class TransferOrderMessageDisplayPipe implements PipeTransform {

    transform(serialNumber: string | null, destinationSalesOrderIsManual: boolean): string {
        if (destinationSalesOrderIsManual) {
            return '*Select the SR Number for the products belonging to the Transfer Order to associate the products with the Transferred Order.';
        }

        if (!isNullOrUndefined(serialNumber)) {
            return '* Select the Serial Number for the products belonging to the Transferred Order to associate the product with the Transfer Order';
        }

        return '*Select the Serial Number for the products belonging to the Transferred Order to associate the respective product with the Transfer Order.';
    }
}
