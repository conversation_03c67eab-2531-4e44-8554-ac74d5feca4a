import { Injectable } from '@angular/core';
import { catchError } from 'rxjs/operators';
import { firstValueFrom, Observable } from 'rxjs';
import { createRequestOption } from '../../util/request-util';
import { HttpClient, HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Member } from '../../../model/memberAdd.model';
import { UserSearchRequest } from '../../../model/User/userSearchRequest';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { UserResponse } from '../../../model/User/UserResponse.model';
import { UserListPageResponse } from '../../../model/User/UserListPageResponse.model';
import { CommonsService } from '../../util/commons.service';
import { SuccessMessageResponse } from '../../../model/common/SuccessMessageResponse.model';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';

@Injectable({
  providedIn: 'root'
})
export class UserApiCallService {


  private serverApiUrl = this.configInjectService.getServerApiUrl() + 'api/user';

  constructor(private http: HttpClient,
    private commonsService: CommonsService,
    private configInjectService: ConfigInjectService,
    private exceptionService: ExceptionHandlingService) { }


  public getMemberList(userSearchRequest?: UserSearchRequest, req?: any): Observable<HttpResponse<UserListPageResponse>> {
    const options = createRequestOption(req);
    return this.http.post<UserListPageResponse>(this.serverApiUrl + '/search', userSearchRequest, { params: options, observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  public saveMember(member: Member): Observable<HttpResponse<UserResponse>> {
    return this.http.post<UserResponse>(this.serverApiUrl, member, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  public updateMember(member: Member, id: number): Observable<HttpResponse<UserResponse>> {
    return this.http.put<UserResponse>(this.serverApiUrl + '/' + id, member, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  public getSingleMember(id): Observable<HttpResponse<UserResponse>> {
    return this.http.get<UserResponse>(this.serverApiUrl + '/' + id, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  public deleteMultipleMember(useridlist: Array<number>): Observable<HttpResponse<SuccessMessageResponse>> {
    return this.http.delete<SuccessMessageResponse>(this.serverApiUrl + '/' + useridlist, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
   * LogOut API 
   * 
   * <AUTHOR>
   * @returns 
   */
  public async logOutMember(): Promise<boolean> {
    let isLogout = false;
    try {
      await firstValueFrom(this.http.put<SuccessMessageResponse>(`${this.serverApiUrl}/logout`, {}, { observe: 'response' }));
      isLogout = true;
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        this.exceptionService.customErrorMessage(error);
      }
      isLogout = false;
    }
    return isLogout;
  }

}
