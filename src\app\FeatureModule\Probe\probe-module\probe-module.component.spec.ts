import { DatePipe } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbPagination } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { ProbListResource } from 'src/app/app.constants';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { DeviceService } from 'src/app/shared/device.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { ProbeOperationService } from '../ProbeService/Probe-Operation/probe-operation.service';
import { OtsProbesFilterComponent } from '../ots-probes-filter/ots-probes-filter.component';
import { OtsProbesComponent } from '../ots-probes-list/ots-probes-list.component';
import { ProbeModuleComponent } from './probe-module.component';

describe('ProbeModuleComponent', () => {
  let component: ProbeModuleComponent;
  let fixture: ComponentFixture<ProbeModuleComponent>;
  let probeOperationServiceSpy: jasmine.SpyObj<ProbeOperationService>;
  let authServiceSpy: jasmine.SpyObj<AuthJwtService>;


  beforeEach(async () => {
    const spy = jasmine.createSpyObj('ProbeOperationService', ['callRefreshPageSubject', 'getProbeListLoadingSubject', 'getProbeListFilterRequestParameterSubject', 'getProbeListRefreshSubject', 'clearAllFiltersAndRefresh', 'getSalesOrderNumberListFromCache', 'filterPageSubjectCallForReloadPage', 'setListPageRefreshForbackToOtherPage', 'getListPageRefreshForbackToOtherPage', 'setIsFilterHiddenForListing', 'getIsFilterHiddenForListing', 'setProbeSearchRequestBodyForListingApi', 'getProbeSearchRequestBodyForListingApi']);
    authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);

    await TestBed.configureTestingModule({
      declarations: [ProbeModuleComponent, OtsProbesComponent, OtsProbesFilterComponent],
      imports: [NgbPagination, NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, FormsModule],
      providers: [
        { provide: ProbeOperationService, useValue: spy },
        { provide: AuthJwtService, useValue: authServiceSpy },
        DeviceService,
        ConfirmDialogService,
        PermissionService,
        LocalStorageService,
        SessionStorageService,
        HidePermissionNamePipe,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        CommonOperationsService,
        ProbeApiService,
        DatePipe,
        commonsProviders(null)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(ProbeModuleComponent);
    component = fixture.componentInstance;
    probeOperationServiceSpy = TestBed.inject(ProbeOperationService) as jasmine.SpyObj<ProbeOperationService>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.isProbeListingPageDisplay).toBe(true);
    expect(component.isProbeDetailPageDisplay).toBe(false);
    expect(component.loading).toBe(false);
    expect(component.probeListResource).toBe(ProbListResource);
    expect(component.listPageRefreshForbackToDetailPage).toBe(false);
  });

  describe('showProbeDetail', () => {
    it('should show probe detail with probe id', () => {
      const probeId = 123;

      component.showProbeDetail(probeId);

      expect(component.probeIdInput).toBe(probeId);
      expect(component.isProbeDetailPageDisplay).toBe(true);
      expect(component.isProbeListingPageDisplay).toBe(false);
    });

    it('should show probe detail with different probe id', () => {
      const probeId = 999;

      component.showProbeDetail(probeId);

      expect(component.probeIdInput).toBe(probeId);
      expect(component.isProbeDetailPageDisplay).toBe(true);
      expect(component.isProbeListingPageDisplay).toBe(false);
    });

    it('should show probe detail when listing page was initially displayed', () => {
      const probeId = 456;
      component.isProbeListingPageDisplay = true;
      component.isProbeDetailPageDisplay = false;

      component.showProbeDetail(probeId);

      expect(component.probeIdInput).toBe(probeId);
      expect(component.isProbeDetailPageDisplay).toBe(true);
      expect(component.isProbeListingPageDisplay).toBe(false);
    });
  });

  describe('showProbe', () => {
    it('should show probe listing from detail page', () => {
      component.isProbeDetailPageDisplay = true;
      component.isProbeListingPageDisplay = false;

      component.showProbe();

      expect(component.isProbeListingPageDisplay).toBe(true);
      expect(component.isProbeDetailPageDisplay).toBe(false);
    });

    it('should show probe listing and call filterPageSubjectCallForReloadPage with correct parameters', () => {
      component.isProbeDetailPageDisplay = true;
      component.isProbeListingPageDisplay = false;

      component.showProbe();

      expect(component.isProbeListingPageDisplay).toBe(true);
      expect(component.isProbeDetailPageDisplay).toBe(false);
    });

    it('should show probe listing when already on listing page', () => {
      component.isProbeDetailPageDisplay = false;
      component.isProbeListingPageDisplay = true;

      component.showProbe();

      expect(component.isProbeListingPageDisplay).toBe(true);
      expect(component.isProbeDetailPageDisplay).toBe(false);
    });
  });

  describe('getProbeId', () => {
    it('should get probe id and set probeIdInput', () => {
      const probeId = 456;

      component.getProbeId(probeId);

      expect(component.probeIdInput).toBe(probeId);
    });

    it('should get probe id and update existing probeIdInput', () => {
      const initialProbeId = 123;
      const newProbeId = 789;

      component.probeIdInput = initialProbeId;
      component.getProbeId(newProbeId);

      expect(component.probeIdInput).toBe(newProbeId);
    });

    it('should handle zero as probe id', () => {
      const probeId = 0;

      component.getProbeId(probeId);

      expect(component.probeIdInput).toBe(0);
    });

    it('should handle negative probe id', () => {
      const probeId = -1;

      component.getProbeId(probeId);

      expect(component.probeIdInput).toBe(-1);
    });
  });

  describe('constructor and dependency injection', () => {
    it('should handle constructor injection', () => {
      expect(component['probeOperationService']).toBeDefined();
    });

    it('should inject ProbeOperationService correctly', () => {
      expect(component['probeOperationService']).toBe(probeOperationServiceSpy);
    });
  });

  describe('property assignments and getters', () => {
    it('should have probeListResource assigned correctly', () => {
      expect(component.probeListResource).toBe(ProbListResource);
    });

    it('should maintain probeListResource reference', () => {
      const originalResource = component.probeListResource;
      expect(originalResource).toBe(ProbListResource);
    });
  });

  describe('edge cases and state transitions', () => {
    it('should handle multiple calls to showProbeDetail', () => {
      component.showProbeDetail(111);
      expect(component.probeIdInput).toBe(111);

      component.showProbeDetail(222);
      expect(component.probeIdInput).toBe(222);
      expect(component.isProbeDetailPageDisplay).toBe(true);
      expect(component.isProbeListingPageDisplay).toBe(false);
    });

    it('should handle multiple calls to showProbe', () => {
      component.showProbe();
      component.showProbe();

      expect(component.isProbeListingPageDisplay).toBe(true);
      expect(component.isProbeDetailPageDisplay).toBe(false);
    });

    it('should handle state changes between detail and listing pages', () => {
      // Start with listing page
      component.isProbeListingPageDisplay = true;
      component.isProbeDetailPageDisplay = false;

      // Go to detail page
      component.showProbeDetail(123);
      expect(component.isProbeListingPageDisplay).toBe(false);
      expect(component.isProbeDetailPageDisplay).toBe(true);

      // Go back to listing page
      component.showProbe();
      expect(component.isProbeListingPageDisplay).toBe(true);
      expect(component.isProbeDetailPageDisplay).toBe(false);
    });
  });
});