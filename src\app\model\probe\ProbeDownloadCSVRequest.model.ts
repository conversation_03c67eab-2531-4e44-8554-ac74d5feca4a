import { ProbeListFilterRequestBody } from "./ProbeListFilterRequestBody.model";

export class ProbeDownloadCSVRequest {
    probeIds: Array<number>;
    timezoneOffset: number;
    filters: ProbeListFilterRequestBody;


    constructor($probeIds: Array<number>, $timezoneOffset: number, $filters: ProbeListFilterRequestBody) {
        this.probeIds = $probeIds;
        this.timezoneOffset = $timezoneOffset;
        this.filters = $filters;
    }

}