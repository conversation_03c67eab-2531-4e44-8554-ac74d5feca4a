export class AuditListResponse {
    id: number;
    module: string;
    modulePrimaryKey: number;
    moduleUnique<PERSON>ey: string;
    action: string;
    modifiedBy: string;
    modifiedDate: number;
    viewDetail: boolean;
    reverseButtonDisplay: boolean;
    reverseButtonDisable: boolean;
    archivedData: boolean;

    constructor( //NOSONAR
        id: number,
        module: string,
        modulePrimaryKey: number,
        moduleUniqueKey: string,
        action: string,
        modifiedBy: string,
        modifiedDate: number,
        viewDetail: boolean,
        reverseButtonDisplay: boolean,
        archivedData: boolean,
        reverseButtonDisable: boolean
    ) {
        this.id = id;
        this.module = module;
        this.modulePrimaryKey = modulePrimaryKey;
        this.moduleUniqueKey = moduleUniqueKey;
        this.action = action;
        this.modifiedBy = modifiedBy;
        this.modifiedDate = modifiedDate;
        this.viewDetail = viewDetail;
        this.reverseButtonDisplay = reverseButtonDisplay;
        this.archivedData = archivedData;
        this.reverseButtonDisable = reverseButtonDisable;
    }
}
