import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ProbeConfigType } from '../../enum/ProbeConfigType.enum';

@Pipe({
    name: 'assignConfigDisablePipe'
})
export class AssignConfigDisablePipe implements PipeTransform {

    transform(config: Array<any>, configId: number, probeTypeConfig: ProbeConfigType): boolean {
        if (!isNullOrUndefined(config)) {
            let selectedFeature = null;
            if (ProbeConfigType.FEATURE == probeTypeConfig) {
                selectedFeature = config.find(obj => obj.featureId === configId);
            } else if (ProbeConfigType.PRESET == probeTypeConfig) {
                selectedFeature = config.find(obj => obj.presetId === configId);
            }
            if (!isNullOrUndefined(selectedFeature)) {
                return !(selectedFeature.partNumbers.some(partNumber => partNumber.allowedToEdit));
            }
        }
        return false;
    }
}
