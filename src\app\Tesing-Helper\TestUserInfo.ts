import { CountryListResponse } from "../model/Country/CountryListResponse.model";
import { Pageable } from "../model/common/pageable.model";
import { Sort } from "../model/sort.model";
import { UserListPageResponse } from "../model/User/UserListPageResponse.model";
import { UserResponse } from "../model/User/UserResponse.model";
import { UserSearchResponse } from "../model/User/UserSearchResponse";
import { AddUserComponent } from "../user/add-user/add-user.component";
import { UpdateUserComponent } from "../user/update-user/update-user.component";
import { ComponentFixture } from "@angular/core/testing";

export function getMockUserList(pageNumber: number): UserListPageResponse {
    let userSearchResponses: Array<UserSearchResponse> = [];
    let userSearchResponse1 = new UserSearchResponse(1, "aakash.brahmbhatt", 1721369109321,
        1734330404156, ["Device / SV Team"], ["Algeria", "Argentina"], ["DEVICES"]);
    let userSearchResponse2 = new UserSearchResponse(2, "raj.brahmbhatt", 1721369109321,
        1734330404156, ["Device / SV Team"], ["Algeria", "Argentina"], ["DEVICES"]);
    userSearchResponses.push(userSearchResponse1);
    userSearchResponses.push(userSearchResponse2);
    let sort: Sort = new Sort(true, false, true);
    let pageable = new Pageable(sort, 0, 10, 0, true, false);
    return new UserListPageResponse(pageable, 2, false, 20, 10, true, sort, 10, pageNumber, false, userSearchResponses);
}

export function getMockUserDetail(): UserResponse {
    let modules: Array<string> = [
        "DEVICES",
        "JOBS",
        "LOGS",
        "PROBES",
        "SOFTWARE BUILDS",
        "VIDEOS"
    ];

    let countryMasters: Array<CountryListResponse> = [];
    let countryMaster = new CountryListResponse(1, "algeria", null);
    countryMasters.push(countryMaster);

    return new UserResponse(1, "aakash.brahmbhatt",
        "Aakash", "Brahmbhatt", "<EMAIL>", 1721369109321,
        1731302098899, 123, 123, 1734330404156, [
        "Device / SV Team"
    ], countryMasters, false, modules);

}

export async function fillAndSubmitForm(component: any, fixture: ComponentFixture<AddUserComponent | UpdateUserComponent>, userbutton: string) {
    component?.get('firstName')?.setValue('aakash');
    component?.get('lastName')?.setValue('brahmbhatt');
    component?.get('email')?.setValue('<EMAIL>');
    component?.get('userRoles')?.setValue(['test']);
    component?.get('country')?.setValue([{ "id": 1 }, { "id": 2 }]);
    fixture.detectChanges();
    const createUserBtn = fixture.nativeElement.querySelector(userbutton);
    createUserBtn.click();
    fixture.detectChanges();
    await fixture.whenStable();
}

