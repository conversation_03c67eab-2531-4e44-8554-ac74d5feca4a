import { CommonsService } from '../../util/commons.service';
import { SoftwareBuildMappedDevicePipe } from './software-build-mapped-device.pipe';

describe('InventoryMappedDevicePipe', () => {
  let pipe: SoftwareBuildMappedDevicePipe;
  let mockCommonsService: jasmine.SpyObj<CommonsService>;

  beforeEach(() => {
    // Mock the CommonsService
    mockCommonsService = jasmine.createSpyObj('CommonsService', ['getDeviceTypeEnumToString']);
    pipe = new SoftwareBuildMappedDevicePipe(mockCommonsService);
  });

  it('should create an instance of the pipe', () => {
    expect(pipe).toBeTruthy();
  });
});
