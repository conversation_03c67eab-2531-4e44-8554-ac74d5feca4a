import { BaseResponse } from "../common/BaseResponse.model";
import { PartNumberBaseResponse } from "./PartNumberBaseResponse.model";

export class FeaturesBaseResponse extends BaseResponse {
    partNumbers: Array<PartNumberBaseResponse>;

    constructor($id: number, $name: string, $displayName: string, $partNumbers: Array<PartNumberBaseResponse>) {
        super($id, $name, $displayName);
        this.partNumbers = $partNumbers;
    }

}