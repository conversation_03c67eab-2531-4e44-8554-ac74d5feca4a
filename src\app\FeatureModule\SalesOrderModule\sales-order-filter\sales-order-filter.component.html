<!--####################################################-->
<!--Filter Start-->
<!--Sales Order Filter--->
<!--####################################################-->
<form id="salesOrderFilterform" role="form" class="form" [formGroup]="filterSalesOrderForm">

  <!------------------------------------->
  <!-----------Order Number-------------->
  <!------------------------------------->
  <div class="form-group">
    <label class="form-control-label" for="field_order_number"><strong>Order
        Number</strong></label>
    <input class="form-control" type="text" formControlName="salesOrderNumber" />
    <div *ngIf="(filterSalesOrderForm.get('salesOrderNumber').touched || filterSalesOrderForm.get('salesOrderNumber').dirty) &&  
                filterSalesOrderForm.get('salesOrderNumber').invalid">
      <div *ngIf="filterSalesOrderForm.get('salesOrderNumber').errors['maxlength']">
        <span class="alert-color font-12">{{small_textBoxMaxLengthMessage}}</span>
      </div>
      <div *ngIf="filterSalesOrderForm.get('salesOrderNumber').errors['pattern']">
        <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
      </div>
    </div>
  </div>

  <!------------------------------------->
  <!-----------Type-------------->
  <!------------------------------------->
  <div class="form-group">
    <label class="form-control-label" for="field_order_type"><strong>Order Type</strong></label>
    <ng-multiselect-dropdown name="field_order_type" class="devicePageDeviceType" [placeholder]="''"
      formControlName="orderType" [settings]="orderTypeSetting" [data]="orderTypeList">
    </ng-multiselect-dropdown>
  </div>

  <!------------------------------------->
  <!-----------Customer Name-------------->
  <!------------------------------------->
  <div class="form-group">
    <label class="form-control-label" for="field_customer_name"><strong>Customer Name
      </strong></label>
    <input class="form-control" type="text" formControlName="customerName" id="field_customer_name" />
    <div *ngIf="(filterSalesOrderForm.get('customerName').touched || filterSalesOrderForm.get('customerName').dirty) &&  
                        filterSalesOrderForm.get('customerName').invalid">
      <div *ngIf="filterSalesOrderForm.get('customerName').errors['maxlength']">
        <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
      </div>
      <div *ngIf="filterSalesOrderForm.get('customerName').errors['pattern']">
        <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
      </div>
    </div>
  </div>

  <!------------------------------------->
  <!-----------Customer Email-------------->
  <!------------------------------------->
  <div class="form-group">
    <label class="form-control-label" for="field_customer_email"><strong>Customer E-mail
      </strong></label>
    <input class="form-control" type="text" formControlName="customerEmail" id="field_customer_email" />
    <div *ngIf="(filterSalesOrderForm.get('customerEmail').touched || filterSalesOrderForm.get('customerEmail').dirty) &&  
                            filterSalesOrderForm.get('customerEmail').invalid">
      <div *ngIf="filterSalesOrderForm.get('customerEmail').errors['maxlength']">
        <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
      </div>
      <div *ngIf="filterSalesOrderForm.get('customerEmail').errors['pattern']">
        <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
      </div>
    </div>
  </div>

  <!------------------------------------->
  <!-----------Country------------------->
  <!------------------------------------->
  <div class="form-group">
    <label class="form-control-label" for="field_order_status"><strong>Country</strong></label>
    <ng-multiselect-dropdown name="country" [placeholder]="''" formControlName="countries" [settings]="countrySetting"
      [data]="countryList">
    </ng-multiselect-dropdown>
  </div>

  <!------------------------------------->
  <!-----------PO#----------------------->
  <!------------------------------------->
  <div class="form-group">
    <label class="form-control-label" for="field_po"><strong>PO#
      </strong></label>
    <input class="form-control" type="text" formControlName="po" id="field_po" />
    <div *ngIf="(filterSalesOrderForm.get('po').touched || filterSalesOrderForm.get('po').dirty) &&  
                                filterSalesOrderForm.get('po').invalid">
      <div *ngIf="filterSalesOrderForm.get('po').errors['maxlength']">
        <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
      </div>
      <div *ngIf="filterSalesOrderForm.get('po').errors['pattern']">
        <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
      </div>
    </div>
  </div>

  <!-----------Order Record Type--------->
  <!------------------------------------->
  <div class="form-group">
    <label class="form-control-label" for="field_order_record_type"><strong>Order Record Type</strong></label>
    <ng-multiselect-dropdown name="field_order_record_type" [placeholder]="''" formControlName="orderRecordType"
      [settings]="orderRecordTypeSetting" [data]="orderRecordTypeList">
    </ng-multiselect-dropdown>
  </div>

  <!------------------------------------->
  <!-----------Status-------------->
  <!------------------------------------->
  <div class="form-group">
    <label class="form-control-label" for="field_productConfigStatus"><strong>SO Config Status</strong></label>
    <ng-multiselect-dropdown name="field_productConfigStatus" [placeholder]="''" formControlName="productConfigStatus"
      [settings]="productConfigStatusListSetting" [data]="productConfigStatusList">
    </ng-multiselect-dropdown>
  </div>


  <hr class="mt-1 mb-2">
  <!--####################################################-->
  <!---------Action Button Start------->
  <!--####################################################-->
  <div class="">
    <button class="btn btn-sm btn-orange mr-3" (click)="searchData()" id="salesOrderFilterSearch"
      [disabled]="filterSalesOrderForm.invalid">Search</button>
    <button class="btn btn-sm btn-orange" (click)="clearFilter(defaultListingPageReloadSubjectParameter)">Clear</button>
  </div>
  <!--####################################################-->
  <!---------Action Button End------->
  <!--####################################################-->
</form>
<!--####################################################-->
<!--Filter End-->
<!--Sales Order Filter--->
<!--####################################################-->