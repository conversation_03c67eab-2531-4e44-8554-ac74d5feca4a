import { Pipe, PipeTransform } from '@angular/core';
import { ProductConfigStatus } from '../../enum/SalesOrder/ProductConfigStatus.enum';

@Pipe({
    name: 'hideAndShowTransferOrderCheckBox'
})
export class HideAndShowTransferOrderCheckBox implements PipeTransform {

    transform(ProductStatus: ProductConfigStatus): boolean {
        return ![ProductConfigStatus.TRANSFERRED, ProductConfigStatus.RMA].includes(ProductConfigStatus[ProductStatus]);
    }

}
