import { HttpResponse } from '@angular/common/http';
import { Component, Input, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SalesOrderSchedulerManualSyncTimeResponse } from 'src/app/model/SalesOrder/SalesOrderSchedulerManuleSyncTimeResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';

@Component({
  selector: 'app-manual-sync',
  templateUrl: './manual-sync.component.html',
  styleUrl: './manual-sync.component.css'
})
export class ManualSyncComponent implements OnInit {

  @Input() title: string;
  @Input() btnOkText: string;
  @Input() salesOrderSchedulerManualSyncTimeResponse: SalesOrderSchedulerManualSyncTimeResponse;

  inProgress: boolean = true;
  timeoutIds: number[] = []; // Array to store all timeout IDs
  attemptCount: number = 0;

  // Define constants for backoff strategy
  private readonly INITIAL_INTERVAL = 1000; // 1 second in milliseconds
  private readonly MAX_INTERVAL = 32000;    // 32 seconds in milliseconds

  constructor(private salesOrderApiCallService: SalesOrderApiCallService,
    private activeModal: NgbActiveModal,
    private exceptionService: ExceptionHandlingService,
  ) { }

  /**
  * <AUTHOR>
  * Lifecycle hook that is called after data-bound properties of a directive are initialized.
  * Initiates variable-interval sync process if salesOrderSchedulerManualSyncTimeResponse exists.
  */
  public ngOnInit(): void {
    if (this.salesOrderSchedulerManualSyncTimeResponse) {
      this.startExponentialBackoffSync(this.salesOrderSchedulerManualSyncTimeResponse.id);
    }
  }

  /**
   * <AUTHOR>
   * Closes the active modal with a true value indicating acceptance.
   * Used to confirm and close the modal dialog with a positive response.
   * @returns {void}
   */
  public accept(): void {
    this.activeModal.close(true);
  }

  /**
  * Initiates a synchronization process with exponential backoff intervals
  * First call after 1s, second after 2s, third after 4s, fourth after 8s, etc.
  * The sync process continues until either the inProgress flag becomes false or an error occurs.
  * Maximum interval is capped at 32 seconds.
  * 
  * @param id - The identifier for the sales order to be synchronized
  */
  private startExponentialBackoffSync(id: number): void {
    this.clearAllTimeouts(); // Clear any existing timeouts
    this.attemptCount = 0;
    this.scheduleNextSync(id);
  }

  /**
  * Schedules the next sync call with an exponential backoff interval
  * 
  * @param id - The identifier for the sales order to be synchronized
  */
  private scheduleNextSync(id: number): void {
    if (!this.inProgress) {
      return; // Stop scheduling if sync is complete
    }

    this.attemptCount++;
    // Calculate the interval using exponential backoff: 2^(attemptCount-1) * INITIAL_INTERVAL
    // This gives us: 1s, 2s, 4s, 8s, 16s, 32s, 32s, 32s, etc.
    const exponentialInterval = Math.min(
      Math.pow(2, this.attemptCount - 1) * this.INITIAL_INTERVAL,
      this.MAX_INTERVAL
    );

    const timeoutId = window.setTimeout(() => {
      this.performSync(id);
    }, exponentialInterval);

    this.timeoutIds.push(timeoutId);
  }

  /**
  * Performs the actual sync API call and handles the response
  * 
  * @param id - The identifier for the sales order to be synchronized
  */
  private performSync(id: number): void {
    this.salesOrderApiCallService.manualsync(id).subscribe({
      next: (response: HttpResponse<boolean>) => {
        this.inProgress = response.body;
        if (this.inProgress) {
          // If still in progress, schedule next sync with exponential backoff
          this.scheduleNextSync(id);
        }
      },
      error: (error) => {
        this.exceptionService.customErrorMessage(error);
        this.clearAllTimeouts();
      }
    });
  }

  /**
  * Clears all pending timeouts
  */
  private clearAllTimeouts(): void {
    this.timeoutIds.forEach(id => window.clearTimeout(id));
    this.timeoutIds = [];
  }

  /**
  * <AUTHOR>
  * Lifecycle hook that is called when the component is destroyed.
  * Cleans up all timeout timers to prevent memory leaks.
  */
  public ngOnDestroy(): void {
    this.clearAllTimeouts();
  }
}