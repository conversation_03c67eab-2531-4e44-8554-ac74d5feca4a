export class KitManagementBaseResponse {
    id: number;
    description: string;
    videoVersion: string;
    softWareVersion: string;
    modifiedDate: number;
    country: string;
    kitPartNumber: string;
    language: string;
    softWarePartNumber: string;
    dummyKitPartNumber: boolean;

    constructor(//NOSONAR
        id: number,
        description: string,
        videoVersion: string,
        softWareVersion: string,
        modifiedDate: number,
        country: string,
        kitPartNumber: string,
        language: string,
        softWarePartNumber: string,
        dummyKitPartNumber: boolean
    ) {
        this.id = id;
        this.description = description;
        this.videoVersion = videoVersion;
        this.softWareVersion = softWareVersion;
        this.modifiedDate = modifiedDate;
        this.country = country;
        this.kitPartNumber = kitPartNumber;
        this.language = language;
        this.softWarePartNumber = softWarePartNumber;
        this.dummyKitPartNumber = dummyKitPartNumber;
    }
}