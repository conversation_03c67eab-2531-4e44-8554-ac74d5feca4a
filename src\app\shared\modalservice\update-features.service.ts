import { Injectable } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AssignFeaturesComponent } from 'src/app/FeatureModule/Probe/assign-features/assign-features.component';
import { Cancel, ConfigureFeaturesHeader, Confirm } from 'src/app/app.constants';
import { BasicModelConfig } from 'src/app/model/common/BasicModelConfig.model';
import { ConfigureLicenceDetails } from 'src/app/model/probe/ConfigureLicenceDetails.model';
import { ConfigureLicenceResponse } from 'src/app/model/probe/ConfigureLicenceResponse.model';

@Injectable({
  providedIn: 'root'
})
export class UpdateFeaturesService {

  constructor(private modalService: NgbModal) { }
  public openAssignProbeFeatureModel(
    basicModelConfig: BasicModelConfig,
    configureLicenceDetails: ConfigureLicenceDetails,
    resource: string,
    activeModelReference?: NgbActiveModal,
    dialogSize: 'sm' | 'lg' = 'sm'): Promise<ConfigureLicenceResponse> {
    const modalRef = this.modalService.open(AssignFeaturesComponent, { windowClass: "modal fade modal-dialog-lg-middle", size: 'xl' });
    modalRef.componentInstance.basicModelConfig = basicModelConfig;
    modalRef.componentInstance.configureLicenceDetails = configureLicenceDetails;
    modalRef.componentInstance.resource = resource;
    modalRef.componentInstance.activeModelReference = activeModelReference;
    return modalRef.result;

  }

  public getAssignProbeBasicModelConfigDetail(): BasicModelConfig {
    return new BasicModelConfig(ConfigureFeaturesHeader, null, Confirm, Cancel);
  }
}