import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { lastValueFrom } from 'rxjs';
import { DeviceService } from './device.service';
import { ExceptionHandlingService } from './ExceptionHandling.service';

@Injectable({
  providedIn: 'root'
})
export class DeviceActionService {

  constructor(private deviceService: DeviceService, private exceptionService: ExceptionHandlingService, private toste: ToastrService,) { }

  /**
  * Device Edit Enable/Disable Api Call
  * 
  * <AUTHOR>
  * 
  * @param deviceIdList
  * @param enableState  
  * @returns 
  */
  public async deviceEditAction(deviceIdList: Array<number>, enableState: boolean): Promise<void> {
    try {
      let res = await lastValueFrom(this.deviceService.editEnableDisableForDevice(deviceIdList, enableState));
      this.toste.success(res.body.message);
    } catch (error) {
      this.exceptionService.customErrorMessage(error);
      throw error;  // Rethrow the error to be handled by the caller
    }
  }
}
