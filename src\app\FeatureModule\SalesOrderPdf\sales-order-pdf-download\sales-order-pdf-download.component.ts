import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';
import { SalesOrderPdfLetterResponse } from 'src/app/model/SalesOrder/SalesOrderPdfLetterResponse';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { DownloadService } from 'src/app/shared/util/download.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';

@Component({
  selector: 'app-sales-order-pdf-download',
  templateUrl: './sales-order-pdf-download.component.html',
  styleUrls: ['./sales-order-pdf-download.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class SalesOrderPdfDownloadComponent implements OnInit {

  salesOrderNumberList: string[] = [];
  dropdownSettingsForSalesOrderNumber: MultiSelectDropdownSettings = null;

  salesOrderForm = new FormGroup({
    salesOrderNumber: new FormControl('', [Validators.required]),
  });

  constructor(private activeModal: NgbActiveModal,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private commonOperationsService: CommonOperationsService,
    private probeApiService: ProbeApiService,
    private downloadService: DownloadService,
    private exceptionService: ExceptionHandlingService,
    private salesOrderApiCallService: SalesOrderApiCallService) { }

  ngOnInit() {
    this.dropdownSettingsForSalesOrderNumber = this.multiSelectDropDownSettingService.getSalesOrderNumberDrpSetting(true, null, null, false);
    this.setInitData()
  }

  public async setInitData(): Promise<void> {
    this.setLoading(true);
    this.salesOrderNumberList = await this.salesOrderApiCallService.getSalesOrderNumberList();
    this.setLoading(false);
  }

  private setLoading(status: boolean): void {
    this.commonOperationsService.callCommonLoadingSubject(status);
  }

  /**
   * Close Popup
   * <AUTHOR>
   * 
   */
  public decline(): void {
    this.activeModal.close(false);
  }

  /**
   * download Pdf File
   */
  public downloadSalesOrderPdfLetter(): void {
    this.setLoading(true);
    let salesOrderNumber = this.salesOrderForm.get('salesOrderNumber').value;
    this.probeApiService.downloadSalesOrderPdfLetter(salesOrderNumber).subscribe({
      next: (res: HttpResponse<SalesOrderPdfLetterResponse>) => {
        this.downloadMyFileAsync(res.body.pdfDownloadURL);
      }, error: (error: HttpErrorResponse) => {
        this.setLoading(false);
        this.activeModal.close(true);
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  /**
   * Download File Async call
   * <AUTHOR>
   * @param url 
   */
  public async downloadMyFileAsync(url: string): Promise<void> {
    await this.downloadService.downloadMyFile(url);
    this.setLoading(false);
    this.activeModal.close(true);
  }

}
