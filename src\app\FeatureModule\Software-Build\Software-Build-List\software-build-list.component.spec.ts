
import { DatePipe } from '@angular/common';
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { INTERNAL_SERVER_ERROR, ITEMS_PER_PAGE } from '../../../app.constants';
import { ConfirmDialogService } from '../../CommonComponent/confirmationdialog/confirmation.service';
import { UploadPackageResponse } from '../../../model/upload.package.response';
import { DownloadJsonResponse, SubTitleInformation, VideoInformation } from '../../../model/video/download-json-response.model';
import { AuthJwtService } from '../../../shared/auth-jwt.service';
import { ExceptionHandlingService } from '../../../shared/ExceptionHandling.service';
import { PermissionService } from '../../../shared/permission.service';
import { HideShowEditIconForSoftwareBuildePipe } from '../../../shared/pipes/HideShowEditIconForSoftwareBuildePipe.pipe';
import { SoftwareBuildMappedDevicePipe } from '../../../shared/pipes/Software Build/software-build-mapped-device.pipe';
import { SoftwareBuildStatusPipe } from '../../../shared/pipes/Software Build/software-build-status.pipe';
import { JsonNamePipe } from '../../../shared/pipes/json-name.pipe';
import { ModelDisplayNameListToStringConvert } from '../../../shared/pipes/ModelDisplayNameListToStringConvert.pipe';
import { PrintListPipe } from '../../../shared/pipes/printList.pipe';
import { CountryCacheService } from '../../../shared/Service/CacheService/countrycache.service';
import { SSOLoginService } from '../../../shared/Service/SSO/ssologin.service';
import { UploadScanService } from '../../../shared/upload-scan.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { DownloadService } from '../../../shared/util/download.service';
import { ModuleValidationServiceService } from '../../../shared/util/module-validation-service.service';
import { VideoService } from '../../../shared/videoservice/video.service';
import { commonsProviders, countryListResponse, testDropdownInteraction, testToggleFilter } from '../../../Tesing-Helper/test-utils';
import { EditSoftwareBuildComponent } from '../Edit-Software-Build/edit-software-build.component';
import { SoftwareBuildConfirmComponent } from '../Software-build-confirm/software-build-confirm.component';
import { SoftwareBuildListComponent } from './software-build-list.component';
import { UploadSoftwareBuildDialogComponent } from '../upload-software-build-dialog/upload-software-build-dialog.component';
import { SoftwareBuildApiCallService } from '../software-build-services/software-api-call/software-build-api-call.service';

describe('ItemInventoryComponent', () => {
  let component: SoftwareBuildListComponent;
  let fixture: ComponentFixture<SoftwareBuildListComponent>;
  let localStorageServiceMock: jasmine.SpyObj<LocalStorageService>;
  let authServiceSpy: jasmine.SpyObj<AuthJwtService>;
  let countryCacheServiceSpy: jasmine.SpyObj<CountryCacheService>;
  let exceptionHandlingService: ExceptionHandlingService;
  let softwareBuildApiCallServiceMock: jasmine.SpyObj<SoftwareBuildApiCallService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let videoServiceSpy: VideoService;
  let moduleValidationServiceService: ModuleValidationServiceService;
  let downloadService: jasmine.SpyObj<DownloadService>;

  let inventoryListResponse = {
    "content": [{
      "id": 78,
      "version": "21.22.11",
      "title": "rajT201",
      "attachmentName": "Checkstyle3.9.1 2.zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 12,
        "version": "1.1.1"
      },
      "partNumber": "P005400-008",
      "createdDate": 1735566757199,
      "isActive": true,
      "countries": ["Argentina"],
      "deviceTypes": []
    }, {
      "id": 77,
      "version": "21.22.11",
      "title": "rajT20",
      "attachmentName": "Checkstyle3.9.1 2.zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 12,
        "version": "1.1.1"
      },
      "partNumber": "P005400-008",
      "createdDate": 1735566495667,
      "isActive": true,
      "countries": ["Akshay443"],
      "deviceTypes": []
    }, {
      "id": 76,
      "version": "20.22.11",
      "title": "test",
      "attachmentName": "Checkstyle3.9.1 2.zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 12,
        "version": "1.1.1"
      },
      "partNumber": "P005400-008",
      "createdDate": 1735566194841,
      "isActive": true,
      "countries": ["Austria"],
      "deviceTypes": []
    }, {
      "id": 73,
      "version": "8.0.1",
      "title": "80170",
      "attachmentName": "thor-ota-********.zip",
      "releaseNoteName": "2.3.json",
      "jsonMaster": {
        "id": 1,
        "version": "2.3"
      },
      "partNumber": "EN",
      "createdDate": 1733485708685,
      "isActive": true,
      "countries": ["India"],
      "deviceTypes": ["CLIENT"]
    }, {
      "id": 71,
      "version": "7.2.5",
      "title": "DemoTestin121",
      "attachmentName": "Final 1 (2) (1).zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 11,
        "version": "2.1.1"
      },
      "partNumber": "P005400-006",
      "createdDate": 1733382706002,
      "isActive": true,
      "countries": ["Belgium"],
      "deviceTypes": []
    }, {
      "id": 70,
      "version": "1.2.3",
      "title": "Demo1q32",
      "attachmentName": "full_backup.zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 11,
        "version": "2.1.1"
      },
      "partNumber": "P006245-009",
      "createdDate": 1733381445732,
      "isActive": true,
      "countries": ["Bosnia and Herzegovina"],
      "deviceTypes": []
    }, {
      "id": 69,
      "version": "7.2.5",
      "title": "Demo12313",
      "attachmentName": "full_backup.zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 15,
        "version": "1.2.1"
      },
      "partNumber": "P005400-006",
      "createdDate": 1733380870026,
      "isActive": true,
      "countries": ["Austria"],
      "deviceTypes": []
    }, {
      "id": 68,
      "version": "1.2.3",
      "title": "Testing12emo",
      "attachmentName": "test (1).zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 15,
        "version": "1.2.1"
      },
      "partNumber": "P005400-006",
      "createdDate": 1733379217893,
      "isActive": true,
      "countries": ["Austria"],
      "deviceTypes": []
    }, {
      "id": 67,
      "version": "1.2.3",
      "title": "demo1233",
      "attachmentName": "test (1).zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 13,
        "version": "1.2.1"
      },
      "partNumber": "P005400-008",
      "createdDate": 1733372843029,
      "isActive": true,
      "countries": ["Belgium"],
      "deviceTypes": []
    }, {
      "id": 66,
      "version": "1.7.3",
      "title": "AkshayDemo13",
      "attachmentName": "full_backup.zip",
      "releaseNoteName": "20.20.20.json",
      "jsonMaster": {
        "id": 12,
        "version": "1.1.1"
      },
      "partNumber": "P005400-008",
      "createdDate": 1733317463066,
      "isActive": true,
      "countries": ["Austria"],
      "deviceTypes": []
    }],
    "pageable": {
      "pageNumber": 0,
      "pageSize": 10,
      "sort": {
        "empty": true,
        "sorted": false,
        "unsorted": true
      },
      "offset": 0,
      "paged": true,
      "unpaged": false
    },
    "last": false,
    "totalPages": 6,
    "totalElements": 53,
    "size": 10,
    "number": 0,
    "sort": {
      "empty": true,
      "sorted": false,
      "unsorted": true
    },
    "first": true,
    "numberOfElements": 10,
    "empty": false
  }

  let videoJosnResponse = [{
    "id": 12,
    "version": "1.1.1"
  }, {
    "id": 13,
    "version": "1.2.1"
  }, {
    "id": 15,
    "version": "1.2.1"
  }, {
    "id": 11,
    "version": "2.1.1"
  }, {
    "id": 19,
    "version": "2.1.3"
  }, {
    "id": 1,
    "version": "2.3"
  }, {
    "id": 21,
    "version": "20.20.20"
  }, {
    "id": 20,
    "version": "test"
  }, {
    "id": 16,
    "version": "Video3_v2-3"
  }]

  const jsonVersion: DownloadJsonResponse = new DownloadJsonResponse(
    "2.3.4",
    [new VideoInformation(
      "sajkl",
      "0:00",
      1234567,
      [new SubTitleInformation("enxsd", "ddccsr")]
    )]
  );
  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getSoftwearBuildPermission']);
    countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheService', ['getCountryListFromCache', 'filterOutUserAssociatedCountries']);
    softwareBuildApiCallServiceMock = jasmine.createSpyObj('InventoryService', ['inventoryList', 'mapInventoryWithDeviceType', 'markInventoriesActiveInactive', 'getAttachmentUrl', 'deleteSoftwearBuild', 'updateInventory', 'pushFileToStorage', 'uploadFileToStorage']);
    downloadService = jasmine.createSpyObj('DownloadService', ['downloadMyFile', 'getisLoadingSubject', 'setLoading']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue(['Algeria', 'Argentina', 'Australia', 'Austria', 'Belgium', 'india']);
    await TestBed.configureTestingModule({

      declarations: [SoftwareBuildListComponent, JsonNamePipe, ModelDisplayNameListToStringConvert, SoftwareBuildMappedDevicePipe, SoftwareBuildStatusPipe, HideShowEditIconForSoftwareBuildePipe, SoftwareBuildConfirmComponent, EditSoftwareBuildComponent, UploadSoftwareBuildDialogComponent],
      imports: [NgbPaginationModule, NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, FormsModule],
      providers: [{ provide: LocalStorageService, useValue: localStorageServiceMock },
        DatePipe,
        PrintListPipe,
        UploadScanService,
        ExceptionHandlingService,
        SessionStorageService,
        AuthJwtService,
        CommonsService,
        ConfirmDialogService,
        SSOLoginService,
      { provide: DownloadService, useValue: downloadService },
      { provide: SoftwareBuildApiCallService, useValue: softwareBuildApiCallServiceMock },
      { provide: CountryCacheService, useValue: countryCacheServiceSpy },
      commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(SoftwareBuildListComponent);
    component = fixture.componentInstance;
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    videoServiceSpy = TestBed.inject(VideoService);
    moduleValidationServiceService = TestBed.inject(ModuleValidationServiceService)
    fixture.detectChanges();

    // Mock API call to fetch the Role list and return a successful response
    softwareBuildApiCallServiceMock.inventoryList?.and.returnValue(of(new HttpResponse<any>({
      body: inventoryListResponse, // Mocked response data
      status: 200, // Simulate a successful API response
    })));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnInit', () => {

    it('should initialize the component when the user is authenticated', async () => {

      // Simulate user authentication
      authServiceSpy.isAuthenticate?.and.returnValue(true);

      // Simulate user permissions for accessing Role functionality
      permissionServiceSpy.getSoftwearBuildPermission?.and.returnValue(true);

      component.ngOnInit();
      // **Act: Trigger Angular lifecycle methods and finalize change detection**
      fixture.detectChanges(); // Run initial change detection
      await fixture.whenStable(); // Wait for asynchronous operations to complete

      spyOn(component, 'clickOnRefreshButton')?.and.callThrough();
      const button = fixture.nativeElement.querySelector('#refresh_InventoryList');
      expect(button).toBeTruthy();
      button?.click();
      expect(component.clickOnRefreshButton).toHaveBeenCalled();

      // Test dropdown interaction
      testDropdownInteraction(fixture, component, '#itemInventoryShowEntry');

      const paginationElement = fixture.debugElement.query(By.css('#inventory-pagination'));
      paginationElement.triggerEventHandler('pageChange', 2);
      fixture.detectChanges();
      expect(component.page).toBe(1);
    });

    // Test: Toggles filter visibility and updates the button text
    it('should toggle filter visibility and update the button text', () => {
      // Use the generic utility to test the toggle behavior
      testToggleFilter(component);
    });

    // Test: Displays an error message when an internal server error occurs
    it('should display an error message when an internal server error occurs', () => {
      // Arrange: Simulate a 500 Internal Server Error
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
      softwareBuildApiCallServiceMock.inventoryList?.and.returnValue(throwError(() => mockError));
      spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

      // Act: Trigger the data loading method
      component.ngOnInit();

      // Assert: Verify the error handling logic is triggered
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
      expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
    });

    // Test: Handles an error response when loading data
    it('should handle error response in the LoadAll method', () => {
      // Arrange: Simulate a 500 status response with no body
      authServiceSpy.isAuthenticate?.and.returnValue(true);
      softwareBuildApiCallServiceMock.inventoryList?.and.returnValue(of(new HttpResponse<any>({
        body: null,
        status: 500,
        statusText: 'OK',
      })));

      // Act: Call `loadAll` to attempt data loading
      component.ngOnInit();

      // Assert: Verify no data is loaded and error state is managed
      expect(component.inventory).toEqual(null);
      expect(component.totalItemInventory).toEqual(0);
      expect(component.totalItemInventoryDisplay).toEqual(0);
      expect(component.loading).toBeFalse();
    });
  });

  it('should initialize form controls on ngOnInit role List', async () => {
    // **Arrange: Setup the required dependencies and initial state**

    // Simulate user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);

    // Simulate user permissions for accessing getSoftwearBuildPermission functionality
    permissionServiceSpy.getSoftwearBuildPermission?.and.returnValue(true);

    countryCacheServiceSpy.getCountryListFromCache?.and.returnValue(Promise.resolve(countryListResponse));
    countryCacheServiceSpy.filterOutUserAssociatedCountries?.and.returnValue(countryListResponse);

    const mockHttpResponse = new HttpResponse({
      body: videoJosnResponse,
      status: 200,
    });

    // Spy on the service method and return a full HttpResponse
    spyOn(videoServiceSpy, 'getListofJsonVersions').and.returnValue(of(mockHttpResponse));
    spyOn(videoServiceSpy, 'downloadJSONFile').and.returnValue(of(new HttpResponse<DownloadJsonResponse>({
      body: jsonVersion,
      status: 200, // Set a valid HTTP status
      statusText: 'OK', // Optionally set a status text
    })));

    spyOn(moduleValidationServiceService, 'validateWithUserCountryForMultileRecord').and.returnValue(true)

    // Arrange: Create a mock HttpResponse
    const mockResponse = new HttpResponse({
      body: { message: 'Device Type(s) mapped successfully.' },
      status: 200, // Set a valid HTTP status
      statusText: 'OK', // Optionally set a status text
    });

    // Mock API response for updating a Role
    softwareBuildApiCallServiceMock.mapInventoryWithDeviceType?.and.returnValue(of(mockResponse));

    // Arrange: Create a mock HttpResponse
    const mockResponseActive = new HttpResponse({
      body: { message: 'Software Build(s) activated Successfully.' },
      status: 200, // Set a valid HTTP status
      statusText: 'OK', // Optionally set a status text
    });

    softwareBuildApiCallServiceMock.updateInventory?.and.returnValue(of(new HttpResponse({
      body: { "message": "Software Build Updated Successfully." },
      status: 200, // Set a valid HTTP status
      statusText: 'OK', // Optionally set a status text
    })));

    // Mock API response for updating a Role
    softwareBuildApiCallServiceMock.markInventoriesActiveInactive?.and.returnValue(of(mockResponseActive))

    softwareBuildApiCallServiceMock.getAttachmentUrl?.and.returnValue(of(new HttpResponse({
      body: {
        "urlToDownload": "https://newqardmsa2.core.windows.net/rdm-container/firmware%2F9.9.1.2%2Flogs%20%2820%29.zip?sv=2024-11-04&se=2025-01-02T09%3A40%3A22Z&sr=b&sp=r&sig=6ZWQzWa%2Ba1MKN%2BNLrO%2Bl2go811tUU7086pGLRmwQ%3D"
      },
      status: 200, // Set a valid HTTP status
      statusText: 'OK', // Optionally set a status text
    })))

    softwareBuildApiCallServiceMock.deleteSoftwearBuild?.and.returnValue(of(new HttpResponse({
      body: { "message": "Software Build Deleted Successfully." },
      status: 200, // Set a valid HTTP status
      statusText: 'OK', // Optionally set a status text
    })))

    // Mock the method to resolve a promise
    downloadService.downloadMyFile.and.returnValue(Promise.resolve());

    // Initialize the component (triggers ngOnInit and sets up component state)
    component.ngOnInit();
    fixture.detectChanges(); // Ensure the view updates after ngOnInit execution

    // Verify permissions and pagination-related properties
    expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE); // Items per page should match the configured constant
    expect(component.drpselectsize).toBe(ITEMS_PER_PAGE); // Dropdown size should match the configured constant
    expect(component.previousPage).toBe(1); // Default page should be the first page
    expect(component.dataSizes).toEqual(['10', '25', '50', '100']); // Data sizes for pagination dropdown

    // Assert that the component state reflects the API response data
    expect(component.totalItems).toEqual(inventoryListResponse.totalElements); // Total items should match the API response
    expect(component.totalItemInventory).toEqual(inventoryListResponse.totalElements); // Total records should match the response
    expect(component.totalItemInventoryDisplay).toEqual(inventoryListResponse.numberOfElements); // Displayed records should match the response

    // Trigger a search without selecting any filter to test error handling
    const roleFilterSearch = fixture.debugElement.query(By.css('#inventoryFilterSearch'));
    roleFilterSearch.nativeElement.click();
    fixture.detectChanges();
    await fixture.whenStable();
    expect(toastrServiceMock.info).toHaveBeenCalledWith("Please Select Filter To Search");

    // Update form controls and simulate search
    spyOn(component, 'reloadItem').and.callThrough();
    component.filterForm?.get('itemNumber').setValue('12SD23');
    fixture.detectChanges();
    await fixture.whenStable();
    roleFilterSearch.nativeElement.click();
    expect(component.reloadItem).toHaveBeenCalled();

    component.updateSoftwareBuildPermission = true;
    spyOn(component, 'selectAllItem').and.callThrough();
    fixture.detectChanges();
    await fixture.whenStable();
    const selectAllCheckbox = fixture.nativeElement.querySelector(`#${component.selectAllCheckboxId}`);
    selectAllCheckbox?.click(); // User selects all items
    fixture.detectChanges();
    expect(component.selectAllItem).toHaveBeenCalled();

    selectAllCheckbox?.click(); // User selects all items
    fixture.detectChanges();
    await fixture.whenStable();
    spyOn(component, 'onChangeInventory').and.callThrough();

    const selectOneCheckBox = fixture.debugElement.query(By.css('#item_78'));

    const checkbox: HTMLInputElement = selectOneCheckBox.nativeElement;
    checkbox.checked = true;
    checkbox.dispatchEvent(new Event('change'));
    expect(component.onChangeInventory).toHaveBeenCalled();

    checkbox.checked = false;
    checkbox.dispatchEvent(new Event('change'));

    checkbox.checked = true;
    checkbox.dispatchEvent(new Event('change'));

    fixture.detectChanges();
    await fixture.whenStable();

    const operationDropDown = fixture.debugElement.query(By.css('#inventoryOperation')).nativeElement;
    // Set the value to the second option ("Map to Client Devices")
    operationDropDown.value = 'Map to Client Devices';
    operationDropDown.dispatchEvent(new Event('change'));


    fixture.detectChanges();
    await fixture.whenStable();

    // First Call: Initial Operation
    let inventoryOperationsElement = document.querySelector('app-software-build-confirm');
    let inventoryOperationsComponent = window['ng'].getComponent(inventoryOperationsElement);

    spyOn(inventoryOperationsComponent, 'accept').and.callThrough();
    const okBtn = document.querySelector<HTMLElement>('#deleteModelOkButton');

    // Simulate the button click
    okBtn.click();

    // Verify the method was called
    expect(inventoryOperationsComponent.accept).toHaveBeenCalled();

    // Clear DOM and Component References
    inventoryOperationsElement = null;
    inventoryOperationsComponent = null;

    // Reset DOM
    document.body.innerHTML = '';

    // Detect Changes for Cleanup
    fixture.detectChanges();
    await fixture.whenStable();

    // Second Call: Mark as Active
    checkbox.checked = true;
    checkbox.dispatchEvent(new Event('change'));

    fixture.detectChanges();
    await fixture.whenStable();

    operationDropDown.value = 'Mark as active';
    operationDropDown.dispatchEvent(new Event('change'));

    fixture.detectChanges();
    await fixture.whenStable();

    // Query Updated Component
    let inventoryOperationsElementActive = document.querySelector('app-software-build-confirm');
    let inventoryOperationsComponentActive = window['ng'].getComponent(inventoryOperationsElementActive);

    spyOn(inventoryOperationsComponentActive, 'accept').and.callThrough();
    const okBtnActive = document.querySelector<HTMLElement>('#deleteModelOkButton');

    // Simulate the button click for the updated state
    okBtnActive.click();

    fixture.detectChanges();
    await fixture.whenStable();

    // Verify Updated Toastr Call
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Software Build(s) activated Successfully.');

    // Clear DOM and Component References
    inventoryOperationsElementActive = null;
    inventoryOperationsComponentActive = null;

    // Reset DOM
    document.body.innerHTML = '';

    // Detect Changes for Cleanup
    fixture.detectChanges();
    await fixture.whenStable();

    // third Call: Mark as Active
    checkbox.checked = true;
    checkbox.dispatchEvent(new Event('change'));

    fixture.detectChanges();
    await fixture.whenStable();

    operationDropDown.value = 'Mark as Inactive';
    operationDropDown.dispatchEvent(new Event('change'));

    // Query Updated Component
    let inventoryOperationsElementActive1 = document.querySelector('app-software-build-confirm');
    let inventoryOperationsComponentActive1 = window['ng'].getComponent(inventoryOperationsElementActive1);

    inventoryOperationsComponentActive1.decline();
    inventoryOperationsComponentActive1.close();

    fixture.detectChanges();
    await fixture.whenStable();

    checkbox.checked = false;
    checkbox.dispatchEvent(new Event('change'));

    fixture.detectChanges();
    await fixture.whenStable();

    operationDropDown.value = 'Mark as Inactive';
    operationDropDown.dispatchEvent(new Event('change'));

    fixture.detectChanges();
    await fixture.whenStable();

    expect(toastrServiceMock.info).toHaveBeenCalledWith("Please Select Software Build(s)")


    fixture.detectChanges();
    await fixture.whenStable();

    // third Call: Mark as Active
    operationDropDown.value = 'Map to Demo Devices';
    operationDropDown.dispatchEvent(new Event('change'));

    fixture.detectChanges();
    await fixture.whenStable();

    expect(toastrServiceMock.info).toHaveBeenCalledWith("Please Select Software Build(s)");


    checkbox.checked = true;
    checkbox.dispatchEvent(new Event('change'));

    fixture.detectChanges();
    await fixture.whenStable();

    operationDropDown.value = 'Map to Both type of Devices';
    operationDropDown.dispatchEvent(new Event('change'));


    checkbox.checked = true;
    checkbox.dispatchEvent(new Event('change'));

    fixture.detectChanges();
    await fixture.whenStable();

    operationDropDown.value = 'Map to Both type of Devicesss';
    operationDropDown.dispatchEvent(new Event('change'));

    component.deleteSoftwareBuildPermission = true
    selectAllCheckbox?.click();

    const getAttachmentUrlId = fixture.debugElement.query(By.css('#getAttachmentUrlId')).nativeElement;
    getAttachmentUrlId.click();

    fixture.detectChanges();
    await fixture.whenStable();

    const deleteSoftwearBuildItemId = fixture.debugElement.query(By.css('#deleteSoftwearBuildItemId78')).nativeElement;

    deleteSoftwearBuildItemId.click();

    fixture.detectChanges();
    await fixture.whenStable();
    // Query Updated Component
    let deleteOperationsElementActive = document.querySelector('app-software-build-confirm');
    let deleteOperationsComponentActive = window['ng'].getComponent(deleteOperationsElementActive);

    spyOn(deleteOperationsComponentActive, 'accept').and.callThrough();
    const okDeleteActive = document.querySelector<HTMLElement>('#deleteItemOkay');

    // Simulate the button click for the updated state
    okDeleteActive.click();


    fixture.detectChanges();
    await fixture.whenStable();

    expect(toastrServiceMock.success).toHaveBeenCalledWith('Software Build Deleted Successfully.');

    fixture.detectChanges();
    await fixture.whenStable();

    spyOn(component, 'downloadJSONFile').and.callThrough();


    const downloadJSONFileId = fixture.debugElement.query(By.css('#downloadJSONFileId')).nativeElement;
    downloadJSONFileId.click();

    expect(component.downloadJSONFile).toHaveBeenCalled();


    fixture.detectChanges();
    await fixture.whenStable();

    const getReleaseNoteUrl = fixture.debugElement.query(By.css('#getReleaseNoteUrlId')).nativeElement;
    getReleaseNoteUrl.click();

    fixture.detectChanges();
    await fixture.whenStable();

    const editInventoryDetailsId = fixture.debugElement.query(By.css('#editInventoryDetailsId')).nativeElement;
    editInventoryDetailsId.click();


    fixture.detectChanges();
    await fixture.whenStable();

    let editInventoryElement = document.querySelector('app-edit-software-build');
    let editInventoryComponent = window['ng'].getComponent(editInventoryElement);

    spyOn(editInventoryComponent, 'decline').and.callThrough();
    const editInventoryCancel = document.querySelector<HTMLElement>('#editInventoryCancel');
    editInventoryCancel.click();

    fixture.detectChanges();
    await fixture.whenStable();

    editInventoryDetailsId.click();
    spyOn(editInventoryComponent, 'accept').and.callThrough();

    const uploadBtn = document.querySelector<HTMLElement>('#uploadBtn');
    uploadBtn.click();

    fixture.detectChanges();
    await fixture.whenStable();

    expect(editInventoryComponent.accept).toHaveBeenCalled();

  });


  it("should handel error api call", async () => {
    // Simulate user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);
    spyOn(moduleValidationServiceService, 'validateWithUserCountryForMultileRecord').and.returnValue(true);
    spyOn(exceptionHandlingService, 'customErrorMessage')?.and.callThrough();

    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
    // Spy on the service method and return a full HttpResponse
    spyOn(videoServiceSpy, 'downloadJSONFile').and.returnValue(throwError(() => mockError));
    softwareBuildApiCallServiceMock.getAttachmentUrl.and.returnValue(throwError(() => mockError));
    softwareBuildApiCallServiceMock.mapInventoryWithDeviceType?.and.returnValue(throwError(() => mockError));
    softwareBuildApiCallServiceMock.markInventoriesActiveInactive?.and.returnValue(throwError(() => mockError));
    softwareBuildApiCallServiceMock.deleteSoftwearBuild?.and.returnValue(throwError(() => mockError));
    // Call ngOnInit, it may contain async operations
    component.ngOnInit();

    // Wait for the async operations to complete
    fixture.detectChanges();  // Detect changes
    await fixture.whenStable(); // Wait for async operations to finish
    component.downloadJSONFile(null);
    component.getAttachmentUrlApiCall(2, null);
    component.updateSoftwareBuildPermission = true;
    component.deleteSoftwareBuildPermission = true;

    // Wait for the async operations to complete
    fixture.detectChanges();  // Detect changes
    await fixture.whenStable(); // Wait for async operations to finish
    const selectOneCheckBox = fixture.debugElement.query(By.css('#item_78'));

    const checkbox: HTMLInputElement = selectOneCheckBox.nativeElement;
    checkbox.checked = true;
    checkbox.dispatchEvent(new Event('change'));

    fixture.detectChanges();
    await fixture.whenStable();

    const operationDropDown = fixture.debugElement.query(By.css('#inventoryOperation')).nativeElement;
    // Set the value to the second option ("Map to Client Devices")
    operationDropDown.value = 'Map to Client Devices';
    operationDropDown.dispatchEvent(new Event('change'));


    fixture.detectChanges();
    await fixture.whenStable();

    // First Call: Initial Operation
    let inventoryOperationsElement = document.querySelector('app-software-build-confirm');
    let inventoryOperationsComponent = window['ng'].getComponent(inventoryOperationsElement);

    spyOn(inventoryOperationsComponent, 'accept').and.callThrough();
    const okBtn = document.querySelector<HTMLElement>('#deleteModelOkButton');

    // Simulate the button click
    okBtn.click();

    // Verify the method was called
    expect(inventoryOperationsComponent.accept).toHaveBeenCalled();

    // Clear DOM and Component References
    inventoryOperationsElement = null;
    inventoryOperationsComponent = null;

    // Reset DOM
    document.body.innerHTML = '';

    fixture.detectChanges();
    await fixture.whenStable();

    // Second Call: Mark as Active
    checkbox.checked = true;
    checkbox.dispatchEvent(new Event('change'));


    operationDropDown.value = 'Mark as active';
    operationDropDown.dispatchEvent(new Event('change'));

    fixture.detectChanges();
    await fixture.whenStable();

    // Query Updated Component
    let inventoryOperationsElementActive = document.querySelector('app-software-build-confirm');
    let inventoryOperationsComponentActive = window['ng'].getComponent(inventoryOperationsElementActive);

    spyOn(inventoryOperationsComponentActive, 'accept').and.callThrough();
    const okBtnActive = document.querySelector<HTMLElement>('#deleteModelOkButton');

    // Simulate the button click for the updated state
    okBtnActive.click();

    fixture.detectChanges();
    await fixture.whenStable();

    const deleteSoftwearBuildItemId = fixture.debugElement.query(By.css('#deleteSoftwearBuildItemId78')).nativeElement;
    deleteSoftwearBuildItemId.click();

    fixture.detectChanges();
    await fixture.whenStable();

    const okDeleteActive = document.querySelector<HTMLElement>('#deleteItemOkay');

    // Simulate the button click for the updated state
    okDeleteActive.click();

    // Clear DOM and Component References
    inventoryOperationsElementActive = null;
    inventoryOperationsComponentActive = null;

    // Reset DOM
    document.body.innerHTML = '';

    fixture.detectChanges();
    await fixture.whenStable();

    // Assert: Verify the error handling logic is triggered
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);

  });

  it("should upload manully", async () => {
    // Simulate user authentication
    authServiceSpy.isAuthenticate?.and.returnValue(true);
    countryCacheServiceSpy.getCountryListFromCache?.and.returnValue(Promise.resolve(countryListResponse));
    countryCacheServiceSpy.filterOutUserAssociatedCountries?.and.returnValue(countryListResponse);

    softwareBuildApiCallServiceMock.uploadFileToStorage?.and.returnValue(of(new HttpResponse({ status: 200 })));
    softwareBuildApiCallServiceMock.pushFileToStorage?.and.returnValue(of(new HttpResponse<UploadPackageResponse>({
      body: {
        "preSignedUrlForAttachmentFile": "https://demo.blob.core.windows.net/rdm-container/firmware%2F1.212.3.2%deno%20%281%29.zip?sv=2024-11-04&se=2025-01-06T08%3A35%3A15Z&sr=b&sp=cw&sig=lcUUfVuK2H5UUtsGCuH1UjUVSf0ClrA7o25IV7fJ1ys%3D",
        "preSignedUrlForReleaseNoteFile": "https://demo.blob.core.windows.net/rdm-container/firmware%2F1.212.3.2%demo.3.json?sv=2024-11-04&se=2025-01-06T08%3A35%3A15Z&sr=b&sp=cw&sig=gdBaVxe7IsNTEYzOuvg%2FE%2FrEESSrkm3r%2FCw4dUzolnc%3D"
      },
      status: 200, // Set a valid HTTP status
      statusText: 'OK', // Optionally set a status text
    })))
    // Call ngOnInit, it may contain async operations
    component.ngOnInit();

    // Wait for the async operations to complete
    fixture.detectChanges();  // Detect changes
    await fixture.whenStable(); // Wait for async operations to finish

    component.uploadSoftwareBuildPermission = true;

    // Wait for the async operations to complete
    fixture.detectChanges();  // Detect changes
    await fixture.whenStable(); // Wait for async operations to finish

    const uploadManually = fixture.debugElement.query(By.css('#uploadManually')).nativeElement;
    uploadManually.click();

    // Wait for the async operations to complete
    fixture.detectChanges();  // Detect changes
    await fixture.whenStable(); // Wait for async operations to finish

    // Query Add Component
    let uploadManuallyElement = document.querySelector<HTMLElement>('app-upload-software-build-dialog');
    let uploadManuallyComponent = window['ng'].getComponent(uploadManuallyElement);

    uploadManuallyComponent?.form.get("Title").setValue("Upload");
    uploadManuallyComponent?.form.get("Version").setValue("1.2.3");
    uploadManuallyComponent?.form.get("country").setValue([{ "id": 1, "country": 'test1' }, { "id": 2, "country": 'test2' }]);
    uploadManuallyComponent?.form.get("jsonVersion").setValue("1.4.7");
    uploadManuallyComponent?.form.get("partNumber").setValue("1.2.7");
    const fileInput: HTMLInputElement = document.querySelector('#file');

    // Create a mock File object
    const mockFile1 = new File(['content'], 'te1.json', { type: 'application/json' });
    const mockFile2 = new File(['content'], 'te2.zip', { type: 'application/zip' });

    // Create a DataTransfer object to mimic a file drop or file selection
    const dataTransfer = new DataTransfer();
    dataTransfer.items.add(mockFile1);
    dataTransfer.items.add(mockFile2);

    // Set the files to the input element
    fileInput.files = dataTransfer.files;

    // Act: Dispatch the 'change' event to trigger the event listener
    fileInput.dispatchEvent(new Event('change'));
    uploadManuallyComponent.buttonDisable("null")
    // Wait for changes to propagate
    fixture.detectChanges();
    await fixture.whenStable();

    const uploadBtnSw = document.querySelector<HTMLElement>('#uploadBtnSw');
    uploadBtnSw.click();

    // Create a DataTransfer object to mimic a file drop or file selection
    const dataTransfer1 = new DataTransfer();
    dataTransfer1.items.add(mockFile1);

    // Set the files to the input element
    fileInput.files = dataTransfer1.files;

    // Act: Dispatch the 'change' event to trigger the event listener
    fileInput.dispatchEvent(new Event('change'));

    uploadManuallyComponent?.form.get("fileSelector").setValue('');

    // Create a DataTransfer object to mimic a file drop or file selection
    dataTransfer1.items.add(mockFile1);

    // Set the files to the input element
    fileInput.files = dataTransfer1.files;

    // Act: Dispatch the 'change' event to trigger the event listener
    fileInput.dispatchEvent(new Event('change'));


  })


});