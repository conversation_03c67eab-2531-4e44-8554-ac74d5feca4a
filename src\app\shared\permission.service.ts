import { Injectable } from '@angular/core';
import { LocalStorageService } from 'ngx-webstorage';
import { RdmAction } from '../app.constants';
import { PermissionAction } from './enum/Permission/permissionAction.enum';
import { PermissionsEnum } from './enum/Permission/permissions.enum';
@Injectable({
    providedIn: 'root'
})
export class PermissionService {


    constructor(private $localStorage: LocalStorageService) { }

    public setPermission(permissionList: string[]): void {
        let permissionObj = {}
        for (let permission in PermissionsEnum) {
            permissionObj[PermissionsEnum[permission]] = permissionList.includes(PermissionsEnum[permission]);
        }
        this.$localStorage.store(RdmAction, permissionObj);
    }

    private getPermissionObejct(): any {
        return this.$localStorage.retrieve(RdmAction);
    }

    /**
     * check Device permission
     * 
     * <AUTHOR>
     * @param actionName 
     * @returns 
     */
    public getDevicePermission(actionName: PermissionAction): boolean {
        let permissionObj = this.getPermissionObejct();
        switch (actionName) {
            case PermissionAction.GET_DEVICE_ACTION:
                return (permissionObj[PermissionsEnum.DEVICE_ADMIN] ||
                    permissionObj[PermissionsEnum.DEVICE_READER] ||
                    permissionObj[PermissionsEnum.UPDATE_DEVICE_TYPE] ||
                    permissionObj[PermissionsEnum.LOCK_DEVICE] ||
                    permissionObj[PermissionsEnum.ASSOCIATE_CUSTOMER_TO_DEVICE] ||
                    permissionObj[PermissionsEnum.DEVICE_DISABLED] ||
                    permissionObj[PermissionsEnum.DEVICE_RMA] ||
                    permissionObj[PermissionsEnum.ENABLE_DISABLE_EDIT_DEVICE] ||
                    permissionObj[PermissionsEnum.TRANSFER_DEVICE]
                );

            case PermissionAction.UPDATE_DEVICE_TYPE_ACTION:
                return (permissionObj[PermissionsEnum.DEVICE_ADMIN] ||
                    permissionObj[PermissionsEnum.UPDATE_DEVICE_TYPE]);

            case PermissionAction.LOCK_DEVICE_ACTION:
                return (permissionObj[PermissionsEnum.DEVICE_ADMIN] ||
                    permissionObj[PermissionsEnum.LOCK_DEVICE])

            case PermissionAction.ASSOCIATE_CUSTOMER_TO_DEVICE_ACTION:
                return (permissionObj[PermissionsEnum.DEVICE_ADMIN] ||
                    permissionObj[PermissionsEnum.ASSOCIATE_CUSTOMER_TO_DEVICE]);

            /**
            * Update Device as Disabled Option Display
            */
            case PermissionAction.DISABLE_DEVICE_ACTION:
                return (permissionObj[PermissionsEnum.DEVICE_ADMIN] ||
                    permissionObj[PermissionsEnum.DEVICE_DISABLED]);
            /**
            * Update Device as RMA Option Display
            */
            case PermissionAction.RMA_DEVICE_ACTION:
                return (permissionObj[PermissionsEnum.DEVICE_ADMIN] ||
                    permissionObj[PermissionsEnum.DEVICE_RMA]);
            /**
            *  Device as  Editable Option Display
            */
            case PermissionAction.EDITABLE_DEVICE_ACTION:
                return (permissionObj[PermissionsEnum.DEVICE_ADMIN] ||
                    permissionObj[PermissionsEnum.ENABLE_DISABLE_EDIT_DEVICE]);
            /**
            *  Device as  Editable Option Display
            */
            case PermissionAction.TRANSFER_DEVICE_ACTION:
                return (permissionObj[PermissionsEnum.DEVICE_ADMIN] ||
                    permissionObj[PermissionsEnum.TRANSFER_DEVICE]);
        }
        return false;
    }

    /**
     * check prob permission
     * 
     * <AUTHOR>
     * @param actionName 
     * @returns 
     */
    public getProbPermission(actionName: PermissionAction): boolean {
        let permissionObj = this.getPermissionObejct();
        switch (actionName) {
            case PermissionAction.GET_PROB_ACTION:
            case PermissionAction.DOWNLOAD_SALESORDER_LETTER_ACTION:
            case PermissionAction.DOWNLOAD_PROB_FEATURE_LICENSE_ACTION:
                return (permissionObj[PermissionsEnum.PROBE_ADMIN] ||
                    permissionObj[PermissionsEnum.PROBE_READER] ||
                    permissionObj[PermissionsEnum.ADD_PROBE] ||
                    permissionObj[PermissionsEnum.UPDATE_PROBE_TYPE] ||
                    permissionObj[PermissionsEnum.ASSIGN_FEATURE_TO_PROBE] ||
                    permissionObj[PermissionsEnum.ASSOCIATE_CUSTOMER_TO_PROBE] ||
                    permissionObj[PermissionsEnum.PROBE_DISABLED] ||
                    permissionObj[PermissionsEnum.ENABLE_DISABLE_EDIT_PROBE] ||
                    permissionObj[PermissionsEnum.PROBE_RMA] ||
                    permissionObj[PermissionsEnum.LOCK_UNLOCK_PROBE] ||
                    permissionObj[PermissionsEnum.PROBE_SET_REMINDER] ||
                    permissionObj[PermissionsEnum.TANSFER_PROBE]);

            case PermissionAction.ADD_PROB_ACTION:
                return (permissionObj[PermissionsEnum.PROBE_ADMIN] ||
                    permissionObj[PermissionsEnum.ADD_PROBE]);

            case PermissionAction.UPDATE_PROB_TYPE_ACTION:
                return (permissionObj[PermissionsEnum.PROBE_ADMIN] ||
                    permissionObj[PermissionsEnum.UPDATE_PROBE_TYPE]);

            case PermissionAction.ASSOCIATE_CUSTOMER_TO_PROBE_ACTION:
                return (permissionObj[PermissionsEnum.PROBE_ADMIN] ||
                    permissionObj[PermissionsEnum.ASSOCIATE_CUSTOMER_TO_PROBE]);

            case PermissionAction.ASSIGN_FEATURE_TO_PROBE_ACTION:
                return (permissionObj[PermissionsEnum.PROBE_ADMIN] ||
                    permissionObj[PermissionsEnum.ASSIGN_FEATURE_TO_PROBE]);

            case PermissionAction.DELETE_PROB_ACTION:
                return (permissionObj[PermissionsEnum.PROBE_ADMIN]);

            case PermissionAction.ADD_PROBE_SET_REMINDER_OPTIONS_DISPLAY:
                return (permissionObj[PermissionsEnum.PROBE_ADMIN] ||
                    permissionObj[PermissionsEnum.PROBE_SET_REMINDER]);

            /**
             * Update Probe as Disabled Option Display
             */
            case PermissionAction.DISABLE_PROBE_ACTION:
                return (permissionObj[PermissionsEnum.PROBE_ADMIN] ||
                    permissionObj[PermissionsEnum.PROBE_DISABLED]);

            /**
            * Update Probe as Enable Disable Edit Option Display
            */
            case PermissionAction.EDITABLE_PROBE_ACTION:
                return (permissionObj[PermissionsEnum.PROBE_ADMIN] ||
                    permissionObj[PermissionsEnum.ENABLE_DISABLE_EDIT_PROBE]);
            /**
            * Update Probe as RMA Option Display
            */
            case PermissionAction.RMA_PROBE_ACTION:
                return (permissionObj[PermissionsEnum.PROBE_ADMIN] ||
                    permissionObj[PermissionsEnum.PROBE_RMA]);

            case PermissionAction.LOCK_DEVICE_ACTION:
                return (permissionObj[PermissionsEnum.PROBE_ADMIN] ||
                    permissionObj[PermissionsEnum.LOCK_UNLOCK_PROBE]);

            case PermissionAction.TRANSFER_PROBE_ACTION:
                return (permissionObj[PermissionsEnum.PROBE_ADMIN] ||
                    permissionObj[PermissionsEnum.TANSFER_PROBE]);
        }
        return false;
    }


    /**
     * check item inventory permission
     * 
     * <AUTHOR>
     * @param actionName 
     * @returns 
     */
    public getSoftwearBuildPermission(actionName: PermissionAction): boolean {
        let permissionObj = this.getPermissionObejct();
        switch (actionName) {
            case PermissionAction.GET_SOFTWARE_BUILD_ACTION:
                return (permissionObj[PermissionsEnum.SOFTWARE_BUILD_ADMIN] ||
                    permissionObj[PermissionsEnum.SOFTWARE_BUILD_READER] ||
                    permissionObj[PermissionsEnum.UPDATE_SOFTWARE_BUILD]);

            case PermissionAction.UPDATE_SOFTWARE_BUILD_ACTION:
                return (permissionObj[PermissionsEnum.SOFTWARE_BUILD_ADMIN] ||
                    permissionObj[PermissionsEnum.UPDATE_SOFTWARE_BUILD]);

            case PermissionAction.UPLOAD_SOFTWARE_BUILD_ACTION:
            case PermissionAction.DELETE_SOFTWARE_BUILD_ACTION:
                return (permissionObj[PermissionsEnum.SOFTWARE_BUILD_ADMIN]);
        }
        return false;
    }


    /**
     * Check role permission
     * 
     * <AUTHOR>
     * 
     * @param actionName 
     * @returns 
     */
    public getRolePermission(actionName: PermissionAction): boolean {
        let permissionObj = this.getPermissionObejct();
        switch (actionName) {
            case PermissionAction.GET_ROLE_ACTION:
                return (permissionObj[PermissionsEnum.ROLE_READER] ||
                    permissionObj[PermissionsEnum.ROLE_ADMIN] ||
                    permissionObj[PermissionsEnum.ADD_ROLE] ||
                    permissionObj[PermissionsEnum.UPDATE_ROLE])

            case PermissionAction.ADD_ROLE_ACTION:
                return (permissionObj[PermissionsEnum.ROLE_ADMIN] ||
                    permissionObj[PermissionsEnum.ADD_ROLE]);

            case PermissionAction.UPDATE_ROLE_ACTION:
                return (permissionObj[PermissionsEnum.ROLE_ADMIN] ||
                    permissionObj[PermissionsEnum.UPDATE_ROLE]);

            case PermissionAction.DELETE_ROLE_ACTION:
                return permissionObj[PermissionsEnum.ROLE_ADMIN];

            case PermissionAction.GET_ROLE_NAME_LIST_ACTION:
                return (permissionObj[PermissionsEnum.ROLE_READER] ||
                    permissionObj[PermissionsEnum.ROLE_ADMIN] ||
                    permissionObj[PermissionsEnum.ADD_ROLE] ||
                    permissionObj[PermissionsEnum.UPDATE_ROLE] ||
                    permissionObj[PermissionsEnum.USER_ADMIN] ||
                    permissionObj[PermissionsEnum.USER_READER] ||
                    permissionObj[PermissionsEnum.ADD_USER] ||
                    permissionObj[PermissionsEnum.UPDATE_USER]);
        }
        return false;
    }

    /**
     * Check User permission
     * 
     * @param actionName 
     * @returns 
     */
    public getUserPermission(actionName: PermissionAction): boolean {
        let permissionObj = this.getPermissionObejct();
        switch (actionName) {
            case PermissionAction.GET_USER_ACTION:
                return (permissionObj[PermissionsEnum.USER_READER] ||
                    permissionObj[PermissionsEnum.USER_ADMIN] ||
                    permissionObj[PermissionsEnum.ADD_USER] ||
                    permissionObj[PermissionsEnum.UPDATE_USER]);

            case PermissionAction.ADD_USER_ACTION:
                return (permissionObj[PermissionsEnum.USER_ADMIN] ||
                    permissionObj[PermissionsEnum.ADD_USER]);

            case PermissionAction.UPDATE_USER_ACTION:
                return (permissionObj[PermissionsEnum.USER_ADMIN] ||
                    permissionObj[PermissionsEnum.UPDATE_USER]);

            case PermissionAction.DELETE_USER_ACTION:
                return (permissionObj[PermissionsEnum.USER_ADMIN]);
        }
        return false;
    }

    /**
     * check video permission
     * 
     * <AUTHOR>
     * @param actionName 
     * @returns 
     */
    public getVideoPermission(actionName: PermissionAction): boolean {
        let permissionObj = this.getPermissionObejct();
        switch (actionName) {
            case PermissionAction.GET_VIDEO_ACTION:
                return (permissionObj[PermissionsEnum.VIDEO_READER] ||
                    permissionObj[PermissionsEnum.VIDEO_ADMIN] ||
                    permissionObj[PermissionsEnum.ADD_VIDEO] ||
                    permissionObj[PermissionsEnum.UPDATE_VIDEO]);

            case PermissionAction.ADD_VIDEO_ACTION:
                return (permissionObj[PermissionsEnum.VIDEO_ADMIN] ||
                    permissionObj[PermissionsEnum.ADD_VIDEO]);

            case PermissionAction.UPDATE_VIDEO_ACTION:
                return (permissionObj[PermissionsEnum.VIDEO_ADMIN] ||
                    permissionObj[PermissionsEnum.UPDATE_VIDEO]);

            case PermissionAction.DELETE_VIDEO_ACTION:
                return (permissionObj[PermissionsEnum.VIDEO_ADMIN]);
        }
        return false;
    }


    /**
     * Check Device log permission
     * 
     * <AUTHOR>
     * @param actionName 
     * @returns 
     */
    public getDeviceLogPermission(actionName: PermissionAction): boolean {
        let permissionObj = this.getPermissionObejct();
        switch (actionName) {
            case PermissionAction.GET_DEVICE_LOG_ACTION:
            case PermissionAction.DOWNLOAD_DEVICE_LOG_ACTION:
                return (permissionObj[PermissionsEnum.DEVICE_LOG_READER]);
        }
        return false;
    }

    /**
     * Check job permission
     * 
     * <AUTHOR>
     * @param actionName 
     * @returns 
     */
    public getJobPermission(actionName: PermissionAction): boolean {
        let permissionObj = this.getPermissionObejct();
        if (actionName == PermissionAction.GET_JOB_ACTION) {
            return permissionObj[PermissionsEnum.JOB_READER];
        }
        return false;
    }

    /**
    * Checks whether the user has the required permission for a specific country-related action.
    * 
    * This method evaluates the user's permissions against the action being performed,
    * such as viewing, adding, activating/deactivating, or deleting countries.
    * 
    * <AUTHOR>
    * @param {PermissionAction} actionName - The action to verify permissions for.
    * @returns {boolean} `true` if the user has the required permission, otherwise `false`.
    */
    public getCountryPermission(actionName: PermissionAction): boolean {
        let permissionObj = this.getPermissionObejct();
        switch (actionName) {
            case PermissionAction.GET_COUNTRY_LIST_ACTION:
                return (permissionObj[PermissionsEnum.COUNTRY_READER] ||
                    permissionObj[PermissionsEnum.COUNTRY_ADMIN] ||
                    permissionObj[PermissionsEnum.ADD_COUNTRY]);

            case PermissionAction.ADD_COUNTRY_ACTION:
                return (permissionObj[PermissionsEnum.COUNTRY_ADMIN] ||
                    permissionObj[PermissionsEnum.ADD_COUNTRY]);

            case PermissionAction.DELETE_COUNTRY_ACTION:
                return (permissionObj[PermissionsEnum.COUNTRY_ADMIN]);
        }
        return false;
    }

    /**
     * check Language permission
     *  
     * @param actionName 
     * @returns 
     */
    public getLanguagePermission(actionName: PermissionAction): boolean {
        let permissionObj = this.getPermissionObejct();
        if (actionName == PermissionAction.GET_LANGUAGE_ACTION) {
            return (permissionObj[PermissionsEnum.COUNTRY_READER] ||
                permissionObj[PermissionsEnum.BRIDGE_KIT_MANAGEMENT_READER] ||
                permissionObj[PermissionsEnum.BRIDGE_KIT_MANAGEMENT_ADMIN] ||
                permissionObj[PermissionsEnum.COUNTRY_ADMIN] ||
                permissionObj[PermissionsEnum.ADD_COUNTRY]);
        }
        return false;
    }

    /**
     * Check PermissionList api permission 
     * 
     * <AUTHOR>
     * @param actionName 
     * @returns 
     */
    public getPermissionForPermissionList(actionName: PermissionAction): boolean {
        let permissionObj = this.getPermissionObejct();
        if (actionName == PermissionAction.GET_PERMISSIONLIST_ACTION) {
            return (permissionObj[PermissionsEnum.ROLE_READER] ||
                permissionObj[PermissionsEnum.ROLE_ADMIN] ||
                permissionObj[PermissionsEnum.ADD_ROLE] ||
                permissionObj[PermissionsEnum.UPDATE_ROLE]);
        }
        return false;
    }

    /**
     * Get Kit Management Permission
     * <AUTHOR>
     * @param actionName 
     * @returns 
     */
    public getKitManagementPermission(actionName: PermissionAction): boolean {
        let permissionObj = this.getPermissionObejct();
        switch (actionName) {
            case PermissionAction.KIT_MANAGEMANT_TAB_ACTION:
                return (permissionObj[PermissionsEnum.BRIDGE_KIT_MANAGEMENT_READER] ||
                    permissionObj[PermissionsEnum.BRIDGE_KIT_MANAGEMENT_ADMIN] || permissionObj[PermissionsEnum.OTS_KIT_MANAGEMENT_READER] ||
                    permissionObj[PermissionsEnum.OTS_KIT_MANAGEMENT_ADMIN]);

            case PermissionAction.GET_BRIDGE_KIT_MANAGEMENT_ACTION:
                return (permissionObj[PermissionsEnum.BRIDGE_KIT_MANAGEMENT_READER] ||
                    permissionObj[PermissionsEnum.BRIDGE_KIT_MANAGEMENT_ADMIN]);

            case PermissionAction.IMPORT_BRIDGE_KIT_CSV_ACTION:
                return (permissionObj[PermissionsEnum.BRIDGE_KIT_MANAGEMENT_ADMIN]);

            case PermissionAction.GET_OTS_KIT_MANAGEMENT_ACTION:
                return (permissionObj[PermissionsEnum.OTS_KIT_MANAGEMENT_READER] ||
                    permissionObj[PermissionsEnum.OTS_KIT_MANAGEMENT_ADMIN]);

            case PermissionAction.IMPORT_OTS_KIT_CSV_ACTION:
                return (permissionObj[PermissionsEnum.OTS_KIT_MANAGEMENT_ADMIN]);
        }
        return false;
    }

    /**
     * Get Sales Order Permission
     * 
     * @param actionName 
     * @returns 
     */
    public getSalesOrderPermission(actionName: PermissionAction): boolean {
        let permissionObj = this.getPermissionObejct();
        switch (actionName) {
            case PermissionAction.GET_SALES_ORDER_ACTION:
            case PermissionAction.DOWNLOAD_SALESORDER_LETTER_ACTION:
                return (permissionObj[PermissionsEnum.SALES_ORDER_READER] ||
                    permissionObj[PermissionsEnum.SALES_ORDER_ADMIN] ||
                    permissionObj[PermissionsEnum.SALES_ORDER_CONFIGURE_BRIDGE_PROBE] ||
                    permissionObj[PermissionsEnum.SALES_ORDER_CONFIGURE_PROBE] ||
                    permissionObj[PermissionsEnum.SALES_ORDER_MANUAL_SYNC] ||
                    permissionObj[PermissionsEnum.SALES_ORDER_TRANSFER_PRODUCT]);
            case PermissionAction.SALES_ORDER_CREATE_PROBE_ACTION:
                return (permissionObj[PermissionsEnum.SALES_ORDER_ADMIN] ||
                    permissionObj[PermissionsEnum.SALES_ORDER_CONFIGURE_PROBE] ||
                    permissionObj[PermissionsEnum.SALES_ORDER_CONFIGURE_BRIDGE_PROBE]);
            case PermissionAction.SALES_ORDER_RESET_BRIDGE_ACTION:
                return (permissionObj[PermissionsEnum.SALES_ORDER_ADMIN] ||
                    permissionObj[PermissionsEnum.SALES_ORDER_CONFIGURE_BRIDGE_PROBE]);
            case PermissionAction.SALES_ORDER_DELETE_ACTION:
                return (permissionObj[PermissionsEnum.SALES_ORDER_ADMIN]);
            case PermissionAction.SALES_ORDER_MANUAL_SYNC_ACTION:
                return (permissionObj[PermissionsEnum.SALES_ORDER_MANUAL_SYNC] ||
                    permissionObj[PermissionsEnum.SALES_ORDER_ADMIN]);
            case PermissionAction.SALES_ORDER_TRANSFER_ORDER_ACTION:
                return (permissionObj[PermissionsEnum.SALES_ORDER_TRANSFER_PRODUCT] ||
                    permissionObj[PermissionsEnum.SALES_ORDER_ADMIN]);
        }
        return false;
    }


    /**
    * Get Audit Permission
    * 
    * @param actionName 
    * @returns 
    */
    public getAuditPermission(actionName: PermissionAction): boolean {
        let permissionObj = this.getPermissionObejct();
        switch (actionName) {
            case PermissionAction.GET_AUDIT_ACTION:
                return (permissionObj[PermissionsEnum.AUDIT_READER] ||
                    permissionObj[PermissionsEnum.AUDIT_ADMIN] ||
                    permissionObj[PermissionsEnum.AUDIT_DEVICE_REVERSE] ||
                    permissionObj[PermissionsEnum.AUDIT_PROBE_REVERSE]);
            case PermissionAction.AUDIT_DEVICE_REVERSE_ACTION:
                return (permissionObj[PermissionsEnum.AUDIT_DEVICE_REVERSE] || permissionObj[PermissionsEnum.AUDIT_ADMIN]);
            case PermissionAction.AUDIT_PROBE_REVERSE_ACTION:
                return (permissionObj[PermissionsEnum.AUDIT_PROBE_REVERSE] || permissionObj[PermissionsEnum.AUDIT_ADMIN]);
        }
        return false;
    }

    /**
     * Get Probe Feature GroupPermission
     * 
     * <AUTHOR>
     * @param actionName 
     * @returns 
     */
    public getProbeConfigGroupPermission(actionName: PermissionAction): boolean {
        let permissionObj = this.getPermissionObejct();
        switch (actionName) {
            case PermissionAction.GET_CONFIG_GROUP_ACTION:
                return (permissionObj[PermissionsEnum.PROBE_CONFIG_GROUP_ADMIN] || permissionObj[PermissionsEnum.PROBE_CONFIG_GROUP_READER] || permissionObj[PermissionsEnum.ADD_PROBE_CONFIG_GROUP]);
            case PermissionAction.ADD_CONFIG_GROUP_ACTION:
                return (permissionObj[PermissionsEnum.PROBE_CONFIG_GROUP_ADMIN] || permissionObj[PermissionsEnum.ADD_PROBE_CONFIG_GROUP]);
            case PermissionAction.DELETE_CONFIG_GROUP_ACTION:
                return (permissionObj[PermissionsEnum.PROBE_CONFIG_GROUP_ADMIN]);
        }
        return false;
    }
}
