import { BaseResponse } from "../common/BaseResponse.model";
import { PresetPartNumberResponse } from "./PresetPartNumberResponse.model";

export class ProbePresetResponse extends BaseResponse {
    presetId: number;
    partNumbers: Array<PresetPartNumberResponse>;

    constructor($id: number, $name: string, $displayName: string, $presetId: number, $partNumbers: Array<PresetPartNumberResponse>) {
        super($id, $name, $displayName);
        this.presetId = $presetId;
        this.partNumbers = $partNumbers;
    }

}