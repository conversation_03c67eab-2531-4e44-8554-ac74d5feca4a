import { <PERSON>ttp<PERSON><PERSON>, <PERSON>ttp<PERSON>and<PERSON>, HttpRequest } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { AuthInterceptor } from './auth.intercepter';

describe('AuthInterceptor', () => {
    let interceptor: AuthInterceptor;
    let mockHandler: jasmine.SpyObj<HttpHandler>;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [AuthInterceptor],
        });
        interceptor = TestBed.inject(AuthInterceptor);
        mockHandler = jasmine.createSpyObj('HttpHandler', ['handle']);
    });

    it('should add withCredentials to the request', () => {
        const mockRequest = new HttpRequest('GET', '/test-url');
        mockHandler.handle.and.returnValue(of({} as HttpEvent<any>));

        interceptor.intercept(mockRequest, mockHandler).subscribe();

        const updatedRequest = mockHandler.handle.calls.argsFor(0)[0];
        expect(updatedRequest.withCredentials).toBeTrue();
    });
});
