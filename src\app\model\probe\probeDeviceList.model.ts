import { OSTypeEnum } from "src/app/shared/enum/Probe/OSTypeEnum.enum";

export interface IProbeDeviceList {
    id?: number,
    appVersion?: string,
    deviceModel?: string,
    manufacturer?: string,
    osVersion?: string,
    pimsDbVersion?: string,
    serialNumber?: string,
    settingsDbVersion?: string,
    timeZone?: string,
    upsDbVersion?: string,
    createdDate?: string,
    osType?: OSTypeEnum
}

export class ProbeDeviceList implements IProbeDeviceList {
    constructor(
        public id?: number,
        public appVersion?: string,
        public deviceModel?: string,
        public manufacturer?: string,
        public osVersion?: string,
        public pimsDbVersion?: string,
        public serialNumber?: string,
        public settingsDbVersion?: string,
        public timeZone?: string,
        public upsDbVersion?: string,
        public createdDate?: string,
        public osType?: OSTypeEnum
    ) { }
}
