
import { HttpErrorResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { throwError } from 'rxjs';
import { INTERNAL_SERVER_ERROR } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { SoftwareBuildStatusEnum } from 'src/app/shared/enum/SoftwareBuildStatusEnum';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { SSOLoginService } from 'src/app/shared/Service/SSO/ssologin.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { DownloadService } from 'src/app/shared/util/download.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { EditSoftwareBuildComponent } from './edit-software-build.component';
import { SoftwareBuildApiCallService } from '../software-build-services/software-api-call/software-build-api-call.service';

describe('EditInventoryComponent', () => {
  let component: EditSoftwareBuildComponent;
  let fixture: ComponentFixture<EditSoftwareBuildComponent>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let exceptionHandlingService: ExceptionHandlingService;
  let softwareBuildApiCallspy: jasmine.SpyObj<SoftwareBuildApiCallService>;
  let downloadService: jasmine.SpyObj<DownloadService>;


  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    softwareBuildApiCallspy = jasmine.createSpyObj('InventoryService', ['inventoryList', 'mapInventoryWithDeviceType', 'markInventoriesActiveInactive', 'getAttachmentUrl', 'deleteSoftwearBuild', 'updateInventory', 'pushFileToStorage', 'uploadFileToStorage']);
    downloadService = jasmine.createSpyObj('DownloadService', ['downloadMyFile', 'getisLoadingSubject', 'setLoading']);

    await TestBed.configureTestingModule({
      declarations: [EditSoftwareBuildComponent],
      imports: [NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, FormsModule],
      providers: [
        NgbActiveModal,
        LocalStorageService,
        CommonsService,
        ConfirmDialogService,
        ExceptionHandlingService,
        AuthJwtService,
        SessionStorageService,
        SSOLoginService,
        { provide: SoftwareBuildApiCallService, useValue: softwareBuildApiCallspy },
        { provide: DownloadService, useValue: downloadService },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(EditSoftwareBuildComponent);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    component = fixture.componentInstance;

    fixture.detectChanges(); // Triggers ngOnInit
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call toastrService.error with INTERNAL_SERVER_ERROR', async () => {
    component.form.get("deviceTypes").setValue([SoftwareBuildStatusEnum.ACTIVE]);
    component.form.get("inventoryStatus").setValue(["Active"]);
    component.form.get('country').setValue([{ country: '20242109-1', id: 135 }]);

    // **Arrange: Mock dependencies and set up the test environment**
    // Create a mock HttpErrorResponse to simulate a server error
    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });

    // Mock the API call to return an error response
    softwareBuildApiCallspy.updateInventory?.and.returnValue(throwError(() => mockError));

    // Spy on the `CustomerrorMessage` method of `exceptionHandlingService`
    spyOn(exceptionHandlingService, 'customErrorMessage').and.callThrough();

    // **Act: Trigger the component's logic**
    component.accept();

    fixture.detectChanges();
    await fixture.whenStable();

    // **Assert: Verify error handling behavior**
    // Ensure the `CustomerrorMessage` method was called to process the error
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();

    // Ensure the toastrService displays an error message for `INTERNAL_SERVER_ERROR`
    expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
  });


});
