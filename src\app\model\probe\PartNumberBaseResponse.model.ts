import { ValidityEnum } from "src/app/shared/enum/ValidityEnum.enum";

export class PartNumberBaseResponse {
    partNumber: string;
    validity: ValidityEnum;
    default: boolean;
    allowedToEdit: boolean;

    constructor(partNumber: string, validity: ValidityEnum, isDefault: boolean, allowedToEdit: boolean) {
        this.partNumber = partNumber;
        this.validity = validity;
        this.default = isDefault;
        this.allowedToEdit = allowedToEdit;
    }
}