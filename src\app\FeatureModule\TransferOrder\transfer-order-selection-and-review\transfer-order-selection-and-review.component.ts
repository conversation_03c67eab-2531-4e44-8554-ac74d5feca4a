import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { isNullOrUndefined } from 'is-what';
import { Subscription } from 'rxjs';
import { DeviceDetailResource, ProbDetailResource } from 'src/app/app.constants';
import { SalesOrderTransferProductRequest } from 'src/app/model/device/SalesOrderTransferProductRequest.model';
import { TransferProductDetails } from 'src/app/model/device/TransferProductDetails.model';
import { SalesOrderTransferProbeRequest } from 'src/app/model/probe/SalesOrderTransferProbeRequest.model';
import { SalesOrderBridgeDetailResponse } from 'src/app/model/SalesOrder/SalesOrderBridgeDetailResponse.model';
import { SalesOrderProbeDetailResponse } from 'src/app/model/SalesOrder/SalesOrderProbeDetailResponse.model';
import { SalesOrderTransferProbeDetailValidateRequest } from 'src/app/model/SalesOrder/SalesOrderTransferProbeDetailValidateRequest.model';
import { SalesOrderTransferProductDetailValidateRequest } from 'src/app/model/SalesOrder/SalesOrderTransferProductDetailValidateRequest.model';
import { SalesOrderTransferProductValidateRequest } from 'src/app/model/SalesOrder/SalesOrderTransferProductValidateRequest.model';
import { SalesOrderTransferValidateRequest } from 'src/app/model/SalesOrder/SalesOrderTransferValidateRequest.model';
import { SourceSelectedProbeAndDevice } from 'src/app/model/SalesOrder/SourceSelectedProbeAndDevice.model';
import { TransferOrderProductReviewResponse } from 'src/app/model/SalesOrder/TransferOrderProductReviewResponse.model';
import { TransferOrderSelectionDetailRequest } from 'src/app/model/SalesOrder/TransferOrderSelectionDetailRequest.model';
import { TransferOrderSelectionResponse } from 'src/app/model/SalesOrder/TransferOrderSelectionResponse.model';
import { ProductConfigStatus } from 'src/app/shared/enum/SalesOrder/ProductConfigStatus.enum';
import { TransferOrderService } from 'src/app/shared/Service/TransferOrderService/transfer-order.service';
import { CommonCheckboxService } from 'src/app/shared/util/common-checkbox.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';

@Component({
  selector: 'app-transfer-order-selection-and-review',
  templateUrl: './transfer-order-selection-and-review.component.html',
  styleUrl: './transfer-order-selection-and-review.component.css',
  encapsulation: ViewEncapsulation.None
})
export class TransferOrderSelectionAndReviewComponent implements OnInit {
  @Input("destinationSalesOrderId") destinationSalesOrderId: number;
  @Input("transferProductDetail") transferProductDetail: TransferProductDetails;
  @Output("showSelectionTable") showSelectionTable = new EventEmitter;
  @Output() selectedProbeAndDevice = new EventEmitter<SourceSelectedProbeAndDevice>();
  @Output() transferOrderReviewRequest = new EventEmitter<SalesOrderTransferValidateRequest>();
  @Output() isSalesOrderManual = new EventEmitter<boolean>();


  tranferOrderData: TransferOrderSelectionResponse = null;
  reviewOrderResponse: TransferOrderProductReviewResponse = null;
  subscriptionForCommonloading: Subscription;
  loading: boolean = false;
  transferOrderRequest: SalesOrderTransferValidateRequest;

  BRIDGE_SERIAL_NUMBER_LIST_CONTROL = "bridgeSerialNumberList";
  PROBE_SERIAL_NUMBER_LIST_CONTROL = "probeSerialNumberList";

  //selected Sales Order Probe Id Collect
  selectedSalesOrderProbeIdList: number[] = [];
  localSalesOrderProbeIdListArray: number[] = [];

  //selected Sales Order Device Id Collect
  selectedSalesOrderDeviceIdList: number[] = [];
  localSalesOrderDeviceIdListArray: number[] = [];

  //unique CheckBox Name
  probeChkPreFix = "probeChkPreFix";
  probeSelectAllCheckboxId = "probeSelectAllSalesOrder";
  probeCheckboxListName = "ProbeSalesOrderItem[]";
  productStatusEnum = ProductConfigStatus;

  //unique CheckBox Name
  deviceChkPreFix = "probeChkPreFix";
  deviceSelectAllCheckboxId = "selectAllSalesOrder";
  deviceCheckboxListName = "salesOrderItem[]";

  deviceSerialNumber: string;

  sourceSelectedProbeAndDevice: SourceSelectedProbeAndDevice;

  //form 
  formGroup = new FormGroup({
    bridgeSerialNumberList: new FormArray([]),
    probeSerialNumberList: new FormArray([])
  });

  transferOrderReviewDisplay: boolean = false;
  tranferOrderSelectionDisaplay: boolean = true;

  //select all checkbox Display Or not
  selectAllProbeCheckboxDisplay: boolean = false;
  selectAllDeviceCheckboxDisplay: boolean = false;

  constructor(private commonOperationsService: CommonOperationsService,
    private readonly transferOrderService: TransferOrderService,
    private readonly cdr: ChangeDetectorRef,
    private readonly commonCheckboxService: CommonCheckboxService,
  ) { }

  /**
  * Initializes the component and loads transfer order details.
  */
  public ngOnInit(): void {
    this.subjectInit();
    this.initData()
  }

  /**
  * Load transfer order data
  */
  public async initData(): Promise<void> {
    let sourceDeviceSelected = this.transferProductDetail?.source === DeviceDetailResource ? new SalesOrderTransferProductRequest(this.transferProductDetail.productId, this.transferProductDetail?.serialNumber) : null;
    let sourceProbeSelected = this.transferProductDetail?.source === ProbDetailResource ? new SalesOrderTransferProbeRequest(this.transferProductDetail.productId, this.transferProductDetail?.serialNumber, this.transferProductDetail?.type) : null;
    this.sourceSelectedProbeAndDevice = new SourceSelectedProbeAndDevice(sourceDeviceSelected, sourceProbeSelected)
    let transferOrderSelectionDetailRequest = new TransferOrderSelectionDetailRequest(this.transferProductDetail?.salesOrderId, this.destinationSalesOrderId, sourceDeviceSelected, sourceProbeSelected);
    this.tranferOrderData = await this.transferOrderService.getTransferOrderSelectionDetail(transferOrderSelectionDetailRequest);
    this.transferOrderRequest = new SalesOrderTransferValidateRequest(this.transferProductDetail?.salesOrderId, this.destinationSalesOrderId, new SalesOrderTransferProductValidateRequest([], []), sourceDeviceSelected, sourceProbeSelected);
    if (isNullOrUndefined(this.tranferOrderData)) {
      this.back();
    }
    if (this.tranferOrderData.destinationSalesOrderIsManual === true && this.tranferOrderData.sourceSalesOrderIsManual === true) {
      this.isSalesOrderManual.emit(true);
      this.reviewProduct();
    }
    this.deviceSerialNumber = isNullOrUndefined(this.transferProductDetail?.serialNumber) ? "" : " - " + this.transferProductDetail?.serialNumber;
    if (!isNullOrUndefined(this.tranferOrderData.sourceSalesOrder.product.probes)) {
      this.setLocalProbeSalesOrderId(this.tranferOrderData.sourceSalesOrder.product.probes);
    }
    if (!isNullOrUndefined(this.tranferOrderData.sourceSalesOrder.product.bridges)) {
      this.setLocalDeviceSalesOrderId(this.tranferOrderData.sourceSalesOrder.product.bridges);
    }
    this.prepareForm(this.tranferOrderData);
    this.SelectAllProbeAndDeviceCheckbox();
  }

  /**
  * Prepares the form with transfer order data.
  */
  private prepareForm(tranferOrderData: TransferOrderSelectionResponse) {
    this.prepareFormForProduct(tranferOrderData?.destinationSalesOrder?.product?.bridges, this.BRIDGE_SERIAL_NUMBER_LIST_CONTROL);
    this.prepareFormForProduct(tranferOrderData?.destinationSalesOrder?.product?.probes, this.PROBE_SERIAL_NUMBER_LIST_CONTROL);
    this.cdr.detectChanges();
  }

  /**
  * Selects all probe and device checkboxes based on their status.
  * <AUTHOR>
  */
  private SelectAllProbeAndDeviceCheckbox(): void {
    const probes = this.tranferOrderData?.sourceSalesOrder?.product?.probes;
    const bridges = this.tranferOrderData?.sourceSalesOrder?.product?.bridges;

    this.selectAllProbeCheckboxDisplay = Array.isArray(probes) &&
      this.hasOnlyTransferredOrRma(probes);

    this.selectAllDeviceCheckboxDisplay = Array.isArray(bridges) &&
      this.hasOnlyTransferredOrRma(bridges);
  }

  /**
  * Checks if all items in the probe list have only 'Transferred' or 'RMA' status.
  * @param probeList - The list of probes to check.
  * @returns {boolean} - Returns true if all items have valid statuses, false otherwise.
  * <AUTHOR>
  */
  private hasOnlyTransferredOrRma(probeList: any[]): boolean {
    const validStatuses = new Set([
      this.productStatusEnum.TRANSFERRED,
      this.productStatusEnum.RMA
    ]);

    return probeList.every(item =>
      validStatuses.has(this.productStatusEnum[item.entityStatus])
    );
  }


  /**
  * Adds serial number fields to the form.
  */
  private prepareFormForProduct(product: any, productType: string) {
    if (product != null) {
      let formArray: FormArray = this.formGroup.get(productType) as FormArray;
      formArray.clear();
      for (let index in product) {
        formArray.push(new FormGroup({
          serialNumber: new FormControl(product[index].entitySerialNumber, [this.transferOrderService.distinctSerialNumberValidate(this, productType, index)]),
        }));
      }
    }
  }

  /**
  * Subscribes to common loading state.
  */
  private subjectInit(): void {
    this.subscriptionForCommonloading = this.commonOperationsService.getCommonLoadingSubject()?.subscribe((res: boolean) => {
      this.loading = res;
    });
    this.cdr.detectChanges();
  }

  /**
  * Emits event to navigate back.
  */
  public back(): void {
    this.showSelectionTable.emit([true, false, false]);
  }

  /**
  * Checks for duplicate serial numbers and updates the request.
  */
  public otherSerialNumberValidate(currentIndex: number, productType: string, product: any, sourceSopmId: string, destinationSopmId: number): void {
    let duplicateSerialNumberList = this.transferOrderService.duplicateSerialNumberValue(productType, this.formGroup);
    for (let index in product) {
      if (currentIndex != parseInt(index)) {
        let control = this.getFormControl(parseInt(index), productType);
        if (!isNullOrUndefined(control) && !isNullOrUndefined(control.value)) {
          this.transferOrderService.updateFormControlvalidation(duplicateSerialNumberList, control);
        }
      }
    }
    this.cdr.detectChanges();
  }

  /**
  * Updates or removes a bridge based on the given source and destination SOPM IDs.
  * <AUTHOR>
  * 
  * @param {number} currentIndex - The index of the current item in the list.
  * @param {string} productType - The type of product being updated.
  * @param {any} product - The product object containing bridge details.
  * @param {string} sourceSopmId - The source SOPM ID (can be "null").
  * @param {number} destinationSopmId - The destination SOPM ID.
  * @returns {void}
  */
  public bridgeSerialNumberChange(currentIndex: number, productType: string, product: any, sourceSopmId: string, destinationSopmId: number): void {
    const exists = this.transferOrderRequest?.product.bridges.filter(item => item.destinationSopmId === destinationSopmId);
    if (exists?.length === 1) {
      const index = this.transferOrderRequest?.product.bridges.findIndex(item => item.destinationSopmId === destinationSopmId);
      if (sourceSopmId === "null") {
        if (index !== -1) {
          this.transferOrderRequest?.product.bridges.splice(index, 1);
        }
      } else {
        this.transferOrderRequest.product.bridges[index].sourceSopmId = parseInt(sourceSopmId);
      }
    } else {
      this.transferOrderRequest?.product.bridges.push(new SalesOrderTransferProductDetailValidateRequest(parseInt(sourceSopmId), destinationSopmId));
    }
    this.otherSerialNumberValidate(currentIndex, productType, product, sourceSopmId, destinationSopmId);
  }

  /**
  * Updates or removes a Probe based on the given source and destination SOPM IDs.
  * <AUTHOR>
  * 
  * @param {number} currentIndex - The index of the current item in the list.
  * @param {string} productType - The type of product Probe updated.
  * @param {any} product - The product object containing probe details.
  * @param {string} sourceSopmId - The source SOPM ID (can be "null").
  * @param {number} destinationSopmId - The destination SOPM ID.
  * @returns {void}
  */
  public probeSerialNumberChange(currentIndex: number, productType: string, product: any, sourceSopmId: string, destinationSopmId: number): void {
    const exists = this.transferOrderRequest.product.probes.filter(item => item.destinationSopmId === destinationSopmId);
    if (exists.length === 1) {
      const index = this.transferOrderRequest.product.probes.findIndex(item => item.destinationSopmId === destinationSopmId);
      if (sourceSopmId === "null") {
        if (index !== -1) {
          this.transferOrderRequest.product.probes.splice(index, 1);
        }
      } else {
        this.transferOrderRequest.product.probes[index].sourceSopmId = parseInt(sourceSopmId);
      }
    } else {
      this.transferOrderRequest.product.probes.push(new SalesOrderTransferProbeDetailValidateRequest(parseInt(sourceSopmId), destinationSopmId, null, null));
    }
    this.otherSerialNumberValidate(currentIndex, productType, product, sourceSopmId, destinationSopmId);
  }


  /**
  * Retrieves a form control.
  */
  private getFormControl(index: number, productType: string): FormControl {
    return this.formGroup.get(productType)['controls'][index].get('serialNumber') as FormControl;
  }

  /**
  * Submits the transfer order for review.
  */
  public async reviewProduct(): Promise<void> {
    this.showSelectionTable.emit([false, false, true]);
    this.selectedProbeAndDevice.emit(this.sourceSelectedProbeAndDevice);
    this.transferOrderReviewRequest.emit(this.transferOrderRequest);
  }

  /**
   * Local Probe Sales Order Id list create for Select all Checkbox
   * <AUTHOR>
   * @param salesOrderProbeIdList 
   */
  public setLocalProbeSalesOrderId(salesOrderProbeIdList: Array<SalesOrderProbeDetailResponse>): void {
    this.localSalesOrderProbeIdListArray = [];
    for (let probeObj of salesOrderProbeIdList) {
      const status = this.productStatusEnum[probeObj.entityStatus];
      if (status !== this.productStatusEnum.TRANSFERRED && status !== this.productStatusEnum.RMA) {
        this.localSalesOrderProbeIdListArray.push(probeObj.sopmId);
      }
    }
    this.defaultProbeSelectAll();
  }

  /**
  * Local Device Sales Order Id list create for Select all Checkbox
  * <AUTHOR>
  * @param salesOrderDeviceIdList 
  */
  public setLocalDeviceSalesOrderId(salesOrderDeviceIdList: Array<SalesOrderBridgeDetailResponse>): void {
    this.localSalesOrderDeviceIdListArray = [];
    for (let deviceObj of salesOrderDeviceIdList) {
      const status = this.productStatusEnum[deviceObj.entityStatus];
      if (status !== this.productStatusEnum.TRANSFERRED && status !== this.productStatusEnum.RMA) {
        this.localSalesOrderDeviceIdListArray.push(deviceObj.sopmId);
      }
    }
    this.defaultDeviceSelectAll();
  }

  /**
  * select All Probe checkbox select or deSelect
  * <AUTHOR>
  */
  private defaultProbeSelectAll(): void {
    this.commonCheckboxService.defaultSelectAll(this.localSalesOrderProbeIdListArray, this.selectedSalesOrderProbeIdList, this.probeSelectAllCheckboxId);
  }

  /**
 * select All Probe checkbox select or deSelect
 * <AUTHOR>
 */
  private defaultDeviceSelectAll(): void {
    this.commonCheckboxService.defaultSelectAll(this.localSalesOrderDeviceIdListArray, this.selectedSalesOrderDeviceIdList, this.deviceSelectAllCheckboxId);
  }

  /**
  * Select All Probe CheckBox
  * <AUTHOR>
  * @param isChecked 
  */
  public selectAllProbeItem(isChecked: boolean): void {
    this.selectedSalesOrderProbeIdList = this.commonCheckboxService.selectAllItem(isChecked, this.localSalesOrderProbeIdListArray, this.selectedSalesOrderProbeIdList, this.probeCheckboxListName);
    if (isChecked) {
      for (let probeSopmId of this.selectedSalesOrderProbeIdList) {
        const alreadyExists = this.transferOrderRequest.product.probes.some(
          probe => probe.sourceSopmId === probeSopmId
        );

        if (!alreadyExists) {
          this.transferOrderRequest.product.probes.push(
            new SalesOrderTransferProbeDetailValidateRequest(probeSopmId, null, null, null)
          );
        }
      }

    }
    else {
      this.transferOrderRequest.product.probes = [];
    }
  }

  /**
  * Select All Device CheckBox
  * <AUTHOR>
  * @param isChecked 
  */
  public selectAllDeviceItem(isChecked: boolean): void {
    this.selectedSalesOrderDeviceIdList = this.commonCheckboxService.selectAllItem(isChecked, this.localSalesOrderDeviceIdListArray, this.selectedSalesOrderDeviceIdList, this.deviceCheckboxListName);
    if (isChecked) {
      for (let deviceSopmId of this.selectedSalesOrderDeviceIdList) {
        const alreadyExists = this.transferOrderRequest.product.bridges.some(
          device => device.sourceSopmId === deviceSopmId
        );

        if (!alreadyExists) {
          this.transferOrderRequest.product.bridges.push(new SalesOrderTransferProductDetailValidateRequest(deviceSopmId, null));
        }
      }
    } else {
      this.transferOrderRequest.product.bridges = [];
    }
  }

  /**
  * single Probe Checkbox Select
  * <AUTHOR>
  * @param salesOrderObj 
  * @param isChecked 
  */
  public selectProbeCheckbox(salesOrderProbeIdList: SalesOrderProbeDetailResponse, isChecked: boolean): void {
    if (isChecked) {
      this.selectedSalesOrderProbeIdList.push(salesOrderProbeIdList.sopmId);
      this.transferOrderRequest.product.probes.push(new SalesOrderTransferProbeDetailValidateRequest(salesOrderProbeIdList.sopmId, null, null, null));
    } else {
      let index = this.selectedSalesOrderProbeIdList.findIndex(obj => obj == salesOrderProbeIdList.sopmId);
      this.selectedSalesOrderProbeIdList.splice(index, 1);
      const duplicateRemove = this.transferOrderRequest.product.probes.findIndex(item => item.sourceSopmId === salesOrderProbeIdList.sopmId);
      this.transferOrderRequest.product.probes.splice(duplicateRemove, 1);
    }
    this.defaultProbeSelectAll();
  }

  /**
  * single Device Checkbox Select
  * <AUTHOR>
  * @param salesOrderObj 
  * @param isChecked 
  */
  public selectDeviceCheckbox(salesOrderDeviceIdList: SalesOrderBridgeDetailResponse, isChecked: boolean): void {
    if (isChecked) {
      this.selectedSalesOrderDeviceIdList.push(salesOrderDeviceIdList.sopmId);
      this.transferOrderRequest.product.bridges.push(new SalesOrderTransferProductDetailValidateRequest(salesOrderDeviceIdList.sopmId, null));
    } else {
      let index = this.selectedSalesOrderDeviceIdList.findIndex(obj => obj == salesOrderDeviceIdList.sopmId);
      this.selectedSalesOrderDeviceIdList.splice(index, 1);
      const duplicateRemove = this.transferOrderRequest.product.bridges.findIndex(item => item.sourceSopmId === salesOrderDeviceIdList.sopmId);
      this.transferOrderRequest.product.bridges.splice(duplicateRemove, 1);
    }
    this.defaultDeviceSelectAll();
  }

  ngOnDestroy(): void {
    if (this.subscriptionForCommonloading) {
      this.subscriptionForCommonloading.unsubscribe();
    }
  }
}