import { Injectable } from '@angular/core';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';

@Injectable({
  providedIn: 'root'
})
export class MultiSelectDropDownSettingService {

  /**
  * GetPermission Dropdown setting
  * <AUTHOR>
  * @returns MultiSelectDropdownSettings
  */
  public getPermissionDrpSetting(isSelectAll: boolean): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(false, 'Select All', 'UnSelect All', 1, true, 'id', 'name', 'Search Permission', true, isSelectAll);
  }

  /**
   * GetRole Dropdown setting
   * <AUTHOR>
   * @returns MultiSelectDropdownSettings
   */
  public getRoleDrpSetting(isSelectAll: boolean): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(false, 'Select All', 'UnSelect All', 1, true, null, null, 'Search Role', true, isSelectAll);
  }


  /**
   * Get Sales order number DropDown setting
   * @returns MultiSelectDropdownSettings
   */
  public getSalesOrderNumberDrpSetting(singleSelection: boolean, selectAllText: string, unselectAllText: string, enableCheckAll: boolean): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(singleSelection, selectAllText, unselectAllText, 1, true, null, null, "Search sales order number", true, enableCheckAll);
  }

  /**
   * Get Feature DropDown setting
   * @returns MultiSelectDropdownSettings
   */
  public getFeatureDrpSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(false, 'Select All', 'UnSelect All', 1, true, 'id', 'displayName', "Search feature", true, false);
  }

  /**
 * Get Feature DropDown setting
 * @returns MultiSelectDropdownSettings
 */
  public getPresetDrpSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(false, 'Select All', 'UnSelect All', 1, true, 'id', 'displayName', "Search preset", true, false);
  }
  /**
   * Get Country dropdown setting
   * 
   * @returns MultiSelectDropdownSettings
   */

  public getCountryDrpSetting(isSingleselect: boolean, isSelectAll: boolean): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(isSingleselect, 'Select All', 'UnSelect All', 1, true, 'id', 'country', 'Search Country', true, isSelectAll);
  }


  /**
   * Get Language dropdown setting
   * 
   * @returns 
   */
  public getLanguageDrpSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(false, 'Select All', 'UnSelect All', 1, true, 'id', 'displayName', 'Search Language', true, false);
  }

  /**
  * Get System Sw Version dropdown setting
  * @returns 
  */
  public getSystemSoftwearVersionDropdownSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(false, 'Select All', 'UnSelect All', 1, true, null, null, 'Search System SW Version', true, false);
  }

  /**
   * Get Device Status dropdown setting
   * 
   * @returns 
   */
  public getDeviceConnectionStateDropdownSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(true, 'Select All', 'UnSelect All', 1, false, 'key', 'value', null, false);
  }


  /**
   * Lock Unlock dropdown setting
   * 
   * @returns 
   */
  public getLockStateDropdownSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(true, 'Select All', 'UnSelect All', 1, false, 'value', 'key', null, true);
  }

  /**
  * Edit Enable/Disable dropdown setting
  * 
  * @returns 
  */
  public getEditStateDropdownSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(true, 'Select All', 'UnSelect All', 1, false, 'value', 'key', null, true);
  }

  /**
   * Get Job Type dropdown setting
   * 
   * @returns 
   */
  public getJobStatusDropdownSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(false, 'Select All', 'UnSelect All', 1, true, null, null, 'Search Job Status', true, false);
  }

  /**
   * Get Job Type DropDown Setting
   * 
   * @returns 
   */
  public getJobTypeDropdownSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(true, 'Select All', 'UnSelect All', 1, false, null, null, null, false, false);
  }

  /**
   * Get Json Version DropDown Setting
   * 
   * @returns 
   */
  public getjsonVersionDropdownSetting(isSingleselect: boolean): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(isSingleselect, 'Select All', 'UnSelect All', 1, true, 'id', 'version', 'Search Json Version', true, false);
  }

  /**
   * Get Device type DropDown Setting
   * 
   * @returns 
   */
  public getDeviceTypeDropdownSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(true, 'Select All', 'UnSelect All', 1, false, null, null, null, null, null);
  }

  /**
   * Get Software Status DropDown Setting
   * 
   * @returns 
   */
  public getSoftwareStatusDropdownSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(true, 'Select All', 'UnSelect All', 1, false, null, null, null, null, null);
  }

  /**
   * Get Probe Type DropDown Setting
   * 
   * @returns 
   */
  public getProbeConfigGroupTypeDropdownSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(false, 'Select All', 'UnSelect All', 1, true, 'key', 'value', "Search probe type", true, false);
  }


  /**
   * Get Probe Type DropDown Setting
   *
   * @returns
   */
  public getProbeTypeDropdownSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(false, 'Select All', 'UnSelect All', 1, true, 'id', 'displayName', "Search probe type", true, false);
  }

  /**
  * Get Feature Validity Period DropDown setting
  * @returns MultiSelectDropdownSettings
  */
  public getFeatureValidityPeriodDrpSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(true, 'Select All', 'UnSelect All', 1, true, 'value', 'key', "Search feature validity period", true);
  }

  /**
   * Get Sales Order Type DropDown setting
   * 
   * <AUTHOR>
   * @returns 
   */
  public getSalesOrderTypeDrpSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(true, 'Select All', 'UnSelect All', 1, false, "key", "value", null, false);
  }

  /**
 * Get Transfer Order Selection DropDown setting
 * 
 * <AUTHOR>
 * @returns 
 */
  public getTransferOrderSelectionDrpSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(true, 'Select All', 'UnSelect All', 1, true, "id", "salesOrderNumber", "Search Sales Order", true);
  }


  /**
  * Get Order Type Record DropDown setting
  * 
  * <AUTHOR>
  * @returns 
  */
  public getOrderRecordTypeDrpSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(false, 'Select All', 'UnSelect All', 1, true, "key", "value", "Search Order Record Type", true, false);
  }

  /**
  * Get Create Order Type Record DropDown setting
  * 
  * <AUTHOR>
  * @returns 
  */
  public getCreateOrderRecordTypeDrpSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(true, 'Select All', 'UnSelect All', 1, true, "key", "value", "Search Order Record Type", true, false);
  }

  /**
   * Get Sales Order Type DropDown setting
   * 
   * <AUTHOR>
   * @returns 
   */
  public getProductConfigStatusDrpSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(false, 'Select All', 'UnSelect All', 1, false, "key", "value", null, false);
  }

  /**
   * Get OS Type DropDown setting
   * 
   * <AUTHOR>
   * @returns 
   */
  public getOTSTypeDrpSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(true, 'Select All', 'UnSelect All', 1, false, 'key', 'value', null, false);
  }

  /**
   * Get Product Status DropDown setting
   * 
   * <AUTHOR>
   * @returns 
   */
  public getProductStatusDrpSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(false, 'Select All', 'UnSelect All', 1, false, 'key', 'value', null, true);
  }

  /**
   * Audit Module Drp Setting 
   * 
   * <AUTHOR>
   * @returns 
   */
  public getAuditModuleDrpSetting(): MultiSelectDropdownSettings {
    return new MultiSelectDropdownSettings(true, 'Select All', 'UnSelect All', 1, true, "name", "displayName", "Search Module", true, false);
  }

  /**
   * Audit Action Drp Setting 
   * 
   * <AUTHOR>
   * @returns 
   */
  public getAuditActionDrpSetting(): MultiSelectDropdownSettings {
    let drpSetting: MultiSelectDropdownSettings = new MultiSelectDropdownSettings(false, 'Select All', 'UnSelect All', 1, true, "name", "displayName", "Search Action", true, false);
    drpSetting.noDataAvailablePlaceholderText = "Please select Module to see the values"
    return drpSetting;
  }

  /**
   * Get List with out Not associated options
   * 
   * <AUTHOR>
   * 
   * @param item 
   * @returns 
   */
  public getListWithoutNotAssociated(item: any[]): any[] {
    return item.filter(obj => obj.id != -1)
  }

  /**
   * set all option enable and return 
   * 
   * <AUTHOR>
   * 
   * @param item 
   * @returns 
   */
  public setAllOptionEnable(item: any[]): any[] {
    return item?.map(itemObj => {
      itemObj.isDisabled = false;
      return itemObj;
    });
  }

  /**
   * Not Associated Option Disabled and return list
   * 
   * <AUTHOR>
   * @param item 
   * @returns 
   */
  public setNotAssociatedOptionDisabled(item: any[]): any[] {
    return item.map(itemObj => {
      if (itemObj.id == -1) {
        itemObj.isDisabled = true;
      }
      return itemObj;
    })
  }

  /**
   * other option is disabled and return list
   * 
   * @param item 
   * @returns 
   */
  public setOtherOptionDisabled(item: any[]): any[] {
    return item?.map(itemObj => {
      if (itemObj.id != -1) {
        itemObj.isDisabled = true;
      }
      return itemObj;
    })
  }

}
