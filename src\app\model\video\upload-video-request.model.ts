export class Subtitles {
    public title: string;
    public changed: boolean;
    public subtitleFile: string;
    public subTitleFileSize: number;

    constructor($title: string, $subtitleFile: string, $changed: boolean, $subTitleFileSize: number) {
        this.title = $title;
        this.changed = $changed;
        this.subtitleFile = $subtitleFile;
        this.subTitleFileSize = $subTitleFileSize;
    }
}
export class UploadVideoRequest {
    public title: string;
    public duration: string;
    public videoFile: string;
    public videoFileSize: number;
    public videoChanged: boolean;
    public thumbnailFile: string;
    public thumbnailFileSize: number;
    public thumbnailChanged: boolean;
    public subtitles: Subtitles[];
    public notes?: string;
    public id?: number;


    constructor($title: string, $duration: string, $videoFile: string, $videoFileSize: number, $videoChanged: boolean, $thumbnailFile: string, $thumbnailFileSize: number, $thumbnailChanged: boolean, $subtitles: Subtitles[], $notes?: string, $id?: number) { // NOSONAR
        this.title = $title;
        this.duration = $duration;
        this.videoFile = $videoFile;
        this.videoFileSize = $videoFileSize;
        this.videoChanged = $videoChanged;
        this.thumbnailFile = $thumbnailFile;
        this.thumbnailFileSize = $thumbnailFileSize;
        this.thumbnailChanged = $thumbnailChanged;
        this.subtitles = $subtitles;
        this.notes = $notes;
        this.id = $id;
    }
}
