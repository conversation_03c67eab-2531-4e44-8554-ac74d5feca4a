import { ProbeRequest } from "../probe/multiProbe/ProbeRequest.model";
import { EnableFeaturesResponse } from "./EnableFeaturesResponse.model";
import { SalesOrderProductBaseResponse } from "./SalesOrderProductBaseResponse.model";

export class SalesOrderProductResponse extends SalesOrderProductBaseResponse {
    displayName: string;
    productCode: string;
    productType: string
    associatedFeatures: string;
    probeType: string;
    enableFeatures: Array<EnableFeaturesResponse>;
    productId: number;
    extraProduct: boolean;
    //UI SIDE
    isEditMode: boolean;
    probe: ProbeRequest;
    apiErrorMessage: string;
}