import { SuccessMessageResponse } from "../common/SuccessMessageResponse.model";
import { TransferOrderProductReviewResponse } from "./TransferOrderProductReviewResponse.model";

export class SalesOrderTransferSuccessResponse extends SuccessMessageResponse {
    salesOrderTransferValidateResponse: TransferOrderProductReviewResponse;

    constructor(message: string, salesOrderTransferValidateResponse: TransferOrderProductReviewResponse) {
        super(message);
        this.salesOrderTransferValidateResponse = salesOrderTransferValidateResponse;
    }
}