import { ListingPageReloadSubjectParameter } from "../common/listingPageReloadSubjectParameter.model";
import { SalesOrderFaieldSearchRequestBody } from "./SalesOrderFaieldSearchRequestBody.model";

export class SalesOrderFailedFilterAction {
    listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter;
    salesOrderFaieldSearchRequestBody: SalesOrderFaieldSearchRequestBody;


    constructor($listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, $salesOrderFaieldSearchRequestBody: SalesOrderFaieldSearchRequestBody) {
        this.listingPageReloadSubjectParameter = $listingPageReloadSubjectParameter;
        this.salesOrderFaieldSearchRequestBody = $salesOrderFaieldSearchRequestBody;
    }
}