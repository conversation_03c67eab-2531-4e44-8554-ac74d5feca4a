import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ValidityEnum } from '../enum/ValidityEnum.enum';
import { CommonsService } from '../util/commons.service';

@Pipe({
  name: 'disableLicenseCheckBoxpipe',
})
export class DisableLicenseCheckBoxpipe implements PipeTransform {

  constructor(private commonsService: CommonsService) { }
  transform(partNumbers: Array<any>, validity: ValidityEnum): boolean {
    if (!isNullOrUndefined(partNumbers)) {
      let validityEnum = this.commonsService.getEnumKey(ValidityEnum, validity)
      let configPartNumber = partNumbers.filter(obj => obj.validity === validityEnum);
      if (configPartNumber.length > 0) {
        return configPartNumber[0].allowedToEdit;
      }
    }
    return true;
  }

}
