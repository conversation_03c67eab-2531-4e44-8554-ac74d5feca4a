import { ValidityEnum } from "src/app/shared/enum/ValidityEnum.enum";
import { BaseResponse } from "../common/BaseResponse.model";

export class EnableFeaturesResponse extends BaseResponse {
    partNumber: string;
    validity: ValidityEnum;
    constructor($id: number, $name: string, $displayName: string, $partNumber: string, $validity: ValidityEnum) {
        super($id, $name, $displayName)
        this.partNumber = $partNumber;
        this.validity = $validity;
    }

}