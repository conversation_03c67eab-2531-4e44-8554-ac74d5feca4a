import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ConfigBaseMappingRequest } from 'src/app/model/probe/ConfigBaseMappingRequest.model';

@Pipe({
    name: 'assignConfigCheckBoxPipe'
})
export class AssignConfigCheckBoxPipe implements PipeTransform {

    transform(config: Array<ConfigBaseMappingRequest>, configId: number, reloadPipe: boolean): boolean {
        if (!isNullOrUndefined(config) && reloadPipe) {
            let filterData = config.filter(obj => obj.id == configId);
            if (filterData.length == 1) {
                return filterData[0].enable;
            }
        }
        return false;
    }

}
