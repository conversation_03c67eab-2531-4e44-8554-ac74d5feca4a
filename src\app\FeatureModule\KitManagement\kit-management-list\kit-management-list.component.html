<!-- loading start -->
<!-- loading gif start -->
<div class="ringLoading" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!-- loading gif end -->
<!-- loading end -->


<body *ngIf="bridgeWorldListDisplay">
    <!-- row start -->
    <div class="row">

        <!--############################################################-->
        <!--Filter start-->
        <!--############################################################-->
        <div class="col-md-3 pr-0" *ngIf="(!isFilterHidden)" id="bridgeKitFilterBtn">
            <label class="col-md-12 h5-tag">Filter</label>
            <div class="card mt-3">
                <div class="card-body">
                    <app-kit-management-filter [isFilterComponentInitWithApicall]="isFilterComponentInitWithApicall"
                        [kitManagemantSearchRequestBody]="kitManagemantSearchRequestBody"
                        [listPageRefreshForbackToDetailPage]="listPageRefreshForbackToDetailPage"></app-kit-management-filter>
                </div>
            </div>
        </div>
        <!--############################################################-->
        <!--Filter End-->
        <!--############################################################-->

        <!--table Block Start-->
        <div [className]="(isFilterHidden)?'col-md-12 pr-0':'col-md-9 pr-0'">
            <div class="container-fluid">
                <!--############################################################-->
                <!--############################################################-->
                <div class="row" class="headerAlignment">
                    <!--############################################################-->
                    <!--Left Side-->
                    <!--############################################################-->
                    <div class="childFlex">
                        <!----------------------------------------------->
                        <!------------Show/hide filter-------------------->
                        <!----------------------------------------------->
                        <div class="dropdown" id="hideShowFilter">
                            <button class="btn btn-sm btn-orange mr-3 ml-0 filter-symbol" (click)="toggleFilter()"
                                id="bridgeKitListHideShowButton">
                                <i class="fas fa-filter" aria-hidden="true"></i>
                                &nbsp;&nbsp;{{ hideShowFilterButtonText }}
                            </button>
                        </div>
                        <!----------------------------------------------->
                        <!------------Pagnatation drp-------------------->
                        <!----------------------------------------------->
                        <div>
                            <label class="mb-0">Show entry</label>
                            <select [(ngModel)]="drpselectsize" class="form-control form-control-sm"
                                (change)="changeDataSize($event)" id="kitManagemantListShowEntry">
                                <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                                    <option [value]="dataSize">{{ dataSize }}</option>
                                </ng-template>
                            </select>
                        </div>
                        <div class="ml-3 versionDiv"
                            *ngIf="kitRevVersionResponse != null && kitRevVersionResponse != null">
                            <div class="versionText">Rev version {{kitRevVersionResponse}}</div>
                        </div>
                    </div>
                    <!--############################################################-->
                    <!--Right Side-->
                    <!--############################################################-->
                    <div class="childFlex">
                        <div class="btn-group btn-group-sm mr-2" role="group" style="display: inline-block;">
                            <button type="button" class="btn btn-sm btn-orange btn-cust-border">Bridge
                                World</button>
                            <button type="button" (click)="showOtsWorldListDisplay()" class="btn btn-sm btn-cust-border"
                                id="bridgeKitToOtsKit" *ngIf="otsKitDisplayPermissions">OTS
                                World</button>
                        </div>
                        <!------------------------------------------------->
                        <!--------------Operations------------------->
                        <!------------------------------------------------->
                        <ng-template [ngIf]="operationsList.length > 1">
                            <div class="mr-3">
                                <select id="bridgeKitOperation" class="form-control form-control-sm"
                                    (change)="changeOperation($any($event.target)?.value)">
                                    <ng-template ngFor let-operation [ngForOf]="operationsList">
                                        <option [value]="operation">{{ operation }}</option>
                                    </ng-template>
                                </select>
                            </div>
                        </ng-template>


                        <!------------------------------------------------>
                        <!----------------refresh------------------------->
                        <!------------------------------------------------>
                        <div>
                            <button class="btn btn-sm btn-orange" (click)="clickOnRefreshButton()"
                                id="refresh_bridgeKitList">
                                <em class="fa fa-refresh"></em>
                            </button>
                        </div>
                    </div>
                </div>
                <!--############################################################-->
                <!--############################################################-->
                <!-- selected probes start -->
                <div>Total {{totalRecord}} Kit(s)
                    <p *ngIf="selectedKitIdList != null && selectedKitIdList.length > 0">
                        <strong>{{selectedKitIdList.length}} Kit(s) selected</strong>
                    </p>
                </div>
                <!-- selected probes end -->

                <!-------------------------------------------->
                <!-------------------------------------------->
                <!-- kit table start -->
                <!-------------------------------------------->
                <!-------------------------------------------->
                <div class="commonTable">
                    <table class="table table-sm table-bordered" style="overflow-x: scroll;" aria-hidden="true">
                        <!--###########################################-->
                        <!-- table header Start -->
                        <!--###########################################-->
                        <thead>
                            <tr class="thead-light">
                                <th class="checkox-table width-unset" *ngIf="isCheckBoxDiaply">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" name="chkselectall"
                                            [id]="selectAllCheckboxId"
                                            (change)="selectAllItem($any($event.target)?.checked)">
                                        <label class="custom-control-label" [for]="selectAllCheckboxId"></label>
                                    </div>
                                </th>
                                <th><span>Country</span></th>
                                <th><span class="text_nowrap">Kit Part Number</span></th>
                                <th><span class="text_nowrap">Bridge Part Number</span></th>
                                <th><span class="text_nowrap">Probe Part Number(s)</span></th>
                                <th><span class="text_nowrap">Description</span></th>
                                <th><span class="text_nowrap">Software Language</span></th>
                                <th><span class="text_nowrap">Video Version</span></th>
                                <th><span class="text_nowrap">Software Version</span></th>
                                <th><span class="text_nowrap">Software Part Number</span></th>
                                <th><span class="text_nowrap">Last Modified Date & Time</span></th>
                            </tr>
                        </thead>
                        <!--###########################################-->
                        <!-- table body start -->
                        <!--###########################################-->
                        <tbody>
                            <tr *ngFor="let kitObj of kitResponseList;">
                                <td class="width-unset" *ngIf="isCheckBoxDiaply">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input"
                                            [id]="chkPreFix+kitObj.id+chkPreFix" [name]="checkboxListName"
                                            (change)="selectCheckbox(kitObj,$any($event.target)?.checked)"
                                            [checked]="selectedKitIdList.includes(kitObj.id)">
                                        <label class="custom-control-label"
                                            [for]="chkPreFix+kitObj.id+chkPreFix"></label>
                                    </div>
                                </td>
                                <td (click)="showkitDetail(kitObj?.id)" class="spanunderline">
                                    <span class="text_nowrap" id="bridgeKitListToDeatil">{{kitObj?.country}}</span>
                                </td>
                                <td>
                                    <span class="text_nowrap">{{kitObj?.kitPartNumber |
                                        kitPartnumberDisplayPipe:kitObj?.dummyKitPartNumber}}</span>
                                </td>
                                <td><span class="text_nowrap">{{kitObj?.bridgePartNumber}}</span></td>
                                <td class="multiprobe"><span class="text_nowrap">
                                        <ul class="ulList">
                                            <li *ngFor="let probe of kitObj?.probePartNumbers;">
                                                {{probe}}
                                            </li>
                                        </ul>
                                    </span></td>
                                <td class="descriptionText"><span>{{kitObj?.description}}</span></td>
                                <td><span class="text_nowrap">{{kitObj?.language}}</span></td>
                                <td><span class="text_nowrap">{{kitObj?.videoVersion}}</span></td>
                                <td><span class="text_nowrap">{{kitObj?.softWareVersion}}</span></td>
                                <td><span class="text_nowrap">{{kitObj?.softWarePartNumber}}</span></td>
                                <td><span class="text_nowrap">{{kitObj?.modifiedDate | date:'MMM d, y, h:mm:ss
                                        a'}}</span></td>
                            </tr>
                        </tbody>
                        <!--###########################################-->
                        <!-- table body end -->
                        <!--###########################################-->
                    </table>

                </div>
                <!-------------------------------------------->
                <!-------------------------------------------->
                <!-- Kit table start -->
                <!-------------------------------------------->
                <!-------------------------------------------->

                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
                <!--pagination Start-->
                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
                <div>
                    <div>Showing {{totalRecordDisplay}} out of {{totalRecord}} Kit(s)</div>
                    <div class="float-right">
                        <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="itemsPerPage"
                            id="bridgeKit-pagination" [maxSize]="5" [rotate]="true" [boundaryLinks]="true"
                            (pageChange)="loadPage(page)">
                        </ngb-pagination>
                    </div>
                </div>
                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
                <!--pagination end-->
                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
            </div>
        </div>
        <!--table Block End-->
    </div>
    <!-- row end -->
</body>

<div *ngIf="bridgeWorldDetailDisplay">
    <app-kit-management-detail (showKitList)="showKitList()" [kitId]="kitId"></app-kit-management-detail>
</div>