import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ModuleValidationServiceService } from '../util/module-validation-service.service';

@Pipe({
  name: 'hideShowEditIconForSoftwareBuilde'
})
export class HideShowEditIconForSoftwareBuildePipe implements PipeTransform {

  constructor(private moduleValidationServiceService: ModuleValidationServiceService) { }

  transform(deviceAssociatedCountry: Array<string>): any {
    if (!isNullOrUndefined(deviceAssociatedCountry) && deviceAssociatedCountry.length > 0) {
      let isDeviceAssociatedWithUserAssociatedCountries = this.moduleValidationServiceService.validateWithUserCountryForMultileRecord(deviceAssociatedCountry, null, false);
      if (isDeviceAssociatedWithUserAssociatedCountries) {
        return 'lockedDiv';
      }
    }
    return 'hideLockedDiv';
  }

}
