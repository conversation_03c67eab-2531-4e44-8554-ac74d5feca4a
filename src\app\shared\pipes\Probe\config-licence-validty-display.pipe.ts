import { <PERSON>pe, PipeTransform } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { ConfigBaseMappingRequest } from 'src/app/model/probe/ConfigBaseMappingRequest.model';
import { EndDateOptions } from '../../enum/endDateOptions.enum';

@Pipe({
    name: 'configLicenceValidityDisplay'
})
export class ConfigLicenceValidityDisplay implements PipeTransform {

    constructor(private sanitizer: DomSanitizer) { }

    transform(configMappingRequest: Array<ConfigBaseMappingRequest>, bothSalesOrderManual: boolean): SafeHtml {
        if (!configMappingRequest || configMappingRequest.length === 0) {
            return this.sanitizer.bypassSecurityTrustHtml('');
        }

        const validEntries = configMappingRequest.filter(request => request.startDate !== null);

        if (validEntries.length === 0) {
            return this.sanitizer.bypassSecurityTrustHtml('');
        }

        const listItems = validEntries.map(request => {
            const endDateOption = bothSalesOrderManual ? EndDateOptions.CUSTOMDATE : EndDateOptions.ONE_YEAR;
            return `<li>${request.displayName} - ${request.endDate === -1 ? EndDateOptions.UNLIMITED : endDateOption}</li>`;
        }).join('');

        const html = `<ul class="ulList">${listItems}</ul>`;
        return this.sanitizer.bypassSecurityTrustHtml(html);
    }
}
