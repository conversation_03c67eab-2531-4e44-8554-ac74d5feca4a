import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { LicensesRequest } from 'src/app/model/probe/multiProbe/LicensesRequest.model';
import { EndDateOptions } from '../../enum/endDateOptions.enum';
import { CommonsService } from '../../util/commons.service';
import { ConfigBaseMappingRequest } from 'src/app/model/probe/ConfigBaseMappingRequest.model';

@Pipe({
    name: 'featuresSelectCustomDatePipe'
})
export class FeaturesSelectCustomDatePipe implements PipeTransform {
    constructor(private commonsService: CommonsService) { }

    transform(features: Array<LicensesRequest> | Array<ConfigBaseMappingRequest>, featureId: number, reloadPipe: boolean): Date {
        if (!isNullOrUndefined(features) && reloadPipe) {
            let filterData = features.filter(obj => obj.id == featureId);
            if (filterData.length == 1 && !isNullOrUndefined(filterData[0].endDate) && !isNullOrUndefined(filterData[0].endDateUi) && filterData[0].endDateUi == EndDateOptions.CUSTOMDATE) {
                return new Date(this.commonsService.getUTCDateForDisplay(filterData[0].endDate));
            }
        }
        return null;
    }

}
