import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { DeleteSoftwareBuildDialogComponent } from '../../delete-software-build-dialog/delete-software-build-dialog.component';

@Injectable({
  providedIn: 'root'
})
export class DeleteSoftwareBildDialogService {

  constructor(private modalService: NgbModal) { }

  public confirm(
    title: string,
    message: string,
    btnOkText: string,
    btnCancelText: string,
    itemNumber: string,
    msgExtention: string,
    dialogSize: 'sm' | 'lg' = 'sm'): Promise<boolean> {
    const modalRef = this.modalService.open(DeleteSoftwareBuildDialogComponent, { size: dialogSize });
    modalRef.componentInstance.title = title;
    modalRef.componentInstance.message = message;
    modalRef.componentInstance.btnOkText = btnOkText;
    modalRef.componentInstance.btnCancelText = btnCancelText;
    modalRef.componentInstance.itemNumber = itemNumber;
    modalRef.componentInstance.msgExtention = msgExtention;

    return modalRef.result;

  }
}
