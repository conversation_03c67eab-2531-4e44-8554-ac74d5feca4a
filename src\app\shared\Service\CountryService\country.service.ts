import { Injectable } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { Subject } from 'rxjs';
import { ListCountryResource } from 'src/app/app.constants';
import { CountryFilterAction } from 'src/app/model/Country/CountryFilterAction.model';
import { CountryRequestBody } from 'src/app/model/Country/CountryRequestBody.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
@Injectable({
  providedIn: 'root'
})
export class CountryService {

  private countryListFilterRequestParameterSubject = new Subject<CountryFilterAction>;

  private countryListRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  public getCountryRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.countryListRefreshSubject;
  }

  public getCountryListFilterRequestParameterSubject(): Subject<CountryFilterAction> {
    return this.countryListFilterRequestParameterSubject;
  }


  public callRefreshPageSubject(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, resourceName: string, isFilterHidden: boolean, countryRequestBodyApply: CountryRequestBody): void {
    if (resourceName == ListCountryResource) {
      if (isFilterHidden) {
        let countryRequestBody = new CountryRequestBody("", "");
        if (!isNullOrUndefined(countryRequestBodyApply) && !listingPageReloadSubjectParameter.isClearFilter) {
          countryRequestBody = countryRequestBodyApply;
        }
        let countryFilterAction = new CountryFilterAction(listingPageReloadSubjectParameter, countryRequestBody);
        this.callCountryListFilterRequestParameterSubject(countryFilterAction);
      } else {
        this.countryListRefreshSubject.next(listingPageReloadSubjectParameter);
      }
    }
  }

  public callCountryListFilterRequestParameterSubject(countryFilterAction: CountryFilterAction): void {
    this.countryListFilterRequestParameterSubject.next(countryFilterAction);
  }

}
