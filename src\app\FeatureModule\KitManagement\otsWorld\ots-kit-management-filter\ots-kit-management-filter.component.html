<!--####################################################-->
<!--Filter Start-->
<!--####################################################-->
<form id="roleFilterform" role="form" class="form" [formGroup]="filterOtsKitForm">

    <!-- countries form field start -->
    <div class="form-group country-form-group">
        <label class="form-control-label" for="field_countries" id="label_countries"><strong>Country</strong></label>
        <ng-multiselect-dropdown id="field_countries" name="countries" [placeholder]="''" formControlName="countries"
            [settings]="countrySetting" [data]="countriesList">
        </ng-multiselect-dropdown>
    </div>
    <!-- countries form field end -->

    <!-----------Kit Part Number start-------------->
    <div class="form-group">
        <label class="form-control-label" for="field_Kit_Part_Number"><strong>Kit Part Number</strong></label>
        <input class="form-control" type="text" formControlName="kitPartNumber" />
        <div *ngIf="(filterOtsKitForm.get('kitPartNumber').touched || filterOtsKitForm.get('kitPartNumber').dirty) && 
    filterOtsKitForm.get('kitPartNumber').invalid">
            <div *ngIf="filterOtsKitForm.get('kitPartNumber').errors['maxlength']" class="pb-2">
                <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
            </div>
            <div *ngIf="filterOtsKitForm.get('kitPartNumber').errors['pattern']" class="pb-2">
                <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
            </div>
        </div>
    </div>
    <!-----------Kit Part Number end-------------->

    <!-----------Probe Part Number start-------------->
    <div class="form-group">
        <label class="form-control-label" for="field_Probe_Part_Number"><strong>Probe Part Number</strong></label>
        <input class="form-control" type="text" formControlName="probePartNumber" />
        <div *ngIf="(filterOtsKitForm.get('probePartNumber').touched || filterOtsKitForm.get('probePartNumber').dirty) && 
        filterOtsKitForm.get('probePartNumber').invalid">
            <div *ngIf="filterOtsKitForm.get('probePartNumber').errors['maxlength']" class="pb-2">
                <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
            </div>
            <div *ngIf="filterOtsKitForm.get('probePartNumber').errors['pattern']" class="pb-2">
                <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
            </div>
        </div>
    </div>
    <!-----------Probe Part Number end-------------->

    <hr class="mt-1 mb-2">
    <!--####################################################-->
    <!---------Action Button Start------->
    <!--####################################################-->
    <div class="">
        <button class="btn btn-sm btn-orange mr-3" (click)="searchData()" id="otsKitFilterSearch"
            [disabled]="filterOtsKitForm.invalid">Search</button>
        <button class="btn btn-sm btn-orange"
            (click)="clearFilter(defaultListingPageReloadSubjectParameter)">Clear</button>
    </div>
    <!--####################################################-->
    <!---------Action Button End------->
    <!--####################################################-->
</form>
<!--####################################################-->
<!--Filter End-->
<!--####################################################-->