import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { DetailRoleResource, ListRoleResource } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { RoleFilterAction } from 'src/app/model/Role/roleFilterAction.model';
import { RolePermissionResponse } from 'src/app/model/Role/rolePermissionResponse.model';
import { RoleRequestBody } from 'src/app/model/Role/roleRequestBody.model';
import { CommonsService } from '../../util/commons.service';

/**
 * <AUTHOR>
 */
@Injectable({
  providedIn: 'root'
})
export class RoleService {

  /**
 * RolePermission List
 */
  private rolePermissionList: RolePermissionResponse[] = [];

  //Loading Status
  private roleListLoadingSubject = new Subject<boolean>();
  private roleDetailLoadingSubject = new Subject<boolean>();

  //Refresh Role List 
  private roleListRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //Refresh Role Detail page
  private roleDetailRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //Role list filter
  private roleListFilterRequestParameterSubject = new Subject<RoleFilterAction>();

  constructor(private commonsService: CommonsService) { }

  /**
   * GetPermissionId List
   * <AUTHOR>
   * @param rolePermissionList 
   * @returns 
   */
  public getPermissionIdList(rolePermissionList: Array<RolePermissionResponse>): Array<number> {
    let idList = [];
    if (!this.commonsService.checkValueIsNullOrEmpty(rolePermissionList)) {
      for (let roleObj of rolePermissionList) {
        idList.push(roleObj.id);
      }
    }
    return idList;
  }


  /**
   * Role List Page Loading
   * <AUTHOR>
   * @returns boolean
   */
  public getRoleListLoadingSubject(): Subject<boolean> {
    return this.roleListLoadingSubject;
  }

  public callRoleListLoadingSubject(status: boolean): void {
    this.roleListLoadingSubject.next(status);
  }

  /**
   * Role detail Page Loading
   * <AUTHOR>
   * @returns boolean
   */
  public getRoleDetailLoadingSubject(): Subject<boolean> {
    return this.roleDetailLoadingSubject;
  }

  public callroleDetailLoadingSubject(status: boolean): void {
    this.roleDetailLoadingSubject.next(status);
  }

  /**
   * Role List Page Refresh After some Action Like Create or Update or Delete Role 
   * <AUTHOR>
   * @returns ListingPageReloadSubjectParameter
   */

  public getRoleListRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.roleListRefreshSubject;
  }


  /**
   * Role Detail Page Refresh After some Action Like Create or Update or Delete Role
   * isReloadData false means delete Role operation and move list page
   * <AUTHOR>
   * @returns ListingPageReloadSubjectParameter
   */
  public getRoleDetailRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.roleDetailRefreshSubject;
  }

  /**
   * Role List Page Refresh After some Action Like Serch parameter add
   * Note : Create or Update or Delete Role After Clear All filter and refresh page 
   * <AUTHOR>
   * @returns RoleList 
   */
  public getRoleListFilterRequestParameterSubject(): Subject<RoleFilterAction> {
    return this.roleListFilterRequestParameterSubject;
  }

  public callRoleListFilterRequestParameterSubject(roleFilterAction: RoleFilterAction): void {
    this.roleListFilterRequestParameterSubject.next(roleFilterAction);
  }

  /**
   * This function call the subject for loading start and stop
   * <AUTHOR>
   * @param status 
   * @param resourceName 
   * 
   */
  public isLoading(status: boolean, resourceName: string): void {
    if (resourceName == ListRoleResource) {
      this.callRoleListLoadingSubject(status);
    } else if (resourceName == DetailRoleResource) {
      this.callroleDetailLoadingSubject(status);
    }
  }

  /**
   * This function call the subject for reload the page data
   *  Note : (ListRoleResource) -> Filter page subject call -> Listing page subject call
   * clear all filter after page data Reload
   * <AUTHOR>
   * @param isReloadData -> false means move to prev page for DetailRoleResource.
   * @param resourceName 
   */
  public callRefreshPageSubject(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, resourceName: string, isFilterHidden: boolean): void {
    if (resourceName == ListRoleResource) {
      if (isFilterHidden) {
        let roleRequestBody = new RoleRequestBody("", []);
        let roleFilterAction = new RoleFilterAction(listingPageReloadSubjectParameter, roleRequestBody);
        this.callRoleListFilterRequestParameterSubject(roleFilterAction);
      } else {
        this.roleListRefreshSubject.next(listingPageReloadSubjectParameter);
      }
    } else if (resourceName == DetailRoleResource) {
      this.roleDetailRefreshSubject.next(listingPageReloadSubjectParameter);
    }
  }

  /**
 * Set RolePermission
 */
  public setRolePermissionList(rolePermissionList: RolePermissionResponse[]): void {
    this.rolePermissionList = rolePermissionList;
  }

  /**
   * Get RolePermission
   */
  public getRolePermissionList(): Array<RolePermissionResponse> {
    return this.rolePermissionList;
  }


}
