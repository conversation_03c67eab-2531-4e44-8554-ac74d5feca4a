import { AuditActionEnum } from "src/app/shared/enum/Audit/AuditActionEnum.enum";
import { AuditModuleEnum } from "src/app/shared/enum/Audit/AuditModuleEnum.enum";

export class AuditSearchRequsetBody {
    auditModule: AuditModuleEnum;
    auditAction: Array<AuditActionEnum>;
    uniqueId: string;
    modifiedBy: string;
    startmodifiedDate: number;
    endModifiedDate: number;

    constructor($auditModule: AuditModuleEnum, $auditAction: Array<AuditActionEnum>, $uniqueId: string, $modifiedBy: string, $startmodifiedDate: number, $endModifiedDate: number) {
        this.auditModule = $auditModule;
        this.auditAction = $auditAction;
        this.uniqueId = $uniqueId;
        this.modifiedBy = $modifiedBy;
        this.startmodifiedDate = $startmodifiedDate;
        this.endModifiedDate = $endModifiedDate;
    }

}