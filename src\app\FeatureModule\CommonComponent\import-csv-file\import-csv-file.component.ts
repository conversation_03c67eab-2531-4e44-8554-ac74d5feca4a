import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, ElementRef, Input, ViewChild } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ListKitManagementResource, OTSKitManagementListResource } from 'src/app/app.constants';
import { ImportFileForUpdateTemplateDataResponse } from 'src/app/model/importCSVFile/ImportFileForUpdateTemplateDataResponse.model';
import { ImportFilePopupInput } from 'src/app/model/importCSVFile/ImportFilePopupInput.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ImportCsvFileApiService } from 'src/app/shared/importFileService/import-csv-file-api.service';
import { SubjectMessageService } from 'src/app/shared/subject-message.service';
import { DownloadService } from 'src/app/shared/util/download.service';

@Component({
  selector: 'app-import-csv-file',
  templateUrl: './import-csv-file.component.html',
  styleUrls: ['./import-csv-file.component.css']
})
export class ImportCsvFileComponent {

  @Input('importFilePopupInput') importFilePopupInput: ImportFilePopupInput;
  @ViewChild('file') updateFileInput: ElementRef;

  public updateFileValidation: string = null;
  public updateFileValidationNotes: string = null;
  public updateFileSuccessStatus: ImportFileForUpdateTemplateDataResponse = null;
  public updateFileErrorStatus: string = null;

  constructor(
    private activeModal: NgbActiveModal,
    public subjectMessageService: SubjectMessageService,
    private exceptionService: ExceptionHandlingService,
    private downloadService: DownloadService,
    private importCsvFileApiService: ImportCsvFileApiService) { }

  private isLoading(status: boolean): void {
    this.subjectMessageService.setLoading(status);
  }

  public downloadUpdateTemplate(): void {
    this.isLoading(true);
    this.importCsvFileApiService.downloadCsvTemplate(this.importFilePopupInput.resourse).subscribe(
      {
        next: (response: HttpResponse<any>) => {
          this.downloadService.downloadTemplate(this.importFilePopupInput.downloadTemplateFileName, response);
          this.isLoading(false);
        }, error: (error: HttpErrorResponse) => {
          this.exceptionService.customErrorMessage(error);
          this.isLoading(false);
        }
      })
  }

  /**
   * File name validation Check
   * 
   * Kit Import Csv File name underscore before 2 charactor required
   * 
   * <AUTHOR>
   * 
   * return true means Invalid and false means valid file name
   */
  private validalidateFileName(fileNameWithExt: string): boolean {
    if (this.importFilePopupInput.resourse == ListKitManagementResource || this.importFilePopupInput.resourse == OTSKitManagementListResource) {
      let regex = /\d+/g;
      if (fileNameWithExt.includes('_')) {
        let fileName = fileNameWithExt.substring(
          0,
          fileNameWithExt.indexOf('_')
        );
        let matches = fileName.match(regex);
        if (matches != null && matches.length > 0) {
          let lastNumber = matches[matches.length - 1];
          let lastIndex = fileName.lastIndexOf(lastNumber) + lastNumber.length;
          if (lastIndex != fileName.length) {
            let revVersionList = fileName.substring(lastIndex).match(/^[a-zA-Z]*$/);
            return (revVersionList == null || revVersionList.length == 0);
          }
        }
      }
      return true;
    }
    return false;
  }


  public onUpdateFileSelect(files: File[]): void {
    if (files.length > 0) {
      this.updateFileSuccessStatus = null;
      this.updateFileErrorStatus = null;
      let fileName = files[0].name;
      let updateFileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
      let velidateFileName = this.validalidateFileName(fileName);
      if (updateFileExtension != "csv") {
        this.updateFileValidation = "Only csv is supported.";
        this.updateFileValidationNotes = null;
      } else if (velidateFileName) {
        if (this.importFilePopupInput.resourse == OTSKitManagementListResource) {
          this.updateFileValidation = "Invalid file name.";
          this.updateFileValidationNotes = "Please consider this as an example: D008855AA_Kosmos Ots Price List.csv"
        }
        if (this.importFilePopupInput.resourse == ListKitManagementResource) {
          this.updateFileValidation = "Invalid file name.";
          this.updateFileValidationNotes = "Please consider this as an example: D008855AA_Kosmos Bridge Price List.csv"
        }
      } else {
        this.updateFileValidation = null;
        this.updateFileValidationNotes = null;
        const updateCsvFile = files[0];
        this.reset(this.updateFileInput);
        this.importFile(updateCsvFile);
      }
    }
  }

  private reset(fileInput): void {
    fileInput.nativeElement.value = "";
  }

  private importFile(updateCsvFile: any): void {
    this.isLoading(true);
    const formData = new FormData();
    formData.append('userFile', updateCsvFile);
    this.importCsvFileApiService.importFileForUpdateTemplateData(formData, this.importFilePopupInput.resourse)?.subscribe({
      next: (response: HttpResponse<ImportFileForUpdateTemplateDataResponse>) => {
        if (response.status == 200) {
          this.updateFileSuccessStatus = response.body;
        }
        this.isLoading(false);
      },
      error: (error) => {
        this.isLoading(false);
        if (error.status == 412) {
          this.updateFileErrorStatus = error.error["errorMessage"];
        } else {
          this.exceptionService.customErrorMessage(error);
        }
      }
    });
  }

  public decline(): void {
    this.activeModal.close(false);
  }

}
