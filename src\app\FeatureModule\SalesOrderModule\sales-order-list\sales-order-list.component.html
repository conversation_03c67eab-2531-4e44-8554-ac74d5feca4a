<!-- loading start -->
<!-- loading gif start -->
<div class="ringLoading" *ngIf="loading">
  <div class="ringLoadingDiv">
    <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
  </div>
</div>
<!-- loading gif end -->
<!-- loading end -->


<body *ngIf="salesOrderListDisplay">
  <!-- row start -->
  <div class="row">

    <!--############################################################-->
    <!--Filter start-->
    <!--############################################################-->
    <div class="col-md-3 pr-0" *ngIf="(!isFilterHidden)">
      <label class="col-md-12 h5-tag">Filter</label>
      <div class="card mt-3">
        <div class="card-body">
          <app-sales-order-filter [isFilterComponentInitWithApicall]="isFilterComponentInitWithApicall"
            [salesOrderSearchRequestBody]="salesOrderSearchRequestBody"
            [listPageRefreshForbackToDetailPage]="listPageRefreshForbackToDetailPage">
          </app-sales-order-filter>
        </div>
      </div>
    </div>
    <!--############################################################-->
    <!--Filter End-->
    <!--############################################################-->

    <!--table Block Start-->
    <div [className]="(isFilterHidden)?'col-md-12 pr-0':'col-md-9 pr-0'">
      <div class="container-fluid" id="salesOrderList">
        <!--############################################################-->
        <!--############################################################-->
        <div class="row" class="headerAlignment">
          <!--############################################################-->
          <!--Left Side-->
          <!--############################################################-->
          <div class="childFlex">
            <!----------------------------------------------->
            <!------------Show/hide filter-------------------->
            <!----------------------------------------------->
            <div class="dropdown" id="hideShowFilter">
              <button class="btn btn-sm btn-orange mr-3 ml-0 filter-symbol" (click)="toggleFilter()">
                <i class="fas fa-filter" aria-hidden="true"></i>
                &nbsp;&nbsp;{{ hideShowFilterButtonText }}
              </button>
            </div>
            <!----------------------------------------------->
            <!------------Pagnatation drp-------------------->
            <!----------------------------------------------->
            <div>
              <label class="mb-0">Show entry</label>
              <select id="salesOrderListShowEntry" [(ngModel)]="drpselectsize" class="form-control form-control-sm"
                (change)="changeDataSize($event)">
                <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                  <option [value]="dataSize">{{ dataSize }}</option>
                </ng-template>
              </select>
            </div>
            <app-sync-time-display [syncTimeResponse]="salesOrderSchedulerSyncTimeResponse"
              [dateTimeDisplayFormat]="dateTimeDisplayFormat"></app-sync-time-display>
          </div>
          <!--############################################################-->
          <!--Right Side-->
          <!--############################################################-->
          <div class="childFlex">
            <div class="btn-group btn-group-sm mr-2" role="group" style="display: inline-block;">
              <button type="button"
                [className]="salesOrderListDisplay?'btn btn-sm btn-orange btn-cust-border':'btn btn-sm btn-cust-border'">Synced</button>
              <button type="button" (click)="hideShowSyncAndErrorListing(false)"
                [className]="salesOrderErrorListDisplay?'btn btn-sm btn-orange btn-cust-border':'btn btn-sm btn-cust-border'">Failed</button>
            </div>

            <!------------------------------------------------->
            <!--------------Sales Order Operations------------------->
            <!------------------------------------------------->
            <div *ngIf="salesOrderOperations.length > 1" class="mr-3">
              <select id="salesOrderOperation" class="form-control form-control-sm"
                (change)="changeSalesOrderOperation($event)">
                <ng-template ngFor let-salesOrderOperation [ngForOf]="salesOrderOperations">
                  <option [value]="salesOrderOperation">{{ salesOrderOperation }}</option>
                </ng-template>
              </select>
            </div>

            <!------------------------------------------------>
            <!----------------refresh------------------------->
            <!------------------------------------------------>
            <div>
              <button class="btn btn-sm btn-orange" (click)="refreshFilter(false)" id="refresh_salesOrderList"><em
                  class="fa fa-refresh"></em></button>
            </div>
          </div>
        </div>
        <!--############################################################-->
        <!--############################################################-->
        <!-- selected sales order start -->
        <div>Total {{totalRecord}} Sales Order(s)
          <p *ngIf="selectedSalesOrderIdList != null && selectedSalesOrderIdList.length>0">
            <strong>{{selectedSalesOrderIdList.length}} Sales Order selected</strong>
          </p>
        </div>
        <!-- selected sales order end -->

        <!-------------------------------------------->
        <!-------------------------------------------->
        <!-- sales order table start -->
        <!-------------------------------------------->
        <!-------------------------------------------->
        <div class="commonTable">
          <table class="table table-sm table-bordered" style="overflow-x: scroll;" aria-hidden="true">
            <!--###########################################-->
            <!-- table header Start -->
            <!--###########################################-->
            <thead>
              <tr class="thead-light">
                <th class="checkox-table width-unset" *ngIf="showCheckBox && salesOrderAdminPermission">
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" name="chkselectall" [id]="selectAllCheckboxId"
                      (change)="selectAllItem($any($event.target)?.checked)">
                    <label class="custom-control-label" [for]="selectAllCheckboxId"></label>
                  </div>
                </th>
                <th><span>Order Number</span></th>
                <th><span>Order Type</span></th>
                <th><span>Customer Name</span></th>
                <th><span>Customer E-mail</span></th>
                <th><span>Country</span></th>
                <th><span>PO#</span></th>
                <th><span>Order Record Type</span></th>
                <th><span>SO Config Status</span></th>
                <th><span>Order Start Date & Time</span></th>
                <th><span>Last Modified Date & Time</span></th>
                <th><span>Last Synced Date & Time</span></th>
              </tr>
            </thead>
            <!--###########################################-->
            <!-- table body start -->
            <!--###########################################-->
            <tbody>
              <tr *ngFor="let salesOrderObj of salesOrderResponseList; let salesOrderIndex = index">
                <td class="width-unset" *ngIf="showCheckBox && salesOrderAdminPermission">
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" [id]="chkPreFix+salesOrderObj.id+chkPreFix"
                      [name]="checkboxListName" (change)="selectCheckbox(salesOrderObj,$any($event.target)?.checked)"
                      [checked]="selectedSalesOrderIdList.includes(salesOrderObj.id)">
                    <label class="custom-control-label" [for]="chkPreFix+salesOrderObj.id+chkPreFix"></label>
                  </div>
                </td>
                <td (click)="showSalesOrderDetail(salesOrderObj?.id)" class="spanunderline" id="salesOrderListToDeatil">
                  <span>{{salesOrderObj?.salesOrderNumber}}</span>
                </td>
                <td><span>{{salesOrderObj?.orderType ?
                    salesOrderTypeStatus[salesOrderObj?.orderType]:''}}</span></td>
                <td><span>{{salesOrderObj?.customerName}}</span></td>
                <td><span>{{salesOrderObj?.customerEmail}}</span></td>
                <td><span>{{salesOrderObj?.country}}</span></td>
                <td><span>{{salesOrderObj?.poNumber}}</span></td>
                <td><span>{{salesOrderObj?.orderRecordType}}</span></td>
                <td><span>{{salesOrderObj?.soStatus?productConfigStatus[salesOrderObj?.soStatus]:''}}</span></td>
                <td><span>{{salesOrderObj?.soCreatedDate | date:dateTimeDisplayFormat}}</span></td>
                <td><span>{{salesOrderObj?.modifiedDate | date:dateTimeDisplayFormat}}</span></td>
                <td><span>{{salesOrderObj?.lastSyncDate | date:dateTimeDisplayFormat}}</span></td>
              </tr>
            </tbody>
            <!--###########################################-->
            <!-- table body end -->
            <!--###########################################-->
          </table>

        </div>
        <!-------------------------------------------->
        <!-------------------------------------------->
        <!-- sales order table end -->
        <!-------------------------------------------->
        <!-------------------------------------------->

        <!----------------------------------------------------------->
        <!----------------------------------------------------------->
        <!--pagination Start-->
        <!----------------------------------------------------------->
        <!----------------------------------------------------------->
        <div>
          <div>Showing {{totalRecordDisplay}} out of {{totalRecord}} Sales Order(s)</div>
          <div class="float-right">
            <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="itemsPerPage" [maxSize]="5"
              id="salesOrderList-pagination" [rotate]="true" [boundaryLinks]="true" (pageChange)="loadPage(page)">
            </ngb-pagination>
          </div>
        </div>
        <!----------------------------------------------------------->
        <!----------------------------------------------------------->
        <!--pagination end-->
        <!----------------------------------------------------------->
        <!----------------------------------------------------------->
      </div>
    </div>
    <!--table Block End-->
  </div>
  <!-- row end -->
</body>

<div *ngIf="salesOrderDetailDisplay">
  <app-sales-order-detail (showSalesOrderList)="showSalesOrderList()" [salesOrderId]="salesOrderId">
  </app-sales-order-detail>
</div>

<div *ngIf="salesOrderErrorListDisplay">
  <app-sales-order-error-list
    (hideShowSyncAndErrorListing)="hideShowSyncAndErrorListing($any($event))"></app-sales-order-error-list>
</div>