import { ProductConfigStatus } from "src/app/shared/enum/SalesOrder/ProductConfigStatus.enum";
import { AssociatedConfigLicence } from "../associated-config-licence.model";
import { SalesOrderProductBaseResponse } from "./SalesOrderProductBaseResponse.model";

export class SalesOrderProductBaseDetailResponse extends SalesOrderProductBaseResponse {
    probeType: string;
    bridgeKitPartNumberCode: string;
    otsKitPartNumberCode: string;
    productCode: string;
    probeConfigGroupPartNumberCodes: Array<string>;
    associatedFeatures: Array<AssociatedConfigLicence>;
    associatedPresets: Array<AssociatedConfigLicence>;
    destinationSalesOrderNumber: string;

    constructor(//NOSONAR
        sopmId: number,
        entitySerialNumber: string,
        entityStatus: ProductConfigStatus,
        entityPk: number,
        probeType: string,
        bridgeKitPartNumberCode: string,
        otsKitPartNumberCode: string,
        productCode: string,
        destinationSalesOrderNumber: string,
        probeConfigGroupPartNumberCodes: Array<string>,
        associatedFeatures: Array<AssociatedConfigLicence>,
        associatedPresets: Array<AssociatedConfigLicence>,
    ) {
        super(sopmId, entitySerialNumber, entityStatus, entityPk);
        this.probeType = probeType;
        this.bridgeKitPartNumberCode = bridgeKitPartNumberCode;
        this.otsKitPartNumberCode = otsKitPartNumberCode;
        this.productCode = productCode;
        this.destinationSalesOrderNumber = destinationSalesOrderNumber;
        this.probeConfigGroupPartNumberCodes = probeConfigGroupPartNumberCodes;
        this.associatedFeatures = associatedFeatures;
        this.associatedPresets = associatedPresets;
    }


}