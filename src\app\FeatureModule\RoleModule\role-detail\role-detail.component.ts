import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { isUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { DetailRoleResource, ROLE_DELETE } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { RoleResponse } from 'src/app/model/Role/roleResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { RoleService } from 'src/app/shared/Service/RoleService/role.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';

@Component({
  selector: 'app-role-detail',
  templateUrl: './role-detail.component.html',
  styleUrls: ['./role-detail.component.css']
})
export class RoleDetailComponent implements OnInit {

  @Input("roleId") roleId: number;
  @Output("showRoleList") showRoleList = new EventEmitter();

  loading: boolean = false;

  operationsList: string[] = [];
  roleResponse: RoleResponse = null;

  //subscription
  subscriptionForLoading: Subscription;
  subscriptionForRefeshRoleDetail: Subscription;


  constructor(
    private commonOperationsService: CommonOperationsService,
    private roleApiCallService: RoleApiCallService,
    private exceptionService: ExceptionHandlingService,
    private roleService: RoleService,
    private toste: ToastrService) { }

  public ngOnInit(): void {
    this.operationsList = this.commonOperationsService.accessRoleOperations();
    this.getRoleDetail();
    this.subjectInit();
  }


  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForLoading)) { this.subscriptionForLoading.unsubscribe() }
    if (!isUndefined(this.subscriptionForRefeshRoleDetail)) { this.subscriptionForRefeshRoleDetail.unsubscribe() }
  }

  private subjectInit(): void {
    /**
     * Loading hide/show
     * <AUTHOR>
     */
    this.subscriptionForLoading = this.roleService.getRoleDetailLoadingSubject().subscribe((res: boolean) => {
      this.setLoadingStatus(res);
    });

    /**
     * Update Role then Refresh page data
     * Delete Role then Go To Role List Page
     * <AUTHOR>
     */
    this.subscriptionForRefeshRoleDetail = this.roleService.getRoleDetailRefreshSubject().subscribe((listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter) => {
      if (listingPageReloadSubjectParameter.isMovePrevPage) {
        this.back();
      } else if (listingPageReloadSubjectParameter.isReloadData) {
        this.getRoleDetail();
      }
    });
  }

  /**
   * Get Role Detail
   * <AUTHOR>
   */
  private getRoleDetail(): void {
    this.setLoadingStatus(true);
    this.roleApiCallService.getRoleDetail(this.roleId)?.subscribe({
      next: (res: HttpResponse<RoleResponse>) => {
        if (res.status == 200) {
          this.roleResponse = res.body;
          this.setLoadingStatus(false);
        } else if (res.status == 204) {
          this.setLoadingStatus(false);
          this.toste.info(ROLE_DELETE);
          this.back();
        }
      }, error: (error: HttpErrorResponse) => {
        this.setLoadingStatus(false);
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  /**
   * Role Update And Delete Operations
   * <AUTHOR>
   * @param operationName 
   */
  public changeOperation(operationName: string): void {
    this.commonOperationsService.changeOperationForRole(operationName, DetailRoleResource, [this.roleId], null, [this.roleResponse]);
  }

  /**
   * Go To Role List Page
   * <AUTHOR>
   */
  public back(): void {
    this.showRoleList.emit();
  }

  /**
   * Loading Display
   * <AUTHOR>
   * @param status 
   */
  private setLoadingStatus(status: boolean): void {
    this.loading = status;
  }

  /**
  * Refresh Role Detail Page Data
  * 
  * <AUTHOR> 
  */
  public refreshRoleDetailPage(): void {
    this.getRoleDetail();
  }
}
