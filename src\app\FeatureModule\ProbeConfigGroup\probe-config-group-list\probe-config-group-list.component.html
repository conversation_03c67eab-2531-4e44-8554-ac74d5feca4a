<!-- loading start -->
<!-- loading gif start -->
<div class="ringLoading" *ngIf="loading">
  <div class="ringLoadingDiv">
    <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading" />
  </div>
</div>
<!-- loading gif end -->
<!-- loading end -->

<body *ngIf="showProbeConfigGroupForListingPage">
  <!--row start-->
  <div class="row">
    <!--Filter start-->
    <div class="col-md-3 pr-0" *ngIf="!isFilterHidden">
      <label class="col-md-12 h5-tag" for="">Filter</label>
      <div class="card mt-3">
        <div class="card-body">
          <app-probe-config-group-filter [isFilterComponentInitWithApicall]="isFilterComponentInitWithApicall"
            [probeConfigGroupRequestBody]="probeConfigGroupRequestBody"
            [listPageRefreshForbackToDetailPage]="listPageRefreshForbackToDetailPage"></app-probe-config-group-filter>
        </div>
      </div>
    </div>
    <!--Filter End-->

    <!--Table block start-->
    <div [className]="isFilterHidden ? 'col-md-12 pr-0' : 'col-md-9 pr-0'">
      <div class="container-fluid">
        <!--############################################################-->
        <!--############################################################-->
        <div class="row" class="headerAlignment">
          <!--############################################################-->
          <!--Left side-->
          <!--############################################################-->
          <div class="childFlex">
            <!----------------------------------------------->
            <!--Show/Hide filter-->
            <!----------------------------------------------->
            <div class="dropdown" id="hideShowFilter">
              <button class="btn btn-sm btn-orange mr-3 ml-0 filter-symbol" (click)="toggleFilter()"
                id="countryListHideShowButton">
                <i class="fas fa-filter" aria-hidden="true"></i>
                &nbsp;&nbsp;{{ hideShowFilterButtonText }}
              </button>
            </div>
            <!----------------------------------------------->
            <!--Pagination-->
            <!----------------------------------------------->
            <div>
              <label class="mb-0" for="">Show entry</label>
              <select [(ngModel)]="drpselectsize" class="form-control form-control-sm" (change)="changeDataSize($event)"
                id="probeConfigGroupListShowEntry">
                <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                  <option [value]="dataSize">{{ dataSize }}</option>
                </ng-template>
              </select>
            </div>
          </div>
          <!--Right side-->
          <!--Refresh-->
          <div class="childFlex">
            <div *ngIf="deleteProbeConfigGroupPermission">
              <button class="btn btn-sm btn-outline-secondary mr-3" (click)="deleteProbeConfigGroupOperation()"
                id="pcgDeleteListing">
                <i class="fa fa-trash" aria-hidden="true"></i>&nbsp;&nbsp;Delete
              </button>
            </div>
            <div *ngIf="addProbeConfigGroupPermission">
              <button class="btn btn-sm btn-orange mr-3" (click)="showAddProbeConfigGroupPage()"
                id="AddProbeConfigGroup">
                <em class="fa fa-plus"></em> &nbsp;&nbsp;Add Probe Config Group
              </button>
            </div>
            <div>
              <button class="btn btn-sm btn-orange" id="refresh_pcgList" (click)="refreshFilter()">
                <em class="fa fa-refresh"> </em>
              </button>
            </div>
          </div>
          <!--Refresh End-->
        </div>
        <!--Total Records-->
        <div>Total {{ totalRecord }} Probe Config Group(s)
          <p *ngIf="selectedProbeConfigGroupIdList != null && selectedProbeConfigGroupIdList.length > 0">
            <strong>{{selectedProbeConfigGroupIdList.length}} Probe config Group(s) selected</strong>
          </p>
        </div>

        <!--Table starts-->
        <div class="commonTable" id="probeConfigGroupTableId">
          <table class="table table-sm table-bordered" style="overflow-x: scroll" aria-hidden="true">
            <!--Header start-->
            <thead>
              <tr class="thead-light">
                <th class="checkox-table width-unset" *ngIf="deleteProbeConfigGroupPermission">
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" name="chkselectall" [id]="selectAllCheckboxId"
                      (change)="selectAllItem($any($event.target)?.checked)" />
                    <label class="custom-control-label" [for]="selectAllCheckboxId"></label>
                  </div>
                </th>
                <th><span>Part Number</span></th>
                <th><span>Probe Type</span></th>
                <th><span>Presets</span></th>
                <th><span>Features</span></th>
                <th><span>Description</span></th>
                <th><span>Last Modified Date & Time</span></th>
              </tr>
            </thead>
            <!--Header End-->

            <!--Body Start-->
            <tbody>
              <tr *ngFor="
                  let probeConfigGroupObj of probeConfigGroupListResponse;
                  let probeConfigGroupIndex = index
                ">
                <td class="width-unset" *ngIf="deleteProbeConfigGroupPermission">
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input"
                      [id]="chkPreFix + probeConfigGroupObj.id + chkPreFix" [name]="checkboxListName" (change)="
                        selectCheckbox(
                          probeConfigGroupObj,
                          $any($event.target)?.checked
                        )
                      " [checked]="
                      selectedProbeConfigGroupIdList.includes(
                          probeConfigGroupObj.id
                        )
                      " />
                    <label class="custom-control-label" [for]="chkPreFix + probeConfigGroupObj.id + chkPreFix"></label>
                  </div>
                </td>
                <td (click)="setProbrFeatureGroupId(probeConfigGroupObj.id)" class="spanunderline" id="pcgListToDeatil">
                  <span>{{ probeConfigGroupObj?.partNumberCode }}</span>
                </td>
                <td>
                  <span>{{ probeConfigGroupObj?.probeType |enumMappingDisplayNamePipe :probeTypesList }}</span>
                </td>
                <td class="nowrap" [innerHTML]="
                    probeConfigGroupObj?.presets | featureInformationDisplayPipe
                  "></td>
                <td class="nowrap" [innerHTML]="
                    probeConfigGroupObj?.features | featureInformationDisplayPipe
                  "></td>
                <td class="descriptionText">
                  <span>{{ probeConfigGroupObj?.description }}</span>
                </td>
                <td>
                  <span>{{
                    probeConfigGroupObj?.modifiedDate
                    | date : dateTimeDisplayFormat
                    }}</span>
                </td>
              </tr>
            </tbody>
            <!--Body End-->
          </table>
          <!--Table End-->
        </div>
        <!--Display Pagination and total items-->
        <div>
          <div>
            Showing {{ totalRecordDisplay }} out of {{ totalRecord }} Probe Config Group(s)
          </div>
          <div class="float-right">
            <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="itemsPerPage" [maxSize]="5"
              id="pcgList-pagination" [rotate]="true" [boundaryLinks]="true" (pageChange)="loadPage(page)">
            </ngb-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>

<div *ngIf="showProbeConfigGroupForAddUpdatePage">
  <app-add-or-update-probe-config-group
    (showListingPage)="showProbeConfigGroupListPage()"></app-add-or-update-probe-config-group>
</div>

<div *ngIf="showProbeConfigGroupForDetailPage">
  <app-probe-config-group-detail (showListingPage)="showProbeConfigGroupListPage()"
    [probeConfigGroupId]="probeConfigGroupId"></app-probe-config-group-detail>
</div>