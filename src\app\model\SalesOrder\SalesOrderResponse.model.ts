import { BasicSalesOrderDetailResponse } from "./BasicSalesOrderDetailResponse.model";
import { SalesOrderStatus } from "src/app/shared/enum/SalesOrder/SalesOrderStatus.enum";
import { SalesOrderTypeStatus } from "src/app/shared/enum/SalesOrder/SalesOrderTypeStatus.enum";

export class SalesOrderResponse extends BasicSalesOrderDetailResponse {
    id: number;
    country: string;
    soCreatedDate: number;
    status: SalesOrderStatus;
    orderType: SalesOrderTypeStatus;
    createdDate: number;
    modifiedDate: number;
    lastSyncDate: number;
    soStatus: string;

    constructor( //NOSONAR
        salesOrderNumber: string,
        customerName: string,
        customerEmail: string,
        countryId: number,
        poNumber: string,
        id: number,
        country: string,
        soCreatedDate: number,
        status: SalesOrderStatus,
        orderType: SalesOrderTypeStatus,
        createdDate: number,
        modifiedDate: number,
        lastSyncDate: number,
        soStatus: string,
        deviceAutoLock: boolean,
        probeAutoLock: boolean,
        orderRecordType: string
    ) {
        super(salesOrderNumber, customerName, customerEmail, countryId, poNumber, deviceAutoLock, probeAutoLock, orderRecordType);
        this.id = id;
        this.country = country;
        this.soCreatedDate = soCreatedDate;
        this.status = status;
        this.orderType = orderType;
        this.createdDate = createdDate;
        this.modifiedDate = modifiedDate;
        this.lastSyncDate = lastSyncDate;
        this.soStatus = soStatus;

    }
}
