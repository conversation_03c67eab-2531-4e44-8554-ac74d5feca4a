<!------------------------------------------->
<!-- loading start -->
<!------------------------------------------->
<div class="ringLoading" *ngIf="loading">
  <!-- loading gif start -->
  <div class="ringLoadingDiv">
    <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
  </div>
  <!-- loading gif end -->
</div>
<!------------------------------------------->
<!-- loading end -->
<!------------------------------------------->


<!--New -->
<!-- row start -->
<div class="row">

  <!--Filter start-->
  <div class="col-md-3 pr-0" *ngIf="allUserShow && (!isFilterHidden)">
    <!-- filter header start -->
    <label class="col-md-12 h5-tag">Filter</label>
    <!-- filter header end -->
    <!-- card start -->
    <div class="card mt-3">
      <!-- card body start -->
      <div class="card-body">
        <!-- filter form start -->
        <form id="filter-form" [formGroup]="filterForm" role="form" class="form">
          <!-- user name form group start -->
          <div class="form-group">
            <!-- user name input start -->
            <label class="form-control-label" for="field_userName"><strong>User Name</strong></label>
            <!-- user name input end -->
            <input class="form-control" type="text" formControlName="login" />
            <!----------------------------------------->
            <div
              *ngIf="(filterForm.get('login').touched || filterForm.get('login').dirty) && filterForm.get('login').invalid ">
              <!---Charater limits-->
              <div *ngIf="filterForm.get('login').errors['maxlength']">
                <p class="alert-color">
                  {{textBoxMaxCharactersAllowedMessage}}</p>
              </div>
              <div *ngIf="filterForm.get('login').errors['pattern']">
                <p class="alert-color">
                  {{specialCharacterErrorMessage}}</p>
              </div>
              <!---Charater limits-->
            </div>
            <!----------------------------------------->
          </div>
          <!-- user name form group end -->
          <!-- user role form group start -->
          <div class="form-group">
            <label class="form-control-label" for="field_userRole" id="label_userRole"><strong>User
                Role</strong></label>
            <!-- user role selection start -->
            <ng-multiselect-dropdown class="" id="field_userRole" name="userRole" [placeholder]="''"
              formControlName="userRole" [settings]="roleDropdownSettings" [data]="userRoleList">
            </ng-multiselect-dropdown>
            <!-- user role selection end -->
          </div>
          <!-- user role form group end -->

          <!-- Country filter start -->
          <div class="form-group">
            <label class="form-control-label" for="field_Country" id="label_Country"><strong>Country</strong></label>
            <!-- Country selection start -->
            <ng-multiselect-dropdown id="label_Country" name="Country" [placeholder]="''" formControlName="country"
              [settings]="countrySetting" [data]="countryList">
            </ng-multiselect-dropdown>
            <!-- Country selection end -->
          </div>
          <!-- Country filter end -->

          <hr class="mt-1 mb-2">
          <div class="">
            <!-- search button start for filter -->
            <button class="btn btn-sm btn-orange mr-3" (click)="searchLogFilter()" [disabled]="filterForm.invalid"
              id="userListSearch">Search</button>
            <!-- clear button for filter -->
            <button class="btn btn-sm btn-orange" (click)="clearFilter()" id="userListClear">Clear</button>
          </div>
        </form>
        <!-- filter form end -->
      </div>
      <!-- card body end -->
    </div>
    <!-- card end -->
  </div>
  <!--Filter End-->
  <div [className]="(isFilterHidden)?'col-md-12 pr-0':'col-md-9 pr-0'" id="userListPage">
    <!-- container start -->
    <div class="container-fluid">

      <!--view data member Start-->
      <div *ngIf="allUserShow">
        <!-- row start -->
        <div class="row" class="headerAlignment">
          <!--------------------------------------->
          <!--Left Side-->
          <!--------------------------------------->
          <div class="childFlex">
            <!-- hide Show FilterButton start -->
            <div class="dropdown" id="userDetailHideShowFilter">
              <button class="btn btn-sm btn-orange mr-3 ml-0 filter-symbol" (click)="toggleFilter()"
                [id]="(isFilterHidden)?'Show Filter':'Hide Filter'">
                <i class="fas fa-filter" aria-hidden="true" [id]="(isFilterHidden)?'Show Filter':'Hide Filter'"></i>
                &nbsp;&nbsp;{{ hideShowFilterButtonText }}
              </button>
            </div>
            <!-- hide Show FilterButton end -->
            <!-- show entry div start -->
            <div>
              <label class="mb-0">Show entry</label>
              <!-- selection of page entry start -->
              <select id="userDetailShowEntry" [(ngModel)]="drpselectsize" class="form-control form-control-sm"
                (change)="changeDataSize($event)" id="userListShowEntry">
                <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                  <option [value]="dataSize">{{ dataSize }}</option>
                </ng-template>
              </select>
              <!-- selection of page entry end -->
            </div>
            <!-- show entry div end -->
          </div>
          <!--------------------------------------->
          <!--Right Side-->
          <!--------------------------------------->
          <div class="childFlex">
            <div *ngIf="deleteUserPermission">
              <!-- delete button start -->
              <button class="btn btn-sm btn-outline-secondary mr-3" (click)="deleteMultipleuser()" id="deleteUser"><i
                  class="fa fa-trash" aria-hidden="true"></i>&nbsp;&nbsp;Delete</button>
              <!-- delete button end -->
            </div>
            <div *ngIf="addUserPermission">
              <!-- new user button start -->
              <button class="btn btn-sm btn-orange mr-3" (click)="addNewMember()"><em class="fa fa-plus"
                  id="addUserBtn"></em>&nbsp;&nbsp;New
                User</button>
              <!-- new user button end -->
            </div>
            <!--Refresh button add start-->
            <div>
              <button class="btn btn-sm btn-orange" (click)="clickOnRefreshButton()"><em
                  class="fa fa-refresh"></em></button>
            </div>
            <!--Refresh button add end-->
          </div>
        </div>
        <!-- row end -->




        <!--view data member Start-->
        <div>Total {{totalMember}} Users <p *ngIf="userIdListcollect != null && userIdListcollect.length>0">
            <strong>{{userIdListcollect.length}} User(s)
              selected</strong>
          </p>
        </div>
        <!-- table start -->
        <div class="commonTable">
          <table class="table table-sm table-bordered" aria-hidden="true" style="overflow-x: scroll;">
            <!-- table header start -->
            <thead>
              <tr class="thead-light">
                <!-- select all checkbox start -->
                <th class="checkox-table" *ngIf="deleteUserPermission">
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" name="chkselectall" [id]="selectAllCheckboxId"
                      (change)="selectAllItem($any($event.target)?.checked)">
                    <label class="custom-control-label" [for]="selectAllCheckboxId"></label>
                  </div>
                </th>
                <!-- select all checkbox end -->
                <th><span class="text_nowrap">Login Name</span></th>
                <th><span>Role</span></th>
                <th><span>Module(s)</span></th>
                <th><span>Country(s)</span></th>
                <th><span class="text_nowrap">Date & Time Added</span></th>
                <th><span class="text_nowrap">Last Logged In Date & Time</span></th>
              </tr>

            </thead>
            <!-- table header end -->
            <!-- table body start -->
            <tbody>
              <tr *ngFor="let user of userDetail;">
                <td *ngIf="deleteUserPermission">
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" [checked]="userIdListcollect.includes(user.id)"
                      [name]="checkboxListName" [id]="chkPreFix+user.id+chkPreFix"
                      (change)="selectCheckbox(user,$any($event.target)?.checked)">
                    <label class="custom-control-label" [for]="chkPreFix+user.id+chkPreFix"></label>
                  </div>
                </td>
                <td (click)="openMemberDetail(user.id)" [id]="tableRowPreFix+user.id+tableRowPreFix"><span
                    class="spanunderline">{{user.login}}</span>
                </td>
                <td>
                  <span> {{ user.userRoles | printListPipe }}</span>
                </td>
                <td class="min_col_width">
                  <span> {{ user?.modules | printListPipe }}</span>
                </td>
                <td class="max_col_width">
                  <span>{{user.countries | printListPipe}}</span>
                </td>
                <td class="text_nowrap">
                  <span>{{user.createdDate | date:dateTimeDisplayFormat}}</span>
                </td>
                <td class="text_nowrap">
                  <span>{{user.lastLoggedIn | date:dateTimeDisplayFormat}}</span>
                </td>
              </tr>

            </tbody>
            <!-- table body end -->

          </table>
          <!-- table end -->
        </div>

        <!-- shown entries start -->
        <div>
          <div>Showing {{totalMemberDisplay}} out of {{totalMember}} Users</div>
          <div class="float-right">
            <!-- ngb pagination start -->
            <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="itemsPerPage" [maxSize]="5"
              [rotate]="true" [boundaryLinks]="true" (pageChange)="loadPage(page)">
            </ngb-pagination>
            <!-- ngb pagination end -->

          </div>
        </div>
        <!-- shown entries end -->
      </div>
      <!--view data member End-->



      <!--flud tab 9 row-->
    </div>
    <!-- container end -->
  </div>
</div>
<!-- row end -->
<!--flud tab 9 row-->


<!--add Member Start-->
<div *ngIf="addUserShow">
  <!-- add user start -->
  <app-add-user (adduser)="viewAllmember()"></app-add-user>
  <!-- add user end -->
</div>
<!--add Member End-->

<!--View Single user start-->
<div *ngIf="singleUserShow">
  <app-user-detail [userId]="userId" (singleuser)="viewAllmember()"></app-user-detail>
</div>
<!--View Single user End-->