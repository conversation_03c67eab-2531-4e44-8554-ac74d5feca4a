import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { LicensesRequest } from 'src/app/model/probe/multiProbe/LicensesRequest.model';
import { ProbeConfigHistoryList } from 'src/app/model/probe/ProbeConfigHistoryList.model';

@Pipe({
    name: 'configBaseResponseDisplayPipe'
})
export class ConfigBaseResponseDisplayPipe implements PipeTransform {

    transform(featuresBaseResponse: LicensesRequest | ProbeConfigHistoryList): string {
        let featureName = featuresBaseResponse.displayName;
        //PartNumber as String
        let featuresPartNumbers = featuresBaseResponse.partNumberCode;
        if (!isNullOrUndefined(featureName) && !isNullOrUndefined(featuresPartNumbers) && featuresPartNumbers.length > 0) {
            if (!isNullOrUndefined(featuresPartNumbers)) {
                return featureName + " - " + featuresPartNumbers.split('-');
            }
        }
        return featureName;
    }

}
