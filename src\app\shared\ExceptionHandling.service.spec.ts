import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { API_TIME_OUT, FORBUDDEN_ERROR_MESSAGE, INTERNAL_SERVER_ERROR } from '../app.constants';
import { AuthJwtService } from '../shared/auth-jwt.service';
import { ExceptionHandlingService } from './ExceptionHandling.service';
import { SSOLoginService } from './Service/SSO/ssologin.service';

describe('ExceptionHandlingService', () => {
    let service: ExceptionHandlingService;
    let toastrService: jasmine.SpyObj<ToastrService>;
    let authService: jasmine.SpyObj<AuthJwtService>;
    let ssoLoginService: jasmine.SpyObj<SSOLoginService>;

    beforeEach(() => {
        const toastrSpy = jasmine.createSpyObj('ToastrService', ['error', 'info', 'warning']);
        const authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['clear']);
        const ssoLoginServiceSpy = jasmine.createSpyObj('SSOLoginService', ['logOutInMicrosoft']);

        TestBed.configureTestingModule({
            providers: [
                ExceptionHandlingService,
                { provide: ToastrService, useValue: toastrSpy },
                { provide: AuthJwtService, useValue: authServiceSpy },
                { provide: SSOLoginService, useValue: ssoLoginServiceSpy },
            ],
        });

        service = TestBed.inject(ExceptionHandlingService);
        toastrService = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
        authService = TestBed.inject(AuthJwtService) as jasmine.SpyObj<AuthJwtService>;
        ssoLoginService = TestBed.inject(SSOLoginService) as jasmine.SpyObj<SSOLoginService>;
    });

    it('should handle 400 Bad Request error', () => {
        const errorResponse = new HttpErrorResponse({
            status: 400,
            error: { errorMessage: 'Bad Request' },
        });

        service.customErrorMessage(errorResponse);
        expect(toastrService.warning).toHaveBeenCalledWith('Bad Request');
    });

    it('should handle 401 Unauthorized error and log out', () => {
        const errorResponse = new HttpErrorResponse({
            status: 401,
            error: { error: 'Unauthorized' },
        });

        service.customErrorMessage(errorResponse);
        expect(toastrService.warning).toHaveBeenCalledWith('Session Timeout');
        expect(authService.clear).toHaveBeenCalled();
        expect(ssoLoginService.logOutInMicrosoft).toHaveBeenCalled();
    });

    it('should handle 500 Internal Server Error', () => {
        const errorResponse = new HttpErrorResponse({ status: 500 });
        service.customErrorMessage(errorResponse);
        expect(toastrService.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
    });

    it('should handle 504 Gateway Timeout error', () => {
        const errorResponse = new HttpErrorResponse({ status: 504 });
        service.customErrorMessage(errorResponse);
        expect(toastrService.error).toHaveBeenCalledWith(API_TIME_OUT);
    });

    it('should handle 0 status (network error) and navigate to home', () => {
        const errorResponse = new HttpErrorResponse({ status: 0 });
        service.customErrorMessage(errorResponse);
        expect(toastrService.error).toHaveBeenCalledWith(FORBUDDEN_ERROR_MESSAGE);
    });
});
