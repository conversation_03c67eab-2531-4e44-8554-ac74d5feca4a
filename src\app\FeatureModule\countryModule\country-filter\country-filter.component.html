<form id="countryFilterform" role="form" class="firm" [formGroup]="filterCountryForm">
    <div class="form-group">
        <label name="form-control-label" for="field_countryName"><strong>Country</strong></label>
        <input class="form-control" type="text" formControlName="countryName" />
        <!--------------------------------------->
        <!---------Country Validation------------>
        <!---------Min Length-------------------->
        <!---------Max Length-------------------->
        <!--------------------------------------->
        <div *ngIf="(filterCountryForm.get('countryName').touched || filterCountryForm.get('countryName').dirty) &&  
                        filterCountryForm.get('countryName').invalid">
            <div *ngIf="filterCountryForm.get('countryName').errors['maxlength']">
                <span class="alert-color font-12" id="countryMaxLengthError">{{small_textBoxMaxLengthMessage}}</span>
            </div>
            <div *ngIf="filterCountryForm.get('countryName').errors['pattern']">
                <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
            </div>
        </div>
        <!--------------------------------------->
        <!--------------------------------------->
    </div>
    <div class="form-group">
        <label name="form-control-label" for="field_languageName"><strong>Languages</strong></label>
        <input class="form-control" type="text" formControlName="languageName" />
        <!--------------------------------------------->
        <!---------Language Name Validation------------>
        <!---------Min Length-------------------------->
        <!---------Max Length-------------------------->
        <!--------------------------------------------->
        <div *ngIf="(filterCountryForm.get('languageName').touched || filterCountryForm.get('languageName').dirty) &&  
                                filterCountryForm.get('languageName').invalid">
            <div *ngIf="filterCountryForm.get('languageName').errors['maxlength']">
                <span class="alert-color font-12">{{small_textBoxMaxLengthMessage}}</span>
            </div>
            <div *ngIf="filterCountryForm.get('languageName').errors['pattern']">
                <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
            </div>
        </div>
        <!---------------------------------------------------->
        <!---------------------------------------------------->
    </div>

    <hr class="mt-1 mb-2">
    <!--####################################################-->
    <!---------Action Button Start------->
    <!--####################################################-->
    <div class="">
        <button class="btn btn-sm btn-orange mr-3" (click)="searchData()" [disabled]="filterCountryForm.invalid"
            id="countryFilterSearch">Search</button>
        <button class="btn btn-sm btn-orange" id="countryRefreshBtn"
            (click)="clearFilter(defaultListingPageReloadSubjectParameter)">Clear</button>
    </div>
    <!--####################################################-->
    <!---------Action Button End------->
    <!--####################################################-->

</form>