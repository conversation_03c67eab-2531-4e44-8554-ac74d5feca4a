import { IDevice } from './device.model';
import { ISort } from './sort.model';
import { IPageable } from './pageable.model';

export interface IDevicechange {
    content?: IDevice[];
    pageable?: IPageable;
    totalPages?: number;
    totalElements?: number;
    last?: boolean;
    first?: boolean;
    sort?: ISort;
    numberOfElements?: number;
    size?: number;
    number?: number;
    empty?: boolean;
}

export class Devicechange {
    constructor(
        public content?: IDevice[],
        public pageable?: IPageable,
        public totalPages?: number,
        public totalElements?: number,
        public last?: boolean,
        public first?: boolean,
        public sort?: ISort,
        public numberOfElements?: number,
        public size?: number,
        public number?: number,
        public empty?: boolean,
    ) { }
}
