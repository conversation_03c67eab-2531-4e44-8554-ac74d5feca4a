import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ChangeDetectorRef, Component, OnInit, ViewEncapsulation } from '@angular/core';
import { isUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { DateDisplayFormat, DateTimeDisplayFormat, DELETE_SALES_ORDER_CONFIRMATION_MESSAGE, ITEMS_PER_PAGE, ListSalesOrderResource, SALES_ORDER_NOT_SELECTED } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { SalesOrderFilterAction } from 'src/app/model/SalesOrder/SalesFilterAction.model';
import { SalesOrderPageResponse } from 'src/app/model/SalesOrder/SalesOrderPageResponse.model';
import { SalesOrderResponse } from 'src/app/model/SalesOrder/SalesOrderResponse.model';
import { SalesOrderSchedulerManualSyncTimeResponse } from 'src/app/model/SalesOrder/SalesOrderSchedulerManuleSyncTimeResponse.model';
import { SalesOrderSchedulerSyncTimeResponse } from 'src/app/model/SalesOrder/SalesOrderSchedulerSyncTimeResponse.model';
import { SalesOrderSearchRequestBody } from 'src/app/model/SalesOrder/SalesOrderSearchRequestBody.model';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { collapseFilterTextEnum } from 'src/app/shared/enum/collapseFilterButtonText.enum';
import { SalesOrderOperationsEnum } from 'src/app/shared/enum/Operations/SalesOrderOperations.enum';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { ProductConfigStatus } from 'src/app/shared/enum/SalesOrder/ProductConfigStatus.enum';
import { SalesOrderTypeStatus } from 'src/app/shared/enum/SalesOrder/SalesOrderTypeStatus.enum';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ManualSyncService } from 'src/app/shared/manual-sync.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { SalesOrderService } from 'src/app/shared/Service/SalesOrderService/sales-order.service';
import { CommonCheckboxService } from 'src/app/shared/util/common-checkbox.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';

@Component({
  selector: 'app-sales-order-list',
  templateUrl: './sales-order-list.component.html',
  styleUrls: ['./sales-order-list.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class SalesOrderListComponent implements OnInit {

  loading: boolean = false;

  //Page
  itemsPerPage: number = 0;
  page: number = 0;
  previousPage: number = 0;
  totalItems: number = 0;

  //Totel Count Display
  totalRecordDisplay: number = 0;
  totalRecord: number = 0;

  //Page Size DropDown
  dataSizes: string[] = [];
  drpselectsize: number = ITEMS_PER_PAGE;
  dateDisplayFormat: string = DateDisplayFormat;
  dateTimeDisplayFormat: string = DateTimeDisplayFormat;

  //Filter
  isFilterComponentInitWithApicall: boolean = true;
  listPageRefreshForbackToDetailPage: boolean = false;
  isFilterHidden: boolean = true;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;

  //Sales Order List
  salesOrderResponseList: SalesOrderResponse[] = [];

  //Permission
  salesOrderAdminPermission: boolean = false;

  //subscription
  subscriptionForSalesOrderListFilterRequestParameter: Subscription;

  //Hide Show List and Detail Page
  salesOrderErrorListDisplay: boolean = false;
  salesOrderListDisplay: boolean = true;
  salesOrderDetailDisplay: boolean = false;
  salesOrderId: number = null;

  //unique CheckBox Name
  chkPreFix = "salesOrder";
  selectAllCheckboxId = "selectAllSalesOrder";
  checkboxListName = "salesOrderItem[]";

  //selected Sales Order Id Collect
  selectedSalesOrderIdList: number[] = [];
  localSalesOrderIdListArray: number[] = [];

  //kit serach request body store
  salesOrderSearchRequestBody: SalesOrderSearchRequestBody = null;

  //checkboxHide
  showCheckBox: boolean = true;

  //Sales Order enum
  productConfigStatus = ProductConfigStatus;
  salesOrderTypeStatus = SalesOrderTypeStatus;

  salesOrderOperations: string[] = [];

  //Sync Time 
  salesOrderSchedulerSyncTimeResponse: SalesOrderSchedulerSyncTimeResponse = null;

  constructor(
    private authservice: AuthJwtService,
    private commonsService: CommonsService,
    private commonOperationsService: CommonOperationsService,
    private salesOrderService: SalesOrderService,
    private salesOrderApiCallService: SalesOrderApiCallService,
    private exceptionService: ExceptionHandlingService,
    private commonCheckboxService: CommonCheckboxService,
    private permissionService: PermissionService,
    private dialogservice: ConfirmDialogService,
    private toste: ToastrService,
    private manualSyncService: ManualSyncService,
    private cdr: ChangeDetectorRef,
    private countryCacheService: CountryCacheService
  ) { }

  /**
   * <AUTHOR>
   */
  public ngOnInit(): void {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.page = 0;
      this.salesOrderOperations = this.commonOperationsService.accessSalesOrderOperations(true, false);
      this.dataSizes = this.commonsService.accessDataSizes();
      this.selectedSalesOrderIdList = [];
      this.isFilterComponentInitWithApicall = true;
      this.listPageRefreshForbackToDetailPage = false;
      this.isFilterHidden = false;
      this.salesOrderListDisplay = true;
      this.salesOrderDetailDisplay = false;
      this.setSalesOrderPermission();
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.drpselectsize = ITEMS_PER_PAGE;
      this.getSalesOrderSchedulerSyncTime();
    }
    this.subjectInit();
  }

  /**
   * Get SalesOrder Scheduler Sync
   * 
   * <AUTHOR>
   */
  private async getSalesOrderSchedulerSyncTime(): Promise<void> {
    this.setLoadingStatus(true);
    this.salesOrderSchedulerSyncTimeResponse = await this.salesOrderApiCallService.getSalesOrderSchedulerSyncTime();
  }

  /**
   * Permission set
   */
  private setSalesOrderPermission(): void {
    this.salesOrderAdminPermission = this.permissionService.getSalesOrderPermission(PermissionAction.SALES_ORDER_DELETE_ACTION);
  }

  private subjectInit(): void {
    /**
     * This Subject call from Filter component
     * Load all the Data
     * <AUTHOR>
     */
    this.subscriptionForSalesOrderListFilterRequestParameter = this.salesOrderService.getSalesOrderListFilterRequestParameterSubject()?.subscribe((salesOrderRequestParameter: SalesOrderFilterAction) => {
      if (salesOrderRequestParameter.listingPageReloadSubjectParameter.isReloadData) {
        if (salesOrderRequestParameter.listingPageReloadSubjectParameter.isDefaultPageNumber) {
          this.selectedSalesOrderIdList = [];
          this.resetPage()
        }
        this.loadAll(salesOrderRequestParameter.salesOrderSearchRequestBody);
      }
    });
  }

  /**
   * Destroy subscription
   * <AUTHOR>
   */
  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForSalesOrderListFilterRequestParameter)) { this.subscriptionForSalesOrderListFilterRequestParameter.unsubscribe() }
    this.salesOrderService.setOrderRecordTypeList([]);
    this.salesOrderService.setCountryList([]);
  }

  /**
   * Reset Page
   * <AUTHOR>
   */
  private resetPage(): void {
    this.page = 0;
    this.previousPage = 1;
  }

  /**
   * Clear all filter ,Reset Page and Reload the page
   * <AUTHOR>
   */
  public async refreshFilter(isClearFilter: boolean): Promise<void> {
    this.loading = true;
    this.resetPage();
    // Filter is hidden, directly update the service cache
    const orderRecordTypeList = await this.salesOrderApiCallService.getOrderRecordNumberList(false);
    const countryList = await this.countryCacheService.getCountryListFromCache();
    this.salesOrderService.setOrderRecordTypeList(orderRecordTypeList);
    this.salesOrderService.setCountryList(countryList);

    this.getSalesOrderSchedulerSyncTime();
    this.filterPageSubjectCallForReloadPage(true, isClearFilter);
  }

  /**
   * Item par page Value Changes like (10,50,100)
   * <AUTHOR>
   * @param datasize 
   */
  public changeDataSize(datasize): void {
    this.setLoadingStatus(true);
    this.selectedSalesOrderIdList = [];
    this.itemsPerPage = datasize.target.value;
    this.filterPageSubjectCallForReloadPage(true, false);
  }

  /**
   * single Checkbox Select
   * <AUTHOR>
   * @param salesOrderObj 
   * @param isChecked 
   */
  public selectCheckbox(salesOrderObj: SalesOrderResponse, isChecked: boolean): void {
    if (isChecked) {
      this.selectedSalesOrderIdList.push(salesOrderObj.id);
    } else {
      let index = this.selectedSalesOrderIdList.findIndex(obj => obj == salesOrderObj.id);
      this.selectedSalesOrderIdList.splice(index, 1);
    }
    this.defaultSelectAll();
  }

  /**
  * Sales Order Operations
  * @param event 
  */
  public changeSalesOrderOperation(event): void {
    switch (event.target.value) {
      case SalesOrderOperationsEnum.DELETE_ORDER:
        this.deleteSalesOrder();
        break
      case SalesOrderOperationsEnum.MANUAL_SYNC:
        this.manualSync();
        break;
      default:
        break;
    }
    let selection = document.getElementById('salesOrderOperation') as HTMLSelectElement;
    selection.value = SalesOrderOperationsEnum.SALES_ORDER_OPERATIONS;
  }

  /**
   * select All checkbox select or deSelect
   * <AUTHOR>
   */
  private defaultSelectAll(): void {
    this.commonCheckboxService.defaultSelectAll(this.localSalesOrderIdListArray, this.selectedSalesOrderIdList, this.selectAllCheckboxId);
  }

  /**
   * Select All CheckBox
   * <AUTHOR>
   * @param isChecked 
   */
  public selectAllItem(isChecked: boolean): void {
    this.selectedSalesOrderIdList = this.commonCheckboxService.selectAllItem(isChecked, this.localSalesOrderIdListArray, this.selectedSalesOrderIdList, this.checkboxListName);
  }

  /**
   * Change The Page
   * callSalesOrderListRefreshSubject ->Call the filter component
   * filter not clear and send with filter requrest and load data 
   * <AUTHOR>
   * @param page 
   */
  public loadPage(page): void {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.commonCheckboxService.clearSelectAllCheckbox(this.selectAllCheckboxId);
      this.filterPageSubjectCallForReloadPage(false, false);
    }
  }

  /**
   * Call Filter component subject and reload page
   * <AUTHOR>
   * @param isDefaultPageNumber 
   * @param isClearFilter 
   */
  public filterPageSubjectCallForReloadPage(isDefaultPageNumber: boolean, isClearFilter: boolean): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false)
    this.salesOrderService.callRefreshPageSubject(listingPageReloadSubjectParameter, ListSalesOrderResource, this.isFilterHidden, this.salesOrderSearchRequestBody);
  }

  /**
   * Toggle Filter
   * <AUTHOR>
   * @param id 
   */
  public toggleFilter(): void {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = false;
    this.isFilterHidden = !this.isFilterHidden;
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }

  /**
   * Get Sales Order List
   * 
   * <AUTHOR>
   * 
   * @param salesOrderRequestBody 
   */
  public loadAll(salesOrderRequestBody: SalesOrderSearchRequestBody): void {
    this.salesOrderSearchRequestBody = salesOrderRequestBody;
    let pageObj = {
      page: this.page - 1,
      size: this.itemsPerPage
    }
    this.setLoadingStatus(true);
    this.salesOrderApiCallService.getSalesOrderList(salesOrderRequestBody, pageObj)
      ?.subscribe(
        {
          next: (salesOrderPageResponse: HttpResponse<SalesOrderPageResponse>) => {
            if (salesOrderPageResponse.status == 200) {
              this.paginateDataset(salesOrderPageResponse.body);
            } else {
              this.salesOrderResponseList = [];
              this.totalRecordDisplay = 0;
              this.totalRecord = 0;
            }
            this.setLoadingStatus(false);
          },
          error: (error: HttpErrorResponse) => {
            this.setLoadingStatus(false);
            this.exceptionService.customErrorMessage(error);
          }
        }
      );
  }

  /**
   * Role Reseponse set 
   * <AUTHOR>
   * @param rolePageResponse 
   */
  private paginateDataset(salesOrderPageResponse: SalesOrderPageResponse): void {
    this.totalItems = salesOrderPageResponse.totalElements;
    this.salesOrderResponseList = salesOrderPageResponse.content;
    this.page = salesOrderPageResponse.number + 1;
    this.totalRecord = salesOrderPageResponse.totalElements;
    this.totalRecordDisplay = salesOrderPageResponse.numberOfElements;
    this.setLocalSalesOrderId(this.salesOrderResponseList);
    this.setLoadingStatus(false);
  }

  /**
   * Local Sales Order Id list create for Select all Checkbox
   * <AUTHOR>
   * @param salesOrderIdList 
   */
  public setLocalSalesOrderId(salesOrderIdList: SalesOrderResponse[]): void {
    this.localSalesOrderIdListArray = [];
    for (let salesOrderObj of salesOrderIdList) {
      this.localSalesOrderIdListArray.push(salesOrderObj.id);
    }
    this.defaultSelectAll();
  }

  /**
   * Loading Status 
   * <AUTHOR>
   */
  private setLoadingStatus(status: boolean): void {
    this.loading = status;
    this.cdr.detectChanges();
  }


  /**
   * show Sales Order List and Hide detail page
   * <AUTHOR>
   */
  public showSalesOrderList(): void {
    this.setLoadingStatus(true);
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = true;
    this.salesOrderId = null;
    this.salesOrderListDisplay = true;
    this.salesOrderDetailDisplay = false;
    this.getSalesOrderSchedulerSyncTime();
    if (this.isFilterHidden) {
      this.filterPageSubjectCallForReloadPage(true, false);
    }
  }

  /**
   * hide Sales Order List and show detail page
   * <AUTHOR>
   * @param id 
   */
  public showSalesOrderDetail(id: number): void {
    this.salesOrderId = id;
    this.salesOrderListDisplay = false;
    this.salesOrderDetailDisplay = true;
  }


  /**
   * Sync and Failed Sales Order Hide/Show
   * 
   * <AUTHOR>
   * @param isSyncSalesOrderDisplay 
   */
  public hideShowSyncAndErrorListing(isSyncSalesOrderDisplay: boolean): void {
    if (isSyncSalesOrderDisplay) {
      this.getSalesOrderSchedulerSyncTime();
      this.salesOrderListDisplay = true;
      this.salesOrderErrorListDisplay = false;
    } else {
      this.salesOrderListDisplay = false;
      this.salesOrderErrorListDisplay = true;
    }
  }

  /**
  * Delete Sales Order
  *
  * <AUTHOR>
  */
  public deleteSalesOrder(): void {
    if (this.selectedSalesOrderIdList.length > 0) {
      this.dialogservice.confirm('Delete', DELETE_SALES_ORDER_CONFIRMATION_MESSAGE)
        .then((confirmed) => {
          if (confirmed) {
            this.setLoadingStatus(true);
            this.salesOrderApiCallService.deleteSalesOrder(this.selectedSalesOrderIdList)?.subscribe({
              next: (res: HttpResponse<SuccessMessageResponse>) => {
                this.toste.success(res.body.message);
                this.filterPageSubjectCallForReloadPage(true, true);
                this.selectedSalesOrderIdList = [];
              },
              error: (error: HttpErrorResponse) => {
                this.setLoadingStatus(false);
                this.exceptionService.customErrorMessage(error);
              }
            })
          }
        });
    } else {
      this.toste.info(SALES_ORDER_NOT_SELECTED);
    }
  }

  /**
   * Initiates a manual synchronization process for sales orders.
   * 
   * <AUTHOR>
   * 
   * This method calls the `salesOrderManualSync` API and handles the response.
   * If the response contains a valid transaction ID, it prompts the user for confirmation.
   * Based on the user's confirmation, it either reloads the page with updated data or stops the loading process.
   * In case of an error, it displays a custom error message.
   * 
   * @returns {void}
   */
  public manualSync(): void {
    this.loading = true;
    this.salesOrderApiCallService.salesOrderManualSync().subscribe({
      next: (response: HttpResponse<SalesOrderSchedulerManualSyncTimeResponse>) => {
        if (response.body?.id) {
          this.setLoadingStatus(false);
          this.manualSyncService.confirm('Manual Sync', response.body)
            .then((confirmed: boolean) => {
              if (confirmed) {
                this.loading = true;
                this.getSalesOrderSchedulerSyncTime();
                this.filterPageSubjectCallForReloadPage(true, false);
              }
            });
        }
      },
      error: (error) => {
        this.exceptionService.customErrorMessage(error);
        this.setLoadingStatus(false);
      }
    });

  }
}