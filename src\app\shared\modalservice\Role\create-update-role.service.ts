import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Cancel } from 'src/app/app.constants';
import { CreateAndUpdateRoleComponent } from 'src/app/FeatureModule/RoleModule/create-and-update-role/create-and-update-role.component';
import { CreateAndUpdateRoleModelRequest } from 'src/app/model/Role/createAndUpdateRoleModelRequest.model';

@Injectable({
  providedIn: 'root'
})
export class CreateUpdateRoleService {
  constructor(
    private modalService: NgbModal
  ) { }

  /**
   * Create/Update Model open
   * <AUTHOR>
   * @param createAndUpdateRoleModelRequest 
   * @returns 
   */
  public openAddUpdateRolePopup(createAndUpdateRoleModelRequest: CreateAndUpdateRoleModelRequest): Promise<boolean> {
    const modalRef = this.modalService.open(CreateAndUpdateRoleComponent, { windowClass: "modal fade" });
    modalRef.componentInstance.createAndUpdateRoleModelRequest = createAndUpdateRoleModelRequest;
    return modalRef.result;
  }

  /**
   * Get Create Role Model Input
   * <AUTHOR>
   * @param resourceName 
   * @param isFilterHidden 
   * @returns 
   */
  public getCreateRoleParameter(resourceName: string, isFilterHidden: boolean): CreateAndUpdateRoleModelRequest {
    return new CreateAndUpdateRoleModelRequest("Role Create", "Create", Cancel, false, null, resourceName, isFilterHidden);
  }

  /**
   * Get Update Role Model Input
   * <AUTHOR>
   * @param roleObject 
   * @param resourceName 
   * @param isFilterHidden 
   * @returns 
   */
  public getUpdateRoleParameter(roleId: number, resourceName: string, isFilterHidden: boolean): CreateAndUpdateRoleModelRequest {
    return new CreateAndUpdateRoleModelRequest("Role Update", "Update", Cancel, true, roleId, resourceName, isFilterHidden);
  }


}
