import { SalesOrderBridgeDetailValidateResponse } from "./SalesOrderBridgeDetailValidateResponse.model";
import { SalesOrderProbeDetailValidateResponse } from "./SalesOrderProbeDetailValidateResponse.model";

export class SalesOrderTransferProductDetailValidateResponse {
    probes: Array<SalesOrderProbeDetailValidateResponse>;
    bridges: Array<SalesOrderBridgeDetailValidateResponse>;

    constructor(probes: Array<SalesOrderProbeDetailValidateResponse>, bridges: Array<SalesOrderBridgeDetailValidateResponse>) {
        this.probes = probes;
        this.bridges = bridges;
    }
}