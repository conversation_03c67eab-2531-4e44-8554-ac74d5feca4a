import { FeaturesValidityPartNumberDisplayPipe } from './features-validity-partNumber-Display.pipe';

describe('FeaturesValidityPartNumberDisplayPipe', () => {
    let pipe: FeaturesValidityPartNumberDisplayPipe;

    beforeEach(() => {
        pipe = new FeaturesValidityPartNumberDisplayPipe();
    });

    it('should create an instance of the pipe', () => {
        expect(pipe).toBeTruthy();
    });

});
