import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { <PERSON><PERSON>rray, FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { DateTimeDisplayFormat, DEVICE_CONNECTION_STATE, DEVICE_COUNTRY, DEVICE_CUSTOMER_NAME, DEVICE_EDITABLE, DEVICE_HW_ID, DEVICE_LOCKED, DEVICE_SALES_ORDER_NUMBER, DEVICE_SERIAL_NO, DEVICE_STATUS, DEVICE_SYSTEM_SW_VERSION, DEVICE_TYPE, DeviceListResource, ITEMS_PER_PAGE, LAST_CHECK_IN_DATE_AND_TIME, SHOW_ENTRY } from '../../../app.constants';
import { ListingPageReloadSubjectParameter } from '../../../model/common/listingPageReloadSubjectParameter.model';
import { IDevice } from '../../../model/device.model';
import { DeviceFilterAction } from '../../../model/device/DeviceFilterAction.model';
import { DeviceSearchRequest } from '../../../model/device/deviceSearchRequest.model';
import { AuthJwtService } from '../../../shared/auth-jwt.service';
import { ProductStatusEnum } from '../../../shared/enum/Common/ProductStatus.enum';
import { PermissionAction } from '../../../shared/enum/Permission/permissionAction.enum';
import { collapseFilterTextEnum } from '../../../shared/enum/collapseFilterButtonText.enum';
import { PermissionService } from '../../../shared/permission.service';
import { CommonOperationsService } from '../../../shared/util/common-operations.service';
import { CommonsService } from '../../../shared/util/commons.service';
import { KeyValueMappingServiceService } from '../../../shared/util/key-value-mapping-service.service';
import { DeviceOperationService } from '../DeviceService/Device-Operation/device-operation.service';

@Component({
  selector: 'app-device-list',
  templateUrl: './device-list.component.html',
  styleUrls: ['./device-list.component.css']
})
export class DeviceComponent implements OnInit {

  @Output('deviceId') deviceId = new EventEmitter<number>();
  @Input("isFilterComponentInitWithApicall") isFilterComponentInitWithApicall: boolean;
  public isFilterHidden: boolean;

  // Output events to communicate changes back to parent device module
  @Output('isFilterComponentInitWithApicallChange') isFilterComponentInitWithApicallChange = new EventEmitter<boolean>();
  @Output("listPageRefreshForbackToDetailPageFlag") listPageRefreshForbackToDetailPageFlag = new EventEmitter<boolean>();
  @Output('showDeviceDetail') showDeviceDetail = new EventEmitter();

  devices: IDevice[] = [];
  totalItems: any;
  itemsPerPage: any;
  page: number = 0;
  previousPage: any;

  loading = false;
  form: FormGroup;

  //Device operations
  deviceOperations: string[] = [];
  deviceListResource = DeviceListResource;

  deviceIdList: Array<number> = [];
  localDeviceList: IDevice[];
  selectedDeviceList: IDevice[];
  drpselectsize: number = ITEMS_PER_PAGE;

  // Product status list for template usage
  productStatusList: Array<any> = [];

  checkboxDisplayPermission: boolean = false;

  //Permission Bind
  deviceRederPermission: boolean = false;
  probeReaderPermission: boolean = false;
  jobReaderPermission: boolean = false;
  softwareBuildReaderPermission: boolean = false;
  logReaderPermission: boolean = false;
  userReaderPermission: boolean = false;
  videoReaderPermission: boolean = false;
  roleReaderPermission: boolean = false;
  salesOrderReaderPermission: boolean = false;
  kitManagementReaderPermission: boolean = false;
  countryReaderPermission: boolean = false;
  auditReaderPermission: boolean = false;
  probeConfigGroupPermission: boolean = false;

  userrow: FormArray;
  myForm: FormGroup;
  totalDeviceDisplay: number = 0;
  totalDevice: number = 0;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;

  // show entry selection
  dataSizes: string[] = [];

  //subscription
  subscriptionForLoading: Subscription;
  subscriptionForDeviceListFilterRequestParameter: Subscription;

  //Audit Module Hide
  isAuditModuleDisplay: boolean = true;

  dateFormat = DateTimeDisplayFormat;

  serialNo: string = DEVICE_SERIAL_NO;
  hwId: string = DEVICE_HW_ID;
  salesOrderNumber: string = DEVICE_SALES_ORDER_NUMBER;
  deviceType: string = DEVICE_TYPE;
  country: string = DEVICE_COUNTRY;
  systemSwVerstion: string = DEVICE_SYSTEM_SW_VERSION;
  connectionState: string = DEVICE_CONNECTION_STATE;
  costomerName: string = DEVICE_CUSTOMER_NAME;
  locked: string = DEVICE_LOCKED;
  editable: string = DEVICE_EDITABLE;
  status: string = DEVICE_STATUS;
  lastCheckinDataAndType: string = LAST_CHECK_IN_DATE_AND_TIME;
  showEntry: string = SHOW_ENTRY;

  constructor(
    protected router: Router,
    private fb: FormBuilder,
    private formBuilder: FormBuilder,
    private commonsService: CommonsService,
    private permissionService: PermissionService,
    private authservice: AuthJwtService,
    private commonOperationsService: CommonOperationsService,
    private deviceOperationService: DeviceOperationService,
    private keyValueMappingServiceService: KeyValueMappingServiceService,
  ) {
    this.itemsPerPage = ITEMS_PER_PAGE;
    this.form = this.formBuilder.group({});

    this.myForm = this.fb.group({
      userrow: this.fb.array([])
    });
  }

  ngOnInit() {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.isFilterHidden = this.deviceOperationService.getIsFilterHiddenForListing();

      this.deviceOperations = this.commonOperationsService.accessDeviceListOperations(true, false, this.deviceListResource);
      this.deviceRederPermission = this.permissionService.getDevicePermission(PermissionAction.GET_DEVICE_ACTION);
      let updateDeviceTypePermission = this.permissionService.getDevicePermission(PermissionAction.UPDATE_DEVICE_TYPE_ACTION);
      let lockDevicePermission = this.permissionService.getDevicePermission(PermissionAction.LOCK_DEVICE_ACTION);
      let associateCustomerToDevicePermission = this.permissionService.getDevicePermission(PermissionAction.ASSOCIATE_CUSTOMER_TO_DEVICE_ACTION);
      let disabledPermission = this.permissionService.getDevicePermission(PermissionAction.DISABLE_DEVICE_ACTION);
      let rmaPermission = this.permissionService.getDevicePermission(PermissionAction.RMA_DEVICE_ACTION);
      let editEnableDisablePermission = this.permissionService.getDevicePermission(PermissionAction.EDITABLE_DEVICE_ACTION);

      this.checkboxDisplayPermission = (this.deviceRederPermission || updateDeviceTypePermission || lockDevicePermission || associateCustomerToDevicePermission || disabledPermission || rmaPermission || editEnableDisablePermission);

      // Initialize product status list for template usage
      this.productStatusList = this.keyValueMappingServiceService.enumOptionToList(ProductStatusEnum);

      // Initialize subscription to filter service
      this.subjectInit();

      if (this.deviceRederPermission) {
        this.getDeviceData();
      }
      //If Filter hide then page refresh with filter
      if (this.isFilterHidden) {
        this.deviceOperationService.setListPageRefreshForbackToOtherPage(false);
        this.filterPageSubjectCallForReloadPage(true, false);
      }
    }
  }

  /**
  * Initialize subject subscriptions
  * <AUTHOR>
  */
  private subjectInit(): void {
    // Loading subscription
    this.subscriptionForLoading = this.deviceOperationService
      .getDeviceListLoadingSubject()
      ?.subscribe((status: boolean) => {
        this.setLoadingStatus(status);
      });



    // Filter subscription
    this.subscriptionForDeviceListFilterRequestParameter =
      this.deviceOperationService
        .getDeviceListFilterRequestParameterSubject()
        ?.subscribe(
          (deviceFilterAction: DeviceFilterAction) => {
            if (deviceFilterAction.listingPageReloadSubjectParameter.isReloadData) {
              if (deviceFilterAction.listingPageReloadSubjectParameter.isDefaultPageNumber) {
                this.deviceIdList = [];
                this.selectedDeviceList = [];
                this.resetPage();
              }
              this.loadAll(deviceFilterAction.deviceSearchRequest);
            }
          }
        );
  }

  /**
  * Loading Status 
  * <AUTHOR>
  */
  private setLoadingStatus(status: boolean): void {
    this.loading = status;
  }

  /**
  * Reset Page
  * <AUTHOR>
  */
  private resetPage(): void {
    this.page = 0;
    this.previousPage = 1;
  }

  /**
  * Refresh Filter
  * <AUTHOR>
  */
  public refreshFilter(): void {
    this.resetPage();
    this.filterPageSubjectCallForReloadPage(true, true);
  }

  /**
  * Device page initialization
  * <AUTHOR>
  */
  public getDeviceData(): void {
    this.page = 0;
    this.dataSizes = this.commonsService.accessDataSizes();
    this.drpselectsize = ITEMS_PER_PAGE;
    this.itemsPerPage = ITEMS_PER_PAGE;
    this.updateIsFilterHidden(this.isFilterHidden);
    this.updateIsFilterComponentInitWithApicall(true);
    this.clearDeviceIdCheckBox();
  }

  /**
  * Refresh Button Click
  *
  * <AUTHOR>
  */
  public clickOnRefreshButton(): void {
    this.deviceOperationService.updateCacheInBackground();
    this.refreshFilter();
  }

  /**
  * Get Device List
  *
  * <AUTHOR>
  *
  * @param deviceSearchRequest
  */
  public async loadAll(deviceSearchRequest: DeviceSearchRequest): Promise<void> {
    this.setLoadingStatus(true);
    this.deviceOperationService.setDeviceSearchRequestBodyForListingApi(deviceSearchRequest);
    const pageObj = {
      page: this.page - 1,
      size: this.itemsPerPage,
    };
    const result = await this.deviceOperationService.loadDeviceList(deviceSearchRequest, pageObj);

    if (result.success) {
      this.devices = result.devices;
      this.totalDeviceDisplay = result.totalDeviceDisplay;
      this.totalDevice = result.totalDevice;
      this.localDeviceList = result.localDeviceList;
      this.totalItems = result.totalItems;
    } else {
      this.devices = [];
      this.totalDeviceDisplay = 0;
      this.totalDevice = 0;
      this.localDeviceList = [];
      this.totalItems = 0;
    }
    this.setLoadingStatus(false);
  }

  /**
  * Change The Page
  * callDeviceListRefreshSubject ->Call the filter component
  * filter not clear and send with filter request and load data
  * <AUTHOR>
  * @param page
  */
  public loadPage(page: any): void {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.clearDeviceIdCheckBox();
      this.filterPageSubjectCallForReloadPage(false, false);
    }
  }

  /**
  * Item par page Value Changes like (10,50,100) 
  * <AUTHOR>
  * @param datasize
  */
  public changeDataSize(datasize: any): void {
    this.setLoadingStatus(true);
    this.deviceIdList = [];
    this.selectedDeviceList = [];
    this.itemsPerPage = datasize.target.value;
    this.filterPageSubjectCallForReloadPage(true, false);
  }

  /**
   * Device Update and Other Operations
   * <AUTHOR>
   * @param operationName
   */
  public changeDeviceOperation(event: any): void {
    this.deviceOperationService.changeOperationForDevice(event.target.value, DeviceListResource, this.deviceIdList, this.selectedDeviceList);
  }

  public onChangeDevice(deviceObj: IDevice, event: any): void {
    if (event.target.checked) {
      this.deviceIdList.push(deviceObj.id);
      this.selectedDeviceList.push(deviceObj);
    }
    else {
      let index = this.deviceIdList.findIndex(obj => obj == deviceObj.id);
      this.deviceIdList.splice(index, 1);
      let deviceObjIndex = this.selectedDeviceList.findIndex(obj => obj.id == deviceObj.id);
      this.selectedDeviceList.splice(deviceObjIndex, 1);
    }
    this.defaultSelectAll()
  }

  public clearDeviceIdCheckBox(): void {
    this.deviceIdList = [];
    this.selectedDeviceList = [];
    let deviceCheckox = (<HTMLInputElement[]><any>document.getElementsByName("device[]"));
    let deviceLength = deviceCheckox.length;
    for (let index = 0; index < deviceLength; index++) {
      deviceCheckox[index].checked = false;
    }
    let selectDeviceElementId = <HTMLInputElement>document.getElementById("selectDevice");
    if (selectDeviceElementId != null) {
      selectDeviceElementId.checked = false;
    }
  }

  ngOnDestroy() {
    if (this.subscriptionForLoading != undefined) {
      this.subscriptionForLoading.unsubscribe();
    }
    if (this.subscriptionForDeviceListFilterRequestParameter != undefined) {
      this.subscriptionForDeviceListFilterRequestParameter.unsubscribe();
    }
  }

  public deviceDetailModel(deviceId: number): void {
    this.deviceId.emit(deviceId);
    this.showDeviceDetail.emit();
  }

  defaultSelectDevice(deviceId: number): boolean {
    let index = this.deviceIdList.findIndex(id => id == deviceId);
    if (index >= 0) {
      return true;
    }
    else {
      return false;
    }
  }

  defaultSelectAll(): void {
    let res: boolean = false;
    for (let device of this.localDeviceList) {
      let deviceIndex = this.deviceIdList.findIndex(id => id == device.id);
      if (deviceIndex < 0) {
        res = false;
        break;
      }
      else {
        res = true;
      }
    }
    let selectDevice = <HTMLInputElement>document.getElementById("selectDevice");
    if (selectDevice != null) {
      selectDevice.checked = res;
    }
  }

  selectAllDevice(event: any): void {
    let deviceDataCheckox = (<HTMLInputElement[]><any>document.getElementsByName("device[]"));
    let l = deviceDataCheckox.length;
    if (event.target.checked) {
      for (let i = 0; i < l; i++) {
        deviceDataCheckox[i].checked = true;
      }
      for (let device of this.localDeviceList) {
        let deviceIndex = this.deviceIdList.findIndex(id => id == device.id);
        if (deviceIndex < 0) {
          this.deviceIdList.push(device.id);
          this.selectedDeviceList.push(device);
        }
      }
    }
    else {
      for (let i = 0; i < l; i++) {
        deviceDataCheckox[i].checked = false;
      }
      for (let removeDevice of this.localDeviceList) {
        let deviceIndexRemove = this.deviceIdList.findIndex(id => id == removeDevice.id);
        this.deviceIdList.splice(deviceIndexRemove, 1);
        let deviceObjIndexRemove = this.selectedDeviceList.findIndex(obj => obj.id == removeDevice.id);
        this.selectedDeviceList.splice(deviceObjIndexRemove, 1);
      }
    }
  }

  public async exportCSV(): Promise<void> {
    this.setLoadingStatus(true);
    try {
      await this.deviceOperationService.exportDeviceCSV(this.deviceIdList);
    } finally {
      this.setLoadingStatus(false);
      this.clearDeviceIdCheckBox();
    }
  }

  /**
  * Toggle Filter
  *
  */
  public toggleFilter(): void {
    this.updateIsFilterComponentInitWithApicall(false);
    this.updateIsFilterHidden(!this.isFilterHidden);
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }


  /**
  * Call Filter component subject and reload page
  * <AUTHOR>
  * @param isDefaultPageNumber
  * @param isClearFilter
  */
  public filterPageSubjectCallForReloadPage(isDefaultPageNumber: boolean, isClearFilter: boolean): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false);
    let deviceSearchRequestBody = this.deviceOperationService.getDeviceSearchRequestBodyForListingApi();
    this.deviceOperationService.callRefreshPageSubject(listingPageReloadSubjectParameter, DeviceListResource, this.isFilterHidden, deviceSearchRequestBody);
  }

  /**
  * Update isFilterComponentInitWithApicall and emit change
  * <AUTHOR>
  * @param value
  */
  public updateIsFilterComponentInitWithApicall(value: boolean): void {
    this.isFilterComponentInitWithApicall = value;
    this.isFilterComponentInitWithApicallChange.emit(value);
  }



  /**
  * Update isFilterHidden and emit change
  * <AUTHOR>
  * @param value
  */
  public updateIsFilterHidden(value: boolean): void {
    this.isFilterHidden = value;
    this.deviceOperationService.setIsFilterHiddenForListing(value);
  }

  public setListToModuleConnect(flag: boolean) {
    this.listPageRefreshForbackToDetailPageFlag.emit(flag);
  }
}
