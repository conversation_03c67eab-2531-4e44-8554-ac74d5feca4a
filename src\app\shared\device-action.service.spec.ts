import { TestBed } from '@angular/core/testing';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from '../FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { commonsProviders } from '../Tesing-Helper/test-utils';
import { DeviceActionService } from './device-action.service';
import { DeviceService } from './device.service';
import { ExceptionHandlingService } from './ExceptionHandling.service';
import { CommonsService } from './util/commons.service';
import { DownloadService } from './util/download.service';

describe('DeviceActionService', () => {
  let service: DeviceActionService;

  beforeEach(() => {
    const toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);

    TestBed.configureTestingModule({
      imports: [],
      providers: [
        DeviceActionService,
        DeviceService, // Make sure to provide DeviceService
        DownloadService,
        ConfirmDialogService,
        CommonsService,
        LocalStorageService,
        ExceptionHandlingService,
        SessionStorageService,
        commonsProviders(toastrServiceMock)
      ]
    });
    service = TestBed.inject(DeviceActionService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
