import { ValidityEnum } from "src/app/shared/enum/ValidityEnum.enum";
import { PartNumberBaseResponse } from "./PartNumberBaseResponse.model";

export class FeaturePartNumberResponse extends PartNumberBaseResponse {
    probeTypeMasterFeatureMasterMappingId: number;

    constructor(
        partNumber: string,
        validity: ValidityEnum,
        isDefault: boolean,
        allowedToEdit: boolean,
        probeTypeMasterFeatureMasterMappingId: number
    ) {
        super(partNumber, validity, isDefault, allowedToEdit);
        this.probeTypeMasterFeatureMasterMappingId = probeTypeMasterFeatureMasterMappingId;
    }
}