import { SalesOrderTransferProductDetailValidateResponse } from "./SalesOrderTransferProductDetailValidateResponse.model";

export class TransferredSalesOrder {
    id: number;
    salesOrderNumber: string;
    countryDisplayName: string
    product: SalesOrderTransferProductDetailValidateResponse;

    constructor(id: number, salesOrderNumber: string, product: SalesOrderTransferProductDetailValidateResponse, countryDisplayName: string) {
        this.id = id;
        this.salesOrderNumber = salesOrderNumber;
        this.product = product;
        this.countryDisplayName = countryDisplayName;
    }
}