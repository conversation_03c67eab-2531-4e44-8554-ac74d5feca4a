import { Injectable } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { lastValueFrom } from 'rxjs';
import { Find_Letter_Pattern } from 'src/app/app.constants';
import { BaseResponse } from 'src/app/model/common/BaseResponse.model';
import { PresetDetailBaseResponse } from 'src/app/model/Presets/PresetDetailBaseResponse.model';
import { ConfigBaseMappingRequest } from 'src/app/model/probe/ConfigBaseMappingRequest.model';
import { ConfigMappingRequest } from 'src/app/model/probe/ConfigMappingRequest.model';
import { FeaturesFilter } from 'src/app/model/probe/FeaturesFilter.model';
import { LicensesRequest } from 'src/app/model/probe/multiProbe/LicensesRequest.model';
import { ProbeFeatureResponse } from 'src/app/model/probe/ProbeFeatureResponse.model';
import { ProbePresetResponse } from 'src/app/model/probe/ProbePresetResponse.model';
import { ProbeTypeResponse } from 'src/app/model/probe/probeTypeResponse.model';
import { EndDateOptions } from '../../enum/endDateOptions.enum';
import { ProbeConfigType } from '../../enum/ProbeConfigType.enum';
import { ValidityEnum } from '../../enum/ValidityEnum.enum';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { CommonsService } from '../../util/commons.service';
import { ProbeApiService } from './probe-api.service';

@Injectable({
  providedIn: 'root'
})
export class ProbeService {

  constructor(private commonsService: CommonsService, private probeApiService: ProbeApiService, private exceptionService: ExceptionHandlingService, private toste: ToastrService,) { }

  /**
  * Prepare Probe Type With Prefix Map
  * 
  * <AUTHOR>
  * 
  * @param probeTypeResponse 
  */
  public prepareProbeTypeWithPrefixMap(probeTypeResponse: Array<ProbeTypeResponse>): Map<string, string> {
    let probeTypeWithPrefixMap = new Map<string, string>();
    for (let probe of probeTypeResponse) {
      if (!isNullOrUndefined(probe.prefix)) {
        probeTypeWithPrefixMap.set(probe.displayName, probe.prefix);
      }
    }
    return probeTypeWithPrefixMap;
  }

  public getFeaturesListForCreate(featuresList: Array<ProbeFeatureResponse> | Array<ProbePresetResponse>): Array<ConfigBaseMappingRequest> {
    let featuresRequestList: Array<ConfigBaseMappingRequest> = [];
    for (let defaultFeaturesObj of featuresList) {
      let featuresRequest: ConfigBaseMappingRequest = new ConfigBaseMappingRequest(
        defaultFeaturesObj.id,
        null,
        null,
        false,
        false,
      );
      featuresRequest.name = defaultFeaturesObj.displayName;
      featuresRequestList.push(featuresRequest)
    }
    return featuresRequestList;
  }

  public getConfigListForAssign(presetOrfeatureList: Array<ProbeFeatureResponse> | Array<ProbePresetResponse>, currentPresetOrFeatureList: Array<ProbeFeatureResponse> | Array<ProbePresetResponse>, probeConfigType: ProbeConfigType): Array<ConfigBaseMappingRequest> {
    let featuresRequestList: Array<ConfigBaseMappingRequest> = [];
    for (let defaultFeaturesObj of presetOrfeatureList) {
      let featuresRequest: ConfigBaseMappingRequest;
      let endDateOption = this.getDefaultEndDateOption(defaultFeaturesObj.id, currentPresetOrFeatureList, probeConfigType);
      if (endDateOption != null) {
        featuresRequest = new ConfigBaseMappingRequest(
          defaultFeaturesObj.id,
          new Date().getTime(),
          this.commonsService.getEndDateConvertion(endDateOption, null),
          false,
          true,
        );
        featuresRequest.name = defaultFeaturesObj.displayName;
        featuresRequest.endDateUi = endDateOption;
      } else {
        featuresRequest = new ConfigBaseMappingRequest(
          defaultFeaturesObj.id,
          null,
          null,
          false,
          false,
        );
      }
      featuresRequest.name = defaultFeaturesObj.displayName;
      featuresRequestList.push(featuresRequest);
    }
    return featuresRequestList;
  }


  /**
   * Get Default EndDateOption
   * <AUTHOR>
   * 
   * @param featureOrPresetId
   * @param currentPresetOrFeatureList 
   * @param probeTypeConfig  
   * @returns 
   */
  public getDefaultEndDateOption(featureOrPresetId: number, currentPresetOrFeatureList: Array<any>, probeTypeConfig: ProbeConfigType): EndDateOptions {
    let currentConfigList = [];
    if (ProbeConfigType.FEATURE == probeTypeConfig) {
      currentConfigList = this.getCurrentFeatureForDefault(currentPresetOrFeatureList, featureOrPresetId);
    } else if (ProbeConfigType.PRESET == probeTypeConfig) {
      currentConfigList = this.getCurrentPresetForDefault(currentPresetOrFeatureList, featureOrPresetId);
    }
    if (currentConfigList.length == 1) {
      let filterPartNumber = currentConfigList[0].partNumbers.filter(obj => obj.default);
      return filterPartNumber.length == 1 ? this.getEndDateOptionForDefault(filterPartNumber[0].validity) : null;
    }
    return null;

  }

  /**
   * Get Current Feature for default
   * <AUTHOR>
   * 
   * @param currentFeatureList
   * @param featureId 
   * @returns 
   */
  public getCurrentFeatureForDefault(currentFeatureList: Array<ProbeFeatureResponse>, featureId: number) {
    return currentFeatureList.filter(obj => obj?.featureId == featureId);
  }

  /**
   * Get Current Preset for Default
   * <AUTHOR>
   * 
   * @param currentPresetList
   * @param presetId 
   * @returns 
   */
  public getCurrentPresetForDefault(currentPresetList: Array<ProbePresetResponse>, presetId: number) {
    return currentPresetList.filter(obj => obj?.presetId == presetId);
  }

  /**
   * Get EndDateOption for Default
   * <AUTHOR>
   * 
   * @param validity
   * @returns 
   */
  public getEndDateOptionForDefault(validity: ValidityEnum): EndDateOptions {
    if (ValidityEnum[validity] == ValidityEnum.ONE_YEAR) {
      return EndDateOptions.ONE_YEAR;
    }
    else if (ValidityEnum[validity] == ValidityEnum.PERPETUAL) {
      return EndDateOptions.UNLIMITED;
    }
    return null;
  }

  /**
  * Get Feature Object with Feature detail
  * <AUTHOR>
  * 
  * @param configList 
  * @param probeDetailWithFeature 
  * @param currentFeatures 
  * @returns 
  */
  public getFeaturesListForUpdate(featuresList: Array<ProbeFeatureResponse>, probeDetailWithFeature: Array<LicensesRequest>, currentFeatures: Array<ProbeFeatureResponse>): Array<ConfigBaseMappingRequest> {
    let featuresRequestList: Array<ConfigBaseMappingRequest> = [];
    for (let defaultFeaturesObj of featuresList) {
      let featuresFilter = currentFeatures.filter(obj => obj.displayName == defaultFeaturesObj.displayName);
      let features: Array<LicensesRequest> = (featuresFilter.length > 0) ? probeDetailWithFeature.filter(obj => obj.displayName == defaultFeaturesObj.displayName) : [];
      let featuresRequest: ConfigBaseMappingRequest = this.getFeaturesRequestObjectForAssign(defaultFeaturesObj, features);
      featuresRequestList.push(featuresRequest)
    }
    return featuresRequestList;
  }


  /**
 * Get Preset Object with Preset detail
 * <AUTHOR>
 * 
 * @param presetsList 
 * @param probeDetailWithPreset 
 * @param currentPresets 
 * @returns 
 */
  public getPresetsListForUpdate(presetsList: Array<ProbePresetResponse>, probeDetailWithPreset: Array<LicensesRequest>, currentPresets: Array<ProbePresetResponse>): Array<ConfigBaseMappingRequest> {
    let presetsRequestList: Array<ConfigBaseMappingRequest> = [];
    for (let defaultPresetsObj of presetsList) {
      let presetsFilter = currentPresets.filter(obj => obj.displayName == defaultPresetsObj.displayName);
      let presets: Array<LicensesRequest> = (presetsFilter.length > 0) ? probeDetailWithPreset.filter(obj => obj.displayName == defaultPresetsObj.displayName) : [];
      let presetsRequest: ConfigBaseMappingRequest = this.getPresetsRequestObjectForAssign(defaultPresetsObj, presets);
      presetsRequestList.push(presetsRequest)
    }
    return presetsRequestList;
  }

  /**
   * Get Feature Config Base Mapping Request
   * 
   * <AUTHOR>
   * @param defaultFeaturesObj 
   * @param features 
   * @returns 
   */
  private getFeaturesRequestObjectForAssign(defaultFeaturesObj: ProbeFeatureResponse, features: Array<LicensesRequest>): ConfigBaseMappingRequest {
    return this.prepareConfigBaseMappingRequest(defaultFeaturesObj.id, features);
  }


  /**
   * Get Preset Config Base Mapping Request
   * 
   * <AUTHOR>
   * @param defaultPresetsObj 
   * @param presets 
   * @returns 
   */
  private getPresetsRequestObjectForAssign(defaultPresetsObj: ProbePresetResponse, presets: Array<LicensesRequest>): ConfigBaseMappingRequest {
    return this.prepareConfigBaseMappingRequest(defaultPresetsObj.id, presets);
  }

  /**
   * Prepare Config Base Mapping Request
   * 
   * <AUTHOR>
   * @param presetOrFeatureId 
   * @param presetOrFeatureRequest 
   * @returns 
   */
  private prepareConfigBaseMappingRequest(presetOrFeatureId: number, presetOrFeatureRequest: Array<LicensesRequest>): ConfigBaseMappingRequest {
    if (presetOrFeatureRequest.length == 1 && this.FeatureEndDateIsvalid(presetOrFeatureRequest)) {
      let presetOrFeatureObj = presetOrFeatureRequest[0];
      let isTrial = isNullOrUndefined(presetOrFeatureObj.trial) ? false : presetOrFeatureObj.trial;
      let endDateUi = isNullOrUndefined(presetOrFeatureObj.endDate) ? null : this.getEndDateOptions(presetOrFeatureObj);
      let configBaseMappingRequest: ConfigBaseMappingRequest = new ConfigBaseMappingRequest(presetOrFeatureId,
        presetOrFeatureObj.startDate,
        presetOrFeatureObj.endDate,
        isTrial,
        presetOrFeatureObj.enable);
      configBaseMappingRequest.endDateUi = endDateUi;
      return configBaseMappingRequest;
    } else {
      return new ConfigBaseMappingRequest(
        presetOrFeatureId,
        null,
        null,
        false,
        false,
      )
    }
  }

  /**
     * Get EndDate from Object
     * 
     * <AUTHOR>
     * @param featureObj 
     * @returns 
     */
  public getEndDateOptions(featureObj: LicensesRequest | ConfigBaseMappingRequest) {
    return featureObj.endDate == -1 ? EndDateOptions.UNLIMITED : EndDateOptions.CUSTOMDATE;
  }

  /**
   * Feature -> feature end date different then set null object 
   * @param features 
   * @returns 
   */
  public FeatureEndDateIsvalid(features: Array<LicensesRequest>): boolean {
    return features.every(element => {
      return (element.endDate === features[0].endDate);
    });
  }

  /**
   * getlist of ids from probeSuccessObjects 
   * @param probeSuccessObjects 
   * @returns 
   */
  public getProbeSuccessObjectToProbeIdList(probeSuccessObjects: Array<BaseResponse>): Array<number> {
    let probeIdList: Array<number> = [];
    probeSuccessObjects.forEach(probe => probeIdList.push(probe.id));
    return probeIdList;
  }

  /**
   * get Feature List For Filter 
   * 
   * <AUTHOR>
   * 
   * @param featuresList 
   * @returns 
   */
  public getFeaturesListForFilter(featuresList: Array<ProbeFeatureResponse>, notAssociatedOptionAdd: boolean): Array<FeaturesFilter> {
    let featuresFilterList: Array<FeaturesFilter> = [];
    for (let features of featuresList) {
      featuresFilterList.push(new FeaturesFilter(features.id, features.displayName, features.displayName, false));
    }
    if (notAssociatedOptionAdd) {
      featuresFilterList.push(this.commonsService.notAssociatedObject());
    }
    return featuresFilterList
  }

  /**
 * get Preset List For Filter 
 * 
 * <AUTHOR>
 * 
 * @param presetsList 
 * @returns 
 */
  public getPresetsListForFilter(presetsList: Array<PresetDetailBaseResponse>, notAssociatedOptionAdd: boolean): Array<PresetDetailBaseResponse> {
    let presetsFilterList: Array<PresetDetailBaseResponse> = [];
    for (let preset of presetsList) {
      presetsFilterList.push(new FeaturesFilter(preset.id, preset.displayName, preset.displayName, false));
    }
    if (notAssociatedOptionAdd) {
      presetsFilterList.push(this.commonsService.notAssociatedObject());
    }
    return presetsFilterList
  }


  /**
   * Auto Selection DropDown
   * 
   * set default value of probe type based on serial Number (QR code);
   */
  public getProbeTypeDropdownValue(serialNumber: string, probeTypeResponse: Array<ProbeTypeResponse>): ProbeTypeResponse {
    if (!isNullOrUndefined(serialNumber)) {
      let probeTypeFilter = probeTypeResponse.filter(probeTypeObj => probeTypeObj.prefix != null && serialNumber.toUpperCase().includes(probeTypeObj.prefix.toUpperCase()));
      if (probeTypeFilter.length == 1) {
        return probeTypeFilter[0];
      } else if (probeTypeFilter.length > 1) {
        return probeTypeFilter[probeTypeFilter.length - 1];
      }
    }
    return (!isNullOrUndefined(probeTypeResponse) && probeTypeResponse.length > 0) ? probeTypeResponse[0] : null;
  }

  /**
   * set Reminder options select based on user Actions
   * 
   * <AUTHOR>
   * @param features 
   * @returns 
   */
  public setReminderForUserAction(features: Array<ConfigBaseMappingRequest>): boolean {
    let filter = features.filter(obj => obj.enable && obj.endDateUi != EndDateOptions.UNLIMITED);
    return filter.length > 0;
  }

  /**
     * set Reminder options select based on user Actions
     * 
     * <AUTHOR>
     * @param config 
     * @returns 
     */
  public setReminderForUserActionForAssignConfig(config: ConfigMappingRequest): boolean {
    let featureStatus = config.features.filter(obj => obj.enable && obj.endDateUi != EndDateOptions.UNLIMITED);
    let presetStatus = config.presets.filter(obj => obj.enable && obj.endDateUi != EndDateOptions.UNLIMITED);
    return featureStatus.length > 0 || presetStatus.length > 0;
  }

  /**
   * Truncate Serial Number
   * 
   * <AUTHOR>
   * 
   * @param serialNumber 
   * @returns 
   */
  public truncateSerialNumber(serialNumber: any): string {
    if (!isNullOrUndefined(serialNumber)) {
      let indexForSerialNumber = serialNumber.indexOf(serialNumber.match(Find_Letter_Pattern));
      return serialNumber.substr(indexForSerialNumber);
    }
    return serialNumber;
  }

  /**
   * Probe Edit Enable/Disable Api Call
   * 
   * <AUTHOR>
   * 
   * @param probeIdList
   * @param enableState  
   * @returns 
   */
  public async probeEditAction(probeIdList: Array<number>, enableState: boolean): Promise<void> {
    try {
      let res = await lastValueFrom(this.probeApiService.editEnableDisableProbe(probeIdList, enableState));
      this.toste.success(res.body.message);
    } catch (error) {
      this.exceptionService.customErrorMessage(error);
      throw error;  // Rethrow the error to be handled by the caller
    }
  }

}
