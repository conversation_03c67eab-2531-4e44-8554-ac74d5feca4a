import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ComponentFixture, fakeAsync, TestBed, waitForAsync } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { getMockCountryList } from '../../Tesing-Helper/TestCountryInfo';
import { getMockUserDetail, getMockUserList } from '../../Tesing-Helper/TestUserInfo';
import { commonsProviders, selectAllTestCase, selectOneFromListTestCase, testAuthentication, testDropdownInteraction, testToggleFilter } from '../../Tesing-Helper/test-utils';
import { AddUserComponent } from '../add-user/add-user.component';
import { ITEMS_PER_PAGE } from '../../app.constants';
import { ConfirmDialogService } from '../../FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { UserListPageResponse } from '../../model/User/UserListPageResponse.model';
import { UserResponse } from '../../model/User/UserResponse.model';
import { SuccessMessageResponse } from '../../model/common/SuccessMessageResponse.model';
import { ExceptionHandlingService } from '../../shared/ExceptionHandling.service';
import { MessageService } from '../../shared/Message.service';
import { CountryCacheService } from '../../shared/Service/CacheService/countrycache.service';
import { RoleApiCallService } from '../../shared/Service/RoleService/role-api-call.service';
import { AuthJwtService } from '../../shared/auth-jwt.service';
import { common_error_empty_end_date, common_error_empty_filter, common_error_empty_start_date, common_error_invalid_date, common_error_other } from '../../shared/config';
import { PermissionService } from '../../shared/permission.service';
import { HidePermissionNamePipe } from '../../shared/pipes/Role/hidePermissionName.pipe';
import { PrintListPipe } from '../../shared/pipes/printList.pipe';
import { UserApiCallService } from '../../shared/Service/user/user-api-call.service';
import { CommonsService } from '../../shared/util/commons.service';
import { UserDetailComponent } from '../user-detail/user-detail.component';
import { UserListingComponent } from './user-listing.component';

describe('UserListingComponent', () => {
  let component: UserListingComponent;
  let fixture: ComponentFixture<UserListingComponent>;
  let mockToastrService: jasmine.SpyObj<ToastrService>;
  let mockLocalStorageService: jasmine.SpyObj<LocalStorageService>;
  let authServiceMock: any;
  let dialogservice: ConfirmDialogService;
  let exceptionService: ExceptionHandlingService;

  let roleApiCallServiceMock: RoleApiCallService;
  let countryCacheServiceMock: CountryCacheService;
  let searchMemberServiceMock: UserApiCallService;

  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;

  const roleFilter = ["Cloud Admin", "Device / SV Team"];

  const countryFilter: any = getMockCountryList();

  const userListResponse: UserListPageResponse = getMockUserList(0);

  const userListResponse1 = getMockUserList(1);

  const userDetailResponse: UserResponse = getMockUserDetail();


  beforeEach(waitForAsync(() => {
    mockToastrService = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    mockLocalStorageService = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Mock the return value of retrieve method in LocalStorageService
    mockLocalStorageService.retrieve.and.returnValue('mock_token');

    authServiceMock = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);

    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getUserPermission']);

    TestBed.configureTestingModule({
      declarations: [UserListingComponent, PrintListPipe, UserDetailComponent, AddUserComponent],
      imports: [NgMultiSelectDropDownModule.forRoot(),
        NgbPaginationModule,
        FormsModule,
        ReactiveFormsModule],
      providers: [MessageService,
        ConfirmDialogService,
        UserApiCallService,
        CommonsService,
        { provide: LocalStorageService, useValue: mockLocalStorageService },
        SessionStorageService,
        AuthJwtService,
        RoleApiCallService,
        HidePermissionNamePipe,
        PrintListPipe,
        ExceptionHandlingService,
        { provide: common_error_empty_filter, useValue: 'Some default value' },
        { provide: common_error_empty_start_date, useValue: 'Start date error' },
        { provide: common_error_empty_end_date, useValue: 'End date cannot be empty' },
        { provide: common_error_invalid_date, useValue: 'Invalid date error' },
        { provide: common_error_other, useValue: 'Mock error message' },
        { provide: AuthJwtService, useValue: authServiceMock },
        { provide: PermissionService, useValue: permissionServiceSpy },
        commonsProviders(mockToastrService)
      ]
    })
      .compileComponents();
  }));

  beforeEach(() => {

    fixture = TestBed.createComponent(UserListingComponent);
    component = fixture.componentInstance;
    authServiceMock.isAuthenticate.and.returnValue(true);
    permissionServiceSpy.getUserPermission?.and.returnValue(true);
    exceptionService = TestBed.inject(ExceptionHandlingService);
    roleApiCallServiceMock = TestBed.inject(RoleApiCallService);
    countryCacheServiceMock = TestBed.inject(CountryCacheService);
    searchMemberServiceMock = TestBed.inject(UserApiCallService);
    dialogservice = TestBed.inject(ConfirmDialogService);
    spyOn(dialogservice, 'confirm').and.returnValue(Promise.resolve(true));
    fixture.detectChanges();
  });


  it('should create UserDetailComponent', () => {
    expect(component).toBeTruthy();
  });

  it('should load User on init when authenticated', async () => {
    //MOCK API
    spyOn(roleApiCallServiceMock, 'getRoleNameList')?.and.returnValue(Promise.resolve(roleFilter));
    spyOn(countryCacheServiceMock, 'getCountryListFromCache')?.and.returnValue(Promise.resolve(countryFilter));
    spyOn(searchMemberServiceMock, 'getMemberList')?.and.returnValue(of(new HttpResponse<UserListPageResponse>({
      body: userListResponse,
      status: 200,
    })));
    spyOn(searchMemberServiceMock, 'deleteMultipleMember')?.and.returnValue(of(new HttpResponse<SuccessMessageResponse>({
      body: { message: 'User deleted successfully.' },
      status: 200,
      statusText: 'OK',
    })));
    spyOn(searchMemberServiceMock, 'getSingleMember')?.and.returnValue(of(new HttpResponse<UserResponse>({
      body: userDetailResponse,
      status: 200,
    })));

    // Arrange: Set up mocks and dependencies
    testAuthentication(authServiceMock, component, fixture);
    authServiceMock.isAuthenticate.and.returnValue(true);

    // Act: Initialize the component
    component.ngOnInit();
    await fixture.whenStable();
    fixture.detectChanges();

    // Assert: Verify the component state and method calls
    expect(component.dataSizes).toEqual(['10', '25', '50', '100']);
    expect(component.itemsPerPage).toBe(ITEMS_PER_PAGE);
    expect(component.drpselectsize).toBe(ITEMS_PER_PAGE);
    expect(component.page).toBe(1);
    expect(component.allUserShow).toBe(true);
    expect(component.addUserShow).toBe(false);
    expect(component.singleUserShow).toBe(false);

    //API Response
    expect(component.totalItems).toBe(20);
    expect(component.userDetail).not.toBeNull();
    expect(component.loading).toBeFalse();
    expect(permissionServiceSpy.getUserPermission).toHaveBeenCalled();

    // Update UI elements for checkbox interactions
    // Test: Select all checkboxes functionality
    selectAllTestCase(fixture, component, `#${component.selectAllCheckboxId}`);

    // Test: Select a single item from the list and verify selection state
    selectOneFromListTestCase(fixture, component, '#user1user');
    expect(component.userIdListcollect.length).toEqual(1); // Confirm one item is selected
    selectOneFromListTestCase(fixture, component, '#user1user');


    //Open user detail page
    const selectElementDetails = fixture.nativeElement.querySelector('#userRow1userRow');
    selectElementDetails?.click();
    expect(component.addUserShow).toBeFalsy();
    expect(component.allUserShow).toBeFalsy();
    expect(component.singleUserShow).toBeTruthy();
    fixture.detectChanges();
    await fixture.whenStable();
    const userDetailDebugElement = fixture.debugElement.query(By.css('app-user-detail'));
    const userDetailComponent = userDetailDebugElement.componentInstance;
    fixture.detectChanges();
    await fixture.whenStable();
    expect(userDetailComponent).toBeTruthy();

    //Refresh detail page
    spyOn(userDetailComponent, 'refreshUserDetailPage').and.callThrough();
    const userDetailDetailRefreshButton = fixture.nativeElement.querySelector('#userDetailPageRefreshBtn');
    userDetailDetailRefreshButton?.click();
    expect(userDetailComponent.refreshUserDetailPage).toHaveBeenCalled();

    //back button in user detail page
    spyOn(userDetailComponent, 'back').and.callThrough();
    spyOn(component, 'viewAllmember').and.callThrough();
    const userDetailBackBtn = fixture.nativeElement.querySelector('#userDetailPageBackBtn');
    userDetailBackBtn?.click();
    expect(userDetailComponent.back).toHaveBeenCalled();
    fixture.detectChanges();
    await fixture.whenStable();
    expect(component.viewAllmember).toHaveBeenCalled();
    expect(component.addUserShow).toBeFalsy();
    expect(component.singleUserShow).toBeFalsy();
    expect(component.allUserShow).toBeTruthy();

  });

  it('should Clear Filter', async () => {
    spyOn(searchMemberServiceMock, 'getMemberList')?.and.returnValue(of(new HttpResponse<UserListPageResponse>({
      body: userListResponse,
      status: 200,
    })));
    const clearButton = fixture.debugElement.query(By.css('#userListClear')).nativeElement as HTMLInputElement;
    clearButton.click();
    fixture.detectChanges();
    await fixture.whenStable();
    fixture.detectChanges();
    expect(component.totalItems).toBe(20);
    expect(component.userDetail).not.toBeNull();
    expect(component.loading).toBeFalse();
  });

  it('should search Filter with Null value', async () => {
    component.filterForm.get("login").setValue(null);
    component.filterForm.get("userRole").setValue(null);
    component.filterForm.get("country").setValue(null);
    const searchButton = fixture.debugElement.query(By.css('#userListSearch')).nativeElement as HTMLInputElement;
    searchButton.click();
    fixture.detectChanges();
    await fixture.whenStable();
    fixture.detectChanges();
    expect(mockToastrService.info).toHaveBeenCalled();
  });

  it('should search Filter with value', async () => {
    spyOn(searchMemberServiceMock, 'getMemberList')?.and.returnValue(of(new HttpResponse<UserListPageResponse>({
      body: userListResponse,
      status: 200,
    })));

    component.filterForm.get("login").setValue("a");
    component.filterForm.get("userRole").setValue(null);
    component.filterForm.get("country").setValue(null);
    const searchButton = fixture.debugElement.query(By.css('#userListSearch')).nativeElement as HTMLInputElement;
    searchButton.click();
    fixture.detectChanges();
    await fixture.whenStable();
    fixture.detectChanges();
    expect(component.page).toBe(1);
    expect(component.userIdListcollect).toHaveSize(0);
    expect(component.totalItems).toBe(20);
    expect(component.userDetail).not.toBeNull();
    expect(component.loading).toBeFalse();
  });


  it('should search Filter with value and throw Exception', async () => {
    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
    // Mock API 
    spyOn(searchMemberServiceMock, 'getMemberList')?.and.returnValue(throwError(() => mockError));
    component.filterForm.get("login").setValue("a'");
    component.filterForm.get("userRole").setValue(null);
    component.filterForm.get("country").setValue(null);
    const searchButton = fixture.debugElement.query(By.css('#userListSearch')).nativeElement as HTMLInputElement;
    searchButton.click();
    fixture.detectChanges();
    await fixture.whenStable();
    fixture.detectChanges();
    spyOn(exceptionService, 'customErrorMessage')?.and.callThrough();
  });

  it('should correctly update page number on page change', async () => {
    spyOn(searchMemberServiceMock, 'getMemberList')?.and.returnValue(of(new HttpResponse<UserListPageResponse>({
      body: userListResponse1,
      status: 200,
    })));
    // Spy on the 'loadPage' method to ensure it is called during the page change
    spyOn(component, 'loadPage').and.callThrough();
    // Arrange: Locate the pagination element in the template
    const paginationElement = fixture.debugElement.query(By.css('ngb-pagination'));
    // Act: Simulate a page change event to page 2
    paginationElement.triggerEventHandler('pageChange', 2);
    fixture.detectChanges();
    expect(component.page).toBe(2); // Page should be updated to 2
  });

  it('should correctly toggle the filter visibility', async () => {
    // Test setup: Use a helper function to validate the toggle functionality of the filter
    // 'testToggleFilter' ensures the filter visibility state is toggled and validated
    testToggleFilter(component);
  });


  it('should display the correct options in the dropdown change', fakeAsync(() => {
    //MOCK API
    spyOn(roleApiCallServiceMock, 'getRoleNameList')?.and.returnValue(Promise.resolve(roleFilter));
    spyOn(countryCacheServiceMock, 'getCountryListFromCache')?.and.returnValue(Promise.resolve(countryFilter));
    spyOn(searchMemberServiceMock, 'getMemberList')?.and.returnValue(of(new HttpResponse<UserListPageResponse>({
      body: userListResponse,
      status: 200,
    })));
    // Arrange: Set up mocks and dependencies
    testAuthentication(authServiceMock, component, fixture);
    authServiceMock.isAuthenticate.and.returnValue(true);
    component.ngOnInit();
    // Test dropdown interaction
    testDropdownInteraction(fixture, component, '#userListShowEntry');
  }));

  it('should create user', async () => {
    spyOn(roleApiCallServiceMock, 'getRoleNameList')?.and.returnValue(Promise.resolve(roleFilter));
    spyOn(countryCacheServiceMock, 'getCountryListFromCache')?.and.returnValue(Promise.resolve(countryFilter));
    const selectElement = fixture.nativeElement.querySelector('#addUserBtn');
    selectElement?.click();
    fixture.detectChanges();
    await fixture.whenStable();
    const addUserDebugElement = fixture.debugElement.query(By.css('app-add-user'));
    const addUserComponent = addUserDebugElement.componentInstance;
    fixture.detectChanges();
    await fixture.whenStable();
    expect(addUserComponent).toBeTruthy();
    expect(component.addUserShow).toBeTruthy();
    expect(component.singleUserShow).toBeFalsy();
    expect(component.allUserShow).toBeFalsy();

    //back button in user detail page
    spyOn(addUserComponent, 'back').and.callThrough();
    spyOn(component, 'viewAllmember').and.callThrough();
    const addUserBackBtn = fixture.nativeElement.querySelector('#addUserBackBtn');
    addUserBackBtn?.click();
    expect(addUserComponent.back).toHaveBeenCalled();
    fixture.detectChanges();
    await fixture.whenStable();
    expect(component.viewAllmember).toHaveBeenCalled();
    expect(component.addUserShow).toBeFalsy();
    expect(component.singleUserShow).toBeFalsy();
    expect(component.allUserShow).toBeTruthy();
  });

  it('should delete user', async () => {
    //MOCK API
    spyOn(roleApiCallServiceMock, 'getRoleNameList')?.and.returnValue(Promise.resolve(roleFilter));
    spyOn(countryCacheServiceMock, 'getCountryListFromCache')?.and.returnValue(Promise.resolve(countryFilter));
    spyOn(searchMemberServiceMock, 'getMemberList')?.and.returnValue(of(new HttpResponse<UserListPageResponse>({
      body: userListResponse,
      status: 200,
    })));
    spyOn(searchMemberServiceMock, 'deleteMultipleMember')?.and.returnValue(of(new HttpResponse<SuccessMessageResponse>({
      body: new SuccessMessageResponse("Test"),
      status: 200,
    })));
    // Arrange: Set up mocks and dependencies
    testAuthentication(authServiceMock, component, fixture);
    authServiceMock.isAuthenticate.and.returnValue(true);

    // Act: Initialize the component
    component.ngOnInit();

    await fixture.whenStable();
    fixture.detectChanges();

    //delete button click before select user 
    const deletebutton = fixture.nativeElement.querySelector('#deleteUser');
    deletebutton.click();
    fixture.detectChanges();
    expect(mockToastrService.info).toHaveBeenCalled();

    //After select delete button click
    await fixture.whenStable();
    fixture.detectChanges();
    selectOneFromListTestCase(fixture, component, '#user2user');
    await fixture.whenStable();
    fixture.detectChanges();
    expect(component.userIdListcollect.length).toEqual(1);
    deletebutton.click();
    fixture.detectChanges();
    await fixture.whenStable();
    fixture.detectChanges();
    expect(dialogservice.confirm).toHaveBeenCalled();
    expect(mockToastrService.success).toHaveBeenCalled();

  });


  it('should delete user exception', async () => {
    //MOCK API
    spyOn(roleApiCallServiceMock, 'getRoleNameList')?.and.returnValue(Promise.resolve(roleFilter));
    spyOn(countryCacheServiceMock, 'getCountryListFromCache')?.and.returnValue(Promise.resolve(countryFilter));
    spyOn(searchMemberServiceMock, 'getMemberList')?.and.returnValue(of(new HttpResponse<UserListPageResponse>({
      body: userListResponse,
      status: 200,
    })));

    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
    // Mock API 
    spyOn(searchMemberServiceMock, 'deleteMultipleMember')?.and.returnValue(throwError(() => mockError));

    // Arrange: Set up mocks and dependencies
    testAuthentication(authServiceMock, component, fixture);
    authServiceMock.isAuthenticate.and.returnValue(true);

    // Act: Initialize the component
    component.ngOnInit();
    await fixture.whenStable();
    fixture.detectChanges();

    //After select delete button click
    await fixture.whenStable();
    fixture.detectChanges();
    selectOneFromListTestCase(fixture, component, '#user2user');
    await fixture.whenStable();
    fixture.detectChanges();
    expect(component.userIdListcollect.length).toEqual(1);
    const deletebutton = fixture.nativeElement.querySelector('#deleteUser');
    deletebutton.click();
    fixture.detectChanges();
    await fixture.whenStable();
    fixture.detectChanges();
    expect(dialogservice.confirm).toHaveBeenCalled();
    spyOn(exceptionService, 'customErrorMessage')?.and.callThrough();
  });



});
