import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { UploadVideoDialogComponent } from '../video/upload-video-dialog/upload-video-dialog.component';

@Injectable({
  providedIn: 'root'
})
export class UploadVideoService {

  constructor(
    private modalService: NgbModal
  ) { }

  public confirm(
    title: string,
    message: string | null,
    videoZipFileId: number | null,
    isEditable: boolean,
    btnOkText: string,
    btnCancelText: string,
    dialogSize: 'sm' | 'lg' = 'sm'): Promise<any> {
    const modalRef = this.modalService.open(UploadVideoDialogComponent, { windowClass: 'uploadVideoModelwidth'});
    modalRef.componentInstance.title = title;
    modalRef.componentInstance.message = message;
    modalRef.componentInstance.videoZipFileId = videoZipFileId;
    modalRef.componentInstance.isEditable = isEditable;
    modalRef.componentInstance.btnOkText = btnOkText;
    modalRef.componentInstance.btnCancelText = btnCancelText;

    return modalRef.result;
  }
}
