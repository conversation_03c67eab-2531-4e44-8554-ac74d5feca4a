import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { getMockCountryList } from '../../Tesing-Helper/TestCountryInfo';
import { fillAndSubmitForm, getMockUserDetail } from '../../Tesing-Helper/TestUserInfo';
import { commonsProviders } from '../../Tesing-Helper/test-utils';
import { ConfirmDialogService } from '../../FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { UserResponse } from '../../model/User/UserResponse.model';
import { ExceptionHandlingService } from '../../shared/ExceptionHandling.service';
import { CountryCacheService } from '../../shared/Service/CacheService/countrycache.service';
import { RoleApiCallService } from '../../shared/Service/RoleService/role-api-call.service';
import { AuthJwtService } from '../../shared/auth-jwt.service';
import { HidePermissionNamePipe } from '../../shared/pipes/Role/hidePermissionName.pipe';
import { UserApiCallService } from '../../shared/Service/user/user-api-call.service';
import { CommonsService } from '../../shared/util/commons.service';
import { AddUserComponent } from './add-user.component';

describe('AddUserComponent', () => {
  let component: AddUserComponent;
  let fixture: ComponentFixture<AddUserComponent>;

  let authServiceMock: any;

  //Service Mock
  let searchMemberServiceMock: UserApiCallService;
  let exceptionService: ExceptionHandlingService;
  let roleApiCallServiceMock: RoleApiCallService;
  let countryCacheServiceMock: CountryCacheService;

  //Service spy
  let mockToastrService: jasmine.SpyObj<ToastrService>;


  const mockUserResponse: UserResponse = getMockUserDetail();
  const roleFilter = ["Cloud Admin", "Device / SV Team"];
  const countryFilter: any = getMockCountryList();

  beforeEach(async () => {
    mockToastrService = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    authServiceMock = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);

    await TestBed.configureTestingModule({
      declarations: [AddUserComponent],
      imports: [NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule],
      providers: [LocalStorageService,
        UserApiCallService,
        CommonsService,
        ConfirmDialogService,
        ExceptionHandlingService,
        SessionStorageService,
        RoleApiCallService,
        HidePermissionNamePipe,
        { provide: AuthJwtService, useValue: authServiceMock },
        commonsProviders(mockToastrService)
      ]
    })
      .compileComponents();

  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AddUserComponent);
    component = fixture.componentInstance;
    authServiceMock.isAuthenticate.and.returnValue(true);
    searchMemberServiceMock = TestBed.inject(UserApiCallService);
    exceptionService = TestBed.inject(ExceptionHandlingService);
    roleApiCallServiceMock = TestBed.inject(RoleApiCallService);
    countryCacheServiceMock = TestBed.inject(CountryCacheService);
    fixture.detectChanges();
  });



  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load add user component when authenticated', async () => {
    // Mock API 
    spyOn(searchMemberServiceMock, 'saveMember')?.and.returnValue(of(new HttpResponse<UserResponse>({
      body: mockUserResponse,
      status: 200,
    })));
    //MOCK API
    spyOn(roleApiCallServiceMock, 'getRoleNameList')?.and.returnValue(Promise.resolve(roleFilter));
    spyOn(countryCacheServiceMock, 'getCountryListFromCache')?.and.returnValue(Promise.resolve(countryFilter));


    // Act: Initialize the component
    component.ngOnInit();
    await fixture.whenStable();
    fixture.detectChanges();
    expect(roleApiCallServiceMock.getRoleNameList).toHaveBeenCalled();
    expect(countryCacheServiceMock.getCountryListFromCache).toHaveBeenCalled();

    //set User Email value
    spyOn(component, 'change').and.callThrough();
    const userEmailField = fixture.nativeElement.querySelector('#userEmail');
    userEmailField.value = 'aakash.brahmbhatt'
    userEmailField.dispatchEvent(new Event('input'));
    fixture.detectChanges();
    expect(component.change).toHaveBeenCalled();
    userEmailField.value = '<EMAIL>'
    userEmailField.dispatchEvent(new Event('input'));
    fixture.detectChanges();
    expect(component.change).toHaveBeenCalled();

    //user_role click
    spyOn(component, 'onItemSelectValidation').and.callThrough();
    const userRoleField = fixture.nativeElement.querySelector('#user_role');
    userRoleField?.click();
    expect(component.onItemSelectValidation).toHaveBeenCalled();

    // Use the helper method to fill and submit the form
    await fillAndSubmitForm(component.form, fixture, '#creatUserBtn');

    expect(mockToastrService.success).toHaveBeenCalled();

  });


  it('should create user api exception', async () => {
    const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });
    spyOn(searchMemberServiceMock, 'saveMember')?.and.returnValue(throwError(() => mockError));
    spyOn(exceptionService, 'customErrorMessage')?.and.callThrough();
    //MOCK API
    spyOn(roleApiCallServiceMock, 'getRoleNameList')?.and.returnValue(Promise.resolve(roleFilter));
    spyOn(countryCacheServiceMock, 'getCountryListFromCache')?.and.returnValue(Promise.resolve(countryFilter));
    component.ngOnInit();
    await fixture.whenStable();
    fixture.detectChanges();
    expect(roleApiCallServiceMock.getRoleNameList).toHaveBeenCalled();
    expect(countryCacheServiceMock.getCountryListFromCache).toHaveBeenCalled();
    // Use the helper method to fill and submit the form
    await fillAndSubmitForm(component.form, fixture, '#creatUserBtn');

    expect(exceptionService.customErrorMessage).toHaveBeenCalledWith(mockError);
  });

});
