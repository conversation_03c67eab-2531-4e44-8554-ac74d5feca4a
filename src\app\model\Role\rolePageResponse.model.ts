import { Pageable } from "../common/pageable.model";
import { PageResponse } from "../common/PageResponse.model";
import { Sort } from "../common/sort.model";
import { RoleResponse } from "./roleResponse.model";

export class RolePageResponse extends PageResponse {
    content: Array<RoleResponse>;

    constructor(pageable: Pageable, totalPages: number, last: boolean, totalElements: number, numberOfElements: number, first: boolean, sort: Sort, size: number, number: number, empty: boolean, content: Array<RoleResponse>) { //NOSONAR
        super(pageable, totalPages, last, totalElements, numberOfElements, first, sort, size, number, empty);
        this.content = content;
    }
}
