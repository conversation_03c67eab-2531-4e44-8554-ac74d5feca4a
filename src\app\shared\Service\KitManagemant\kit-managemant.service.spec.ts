import { HttpResponse, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { throwError } from 'rxjs';
import { KitManagemantSearchRequestBody } from 'src/app/model/KitManagement/KitManagemantSearchRequestBody.model';
import { KitManagementDetailResponse } from 'src/app/model/KitManagement/KitManagementDetailResponse.model';
import { KitManagementPageResponse } from 'src/app/model/KitManagement/KitManagementPageResponse.model';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { CommonsService } from '../../util/commons.service';
import { KitManagemantApiCallService } from './kit-managemant-api-call.service';

describe('KitManagemantApiCallService', () => {
  let service: KitManagemantApiCallService;
  let httpMock: HttpTestingController;
  let configServiceSpy: jasmine.SpyObj<ConfigInjectService>;
  let commonsServiceSpy: jasmine.SpyObj<CommonsService>;
  const mockApiUrl = 'http://example.com/';

  beforeEach(() => {
    // Create spies for dependencies
    const configSpy = jasmine.createSpyObj('ConfigInjectService', ['getServerApiUrl']);
    configSpy.getServerApiUrl.and.returnValue(mockApiUrl);

    const commonsSpy = jasmine.createSpyObj('CommonsService', ['handleError']);

    TestBed.configureTestingModule({
      imports: [],
      providers: [
        KitManagemantApiCallService,
        { provide: ConfigInjectService, useValue: configSpy },
        { provide: CommonsService, useValue: commonsSpy },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting()
      ]
    });

    service = TestBed.inject(KitManagemantApiCallService);
    httpMock = TestBed.inject(HttpTestingController);
    configServiceSpy = TestBed.inject(ConfigInjectService) as jasmine.SpyObj<ConfigInjectService>;
    commonsServiceSpy = TestBed.inject(CommonsService) as jasmine.SpyObj<CommonsService>;
  });

  afterEach(() => {
    // Verify that no unmatched requests are outstanding
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should initialize with correct URL from config service', () => {
    expect(configServiceSpy.getServerApiUrl).toHaveBeenCalled();
    expect(service.kitBaseUrl).toBe(mockApiUrl + 'api/kitManagement/bridge-world/');
  });

  describe('getKitList', () => {
    it('should send POST request with correct parameters', () => {
      // Mock request data
      const mockRequestBody: KitManagemantSearchRequestBody = new KitManagemantSearchRequestBody(null, null, null, null, null, null)
      const mockPagination = { page: 0, size: 10 };

      // Mock response
      const mockResponse: KitManagementPageResponse = new KitManagementPageResponse(null, null, null, null, null, null, null, null, null, null, null)

      let response: HttpResponse<KitManagementPageResponse> | null = null;

      // Call the service method
      service.getKitList(mockRequestBody, mockPagination).subscribe(res => {
        response = res;
      });

      // Verify the request
      const req = httpMock.expectOne(`${service.kitBaseUrl}search?page=0&size=10`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(mockRequestBody);

      // Respond with mock data
      req.flush(mockResponse, { status: 200, statusText: 'OK' });

      // Verify the response
      expect(response).toBeTruthy();
      expect(response?.body).toEqual(mockResponse);
      expect(response?.status).toBe(200);
    });

    it('should handle errors using CommonsService', () => {
      // Setup error handling spy
      commonsServiceSpy.handleError.and.returnValue(throwError(() => new Error('Test error')));

      const mockRequestBody = {} as KitManagemantSearchRequestBody;
      const mockPagination = { page: 0, size: 10 };

      // Call the service method
      service.getKitList(mockRequestBody, mockPagination).subscribe({
        error: (error) => {
          expect(error).toBeTruthy();
        }
      });

      // Simulate HTTP error
      const req = httpMock.expectOne(`${service.kitBaseUrl}search?page=0&size=10`);
      req.error(new ProgressEvent('error'));

      // Verify error handler was called
      expect(commonsServiceSpy.handleError).toHaveBeenCalled();
    });
  });

  describe('getKitDetail', () => {
    it('should send GET request with correct kit ID', () => {
      const mockKitId = 123;
      const mockDetailResponse: KitManagementDetailResponse = new KitManagementDetailResponse(null, null, null, null, null, null, null, null, null, null, null, null)

      let response: HttpResponse<KitManagementDetailResponse> | null = null;

      // Call the service method
      service.getKitDetail(mockKitId).subscribe(res => {
        response = res;
      });

      // Verify the request
      const req = httpMock.expectOne(`${service.kitBaseUrl}${mockKitId}`);
      expect(req.request.method).toBe('GET');

      // Respond with mock data
      req.flush(mockDetailResponse, { status: 200, statusText: 'OK' });

      // Verify the response
      expect(response).toBeTruthy();
      expect(response?.body).toEqual(mockDetailResponse);
      expect(response?.status).toBe(200);
    });

    it('should handle errors using CommonsService', () => {
      // Setup error handling spy
      commonsServiceSpy.handleError.and.returnValue(throwError(() => new Error('Test error')));

      const mockKitId = 123;

      // Call the service method
      service.getKitDetail(mockKitId).subscribe({
        error: (error) => {
          expect(error).toBeTruthy();
        }
      });

      // Simulate HTTP error
      const req = httpMock.expectOne(`${service.kitBaseUrl}${mockKitId}`);
      req.error(new ProgressEvent('error'));

      // Verify error handler was called
      expect(commonsServiceSpy.handleError).toHaveBeenCalled();
    });
  });

  describe('getKitRevVersion', () => {
    it('should send GET request and return the version string', async () => {
      const mockVersionResponse = 'v1.2.3';

      // Create a promise for the async method
      const versionPromise = service.getKitRevVersion();

      // Verify the request
      const req = httpMock.expectOne(`${service.kitBaseUrl}revVersion`);
      expect(req.request.method).toBe('GET');

      // Respond with mock data
      req.flush(mockVersionResponse);

      // Await the promise and verify the result
      const version = await versionPromise;
      expect(version).toBe(mockVersionResponse);
    });

    it('should throw an error if the request fails', async () => {
      // Create a promise for the async method
      const versionPromise = service.getKitRevVersion();

      // Simulate HTTP error
      const req = httpMock.expectOne(`${service.kitBaseUrl}revVersion`);
      req.error(new ProgressEvent('error'));

      // Await the promise and check it rejects
      await expectAsync(versionPromise).toBeRejected();
    });
  });
});