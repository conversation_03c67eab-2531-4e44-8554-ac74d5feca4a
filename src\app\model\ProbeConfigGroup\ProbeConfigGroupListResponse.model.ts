import { FeatureResponse } from "./FeatureResponse.model";
import { PresetResponse } from "./PresetResponse.model";

export class ProbeConfigGroupListResponse {
    id: number;
    partNumberCode: string;
    probeType: string;
    features: Array<FeatureResponse>;
    presets: Array<PresetResponse>;
    description: string;
    modifiedDate: number;

    constructor(
        id: number,
        partNumberCode: string,
        probeType: string,
        features: Array<FeatureResponse>,
        presets: Array<PresetResponse>,
        description: string,
        modifiedDate: number
    ) {
        this.id = id;
        this.partNumberCode = partNumberCode;
        this.probeType = probeType;
        this.features = features;
        this.presets = presets;
        this.description = description;
        this.modifiedDate = modifiedDate;
    }
}