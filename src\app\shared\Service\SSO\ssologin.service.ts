import { Injectable } from '@angular/core';
import { ConfigInjectService } from '../../InjectService/config-inject.service';

@Injectable({
  providedIn: 'root'
})
export class SSOLoginService {

  private ssoServerApiUrl: string = this.configInjectService.getSSOServerApiUrl();
  private ssoRegistrationId: string = this.configInjectService.getSSORegistrationId();

  constructor(private configInjectService: ConfigInjectService) { }

  /**
   * Login With Microsoft
   * 
   * <AUTHOR>
   */
  public loginWithMicrosoft(): void {
    window.location.href = this.ssoServerApiUrl + "saml2/authenticate/" + this.ssoRegistrationId;
  }

  /**
   * LogOut In Microsoft
   * 
   * <AUTHOR>
   */
  public logOutInMicrosoft(): void {
    window.location.href = this.ssoServerApiUrl + "logout";
  }


}
