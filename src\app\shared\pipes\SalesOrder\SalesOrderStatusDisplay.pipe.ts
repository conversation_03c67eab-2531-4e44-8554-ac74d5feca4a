import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ProductConfigStatus } from '../../enum/SalesOrder/ProductConfigStatus.enum';

@Pipe({
    name: 'salesOrderStatusDisplay'
})
export class SalesOrderStatusDisplay implements PipeTransform {

    public transform(status: ProductConfigStatus): string {
        return isNullOrUndefined(status) ? '' : ProductConfigStatus[status];
    }

}
