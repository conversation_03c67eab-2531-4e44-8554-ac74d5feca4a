import { TestBed } from '@angular/core/testing';
import { LocalStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { CommonsService } from './commons.service';

describe('CommonsService', () => {
  let service: CommonsService;

  beforeEach(() => {
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);

    // Mock the return value of retrieve for the getPermissionObject function
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);

    TestBed.configureTestingModule({
      providers: [
        CommonsService,
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: LocalStorageService, useValue: localStorageServiceMock },
      ]
    });
    service = TestBed.inject(CommonsService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
