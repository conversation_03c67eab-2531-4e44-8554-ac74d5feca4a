import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { EditSoftwareBuildComponent } from '../../Edit-Software-Build/edit-software-build.component';
import { SoftwareBuildListResponse } from '../../../../model/SoftwaarBuilds/SoftwareBuildListResponse.model';
import { BasicModelConfig } from '../../../../model/common/BasicModelConfig.model';
import { CountryListResponse } from '../../../../model/Country/CountryListResponse.model';

@Injectable({
  providedIn: 'root'
})
export class EditSoftwareBuildDialogService {

  constructor(
    private modalService: NgbModal
  ) { }

  public openEditInventoryModel(
    basicModelConfig: BasicModelConfig,
    inventory: SoftwareBuildListResponse,
    jsonVersionList: any[],
    countryList: CountryListResponse[],
    dialogSize: 'sm' | 'lg' = 'sm'): Promise<boolean> {
    const modalRef = this.modalService.open(EditSoftwareBuildComponent, { size: dialogSize, modalDialogClass: 'editItemInventoryModelSize' });
    modalRef.componentInstance.basicModelConfig = basicModelConfig;
    modalRef.componentInstance.inventory = inventory;
    modalRef.componentInstance.jsonVersionList = jsonVersionList;
    modalRef.componentInstance.countryList = countryList;
    return modalRef.result;
  }

}
