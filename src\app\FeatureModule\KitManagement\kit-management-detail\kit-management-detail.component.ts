import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { BRIDGE_KIT_MANAGEMANT_DELETE } from 'src/app/app.constants';
import { KitManagementDetailResponse } from 'src/app/model/KitManagement/KitManagementDetailResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { KitManagemantApiCallService } from 'src/app/shared/Service/KitManagemant/kit-managemant-api-call.service';

@Component({
  selector: 'app-kit-management-detail',
  templateUrl: './kit-management-detail.component.html',
  styleUrls: ['./kit-management-detail.component.css']
})
export class KitManagementDetailComponent implements OnInit {

  loading: boolean = false;

  @Input("kitId") kitId: number;
  @Output("showKitList") showKitList = new EventEmitter();

  kitManagementDetailResponse: KitManagementDetailResponse = null;

  //kit Rev Version 
  KitRevVersionResponse: string = null;


  constructor(
    private kitManagemantApiCallService: KitManagemantApiCallService,
    private exceptionService: ExceptionHandlingService,
    private toste: ToastrService) {

  }

  /**
   * On Init (get kit device detail and operation list)
   * 
   * <AUTHOR>
   */
  public ngOnInit(): void {
    this.getKitDetail();
    this.getKitRevVersion();
  }

  /**
   * Go To Kit List Page
   * 
   * <AUTHOR>
   */
  public back(): void {
    this.showKitList.emit();
  }

  /**
   * Get Kit Details
   * 
   * <AUTHOR>
   */
  private getKitDetail(): void {
    this.setLoadingStatus(true);
    this.kitManagemantApiCallService.getKitDetail(this.kitId)?.subscribe({
      next: (res: HttpResponse<KitManagementDetailResponse>) => {
        if (res.status == 200) {
          this.kitManagementDetailResponse = res.body;
          this.setLoadingStatus(false);
        } else {
          this.setLoadingStatus(false);
          this.toste.info(BRIDGE_KIT_MANAGEMANT_DELETE);
          this.back();
        }
      }, error: (error: HttpErrorResponse) => {
        this.setLoadingStatus(false);
        this.exceptionService.customErrorMessage(error);
      }
    });
  }

  public async getKitRevVersion(): Promise<void> {
    this.KitRevVersionResponse = await this.kitManagemantApiCallService.getKitRevVersion();
  }


  /**
   * Loading Display
   * 
   * <AUTHOR>
   * 
   * @param status 
   */
  private setLoadingStatus(status: boolean) {
    this.loading = status;
  }

  /**
  * Refresh Bridge Kit Detail Page Data
  * 
  * <AUTHOR> 
  */
  public refreshBridgekitDetailPage(): void {
    this.getKitDetail();
    this.getKitRevVersion();
  }

}
