import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { isNullOrUndefined } from 'is-what';
import { AuthJwtService } from '../shared/auth-jwt.service';
import { API_TIME_OUT, FORBUDDEN_ERROR_MESSAGE, INTERNAL_SERVER_ERROR } from '../app.constants';
import { SSOLoginService } from './Service/SSO/ssologin.service';


@Injectable({
  providedIn: 'root'
})
export class ExceptionHandlingService {

  constructor(private toste: ToastrService, private router: Router, private authservice: AuthJwtService,
    private ssoLoginService: SSOLoginService
  ) { }

  customErrorMessage(error: HttpErrorResponse) {
    if (error.status == 400) {
      this.badRequestError(error);
    } else if (error.status == 401) {
      this.unauthorizedError(error);
    } else if (error.status == 204) {
      this.toste.error(error.error["errorMessage"]);
    } else if (error.status == 404) {
      this.serverNotAvailable(error);
    } else if ([412, 403].includes(error?.status)) {
      const errorMessage = error.error?.["errorMessage"];
      if (errorMessage) {
        this.toste.info(errorMessage);
      } else {
        this.toste.error(FORBUDDEN_ERROR_MESSAGE);
      }
    } else if (error.status == 409) {
      // We are using this status in Item Inventory page while uploading any new package (409)
      this.errorMessageDisplay(error);
    } else if (error.status == 405) {
      this.toste.info(error.error["error"]);
    } else if (error.status == 424) {
      this.toste.error(error.error["errorMessage"]);
    } else if (error.status == 500) {
      this.toste.error(INTERNAL_SERVER_ERROR);
    } else if (error.status == 504) {
      this.toste.error(API_TIME_OUT);
    } else if (error.status == 502) {
      this.logoutSystem();
    } else if (error.status == 0) {
      this.toste.error(FORBUDDEN_ERROR_MESSAGE);
    }
  }

  /**
   * Error Message Display
   * 
   * <AUTHOR>
   * @param error 
   */
  private errorMessageDisplay(error): void {
    if (!isNullOrUndefined(error.error["errorMessage"])) {
      this.toste.info(error.error["errorMessage"]);
    } else if (!isNullOrUndefined(error.error["message"])) {
      this.toste.error(error.error["message"]);
    } else {
      this.toste.error(error.error["error"]);
    }
  }

  /**
   * Unauthorized Error
   * 
   * <AUTHOR>
   * @param error 
   */
  private unauthorizedError(error: HttpErrorResponse): void {
    if (error.error == null || error.error["error"] == "Unauthorized") {
      this.toste.warning("Session Timeout");
      this.logoutSystem();
    }
    else if (error.error["errorMessage"] != null && error.error["errorMessage"] != "" && error.error["errorMessage"] != undefined) {
      this.toste.info(error.error["errorMessage"]);
    }
  }

  /**
  * Logout From System
  * 
  */
  private logoutSystem(): void {
    this.authservice.clear();
    this.ssoLoginService.logOutInMicrosoft();
  }
  /**
   * Bad Request
   * 
   * <AUTHOR>
   * @param error 
   */
  private badRequestError(error: HttpErrorResponse): void {
    if (error.error["errorMessage"] != null && error.error["errorMessage"] != "" && error.error["errorMessage"] != undefined) {
      this.toste.warning(error.error["errorMessage"]);
    } else if (!isNullOrUndefined(error.error["message"])) {
      this.toste.error(error.error["message"]);
    } else {
      this.toste.error(error.message);
    }
  }

  /**
   * Server Not Available 
   * 
   * <AUTHOR>
   * @param error 
   */
  private serverNotAvailable(error: HttpErrorResponse): void {
    if (error.error["error"] != null && error.error["error"] != "" && error.error["error"] != undefined) {
      this.toste.info(error.error["error"]);
    }
    else {
      this.toste.info("Server Not Available");
    }
  }

}
