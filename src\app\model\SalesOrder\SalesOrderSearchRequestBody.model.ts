import { ProductConfigStatus } from "src/app/shared/enum/SalesOrder/ProductConfigStatus.enum";
import { SalesOrderTypeStatus } from "src/app/shared/enum/SalesOrder/SalesOrderTypeStatus.enum";

export class SalesOrderSearchRequestBody {
    salesOrderNumber: string;
    customerName: string;
    customerEmail: string;
    countryIds: number[];
    poNumber: string;
    orderType: SalesOrderTypeStatus;
    orderRecordType: Array<string>;
    productConfigStatus: Array<ProductConfigStatus>;


    constructor($salesOrderNumber: string, $customerName: string, $customerEmail: string,//NOSONAR
        $countryIds: number[], $poNumber: string, $orderType: SalesOrderTypeStatus,
        $orderRecordType: Array<string>,
        $productConfigStatus: Array<ProductConfigStatus>) {
        this.salesOrderNumber = $salesOrderNumber;
        this.customerName = $customerName;
        this.customerEmail = $customerEmail;
        this.countryIds = $countryIds;
        this.poNumber = $poNumber;
        this.orderType = $orderType;
        this.orderRecordType = $orderRecordType;
        this.productConfigStatus = $productConfigStatus;
    }

}