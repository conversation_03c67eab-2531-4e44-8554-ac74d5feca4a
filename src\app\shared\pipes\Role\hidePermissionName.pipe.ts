import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { PERMISSION_NAME_HIDE } from 'src/app/app.constants';
import { RolePermissionResponse } from 'src/app/model/Role/rolePermissionResponse.model';

@Pipe({
    name: 'hidePermissionNamePipe'
})
export class HidePermissionNamePipe implements PipeTransform {

    transform(permissionList: Array<RolePermissionResponse>): Array<RolePermissionResponse> {
        if (!isNullOrUndefined(permissionList)) {
            return permissionList.filter(permission => !PERMISSION_NAME_HIDE.includes(permission.name));
        }
        return [];
    }

}
