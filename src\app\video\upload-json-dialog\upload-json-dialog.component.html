<!------------------------------------>
<!------------------------------------>
<!--
    Component Name : uplaod-json-dialog
    Description : create json file from existing video versions.
    Functionality : select videos from list to create json file
                    with jsonVersion and json id.
    Validations : Title - required
                          Must contain at least one character
                  Version - required 
-->
<!------------------------------------>
<!------------------------------------>
<!--Model - start-->
<!-- Model Header - start -->
<div class="modal-header">
    <label class="modal-title"><strong>{{ basicModelConfig?.title }}</strong></label>
</div>
<!-- Model Header - end -->
<!--Model Body - start-->
<div class="modal-body">
    <div>
        <form enctype="multipart/form-data" name='uploadjsonfile' [formGroup]="createJSONForm"
            class="upload-video-form">
            <!-- Title Field - start -->
            <div class="form-group mb-4">
                <div class="row">
                    <div class="col-md-3">
                        <span class="json-upload-title">Title *</span>
                    </div>
                    <div class="col-md-9">
                        <input type="text" id="title" name="title" formControlName="Title"
                            [attr.disabled]="(jsonId != null)?'disabled':null"
                            [className]="(jsonId != null)?'form-control disabledField':'form-control'" tabindex="-1"
                            autocomplete="off" required />
                        <!-- Title Field Validation -->
                        <div
                            *ngIf="(createJSONForm.get('Title').touched || createJSONForm.get('Title').dirty) && createJSONForm.get('Title').invalid ">
                            <div *ngIf="createJSONForm.get('Title').errors['required']" class="validation">
                                Enter valid title
                            </div>
                            <div *ngIf="createJSONForm.get('Title').errors['maxlength']" class="validation">
                                <span class="alert-color font-12">{{textBoxMaxCharactersAllowedMessage}}</span>
                            </div>
                            <div *ngIf="createJSONForm.get('Title').errors['pattern']" class="validation">
                                <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Title Field - end -->
            <!-- Version Field - start -->
            <div class="form-group mb-4">
                <div class="row">
                    <div class="col-md-3">
                        <span class="json-upload-title">JSON Version *</span>
                    </div>
                    <div class="col-md-9">
                        <input type="text" id="version" name="version" formControlName="version" placeholder="1.1"
                            [attr.disabled]="(jsonId != null)?'disabled':null"
                            [className]="(jsonId != null)?'form-control disabledField':'form-control'"
                            class="form-control" tabindex="-1" autocomplete="off" required />
                        <!-- validation for version start -->
                        <div
                            *ngIf="(createJSONForm.get('version').touched || createJSONForm.get('version').dirty) && createJSONForm.get('version').invalid ">
                            <div
                                *ngIf="createJSONForm.get('version').errors['required'] || createJSONForm.get('version').invalid">
                                <span class="validation">Enter Valid JSON</span>
                            </div>
                        </div>
                        <!-- validation for version end -->
                    </div>
                </div>
            </div>
            <!-- Version Field - end -->
            <!-- Notes Field - start -->
            <div class="form-group mb-2">
                <div class="row">
                    <div class="col-md-3">
                        <span class="json-upload-title">Notes</span>
                    </div>
                    <div class="col-md-9">
                        <textarea formControlName="notes" style="resize: none;" class="form-control"
                            tabindex="-1"></textarea>
                        <div
                            *ngIf="(createJSONForm.get('notes').touched || createJSONForm.get('notes').dirty) && createJSONForm.get('notes').invalid ">
                            <div *ngIf="createJSONForm.get('notes').errors['maxlength']" class="validation">
                                <span class="alert-color font-12">{{textAreaMaxLengthMessage}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Notes Field - end -->
        </form>
    </div>
</div>
<!--Model Body - end-->
<!--Footer Buttons - start-->
<div class="modal-footer leftbtn">
    <button type="button" class="btn btn-sm btn-outline-secondary" (click)="decline()" id="decineJsonButton">{{
        basicModelConfig?.btnCancelText
        }}</button>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-sm btn-orange" (click)="dismiss()" id="dismissVideoJson"
        *ngIf="btnBacktoSelection!=null || btnBacktoSelection!=undefined">{{ btnBacktoSelection }}</button>
    <button type="button" class="btn btn-sm btn-orange" id="uploadBtn" (click)="accept()" id="AddEditJson"
        [disabled]="disableBtn && !createJSONForm.valid">{{ basicModelConfig?.btnOkText }}</button>
</div>
<!--Footer Buttons - end-->
<!--Model - end-->