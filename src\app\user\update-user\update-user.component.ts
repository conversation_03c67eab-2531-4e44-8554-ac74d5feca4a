import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { ConfirmDialogService } from '../../FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { Member } from '../../model/memberAdd.model';
import { MultiSelectDropdownSettings } from '../../model/MultiSelectDropdownSettings.model';
import { ExceptionHandlingService } from '../../shared/ExceptionHandling.service';
import { UserApiCallService } from '../../shared/Service/user/user-api-call.service';
import { RoleApiCallService } from '../../shared/Service/RoleService/role-api-call.service';
import { MultiSelectDropDownSettingService } from '../../shared/util/multi-select-drop-down-setting.service';
import { MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, MAXIMUM_TEXTBOX_LENGTH } from '../../app.constants';
import { CommonsService } from '../../shared/util/commons.service';
import { UserResponse } from '../../model/User/UserResponse.model';
import { CountryCacheService } from '../../shared/Service/CacheService/countrycache.service';
import { CountryListResponse } from '../../model/Country/CountryListResponse.model';

@Component({
  selector: 'app-update-user',
  templateUrl: './update-user.component.html',
  styleUrls: ['./update-user.component.css']
})
export class UpdateUserComponent implements OnInit {
  @Input('userId') userId;
  @Output('viewmemberlist') hideupdateuser = new EventEmitter;

  //loading
  loading = false;
  login: any;
  countryList: CountryListResponse[] = [];
  roleSetting: MultiSelectDropdownSettings;
  countrySetting: MultiSelectDropdownSettings;
  isRefreshForm: boolean = false;
  userRolesSelection: string[] = [];
  //user response
  userResponse: UserResponse = null;

  //Text box max limit set
  textBoxMaxLength: number = MAXIMUM_TEXTBOX_LENGTH;
  textBoxMaxCharactersAllowedMessage: string = MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;

  form = new FormGroup({
    firstName: new FormControl('', [Validators.required, Validators.pattern('[a-zA-Z ]*'), Validators.maxLength(this.textBoxMaxLength)]),
    lastName: new FormControl('', [Validators.required, Validators.pattern('[a-zA-Z ]*'), Validators.maxLength(this.textBoxMaxLength)]),
    email: new FormControl(''),
    userRoles: new FormControl([], Validators.required),
    country: new FormControl([], Validators.required)
  });

  constructor(
    private exceptionService: ExceptionHandlingService,
    private userApiCallService: UserApiCallService,
    private toste: ToastrService,
    private dialogservice: ConfirmDialogService,
    private roleApiCallService: RoleApiCallService,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private commonsService: CommonsService,
    private countryCacheService: CountryCacheService
  ) { }

  ngOnInit() {
    this.loading = true;
    this.roleSetting = this.multiSelectDropDownSettingService.getRoleDrpSetting(true);
    this.countrySetting = this.multiSelectDropDownSettingService.getCountryDrpSetting(false, true);
    this.getInitData();
  }

  /**
   * Get Role and country list
   * <AUTHOR>
   */
  private async getInitData(): Promise<void> {
    this.loading = true;
    this.userRolesSelection = await this.roleApiCallService.getRoleNameList();
    this.countryList = await this.countryCacheService.getCountryListFromCache();
    this.getsingleuser();
  }

  public onItemSelectValidation(fieldName: string): void {
    if (this.form.invalid) {
      this.form.get(fieldName).markAsTouched();
    }
  }

  private getsingleuser(): void {
    this.userApiCallService.getSingleMember(this.userId).subscribe({
      next: (response: HttpResponse<UserResponse>) => {
        if (response.status == 200) {
          this.userResponse = response.body;
          this.login = response.body.login;
          this.form.get("firstName").setValue(response.body.firstName);
          this.form.get("lastName").setValue(response.body.lastName);
          this.form.get("email").setValue(response.body.email);
          this.form.get("userRoles").setValue(response.body.userRoles);
          this.form.get("country").setValue(response.body.countryMasters);
          this.isRefreshForm = true;
        }
        else if (response.status == 204) {
          this.toste.warning("NO USER AVALABLE");
        }
        this.loading = false;
      }, error: (error: HttpErrorResponse) => {
        this.loading = false;
        this.exceptionService.customErrorMessage(error);
      }
    });
  }




  public updateAccountConfirmation(): void {
    this.dialogservice.confirm('Modify', 'Please confirm to Modify.')
      .then((confirmed) => {
        if (confirmed) {
          this.updateAccount();
        }
      });
  }


  public updateAccount(): void {
    this.loading = true;
    let formData = this.form.value;
    let member = new Member(formData.email.split("@")[0],
      formData.firstName,
      formData.lastName,
      formData.email,
      formData.userRoles,
      this.commonsService.getIdsFromArray(formData.country));
    this.userApiCallService.updateMember(member, this.userId).subscribe(
      {
        next: (response: HttpResponse<UserResponse>) => {
          this.hideupdateuser.emit();
          if (response.status == 200) {
            this.toste.success("Successfully modified.");
          }
          else if (response.status == 204) {
            this.toste.info("No content");
          }
        },
        error: (error: HttpErrorResponse) => {
          this.loading = false;
          this.exceptionService.customErrorMessage(error);
        }
      });
  }

  public back(): void {
    this.hideupdateuser.emit();
  }

}
