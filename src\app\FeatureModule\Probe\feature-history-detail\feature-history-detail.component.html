<body>
  <!-- ------------------------------------------------ -->
  <!-- model header start -->
  <!-- ------------------------------------------------ -->
  <div class="modal-header">
    <label class="modal-title">{{ header }}
      <span *ngIf="history?.reminder"> - Reminder Enabled</span>
      <span *ngIf="!history?.reminder"> - Reminder Disabled</span>
    </label>
    <div class="text-right">
      <button type="button" tabindex="-1" class="btn p-0" id="table_popup_close_btn">
        <img src="assets/images/close-m.svg" (click)="dismiss()" alt="close" id="feature-history-detail" />
      </button>
    </div>
  </div>
  <!-- ------------------------------------------------ -->
  <!-- model header end -->
  <!-- ------------------------------------------------ -->
  <!-- ------------------------------------------------ -->
  <!-- model body start -->
  <!-- ------------------------------------------------ -->
  <div class="modal-body">
    <label class="mb-3 h6-tag">
      <span *ngIf="history?.modifiedDate!=null">Last Modified Date & Time :
        {{history.modifiedDate | date:dateDisplayFormat}}</span>
      <span *ngIf="history?.modifiedDate!=null && history.modifiedBy!=null"> | </span>
      <span *ngIf="history?.modifiedBy!=null">Modified By : {{history.modifiedBy}}</span>
    </label><br />

    <!-------------------------------------------------- -->
    <!-- table Start -->
    <!-------------------------------------------------- -->
    <div class="featureHistoryBody"
      *ngIf="featureHistoryDetailList?.presets!=null && featureHistoryDetailList?.presets?.length > 0">
      <table class="table table-sm table-bordered" id="featureHistorytable" aria-hidden="true">
        <thead id="featureHistorytableHead">
          <tr class="thead-light">
            <th style="width:20%;">Preset Name</th>
            <th style="width:15%">Enable</th>
            <th style="width:25%">Start Date</th>
            <th style="width:25%">End Date</th>
            <th style="width:15%" class="trailColumeHide">Trial</th>
          </tr>
        </thead>
        <tbody id="featureHistorytableBody">
          <ng-template ngFor let-featuresObject [ngForOf]="featureHistoryDetailList.presets">
            <tr>
              <td>{{featuresObject |configBaseResponseDisplayPipe}}</td>
              <td>
                <span>{{featuresObject.enable | commonBooleanValueDisplayPipe}}</span>
              </td>
              <td>{{featuresObject.startDate | featuresStartEndDateDisplay:true}}</td>
              <td>{{featuresObject.endDate | featuresStartEndDateDisplay:false}}</td>
              <td class="trailColumeHide">
                <span>{{featuresObject.trial | commonBooleanValueDisplayPipe}}</span>
              </td>
            </tr>
          </ng-template>
        </tbody>
      </table>
    </div>
    <!-------------------------------------------------- -->
    <!-- table End -->
    <!-------------------------------------------------- -->

    <!-------------------------------------------------- -->
    <!-- table Start -->
    <!-------------------------------------------------- -->
    <div class="featureHistoryBody"
      *ngIf="featureHistoryDetailList?.features && featureHistoryDetailList?.features?.length > 0">
      <table class="table table-sm table-bordered" id="featureHistorytable" aria-hidden="true">
        <thead id="featureHistorytableHead">
          <tr class="thead-light">
            <th style="width:20%;">Feature Name</th>
            <th style="width:15%">Enable</th>
            <th style="width:25%">Start Date</th>
            <th style="width:25%">End Date</th>
            <th style="width:15%" class="trailColumeHide">Trial</th>
          </tr>
        </thead>
        <tbody id="featureHistorytableBody">
          <ng-template ngFor let-featuresObject [ngForOf]="featureHistoryDetailList.features">
            <tr>
              <td>{{featuresObject |configBaseResponseDisplayPipe}}</td>
              <td>
                <span>{{featuresObject.enable | commonBooleanValueDisplayPipe}}</span>
              </td>
              <td>{{featuresObject.startDate | featuresStartEndDateDisplay:true}}</td>
              <td>{{featuresObject.endDate | featuresStartEndDateDisplay:false}}</td>
              <td class="trailColumeHide">
                <span>{{featuresObject.trial | commonBooleanValueDisplayPipe}}</span>
              </td>
            </tr>
          </ng-template>
        </tbody>
      </table>
    </div>
    <!-------------------------------------------------- -->
    <!-- table End -->
    <!-------------------------------------------------- -->

  </div>
  <!-- ------------------------------------------------ -->
  <!-- model body end -->
  <!-- ------------------------------------------------ -->
</body>