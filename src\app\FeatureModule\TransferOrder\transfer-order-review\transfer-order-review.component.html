<div *ngIf="transferOrderReviewDisplay">
    <!------------------------------------------->
    <!-- loading start -------------------------->
    <!------------------------------------------->
    <div class="ringLoading" *ngIf="loading">
        <!-- loading gif start -->
        <div class="ringLoadingDiv">
            <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
        </div>
        <!-- loading gif end -->
    </div>
    <!------------------------------------------->
    <!-- loading end ---------------------------->
    <!------------------------------------------->

    <body class="bg-white" *ngIf="transferOrderData">
        <!------------------------------------------->
        <!-- container fluid start -->
        <!------------------------------------------->
        <div class="container-fluid" id="transferOrderReviewPage">
            <!-- row start -->
            <div class="row">
                <div class="col-md-12">
                    <div class="row" class="headerAlignment">
                        <div>
                            <label class="childFlex h5-tag"> Transfer Order - Review Product {{serialNumber}}</label>
                        </div>
                        <div class="childFlex">

                            <!-------------------------------------------->
                            <!-- back button div start -->
                            <button class="btn btn-sm btn-outline-secondary role-back-btn  mr-3"
                                id="salesOrderReviewBack" (click)="back()"><i class="fa fa-reply"
                                    aria-hidden="true"></i>&nbsp;&nbsp;Back</button>
                            <!-- back button div end -->
                            <!-------------------------------------------->
                        </div>
                    </div>
                    <div>
                        <label class="text-primary">* Review the Order details to transfer the products associated with
                            the Transfer Order.</label>
                    </div>

                    <!---##############################################################-->
                    <!---##############################################################-->
                    <!-----------------Source SalesOrder Product------------------------->
                    <!---##############################################################-->
                    <!---##############################################################-->

                    <div class="row">
                        <div class="col-md-12">
                            <!-- main card start -->
                            <div class="card">
                                <div class="card-body">
                                    <div class="card shadow">
                                        <div class="card-body">
                                            <!-- ------------------------------------------------------- -->
                                            <!-- ------Source Sales Order Probe Table start ------------ -->
                                            <!-- ------------------------------------------------------- -->
                                            <div class="container-fluid ml-4 mr-4">
                                                <label class="mb-1 h5-tag"><span>Transfer Sales Order -
                                                        {{transferOrderData?.sourceSalesOrder?.salesOrderNumber}} -
                                                        <span
                                                            [class.text-primary]="transferOrderData?.destinationSalesOrder?.countryDisplayName!==transferOrderData?.sourceSalesOrder?.countryDisplayName">
                                                            {{transferOrderData?.sourceSalesOrder?.countryDisplayName}}</span></span></label>
                                                <hr class="hrMargin">
                                                <ng-template
                                                    [ngIf]="transferOrderData?.sourceSalesOrder?.product?.probes!==null">
                                                    <label class=" mb-1 h5-tag"><span>Probe(s)</span></label>
                                                    <hr class="hrMargin">
                                                    <!-- ------------------------------------------------------- -->
                                                    <!-- -------------Total Probe(s)---------------------------- -->
                                                    <!---------------------------------------------------------- -->
                                                    <div class="bottomMargin-5">Total
                                                        {{transferOrderData?.sourceSalesOrder?.product?.probes?.length}}
                                                        Transfer
                                                        Order Probe(s)</div>
                                                    <div class="transferOrderReviewTable">
                                                        <table class="table table-sm table-bordered mr-1"
                                                            id="relatedProduct" aria-hidden="true">
                                                            <!-- table header start -->
                                                            <thead>
                                                                <tr class="thead-light">
                                                                    <th class="nowrap">Sr. No.</th>
                                                                    <th class="nowrap">Probe Type</th>
                                                                    <th class="nowrap">Part Number</th>
                                                                    <th class="nowrap">Bridge Kit Part Number</th>
                                                                    <th class="nowrap">OTS Kit Part Number</th>
                                                                    <th class="nowrap">Preset(s)</th>
                                                                    <th class="nowrap">Feature(s)</th>
                                                                    <th class="nowrap">Config Groups(s)</th>
                                                                    <th class="nowrap ">Serial Number</th>
                                                                    <th class="nowrap">Transferred Order Number</th>
                                                                    <th class="nowrap">Status</th>
                                                                </tr>
                                                            </thead>
                                                            <!-- table header end -->
                                                            <!-- table body start -->
                                                            <tbody>
                                                                <tr
                                                                    *ngFor="let tranferUserProbe of transferOrderData?.sourceSalesOrder?.product?.probes; let probeIndex = index">
                                                                    <td>{{probeIndex + 1}}</td>
                                                                    <td class="nowrap">{{tranferUserProbe.probeType}}
                                                                    </td>
                                                                    <td class="nowrap">
                                                                        {{tranferUserProbe.productCode}}</td>
                                                                    <td class="nowrap">
                                                                        {{tranferUserProbe.bridgeKitPartNumberCode}}
                                                                    </td>
                                                                    <td class="nowrap">
                                                                        {{tranferUserProbe.otsKitPartNumberCode}}
                                                                    </td>
                                                                    <td class="nowrap"
                                                                        [innerHTML]="tranferUserProbe.associatedPresets| featureInformationDisplayPipe">
                                                                    </td>
                                                                    <td class="nowrap"
                                                                        [innerHTML]="tranferUserProbe.associatedFeatures | featureInformationDisplayPipe">
                                                                    </td>
                                                                    <td class="nowrap">
                                                                        {{tranferUserProbe.probeConfigGroupPartNumberCodes}}
                                                                    </td>
                                                                    <td class="nowrap">
                                                                        {{tranferUserProbe.entitySerialNumber}}
                                                                    </td>
                                                                    <td class="nowrap">
                                                                        {{tranferUserProbe.destinationSalesOrderNumber}}
                                                                    </td>
                                                                    <td class="nowrap">{{tranferUserProbe?.entityStatus
                                                                        |salesOrderStatusDisplay}}</td>
                                                                </tr>
                                                            </tbody>
                                                            <!-- table body end -->
                                                        </table>
                                                    </div>
                                                </ng-template>
                                            </div>
                                            <!-- ------------------------------------------------------- -->
                                            <!-- ------Source Sales Order Probe Table end-- ------------ -->
                                            <!-- ------------------------------------------------------- -->

                                            <!-- ------------------------------------------------------- -->
                                            <!-- ---------Source SalesOrder Bridge table start --------- -->
                                            <!-- ------------------------------------------------------- -->
                                            <div class="container-fuild ml-4 mr-4 px-3 mt-3"
                                                *ngIf="transferOrderData?.sourceSalesOrder?.product?.bridges!==null">
                                                <label class="mb-1 h5-tag"><span>Bridge(s)</span>
                                                </label>
                                                <hr class="hrMargin">
                                                <!-------------------------------------->
                                                <!-------------------------------------->
                                                <!-------------------------------------->
                                                <!---------------Total Bridge(s)-------->
                                                <div class="bottomMargin-5">Total
                                                    {{transferOrderData?.sourceSalesOrder?.product?.bridges?.length}}
                                                    Transfer Order Bridge(s)</div>
                                                <!-------------------------------------->
                                                <table class="table table-sm table-bordered" aria-hidden="true"
                                                    id="bridgeTable">
                                                    <!-- table header start -->
                                                    <thead>
                                                        <tr class="thead-light">
                                                            <th class="nowrap">Sr. No.</th>
                                                            <th class="nowrap">Part Number</th>
                                                            <th class="nowrap">Bridge Kit Part Number</th>
                                                            <th class="nowrap">Serial Number</th>
                                                            <th class="nowrap">Transferred Order Number</th>
                                                            <th class="nowrap">Status</th>
                                                        </tr>
                                                    </thead>
                                                    <!-- table header end -->
                                                    <!-- table body start -->
                                                    <tbody>
                                                        <tr
                                                            *ngFor="let tranferUserBridge of transferOrderData?.sourceSalesOrder?.product?.bridges; let bridgeIndex = index">
                                                            <td class="nowrap">{{bridgeIndex + 1}}</td>
                                                            <td class="nowrap">
                                                                {{tranferUserBridge.productCode}}
                                                            </td>
                                                            <td class="nowrap">
                                                                {{tranferUserBridge.bridgeKitPartNumberCode}}</td>
                                                            <td class="nowrap">
                                                                {{tranferUserBridge.entitySerialNumber}}

                                                            <td class="nowrap">
                                                                {{tranferUserBridge.destinationSalesOrderNumber}}
                                                            <td class="nowrap">
                                                                {{tranferUserBridge?.entityStatus |
                                                                salesOrderStatusDisplay}}</td>
                                                        </tr>
                                                    </tbody>
                                                    <!-- table body start -->
                                                </table>
                                            </div>
                                            <!-- ------------------------------------------------------- -->
                                            <!-- ---------sourceSalesOrder Bridge table End-- ---------- -->
                                            <!-- ------------------------------------------------------- -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!---##############################################################-->
                    <!---##############################################################-->
                    <!---------------Destination SalesOrder Product--------------------->
                    <!---##############################################################-->
                    <!---##############################################################-->

                    <div class="row">
                        <div class="col-md-12">
                            <!-- main card start -->
                            <div class="card">
                                <div class="card-body">
                                    <div class="card shadow">
                                        <div class="card-body">
                                            <!-- ------------------------------------------------------- -->
                                            <!-- -------destinationSalesOrder Probe table start -------- -->
                                            <!-- ------------------------------------------------------- -->
                                            <div class="container-fluid ml-4 mr-4">
                                                <label class="mb-1 h5-tag"><span>Tranferred Sales Order -
                                                        {{transferOrderData?.destinationSalesOrder?.salesOrderNumber}} -
                                                        <span
                                                            [class.text-primary]="transferOrderData?.destinationSalesOrder?.countryDisplayName!==transferOrderData?.sourceSalesOrder?.countryDisplayName">
                                                            {{transferOrderData?.destinationSalesOrder?.countryDisplayName}}</span></span></label>
                                                <hr class="hrMargin">
                                                <div
                                                    *ngIf="transferOrderData?.destinationSalesOrder?.product?.probes!==null">
                                                    <label class=" mb-1 h5-tag"><span>Probe(s)</span></label>
                                                    <hr class="hrMargin">
                                                    <!-- ------------------------------------------------------- -->
                                                    <!-- table start -->
                                                    <!-- ------------------------------------------------------- -->
                                                    <!-------------------------------------->
                                                    <!---------------Total Probe(s)-------->
                                                    <div class="bottomMargin-5 table-responsive">Total
                                                        {{transferOrderData?.destinationSalesOrder?.product?.probes?.length}}
                                                        Transferred Order Probe(s)</div>
                                                    <div class="transferOrderReviewTable">
                                                        <table
                                                            class="table table-sm table-bordered mr-1 transferOrderReviewTable"
                                                            id="relatedProduct" aria-hidden="true">
                                                            <!-- table header start -->
                                                            <thead>
                                                                <tr class="thead-light">
                                                                    <th class="nowrap">Sr. No.</th>
                                                                    <th class="nowrap">Probe Type</th>
                                                                    <th class="nowrap">Part Number</th>
                                                                    <th class="nowrap">Bridge Kit Part Number</th>
                                                                    <th class="nowrap">OTS Kit Part Number</th>
                                                                    <th class="nowrap">Preset(s)</th>
                                                                    <th class="nowrap">Feature(s)</th>
                                                                    <th class="nowrap">Config Groups(s)</th>
                                                                    <th class="nowrap">Serial Number</th>
                                                                    <th class="nowrap"
                                                                        *ngIf="transferOrderData.destinationSalesOrderIsManual">
                                                                        Configure Licence</th>
                                                                    <th class="nowrap">Status</th>
                                                                    <th class="nowrap"><span>Error</span> /
                                                                        <span>Warning</span>
                                                                    </th>
                                                                    <th class="nowrap">
                                                                        Action
                                                                    </th>
                                                                </tr>
                                                            </thead>
                                                            <!-- table header end -->
                                                            <!-- table body start -->
                                                            <tbody>
                                                                <tr
                                                                    *ngFor="let endUserProbe of transferOrderData?.destinationSalesOrder?.product?.probes; let probeIndex = index">
                                                                    <td>{{probeIndex + 1}}</td>
                                                                    <td class="nowrap">{{endUserProbe.probeType}}</td>
                                                                    <td class="nowrap">
                                                                        {{endUserProbe.productCode}}</td>
                                                                    <td class="nowrap">
                                                                        {{endUserProbe.bridgeKitPartNumberCode}}</td>
                                                                    <td class="nowrap">
                                                                        {{endUserProbe.otsKitPartNumberCode}}</td>
                                                                    <td class="nowrap"
                                                                        [innerHTML]="endUserProbe.configMappingRequest?.presets| configLicenceValidityDisplay:bothSalesOrderManual">
                                                                    </td>
                                                                    <td class="nowrap"
                                                                        [innerHTML]="endUserProbe.configMappingRequest?.features| configLicenceValidityDisplay:bothSalesOrderManual">
                                                                    </td>
                                                                    <td class="nowrap">
                                                                        {{endUserProbe.probeConfigGroupPartNumberCodes}}
                                                                    </td>
                                                                    <td class="nowrap">
                                                                        {{endUserProbe.entitySerialNumber}} </td>
                                                                    <td class="nowrap text-center align-middle"
                                                                        *ngIf="transferOrderData.destinationSalesOrderIsManual"
                                                                        (click)="updateProbeFeatures(endUserProbe.configMappingRequest,endUserProbe.probeType,probeIndex)">
                                                                        <span class="fa fa-pencil-alt"
                                                                            style="font-size: 18px; color: #f67409;"></span>
                                                                    </td>
                                                                    <td class="nowrap">{{endUserProbe?.entityStatus
                                                                        |salesOrderStatusDisplay}}</td>
                                                                    <td class="nowrap">
                                                                        <ul class="ulList">
                                                                            <li [class]="errorTextColor"
                                                                                *ngFor="let error of endUserProbe.errorMessages">
                                                                                {{error}}</li>
                                                                            <li [class]="warningTextColor"
                                                                                *ngFor="let warning of endUserProbe.warningMessages">
                                                                                {{warning}}</li>
                                                                        </ul>
                                                                    </td>
                                                                    <td *ngIf="probeReadOnlyPermission">
                                                                        <div class="spanunderline"
                                                                            (click)="showProbeDetailPage(endUserProbe.entityPk)"
                                                                            id="salesorderDetailToDeviceDetail">
                                                                            View
                                                                            Details</div>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                            <!-- table body end -->
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- ------------------------------------------------------- -->
                                            <!-- -------destinationSalesOrder Probe table End ---------- -->
                                            <!-- ------------------------------------------------------- -->

                                            <!-- ------------------------------------------------------- -->
                                            <!-- ------destinationSalesOrder Bridge table start -------- -->
                                            <!-- ------------------------------------------------------- -->
                                            <div class="container-fuild ml-4 mr-4 px-3 mt-3"
                                                *ngIf="transferOrderData?.destinationSalesOrder?.product?.bridges !== null">
                                                <label class="mb-1 h5-tag"><span>Bridge(s)</span>
                                                </label>
                                                <hr class="hrMargin">
                                                <!-------------------------------------->
                                                <!-------------------------------------->
                                                <!-------------------------------------->
                                                <!---------------Total Bridge(s)-------->
                                                <div class="bottomMargin-5">Total
                                                    {{transferOrderData?.destinationSalesOrder?.product?.bridges?.length}}
                                                    Transferred Order Bridge(s)</div>
                                                <!-------------------------------------->
                                                <table class="table table-sm table-bordered" aria-hidden="true"
                                                    id="bridgeTable">
                                                    <!-- table header start -->
                                                    <thead>
                                                        <tr class="thead-light">
                                                            <th class="nowrap">Sr. No.</th>
                                                            <th class="nowrap">Part Number</th>
                                                            <th class="nowrap">Bridge Kit Part Number</th>
                                                            <th class="nowrap">Serial Number</th>
                                                            <th class="nowrap">Status</th>
                                                            <th class="nowrap"><span>Error</span> /
                                                                <span>Warning</span>
                                                            </th>
                                                            <th class="nowrap">Action
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <!-- table header end -->
                                                    <!-- table body start -->
                                                    <tbody>
                                                        <tr
                                                            *ngFor="let endUserBridge of transferOrderData?.destinationSalesOrder?.product?.bridges; let bridgeIndex = index">
                                                            <td class="nowrap">{{bridgeIndex + 1}}</td>
                                                            <td class="nowrap">{{endUserBridge.productCode}}
                                                            </td>
                                                            <td class="nowrap">
                                                                {{endUserBridge.bridgeKitPartNumberCode}}</td>
                                                            <td class="nowrap">
                                                                {{endUserBridge.entitySerialNumber}}
                                                            </td>
                                                            <td class="nowrap">{{endUserBridge?.entityStatus
                                                                |salesOrderStatusDisplay}}</td>
                                                            <td class="nowrap">
                                                                <ul class="ulList">
                                                                    <li [class]="errorTextColor"
                                                                        *ngFor="let error of endUserBridge.errorMessages">
                                                                        {{error}}</li>
                                                                    <li [class]="warningTextColor"
                                                                        *ngFor="let warning of endUserBridge.warningMessages">
                                                                        {{warning}}</li>
                                                                </ul>
                                                            </td>
                                                            <td *ngIf="deviceReadOnlyPermission">
                                                                <div class="spanunderline"
                                                                    (click)="showDeviceDetailPage(endUserBridge.entityPk)"
                                                                    id="salesorderDetailToDeviceDetail">
                                                                    View
                                                                    Details</div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                    <!-- table body start -->
                                                </table>
                                                <!-------------------------------------->
                                                <!-------------------------------------->
                                                <!-------------------------------------->
                                            </div>
                                            <!-- ------------------------------------------------------- -->
                                            <!-- -------destinationSalesOrder Bridge table end --------- -->
                                            <!-- ------------------------------------------------------- -->
                                        </div>
                                    </div>
                                </div>

                                <!----------  Transfer Order Button Section Start ------ -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="card">
                                            <div class="card-body">
                                                <div class="card shadow">
                                                    <div class="card-body p-2">
                                                        <div class="row mt-3">
                                                            <div class="col-md-8 col-sm-8"></div>
                                                            <div
                                                                class="col-md-4 col-sm-4 d-flex align-items-end justify-content-end">
                                                                <div class="multiprobe-div mr-3">
                                                                    <!-------------------------------------------->
                                                                    <!-- Transfer button Div start --------------->
                                                                    <button class="btn btn-sm btn-orange w-100 mb-3"
                                                                        style="text-align: center;"
                                                                        id="transferOrderReviewbtn"
                                                                        (click)="transferOrder(transferOrderData.warning)"
                                                                        [disabled]="transferOrderData?.error">
                                                                        Transfer
                                                                    </button>
                                                                </div>
                                                                <!-------------------------------------------->
                                                                <!-- Transfer button Div End ----------------->
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- main card end -->
                        </div>
                    </div>
                    <!-- Related Products row end -->
                </div>
            </div>
            <!------------------------------------------->
            <!-- row end -->
            <!------------------------------------------->
        </div>
        <!------------------------------------------->
        <!-- container fluid end -->
        <!------------------------------------------->
    </body>
</div>

<!-- Transfer Review Table -->

<!----------------------------------------------------------->
<!-----------Probe Detail Page Start------------------------->
<!----------------------------------------------------------->
<ng-template [ngIf]="probeDetailPageDiplay">
    <app-ots-probes-detail [probeId]="probeEntityId" [resource]="transferOrderResource"
        (showOtsProbe)="showTransferOrderReviewPage()"></app-ots-probes-detail>
</ng-template>
<!----------------------------------------------------------->
<!-----------Probe Detail Page End------------------------->
<!----------------------------------------------------------->

<!------------------------------------------------------------->
<!------------Device Detail Page Start------------------------->
<!------------------------------------------------------------->
<ng-template [ngIf]="deviceDetailPageDiplay">
    <app-device-detail [deviceIdInput]="bridgeEntityValue" [resource]="transferOrderResource"
        (showDevice)="showTransferOrderReviewPage()">
    </app-device-detail>
</ng-template>
<!------------------------------------------------------------>
<!------------Device Detail Page End--------------------------->
<!------------------------------------------------------------->