import { TransferOrderSelectionProductResponse } from "./TransferOrderSelectionProductResponse.model"
import { TransferOrderSerialNumber } from "./TransferOrderSerialNumber.model";

export class TransferOrderSelectionResponse {
    sourceSalesOrder: TransferOrderSelectionProductResponse;
    destinationSalesOrder: TransferOrderSelectionProductResponse;
    sourceSalesOrderBridgeSerialNumbers: Array<TransferOrderSerialNumber>;
    sourceSalesOrderProbeSerialNumbers: Map<string, Array<TransferOrderSerialNumber>>;
    sourceSalesOrderIsManual: boolean;
    destinationSalesOrderIsManual: boolean;

    constructor(
        sourceSalesOrder: TransferOrderSelectionProductResponse,
        destinationSalesOrder: TransferOrderSelectionProductResponse,
        sourceSalesOrderBridgeSerialNumbers: Array<TransferOrderSerialNumber>,
        sourceSalesOrderProbeSerialNumbers: Map<string, Array<TransferOrderSerialNumber>>,
        sourceSalesOrderIsManual: boolean,
        destinationSalesOrderIsManual: boolean
    ) {
        this.sourceSalesOrder = sourceSalesOrder;
        this.destinationSalesOrder = destinationSalesOrder;
        this.sourceSalesOrderBridgeSerialNumbers = sourceSalesOrderBridgeSerialNumbers;
        this.sourceSalesOrderProbeSerialNumbers = sourceSalesOrderProbeSerialNumbers;
        this.sourceSalesOrderIsManual = sourceSalesOrderIsManual;
        this.destinationSalesOrderIsManual = destinationSalesOrderIsManual;
    }
}
