import { Component, OnInit } from '@angular/core';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { TabActiveEnum } from 'src/app/shared/enum/Tab/tabActiveEnum.enum';
import { PermissionService } from 'src/app/shared/permission.service';

@Component({
  selector: 'app-tab',
  templateUrl: './tab.component.html',
  styleUrl: './tab.component.css'
})
export class TabComponent implements OnInit {
  //Active Tab
  tabActiveEnum = TabActiveEnum;
  tabActiveObject = this.setActiveTab(null);
  noDataDisplay: boolean = false;

  //Permission Bind
  deviceRederPermission: boolean = false;
  probeReaderPermission: boolean = false;
  jobReaderPermission: boolean = false;
  softwareBuildReaderPermission: boolean = false;
  logReaderPermission: boolean = false;
  userReaderPermission: boolean = false;
  videoReaderPermission: boolean = false;
  roleReaderPermission: boolean = false;
  salesOrderReaderPermission: boolean = false;
  kitManagementReaderPermission: boolean = false;
  countryReaderPermission: boolean = false;
  auditReaderPermission: boolean = false;
  probeConfigGroupPermission: boolean = false;

  constructor(
    private permissionService: PermissionService,) { }

  /**
  * <AUTHOR>
  * OnInit method.
  */
  public ngOnInit(): void {
    this.deviceRederPermission = this.permissionService.getDevicePermission(PermissionAction.GET_DEVICE_ACTION);
    this.probeReaderPermission = this.permissionService.getProbPermission(PermissionAction.GET_PROB_ACTION);
    this.jobReaderPermission = this.permissionService.getJobPermission(PermissionAction.GET_JOB_ACTION);
    this.softwareBuildReaderPermission = this.permissionService.getSoftwearBuildPermission(PermissionAction.GET_SOFTWARE_BUILD_ACTION);
    this.logReaderPermission = this.permissionService.getDeviceLogPermission(PermissionAction.GET_DEVICE_LOG_ACTION);
    this.userReaderPermission = this.permissionService.getUserPermission(PermissionAction.GET_USER_ACTION);
    this.videoReaderPermission = this.permissionService.getVideoPermission(PermissionAction.GET_VIDEO_ACTION);
    this.roleReaderPermission = this.permissionService.getRolePermission(PermissionAction.GET_ROLE_ACTION);
    this.kitManagementReaderPermission = this.permissionService.getKitManagementPermission(PermissionAction.KIT_MANAGEMANT_TAB_ACTION);
    this.salesOrderReaderPermission = this.permissionService.getSalesOrderPermission(PermissionAction.GET_SALES_ORDER_ACTION);
    this.countryReaderPermission = this.permissionService.getCountryPermission(PermissionAction.GET_COUNTRY_LIST_ACTION);
    this.auditReaderPermission = this.permissionService.getAuditPermission(PermissionAction.GET_AUDIT_ACTION);
    this.probeConfigGroupPermission = this.permissionService.getProbeConfigGroupPermission(PermissionAction.GET_CONFIG_GROUP_ACTION);
    this.defaultTabActive();

  }

  /**
  * <AUTHOR>
  * Change tab to active tab.
  * @param tabName Tab name to be active.
  */
  public changeTab(tabName: TabActiveEnum): void {
    this.tabActiveObject = this.setActiveTab(tabName);
  }

  /**
  * <AUTHOR>
  * Set active tab.
  * @param activeTabName Tab name to be active.
  * @returns Object with tab names as keys and boolean values indicating active status.
  */
  private setActiveTab(activeTabName: TabActiveEnum) {
    let tabActiveObject = {};
    for (let tabName in TabActiveEnum) {
      if (tabName == activeTabName) {
        tabActiveObject[TabActiveEnum[tabName]] = true;
      } else {
        tabActiveObject[TabActiveEnum[tabName]] = false;
      }
    }
    return tabActiveObject;
  }

  /**
  * <AUTHOR>
  * Set default tab active based on user permissions.
  */
  public defaultTabActive(): void {
    const permissionsMap = this.prepareTabPermissionMap();
    let tabName = null;

    // Iterating over the Map entries
    for (const [tab, permission] of permissionsMap) {
      if (permission) {
        tabName = tab;
        break;
      }
    }
    this.noDataDisplay = tabName === null;
    this.changeTab(tabName);
  }

  /**
  * <AUTHOR>
  * Prepare tab permission map.
  * @returns Map with tab names as keys and boolean values indicating permission status.
  */
  public prepareTabPermissionMap(): Map<string, boolean> {
    const permissionsMap = new Map<string, boolean>();

    // Adding entries to the Map
    permissionsMap.set(TabActiveEnum.deviceTab_Active, this.deviceRederPermission);
    permissionsMap.set(TabActiveEnum.probeTab_Active, this.probeReaderPermission);
    permissionsMap.set(TabActiveEnum.jobTab_Active, this.jobReaderPermission);
    permissionsMap.set(TabActiveEnum.itemInventoryTab_Active, this.softwareBuildReaderPermission);
    permissionsMap.set(TabActiveEnum.logTab_Active, this.logReaderPermission);
    permissionsMap.set(TabActiveEnum.userTab_Active, this.userReaderPermission);
    permissionsMap.set(TabActiveEnum.videoTab_Active, this.videoReaderPermission);
    permissionsMap.set(TabActiveEnum.roleTab_Active, this.roleReaderPermission);
    permissionsMap.set(TabActiveEnum.salesOrderTab_Active, this.salesOrderReaderPermission);
    permissionsMap.set(TabActiveEnum.kitManagementTab_Active, this.kitManagementReaderPermission);
    permissionsMap.set(TabActiveEnum.countryTab_Active, this.countryReaderPermission);
    permissionsMap.set(TabActiveEnum.auditTab_Active, this.auditReaderPermission);
    permissionsMap.set(TabActiveEnum.probeConfigGroupTab_Active, this.probeConfigGroupPermission);
    return permissionsMap;
  }
}