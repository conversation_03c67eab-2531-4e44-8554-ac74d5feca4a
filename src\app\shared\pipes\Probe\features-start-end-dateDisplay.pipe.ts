import { DatePipe } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';
import { DateDisplayFormat } from 'src/app/app.constants';
import { isNullOrUndefined } from 'is-what';
import { EndDateOptions } from '../../enum/endDateOptions.enum';
import { CommonsService } from '../../util/commons.service';

@Pipe({
    name: 'featuresStartEndDateDisplay'
})
export class FeaturesStartEndDateDisplay implements PipeTransform {
    unknown: string = "Unknown";

    constructor(private datePipe: DatePipe,
        private commonsService: CommonsService) { }

    transform(dateValue: number, isStartDate: boolean): string {
        if (!isNullOrUndefined(dateValue)) {
            if (isStartDate) {
                return (dateValue != -1) ? this.datePipe.transform(dateValue, DateDisplayFormat) : this.unknown;
            } else {
                return (dateValue != -1) ? this.datePipe.transform(this.commonsService.getUTCDateForDisplay(dateValue), DateDisplayFormat) : EndDateOptions.UNLIMITED;
            }
        }
        return null;
    }

}
