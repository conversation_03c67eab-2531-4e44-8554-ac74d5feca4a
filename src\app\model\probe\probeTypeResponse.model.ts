import { ProbeFeatureResponse } from "./ProbeFeatureResponse.model";
import { ProbePresetResponse } from "./ProbePresetResponse.model";

export class ProbeTypeResponse {
	probeTypeId: number;
	name: string;
	displayName: string;
	prefix: string;
	features: Array<ProbeFeatureResponse>;
	presets: Array<ProbePresetResponse>;

	constructor($probeTypeId: number, $name: string, $displayName: string, $prefix: string, $features: Array<ProbeFeatureResponse>, $presets: Array<ProbePresetResponse>) {
		this.probeTypeId = $probeTypeId;
		this.name = $name;
		this.displayName = $displayName;
		this.prefix = $prefix;
		this.features = $features;
		this.presets = $presets;
	}

}