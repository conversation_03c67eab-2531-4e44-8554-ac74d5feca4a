export class CreateAndUpdateRoleModelRequest {
	title: string;
	okButton: string;
	cancelButton: string;
	isUpdateData: boolean;
	roleId: number;
	resourseName: string;
	isFilterHidden: boolean;



	constructor($title: string, $okButton: string, $cancelButton: string, $isUpdateData: boolean, $roleId: number, $resourseName: string, $isFilterHidden: boolean) {
		this.title = $title;
		this.okButton = $okButton;
		this.cancelButton = $cancelButton;
		this.isUpdateData = $isUpdateData;
		this.roleId = $roleId;
		this.resourseName = $resourseName;
		this.isFilterHidden = $isFilterHidden;
	}

}