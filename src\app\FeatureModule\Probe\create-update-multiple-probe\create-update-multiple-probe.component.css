@import '../../../../assets/css/custom_style.css';

.probe-table {
    overflow-x: auto;
}

.probe-table th,
.probe-table td {
    min-width: 100px;
    text-align: center;
}

.probe-table .width-unset {
    min-width: auto;
}

.probe-table th {
    vertical-align: middle;
    text-align: center;
    white-space: nowrap;
}

.probe-table .col_button {
    min-width: unset;
    width: 40px;
}

.probe-table .col_button button {
    width: 40px;
}

/*------------------*/
.probe-table .clildTable {
    height: 40px;
    width: 100%;
}

.probe-table .clildTable th:first-child {
    border-left: 0px;
}

.probe-table .clildTable th:last-child {
    border-right: 0px;
    border-bottom: 0px;
}

.probe-table .clildTable td {
    border-left: 0px;
    border-top: 0px;
    border-bottom: 0px;
}


.probe-table .clildTable td:last-child {
    border: none;
}

.editIcon,
.deleteIcon {
    color: #f46c26;
}


/*Plus button*/
#addProbeFeature .sales-order-control {
    float: left;
    width: calc(100% - 40px);
}

#addProbeFeature .radius-50 {
    border-radius: 50%;
}

.form-group .form-err {
    font-size: 13px;
    color: red;
}

#addProbeFeature .ponumber {
    width: 100%;
}

#addProbeFeature .salesforceMessage {
    padding: 0 5px;
}

#manualSalesOrder .manual-sales-order-control {
    display: none;
}

#ConfigureFeatures .col_border {
    border: 1px solid #c9c9c9;
}

#ConfigureFeatures .middleText {
    flex-direction: column;
    display: flex;
    justify-content: center;
}

#addProbeFeature .checboxLineHeight {
    line-height: 24px;
}

#addProbeFeature .radioButtonLineHeight {
    line-height: 22px;
    white-space: nowrap;
}

.probe-table button[disabled] {
    cursor: default;
    background: none;
}

.multiprobe-div {
    display: flex;
    justify-content: right;
}

.multiprobe-div button {
    text-align: center;
    width: 80px;
}

.form_dropdown {
    height: 38px;
    padding-top: 6px;
    padding-bottom: 6px;
}

#addProbeFeature .deviceAutoLock {
    margin-left: 3.5rem;
    width: 1.5rem;
    height: 1.5rem;
}