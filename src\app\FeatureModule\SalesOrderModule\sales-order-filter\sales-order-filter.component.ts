import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { isUndefined } from 'is-what';
import { forkJoin, Subscription } from 'rxjs';
import { SPECIAL_CHARACTER_PATTERN, SPECIAL_CHARACTER_ERROR_MESSAGE, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH, MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, MAXIMUM_TEXTBOX_LENGTH } from 'src/app/app.constants';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';
import { SalesOrderFilterAction } from 'src/app/model/SalesOrder/SalesFilterAction.model';
import { SalesOrderSearchRequestBody } from 'src/app/model/SalesOrder/SalesOrderSearchRequestBody.model';
import { EnumMapping } from 'src/app/model/common/EnumMapping.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { SalesOrderService } from 'src/app/shared/Service/SalesOrderService/sales-order.service';
import { ProductConfigStatus } from 'src/app/shared/enum/SalesOrder/ProductConfigStatus.enum';
import { SalesOrderTypeStatus } from 'src/app/shared/enum/SalesOrder/SalesOrderTypeStatus.enum';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';

@Component({
  selector: 'app-sales-order-filter',
  templateUrl: './sales-order-filter.component.html',
  styleUrls: ['./sales-order-filter.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class SalesOrderFilterComponent implements OnInit {

  @Input("isFilterComponentInitWithApicall") isFilterComponentInitWithApicall: boolean;
  @Input("listPageRefreshForbackToDetailPage") listPageRefreshForbackToDetailPage: boolean;
  @Input("salesOrderSearchRequestBody") salesOrderSearchRequestBody: SalesOrderSearchRequestBody;

  orderTypeSetting: MultiSelectDropdownSettings = null;
  countrySetting: MultiSelectDropdownSettings = null;
  productConfigStatusListSetting: MultiSelectDropdownSettings = null;
  orderRecordTypeSetting: MultiSelectDropdownSettings = null;

  orderTypeList: any[] = [];
  orderRecordTypeList: any[] = [];
  countryList: Array<CountryListResponse> = [];
  productConfigStatusList: Array<EnumMapping> = [];

  //MaxLength Message
  small_textBoxMaxLengthMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  textBoxMaxLengthMessage: string = MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;


  filterSalesOrderForm = new FormGroup({
    salesOrderNumber: new FormControl('', [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    customerName: new FormControl('', [Validators.maxLength(MAXIMUM_TEXTBOX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    customerEmail: new FormControl('', [Validators.maxLength(MAXIMUM_TEXTBOX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    countries: new FormControl([], []),
    orderType: new FormControl([], []),
    po: new FormControl('', [Validators.maxLength(MAXIMUM_TEXTBOX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    productConfigStatus: new FormControl([], []),
    orderRecordType: new FormControl([], [])
  });

  subscriptionForRefeshList: Subscription;

  defaultListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

  constructor(
    private salesOrderService: SalesOrderService,
    private commonsService: CommonsService,
    private commonOperationsService: CommonOperationsService,
    private countryCacheService: CountryCacheService,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private salesOrderApiCallService: SalesOrderApiCallService,
    private exceptionService: ExceptionHandlingService) { }

  public ngOnInit(): void {
    this.orderTypeSetting = this.multiSelectDropDownSettingService.getSalesOrderTypeDrpSetting();
    this.orderRecordTypeSetting = this.multiSelectDropDownSettingService.getOrderRecordTypeDrpSetting();
    this.countrySetting = this.multiSelectDropDownSettingService.getCountryDrpSetting(false, false);
    this.productConfigStatusListSetting = this.multiSelectDropDownSettingService.getProductConfigStatusDrpSetting();
    this.onInitSubject();
    this.getFilterList();
    if (this.isFilterComponentInitWithApicall) {
      this.clearFilter(this.defaultListingPageReloadSubjectParameter);
    }
  }

  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForRefeshList)) { this.subscriptionForRefeshList.unsubscribe() }
  }

  /**
   * Get Filter List
   * 
   * <AUTHOR>
   */
  public async getFilterList(): Promise<void> {
    this.initApiCall();
    this.orderTypeList = this.commonsService.getEnumToList(SalesOrderTypeStatus);
    this.productConfigStatusList = this.commonsService.getEnumMappingListFilter(this.commonsService.getEnumToList(ProductConfigStatus), [ProductConfigStatus.RMA, ProductConfigStatus.IN_PROGRESS]);
    this.setFilterValue();
  }

  /**
  * Init Api Call
  *
  * <AUTHOR>
  */
  public initApiCall(): void {
    // Only call API if service cache is empty (first time mount)
    if (this.salesOrderService.getCountryList().length === 0 || this.salesOrderService.getOrderRecordTypeList().length === 0) {
      forkJoin({
        orderRecordTypeList: this.salesOrderApiCallService.getOrderRecordNumberList(false),
        countryList: this.countryCacheService.getCountryListFromCache()
      }).subscribe({
        next: ({ orderRecordTypeList, countryList }) => {
          this.orderRecordTypeList = orderRecordTypeList;
          this.countryList = countryList;
          this.salesOrderService.setOrderRecordTypeList(this.orderRecordTypeList);
          this.salesOrderService.setCountryList(this.countryList);
          this.setFilterValue();
        },
        error: (error) => {
          this.exceptionService.customErrorMessage(error);
        }
      })
    } else {
      // Use cached data from service
      this.orderRecordTypeList = this.salesOrderService.getOrderRecordTypeList();
      this.countryList = this.salesOrderService.getCountryList();
      this.setFilterValue();
    }
  }

  /**
   * Set Filter value 
   * 
   * Note : if user hide and show filter then set data in storage
   * 
   * <AUTHOR>
   */
  private setFilterValue() {
    if (this.salesOrderSearchRequestBody != null) {
      let salesOrderType = this.commonsService.getEnumMappingSelectedValue(SalesOrderTypeStatus, [this.salesOrderSearchRequestBody.orderType]);
      let productConfigStatusValue = this.commonsService.getEnumMappingSelectedValue(ProductConfigStatus, this.salesOrderSearchRequestBody.productConfigStatus);
      this.filterSalesOrderForm.get('salesOrderNumber').setValue(this.salesOrderSearchRequestBody.salesOrderNumber);
      this.filterSalesOrderForm.get('customerName').setValue(this.salesOrderSearchRequestBody.customerName);
      this.filterSalesOrderForm.get('customerEmail').setValue(this.salesOrderSearchRequestBody.customerEmail);
      this.filterSalesOrderForm.get('countries').setValue(this.commonsService.getDropDownValue(this.countryList, this.salesOrderSearchRequestBody.countryIds));
      this.filterSalesOrderForm.get('orderType').setValue(salesOrderType);
      this.filterSalesOrderForm.get('po').setValue(this.salesOrderSearchRequestBody.poNumber);
      this.filterSalesOrderForm.get('productConfigStatus').setValue(productConfigStatusValue);
      this.filterSalesOrderForm.get('orderRecordType').setValue(this.salesOrderSearchRequestBody.orderRecordType);
    }
    if (this.listPageRefreshForbackToDetailPage) {
      this.salesOrderListPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }


  public onInitSubject(): void {
    /**
     * Sales Order Detail Page Refresh After some Action Like Create or Update or Delete Sales Order
     * <AUTHOR>
     */
    this.subscriptionForRefeshList = this.salesOrderService.getSalesOrderListRefreshSubject().subscribe((listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter) => {
      if (listingPageReloadSubjectParameter.isReloadData) {
        if (listingPageReloadSubjectParameter.isClearFilter) {
          this.clearFilter(listingPageReloadSubjectParameter);
        } else {
          //page change 1,2,3
          this.salesOrderListPageRefresh(listingPageReloadSubjectParameter);
        }
      }
    });
  }


  /**
   * Reload Listing Data
   * <AUTHOR>
   */
  public searchData(): void {
    let allFormValue = this.filterSalesOrderForm.value;
    this.filterSalesOrderForm.get('salesOrderNumber').setValue(this.commonsService.checkNullFieldValue(allFormValue.salesOrderNumber));
    this.filterSalesOrderForm.get('customerName').setValue(this.commonsService.checkNullFieldValue(allFormValue.customerName));
    this.filterSalesOrderForm.get('customerEmail').setValue(this.commonsService.checkNullFieldValue(allFormValue.customerEmail));
    this.filterSalesOrderForm.get('po').setValue(this.commonsService.checkNullFieldValue(allFormValue.po));

    if (this.filterSalesOrderForm.invalid || (this.commonsService.checkValueIsNullOrEmpty(allFormValue.salesOrderNumber) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.customerName) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.customerEmail) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.countries) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.orderType) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.po) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.productConfigStatus) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.orderRecordType)
    )) {
      this.commonOperationsService.showEmptyFilterTosteMessge();
    } else {
      this.salesOrderListPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }

  /**
   * Clear All filter and Reload Data
   * <AUTHOR>
   */
  public clearFilter(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    this.filterSalesOrderForm.get('salesOrderNumber').setValue("");
    this.filterSalesOrderForm.get('customerName').setValue("");
    this.filterSalesOrderForm.get('customerEmail').setValue("");
    this.filterSalesOrderForm.get('countries').setValue([]);
    this.filterSalesOrderForm.get('orderType').setValue([]);
    this.filterSalesOrderForm.get('po').setValue("");
    this.filterSalesOrderForm.get('productConfigStatus').setValue([]);
    this.filterSalesOrderForm.get('orderRecordType').setValue([]);
    this.salesOrderListPageRefresh(listingPageReloadSubjectParameter);
  }

  /**
   * Get Filter Data and pass to Listing page and Reload Page 
   * <AUTHOR>
   */
  private salesOrderListPageRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    if (this.filterSalesOrderForm.invalid) {
      this.filterSalesOrderForm.reset();
    }
    let allFormValue = this.filterSalesOrderForm.value;
    let countryIdList = this.commonsService.getIdsFromArray(allFormValue.countries);
    let salesOrderType = this.commonsService.getSelectedValueFromEnum(allFormValue.orderType);
    let productConfigStatus = this.commonsService.getSelectedValueFromEnum(allFormValue.productConfigStatus);
    let orderRecordType = this.commonsService.checkNullFieldValue(allFormValue.orderRecordType);

    let saleOrderRequestBody = new SalesOrderSearchRequestBody(
      this.commonsService.checkNullFieldValue(allFormValue.salesOrderNumber),
      this.commonsService.checkNullFieldValue(allFormValue.customerName),
      this.commonsService.checkNullFieldValue(allFormValue.customerEmail),
      countryIdList,
      this.commonsService.checkNullFieldValue(allFormValue.po),
      (salesOrderType.length == 1) ? salesOrderType[0] : null,
      orderRecordType,
      productConfigStatus
    );
    let salesOrderFilterAction = new SalesOrderFilterAction(listingPageReloadSubjectParameter, saleOrderRequestBody);
    this.salesOrderService.callSalesOrderListFilterRequestParameterSubject(salesOrderFilterAction);
  }

}
