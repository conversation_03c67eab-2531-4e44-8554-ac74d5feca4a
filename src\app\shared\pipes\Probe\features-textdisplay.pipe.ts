import { Pipe, PipeTransform } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ConfigBaseMappingRequest } from 'src/app/model/probe/ConfigBaseMappingRequest.model';
import { PrintListPipe } from '../printList.pipe';

@Pipe({
    name: 'featuresTextDisplayPipe'
})
export class FeaturesTextDisplayPipe implements PipeTransform {

    constructor(private printListPipe: PrintListPipe) { }

    transform(features: Array<ConfigBaseMappingRequest>): string {
        if (!isNullOrUndefined(features)) {
            let featureName = [];
            let enableFeature = features.filter(obj => obj.enable);
            for (let feature of enableFeature) {
                let value = feature.name + " (" + feature.endDateUi + ")";
                featureName.push(value);
            }
            return this.printListPipe.transform(featureName);
        }
        return "";
    }

}
