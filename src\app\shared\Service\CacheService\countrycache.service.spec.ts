import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { LocalStorageService } from 'ngx-webstorage';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { CommonsService } from '../../util/commons.service';
import { CountryCacheService } from './countrycache.service';

describe('CountryCacheService', () => {
  let service: CountryCacheService;
  let httpMock: HttpTestingController;
  let mockLocalStorageService: jasmine.SpyObj<LocalStorageService>;
  let mockCommonsService: CommonsService;
  let mockExceptionService: jasmine.SpyObj<ExceptionHandlingService>;
  let mockConfigInjectService: jasmine.SpyObj<ConfigInjectService>;

  beforeEach(() => {
    mockLocalStorageService = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    mockExceptionService = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    mockConfigInjectService = jasmine.createSpyObj('ConfigInjectService', ['getServerApiUrl']);

    mockConfigInjectService.getServerApiUrl.and.returnValue('http://localhost/');

    TestBed.configureTestingModule({
      imports: [],
      providers: [
        CountryCacheService,
        { provide: LocalStorageService, useValue: mockLocalStorageService },
        CommonsService,
        { provide: ExceptionHandlingService, useValue: mockExceptionService },
        { provide: ConfigInjectService, useValue: mockConfigInjectService },
        provideHttpClient(withInterceptorsFromDi()),
        provideHttpClientTesting()
      ]
    });

    service = TestBed.inject(CountryCacheService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('getCountryListFromCache should return list and append notAssociatedCountry if flag is true', async () => {
    const mockCountryList: CountryListResponse[] = [{ country: 'IN' } as CountryListResponse];
    const notAssociatedObj = null;

    const promise = service.getCountryListFromCache(true);

    const req = httpMock.expectOne('http://localhost/api/country');
    expect(req.request.method).toBe('GET');
    req.flush(mockCountryList);

    const result = await promise;
    expect(result.length).toBe(2);
  });

  it('getCountryListFromCache should return list as-is if notAssociatedCountryOptionAdd is false', async () => {
    const mockCountryList: CountryListResponse[] = [{ country: 'IN' } as CountryListResponse];

    const promise = service.getCountryListFromCache(false);

    const req = httpMock.expectOne('http://localhost/api/country');
    expect(req.request.method).toBe('GET');
    req.flush(mockCountryList);

    const result = await promise;
    expect(result).toEqual(mockCountryList);
  });

  it('getCountryList should return empty array on HTTP error', async () => {
    const promise = service['getCountryList']();
    const req = httpMock.expectOne('http://localhost/api/country');
    req.flush({}, { status: 500, statusText: 'Internal Server Error' });

    const result = await promise;
    expect(result).toEqual([]);
    expect(mockExceptionService.customErrorMessage).toHaveBeenCalled();
  });

  describe('filterOutUserAssociatedCountries', () => {
    it('should return filtered countries if associated list exists', () => {
      const countryList: CountryListResponse[] = [
        { country: 'US' } as CountryListResponse,
        { country: 'IN' } as CountryListResponse,
        { country: 'FR' } as CountryListResponse
      ];
      const rdmUserAssociatedCountries = [{ country: 'US' } as CountryListResponse, { country: 'FR' } as CountryListResponse];
      mockLocalStorageService.retrieve.and.returnValue(rdmUserAssociatedCountries);

      const result = service.filterOutUserAssociatedCountries(countryList);
      expect(result).toEqual([
        { country: 'US' } as CountryListResponse,
        { country: 'FR' } as CountryListResponse
      ]);
    });

    it('should return empty array if associated countries is empty array', () => {
      const countryList: CountryListResponse[] = [{ country: 'US' } as CountryListResponse];
      mockLocalStorageService.retrieve.and.returnValue([]);

      const result = service.filterOutUserAssociatedCountries(countryList);
      expect(result).toEqual([]);
    });

    it('should return empty array if associated countries is null', () => {
      const countryList: CountryListResponse[] = [{ country: 'US' } as CountryListResponse];
      mockLocalStorageService.retrieve.and.returnValue(null);

      const result = service.filterOutUserAssociatedCountries(countryList);
      expect(result).toEqual([]);
    });

    it('should return empty array if countryList is null', () => {
      mockLocalStorageService.retrieve.and.returnValue([{ country: 'US' }]);

      const result = service.filterOutUserAssociatedCountries(null);
      expect(result).toEqual([]);
    });
  });

});
