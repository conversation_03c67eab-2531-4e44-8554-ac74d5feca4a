import { ListingPageReloadSubjectParameter } from "../common/listingPageReloadSubjectParameter.model";
import { SalesOrderSearchRequestBody } from "./SalesOrderSearchRequestBody.model";

export class SalesOrderFilterAction {
    listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter;
    salesOrderSearchRequestBody: SalesOrderSearchRequestBody;


    constructor($listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, $salesOrderRequestBody: SalesOrderSearchRequestBody) {
        this.listingPageReloadSubjectParameter = $listingPageReloadSubjectParameter;
        this.salesOrderSearchRequestBody = $salesOrderRequestBody;
    }

}