import { TestBed } from '@angular/core/testing';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { ConfigService, ConfigFactory } from './config';

describe('ConfigService', () => {
    let service: ConfigService;

    class MockXMLHttpRequest {
        public status: number = 200;
        public responseText: string = '{"API_BASE_URL":"http://localhost:8080"}';
        public opened = false;
        public sent = false;
        public mimeType: string | null = null;

        open(method: string, url: string, async: boolean): void {
            this.opened = true;
        }

        overrideMimeType(mimeType: string): void {
            this.mimeType = mimeType;
        }

        send(): void {
            this.sent = true;
        }
    }

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                ConfigService,
                provideHttpClient(withInterceptorsFromDi()),
                provideHttpClientTesting()
            ]
        });

        service = TestBed.inject(ConfigService);
    });

    afterEach(() => {
        delete (window as any).XMLHttpRequest;
    });

    describe('loadTextFileAjaxSync', () => {
        it('should return responseText when status is 200 and mimeType is set', () => {
            const mockXHR = new MockXMLHttpRequest();
            spyOn(mockXHR, 'overrideMimeType').and.callThrough();
            spyOn(mockXHR, 'open').and.callThrough();
            spyOn(mockXHR, 'send').and.callThrough();

            (window as any).XMLHttpRequest = function () {
                return mockXHR;
            };

            const result = service.loadTextFileAjaxSync('assets/config.json', 'application/json');

            expect(result).toBe(mockXHR.responseText);
            expect(mockXHR.overrideMimeType).toHaveBeenCalledWith('application/json');
            expect(mockXHR.open).toHaveBeenCalledWith('GET', 'assets/config.json', false);
            expect(mockXHR.send).toHaveBeenCalled();
        });

        it('should return responseText when mimeType is null', () => {
            const mockXHR = new MockXMLHttpRequest();
            spyOn(mockXHR, 'open').and.callThrough();
            spyOn(mockXHR, 'send').and.callThrough();

            (window as any).XMLHttpRequest = function () {
                return mockXHR;
            };

            const result = service.loadTextFileAjaxSync('file.json', null);

            expect(result).toBe(mockXHR.responseText);
            expect(mockXHR.open).toHaveBeenCalled();
            expect(mockXHR.send).toHaveBeenCalled();
        });

        it('should return null when status is not 200', () => {
            class ErrorMockXHR extends MockXMLHttpRequest {
                override status = 404;
            }

            (window as any).XMLHttpRequest = function () {
                return new ErrorMockXHR();
            };

            const result = service.loadTextFileAjaxSync('not-found.json', 'application/json');
            expect(result).toBeNull();
        });
    });

    describe('loadJSON', () => {
        it('should parse JSON and return the object', () => {
            spyOn(service, 'loadTextFileAjaxSync').and.returnValue('{"key":"value"}');
            const result = service.loadJSON('file.json');
            expect(result).toEqual({ key: 'value' });
        });

        it('should throw error if JSON is invalid', () => {
            spyOn(service, 'loadTextFileAjaxSync').and.returnValue('invalid-json');
            expect(() => service.loadJSON('file.json')).toThrowError();
        });
    });
});

describe('ConfigFactory', () => {
    let mockService: jasmine.SpyObj<ConfigService>;

    beforeEach(() => {
        mockService = jasmine.createSpyObj('ConfigService', ['loadJSON']);
    });

    it('should return the property value from loaded JSON', () => {
        mockService.loadJSON.and.returnValue({
            API_BASE_URL: 'http://localhost:8080',
            RDM_ENVIRONMENT: 'DEV'
        });

        const value = ConfigFactory(mockService, 'config.json', 'API_BASE_URL');

        expect(mockService.loadJSON).toHaveBeenCalledWith('config.json');
        expect(value).toBe('http://localhost:8080');
    });

    it('should return undefined if the property is not found', () => {
        mockService.loadJSON.and.returnValue({});

        const value = ConfigFactory(mockService, 'config.json', 'UNKNOWN_KEY');

        expect(value).toBeUndefined();
    });
});
