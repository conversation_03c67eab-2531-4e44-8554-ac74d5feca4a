import { BaseResponse } from "../../common/BaseResponse.model";

export class MultipleProbeResponse extends BaseResponse {
    probeId: number;
    serialNumber: string;
    errorMessage: string | null;

    constructor($id: number, $name: string, $displayName: string, probeId: number, serialNumber: string, errorMessage: string | null) {
        super($id, $name, $displayName);
        this.probeId = probeId;
        this.serialNumber = serialNumber;
        this.errorMessage = errorMessage;
    }
}