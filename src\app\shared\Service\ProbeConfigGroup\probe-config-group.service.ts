import { Injectable } from "@angular/core";
import { isNullOrUndefined } from "is-what";
import { Subject } from "rxjs";
import { ListProbeFearureGroupResource, } from "src/app/app.constants";
import { ListingPageReloadSubjectParameter } from "src/app/model/common/listingPageReloadSubjectParameter.model";
import { PresetDetailBaseResponse } from "src/app/model/Presets/PresetDetailBaseResponse.model";
import { FeaturesFilter } from "src/app/model/probe/FeaturesFilter.model";
import { ProbeConfigGroupFilterAction } from "src/app/model/ProbeConfigGroup/ProbeConfigGroupFilterAction.model";
import { ProbeConfigGroupRequestBody } from "src/app/model/ProbeConfigGroup/ProbeConfigGroupRequestBody.model";

@Injectable({
  providedIn: "root",
})
export class ProbeConfigGroupService {

  /**
  * Country List
  */
  private featuresList: Array<FeaturesFilter> = [];

  /**
* Country List
*/
  private presetsList: Array<PresetDetailBaseResponse> = [];



  //Probe Config Group list filter
  private probeConfigGroupListFilterRequestParameterSubject = new Subject<ProbeConfigGroupFilterAction>();

  //Refresh Probe Config Group  List
  private probeConfigGroupListRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();


  /**
   *  Probe config Group Page Refresh After some Action
   * <AUTHOR>
   * @returns ListingPageReloadSubjectParameter
   */
  public getProbeConfigGroupListRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.probeConfigGroupListRefreshSubject;
  }


  /**
   * Probe Config Group List Page Refresh After some Action Like Serch parameter add
   * Note : Clear All filter and refresh page
   * <AUTHOR>
   * @returns ProbeConfigGrouprList
   */
  public getProbeConfigGroupListFilterRequestParameterSubject(): Subject<ProbeConfigGroupFilterAction> {
    return this.probeConfigGroupListFilterRequestParameterSubject;
  }

  public callProbeConfigGroupListFilterRequestParameterSubject(probeConfigGroupFilterAction: ProbeConfigGroupFilterAction): void {
    this.probeConfigGroupListFilterRequestParameterSubject.next(probeConfigGroupFilterAction);
  }

  /**
   * This function call the subject for reload the page data
   *  Note : (ListProbeConfigGroupResource) -> Filter page subject call -> Listing page subject call
   * clear all filter after page data Reload
   * <AUTHOR>
   * @param isReloadData -> false means move to prev page for DetailProbeConfigGroupResource.
   * @param resourceName
   */
  public callRefreshPageSubject(
    listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter,
    resourceName: string,
    isFilterHidden: boolean, probeConfigGroupRequestBodyApply: ProbeConfigGroupRequestBody
  ): void {
    if (resourceName == ListProbeFearureGroupResource) {
      if (isFilterHidden) {
        let probeConfigGroupRequestBody = new ProbeConfigGroupRequestBody(null, [], [], []);
        if (!isNullOrUndefined(probeConfigGroupRequestBodyApply) && !listingPageReloadSubjectParameter.isClearFilter) {
          probeConfigGroupRequestBody = probeConfigGroupRequestBodyApply;
        }
        let probeConfigGroupFilterAction = new ProbeConfigGroupFilterAction(listingPageReloadSubjectParameter, probeConfigGroupRequestBody);
        this.callProbeConfigGroupListFilterRequestParameterSubject(probeConfigGroupFilterAction);
      } else {
        this.probeConfigGroupListRefreshSubject.next(
          listingPageReloadSubjectParameter
        );
      }
    }
  }

  /**
   * Set features
   */
  public setFeaturesList(featuresList: Array<FeaturesFilter>): void {
    this.featuresList = featuresList;
  }

  /**
   * Get features
   */
  public getFeaturesList(): Array<FeaturesFilter> {
    return this.featuresList;
  }

  /**
 * Set presets
 */
  public setPresetsList(presetList: Array<PresetDetailBaseResponse>): void {
    this.presetsList = presetList;
  }

  /**
   * Get presets
   */
  public getPresetsList(): Array<PresetDetailBaseResponse> {
    return this.presetsList;
  }

}
