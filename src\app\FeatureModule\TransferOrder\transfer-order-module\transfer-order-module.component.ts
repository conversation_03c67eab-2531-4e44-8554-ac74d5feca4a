import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TransferProductDetails } from 'src/app/model/device/TransferProductDetails.model';
import { SalesOrderTransferValidateRequest } from 'src/app/model/SalesOrder/SalesOrderTransferValidateRequest.model';
import { SourceSelectedProbeAndDevice } from 'src/app/model/SalesOrder/SourceSelectedProbeAndDevice.model';

@Component({
  selector: 'app-transfer-order-module',
  templateUrl: './transfer-order-module.component.html',
  styleUrl: './transfer-order-module.component.css'
})
export class TransferOrderModuleComponent {
  @Input("transferProductDetail") transferProductDetail: TransferProductDetails;
  @Output("showTranferOrder") showTranferOrder = new EventEmitter();

  // Flags to control visibility of different transfer order pages
  transferOrderSelectionDisaplay: boolean = true;
  transferOrderValidationDisaplay: boolean = false;
  transferOrderReviewDisaplay: boolean = false;
  salesOrderIsManual: boolean = false;

  destinationSalesOrderId: number; // Stores the selected destination sales order ID
  sourceSelectedProbeAndDevice: SourceSelectedProbeAndDevice; // Holds the transfer order request data
  transferOrderRequest: SalesOrderTransferValidateRequest;

  /**
  * Toggles the visibility of different sections in the transfer order module.
  * @param transferOrderSelectionDisaplay - Whether to show the selection page
  * @param transferOrderValidationDisaplay - Whether to show the validation page
  * @param transferOrderReviewDisaplay - Whether to show the review page
  */
  tansferOrderPagesToggle(
    transferOrderSelectionDisaplay: boolean,
    transferOrderValidationDisaplay: boolean,
    transferOrderReviewDisaplay: boolean
  ): void {
    this.transferOrderSelectionDisaplay = transferOrderSelectionDisaplay;
    this.transferOrderValidationDisaplay = transferOrderValidationDisaplay;
    this.transferOrderReviewDisaplay = transferOrderReviewDisaplay;
  }

  /**
  * Updates the destination sales order ID.
  * @param newId - The new destination sales order ID
  */
  updateDestinationId(newId: number): void {
    this.destinationSalesOrderId = newId;
  }

  /**
  * Sets the transfer order request details.
  * @param transferOrderRequest - The request object containing order details
  */
  selectedProduct(selectedProbeAndDevice: SourceSelectedProbeAndDevice): void {
    this.sourceSelectedProbeAndDevice = selectedProbeAndDevice;
  }

  /**
  * Sets the transfer order review request.
  * @param transferOrderRequest - The request object for the review
  */
  transferOrderReviewRequest(transferOrderRequest: SalesOrderTransferValidateRequest): void {
    this.transferOrderRequest = transferOrderRequest;
  }

  /**
  * Checks if the sales order is manual.
  * @returns boolean - True if the sales order is manual, otherwise false
  */
  public bothSalesOrderIsManual(salesOrderIsManual: boolean): void {
    this.salesOrderIsManual = salesOrderIsManual;
  }

  /**
  * Emits an event to navigate back to the previous detail page.
  */
  backToDetailPage(): void {
    this.showTranferOrder.emit();
  }
}
