import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastrService } from 'ngx-toastr';
import { DateTimeDisplayFormat, REVERSE_TRANSFER_PRODUCT_CONFIRMATION_MESSAGE } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { AuditListResponse } from 'src/app/model/Audit/AuditListResponse';
import { AuditDetailResponse } from 'src/app/model/Audit/auditDetailResponse.model';
import { BooleanKeyValueMapping } from 'src/app/model/common/BooleanKeyValueMapping.model';
import { EnumMapping } from 'src/app/model/common/EnumMapping.model';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { AuditApiCallService } from 'src/app/shared/Service/Audit/audit-api-call.service';
import { AuditService } from 'src/app/shared/Service/Audit/audit.service';
import { AuditActionEnum } from 'src/app/shared/enum/Audit/AuditActionEnum.enum';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';

@Component({
  selector: 'app-audit-detail-model',
  templateUrl: './audit-detail-model.component.html',
  styleUrls: ['./audit-detail-model.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class AuditDetailModelComponent implements OnInit {

  @Input("auditResponse") auditResponse: AuditListResponse;

  actionType: string = null;

  auditDetailResponse: Array<AuditDetailResponse> = [];

  dateTimeDisplayFormat: string = DateTimeDisplayFormat;

  //boolean key value mapping
  booleanKeyValueMap: Map<string, Array<BooleanKeyValueMapping>>;
  enumKeyValueMap: Map<string, Array<EnumMapping>>;

  isReversePermission: boolean = false;

  //Old Colume Hide Action List
  oldDataColumeHideForActionList: Array<string> = [AuditActionEnum.PROBE_ADD,
  AuditActionEnum.USER_CREATE, AuditActionEnum.ROLE_CREATE, AuditActionEnum.SOFTWARE_BUILD_SAVE,
  AuditActionEnum.VIDEO_ADD_VIDEO, AuditActionEnum.VIDEO_ADD_JSON, AuditActionEnum.PROBE_CREATE_IMPORT_CSV,
  AuditActionEnum.DEVICE_REGISTER_ADD, AuditActionEnum.BRIDGE_KIT_MANGEMENT_ADD_IMPORT_CSV, AuditActionEnum.OTS_KIT_MANGEMENT_ADD_IMPORT_CSV,
  AuditActionEnum.SALES_ORDER_SYNC_ERROR, AuditActionEnum.PROBE_CONFIG_GROUP_ADD, AuditActionEnum.SALES_ORDER_ADD_USER_DEFINED_ORDER, AuditActionEnum.COUNTRY_ADD];

  //New Colume Hide Action List
  newDataColumeHideForActionList: Array<string> = [AuditActionEnum.BRIDGE_MANGEMENT_DELETE_IMPORT_CSV, AuditActionEnum.OTS_KIT_MANGEMENT_DELETE_IMPORT_CSV];


  isOldDataColumeHide: boolean = false;
  isNewDataColumeHide: boolean = false;
  moduleUniqueKeyDisplayName: string = null;

  constructor(private activeModal: NgbActiveModal,
    private auditApiCallService: AuditApiCallService,
    private exceptionService: ExceptionHandlingService,
    private toste: ToastrService,
    private commonOperationsService: CommonOperationsService,
    private auditService: AuditService,
    private dialogservice: ConfirmDialogService,
  ) { }

  public ngOnInit(): void {
    this.booleanKeyValueMap = this.auditService.getBooleanKeyValueMap();
    this.enumKeyValueMap = this.auditService.getEnumKeyValueMap();
    this.isOldDataColumeHide = this.oldDataColumeHideForActionList.includes(this.auditResponse?.action);
    this.isNewDataColumeHide = this.newDataColumeHideForActionList.includes(this.auditResponse?.action);
    let moduleUniqueKeyMapping: Map<string, string> = this.auditService.getModuleUniqueKeyDisplayMapping();
    this.moduleUniqueKeyDisplayName = moduleUniqueKeyMapping.get(this.auditResponse?.module) == null ? this.auditService.getChildModuleUniqueKeyDisplay(this.auditResponse?.module, this.auditResponse?.action) : moduleUniqueKeyMapping.get(this.auditResponse?.module);
    this.isReversePermission = this.auditService.checkPermissionForRevert(this.auditResponse?.module);
    this.getAuditDetailData();
  }

  /**
   * Get Audit Detail
   *  
   * <AUTHOR>
   */
  public getAuditDetailData(): void {
    this.loadingStatus(true);
    this.auditApiCallService.getAuditDetail(this.auditResponse?.id, this.auditResponse?.archivedData)?.subscribe({
      next: (res: HttpResponse<Array<AuditDetailResponse>>) => {
        if (res.status === 200) {
          this.auditDetailResponse = res.body;
        } else {
          this.toste.info("No Data");
          this.decline();
        }
        this.loadingStatus(false);
      },
      error: (error: HttpErrorResponse) => {
        this.loadingStatus(false);
        this.exceptionService.customErrorMessage(error);
        this.decline();
      }
    })
  }

  /**
   * Set Loading Staus
   * 
   * <AUTHOR>
   * 
   * @param status 
   */
  private loadingStatus(status: boolean): void {
    this.commonOperationsService.callCommonLoadingSubject(status);
  }

  /**
   * Close Audit Detail Model
   * 
   * <AUTHOR>
   */
  public decline(): void {
    this.activeModal.close(false);
  }

  /**
  * Reverse Transfer Product
  *
  * <AUTHOR>
  */
  public reverseTransferProduct(): void {
    if (!this.auditResponse?.reverseButtonDisable) {
      this.dialogservice.confirm('Reverse Transfer', REVERSE_TRANSFER_PRODUCT_CONFIRMATION_MESSAGE)
        .then((confirmed) => {
          if (confirmed) {
            this.loadingStatus(true);
            this.auditApiCallService.reverseTransferProduct(this.auditResponse?.id, this.auditResponse?.module)?.subscribe({
              next: (res: HttpResponse<SuccessMessageResponse>) => {
                if (res.status === 200) {
                  this.toste.success(res.body?.message);
                  this.activeModal.close(true);
                }
                if (res.status === 204) {
                  this.toste.error(`No details found for auditId ${this.auditResponse?.id}`);
                  this.decline();
                }
                this.loadingStatus(false);
              },
              error: (error: HttpErrorResponse) => {
                this.loadingStatus(false);
                this.exceptionService.customErrorMessage(error);
                this.decline();
              }
            })
          }
        }).finally(() => {
        });
    }
  }
}
